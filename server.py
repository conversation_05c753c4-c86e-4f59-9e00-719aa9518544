#!/usr/bin/env python3
"""
Simple Flask server for Clinical Trials Web UI
"""

from flask import Flask, send_file, jsonify, request
import requests
import json
import os
import subprocess
import sys
import threading
import time
from datetime import datetime

app = Flask(__name__)

# Global progress tracking
progress_data = {
    'status': 'idle',
    'step': 0,
    'progress': 0,
    'message': 'Ready',
    'terminal_output': [],
    'results': []
}

@app.route('/')
def home():
    return send_file('clinical_trials_web_ui.html')

@app.route('/api/models')
def get_models():
    """Get Ollama models"""
    try:
        print("Fetching models from Ollama...")
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"Found {len(models)} models")
            
            model_list = []
            for model in models:
                size_gb = model.get('size', 0) / (1024 * 1024 * 1024)
                model_list.append({
                    'name': model.get('name', ''),
                    'display': f"{model.get('name', '')} ({size_gb:.1f} GB)"
                })
            
            return jsonify({'success': True, 'models': model_list})
        else:
            return jsonify({'success': False, 'error': 'Ollama not responding'})
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search', methods=['POST'])
def search():
    """Handle search request and start real extraction"""
    global progress_data

    data = request.json
    drug = data.get('drug', '').strip()
    criteria = data.get('search_criteria', '').strip()
    llm = data.get('llm', '')

    print(f"🚀 Starting real extraction: drug={drug}, criteria={criteria}, llm={llm}")

    if not drug or not criteria or not llm:
        return jsonify({'error': 'All fields required'}), 400

    # Reset progress
    progress_data = {
        'status': 'running',
        'step': 1,
        'progress': 0,
        'message': f'Starting search for {drug}...',
        'terminal_output': [f'🏥 Clinical Trials Extraction & Analysis'],
        'results': []
    }

    # Start real extraction in background
    thread = threading.Thread(target=run_real_extraction, args=(drug, criteria, llm))
    thread.daemon = True
    thread.start()

    return jsonify({'status': 'success', 'message': 'Real extraction started'})

@app.route('/api/progress')
def get_progress():
    """Get current progress"""
    return jsonify(progress_data)

def add_terminal_output(message):
    """Add message to terminal output"""
    global progress_data
    progress_data['terminal_output'].append(message)
    print(f"Terminal: {message}")

def run_real_extraction(drug, criteria, llm):
    """Run the actual extraction and analysis"""
    global progress_data

    try:
        add_terminal_output(f"Drug: {drug}")
        add_terminal_output(f"Search Criteria: {criteria}")
        add_terminal_output(f"AI Model: {llm}")
        add_terminal_output("=" * 50)

        # Step 1: Search for studies
        progress_data.update({
            'step': 1,
            'progress': 10,
            'message': f'Searching for {drug} studies...'
        })
        add_terminal_output(f"🔍 Searching for {drug} studies...")

        # Run search script
        result = subprocess.run([sys.executable, 'test_search_only.py', drug],
                              capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Search failed: {result.stderr}")

        add_terminal_output("✅ Search completed successfully")

        # Step 1: Extract studies
        progress_data.update({
            'step': 1,
            'progress': 50,
            'message': 'Running batch extraction...'
        })
        add_terminal_output("📁 Starting batch extraction...")

        # Run batch extraction
        process = subprocess.Popen([sys.executable, 'batch_extract_all_studies.py'],
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 text=True, bufsize=1, universal_newlines=True)

        # Stream output in real-time
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                add_terminal_output(line.strip())
                # Update progress based on output
                if "Processing" in line:
                    try:
                        # Extract progress from "[X/Y]" pattern
                        if "[" in line and "/" in line and "]" in line:
                            progress_part = line[line.find("[")+1:line.find("]")]
                            current, total = map(int, progress_part.split("/"))
                            progress_pct = 50 + (current / total * 40)  # 50-90%
                            progress_data['progress'] = min(90, progress_pct)
                    except:
                        pass

        process.wait()

        if process.returncode != 0:
            raise Exception("Batch extraction failed")

        progress_data.update({
            'step': 1,
            'progress': 100,
            'message': 'Study extraction completed!'
        })
        add_terminal_output("✅ Batch extraction completed!")

        time.sleep(1)

        # Step 2: AI Analysis
        progress_data.update({
            'step': 2,
            'progress': 0,
            'message': f'Starting AI analysis with {llm}...'
        })
        add_terminal_output("=" * 50)
        add_terminal_output(f"🤖 Starting AI analysis with {llm}...")

        # Run AI analysis
        process = subprocess.Popen([sys.executable, 'ai_analyze_studies.py', criteria, llm],
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 text=True, bufsize=1, universal_newlines=True)

        # Stream AI analysis output
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                add_terminal_output(line.strip())
                # Update progress based on AI analysis
                if "Analyzing" in line:
                    try:
                        if "[" in line and "/" in line and "]" in line:
                            progress_part = line[line.find("[")+1:line.find("]")]
                            current, total = map(int, progress_part.split("/"))
                            progress_pct = (current / total * 90)  # 0-90%
                            progress_data['progress'] = min(90, progress_pct)
                    except:
                        pass

        process.wait()

        if process.returncode != 0:
            raise Exception("AI analysis failed")

        # Load final results
        try:
            with open('ai_analysis_results.json', 'r') as f:
                analysis_results = json.load(f)

            matching_studies = analysis_results.get('matching_studies', [])
            total_analyzed = analysis_results.get('total_analyzed', 0)

            progress_data.update({
                'status': 'completed',
                'step': 2,
                'progress': 100,
                'message': f'Analysis complete! Found {len(matching_studies)} matching studies',
                'results': matching_studies
            })

            add_terminal_output("=" * 50)
            add_terminal_output("🎉 ANALYSIS COMPLETED!")
            add_terminal_output(f"Total studies analyzed: {total_analyzed}")
            add_terminal_output(f"Studies matching criteria: {len(matching_studies)}")

            for i, study in enumerate(matching_studies, 1):
                confidence = study.get('analysis', {}).get('confidence', 0)
                add_terminal_output(f"{i}. {study['nct_id']} (confidence: {confidence:.2f})")

        except Exception as e:
            raise Exception(f"Failed to load results: {str(e)}")

    except Exception as e:
        error_msg = f"Error: {str(e)}"
        add_terminal_output(f"❌ {error_msg}")
        progress_data.update({
            'status': 'error',
            'message': error_msg
        })

if __name__ == '__main__':
    print("🌐 Starting Clinical Trials Server")
    print("📍 Open: http://localhost:8080")
    
    # Check Ollama
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            print("✅ Ollama is running")
        else:
            print("⚠️ Ollama not responding")
    except:
        print("❌ Ollama not running")
    
    app.run(debug=True, host='0.0.0.0', port=8080)
