<!DOCTYPE html>
<html>
<head>
    <title>Test Ollama Models</title>
</head>
<body>
    <h1>Test Ollama Models Loading</h1>
    
    <select id="llm">
        <option value="">Loading models...</option>
    </select>
    
    <div id="status">Checking...</div>
    
    <button onclick="testAPI()">Test API Direct</button>
    
    <div id="debug"></div>

    <script>
        async function loadModels() {
            const llmSelect = document.getElementById('llm');
            const status = document.getElementById('status');
            const debug = document.getElementById('debug');
            
            try {
                console.log('Fetching models...');
                const response = await fetch('/api/ollama/models');
                console.log('Response:', response);
                
                const data = await response.json();
                console.log('Data:', data);
                
                debug.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success && data.models) {
                    llmSelect.innerHTML = '<option value="">Select Model</option>';
                    
                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name;
                        option.textContent = model.name;
                        llmSelect.appendChild(option);
                    });
                    
                    status.textContent = `Loaded ${data.models.length} models`;
                } else {
                    status.textContent = 'Failed to load models';
                }
                
            } catch (error) {
                console.error('Error:', error);
                status.textContent = 'Error: ' + error.message;
            }
        }
        
        function testAPI() {
            fetch('/api/ollama/models')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('debug').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    document.getElementById('debug').innerHTML = 'Error: ' + error.message;
                });
        }
        
        // Load on page ready
        document.addEventListener('DOMContentLoaded', loadModels);
    </script>
</body>
</html>
