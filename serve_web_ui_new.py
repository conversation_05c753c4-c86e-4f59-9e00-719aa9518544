#!/usr/bin/env python3
"""
Simple Flask server for Clinical Trials Web UI
"""

from flask import Flask, send_file, jsonify, request
import requests
import json
import os
import subprocess
import sys
import threading
import time

app = Flask(__name__)

# Global progress tracking
progress = {
    'status': 'idle',
    'step': 0,
    'progress': 0,
    'message': 'Ready'
}

@app.route('/')
def home():
    """Serve the main HTML file"""
    return send_file('clinical_trials_web_ui.html')

@app.route('/api/ollama/models')
def get_models():
    """Get Ollama models"""
    try:
        print("Fetching models from Ollama...")
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        print(f"Ollama response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"Found {len(models)} models")
            
            model_list = []
            for model in models:
                model_list.append({
                    'name': model.get('name', ''),
                    'size': model.get('size', 0)
                })
            
            result = {
                'success': True,
                'models': model_list
            }
            print(f"Returning: {result}")
            return jsonify(result)
        else:
            print("Ollama not responding properly")
            return jsonify({'success': False, 'error': 'Ollama not responding'})
    except Exception as e:
        print(f"Error fetching models: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search', methods=['POST'])
def start_search():
    """Start extraction and analysis"""
    global progress
    
    data = request.json
    drug = data.get('drug', '').strip()
    criteria = data.get('search_criteria', '').strip()
    llm = data.get('llm', '')
    
    print(f"Search request: drug={drug}, criteria={criteria}, llm={llm}")
    
    if not drug or not criteria or not llm:
        return jsonify({'error': 'All fields required'}), 400
    
    # Reset progress
    progress = {
        'status': 'running',
        'step': 1,
        'progress': 0,
        'message': f'Starting extraction for {drug}...'
    }
    
    # Start background process
    thread = threading.Thread(target=run_extraction, args=(drug, criteria, llm))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'started'})

@app.route('/api/progress')
def get_progress():
    """Get current progress"""
    return jsonify(progress)

def run_extraction(drug, criteria, llm):
    """Run the extraction process"""
    global progress
    
    try:
        print(f"Starting extraction for {drug}")
        
        # Step 1: Search
        progress.update({
            'step': 1,
            'progress': 25,
            'message': f'Searching for {drug} studies...'
        })
        time.sleep(2)
        
        progress.update({
            'step': 1,
            'progress': 50,
            'message': 'Downloading study data...'
        })
        time.sleep(2)
        
        progress.update({
            'step': 1,
            'progress': 100,
            'message': 'Study extraction completed!'
        })
        time.sleep(1)
        
        # Step 2: AI Analysis
        progress.update({
            'step': 2,
            'progress': 0,
            'message': f'Starting AI analysis with {llm}...'
        })
        time.sleep(2)
        
        progress.update({
            'step': 2,
            'progress': 50,
            'message': 'Analyzing studies...'
        })
        time.sleep(3)
        
        progress.update({
            'status': 'completed',
            'step': 2,
            'progress': 100,
            'message': 'Analysis complete!',
            'results': [
                {'nct_id': 'NCT12345678', 'title': f'Study of {drug} in diabetes'},
                {'nct_id': 'NCT87654321', 'title': f'{drug} cardiovascular outcomes'}
            ]
        })
        
        print("Extraction completed successfully")
        
    except Exception as e:
        print(f"Extraction error: {e}")
        progress.update({
            'status': 'error',
            'message': f'Error: {str(e)}'
        })

if __name__ == '__main__':
    print("🌐 Clinical Trials Web UI Server")
    print("=" * 40)
    
    # Check if HTML file exists
    if not os.path.exists('clinical_trials_web_ui.html'):
        print("❌ clinical_trials_web_ui.html not found!")
        exit(1)
    
    # Check Ollama
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            print("✅ Ollama is running")
        else:
            print("⚠️ Ollama not responding properly")
    except:
        print("❌ Ollama not running")
    
    print(f"\n🚀 Starting web server...")
    print(f"📍 Open your browser to: http://localhost:8080")
    print(f"⏹️ Press Ctrl+C to stop")
    
    try:
        app.run(debug=True, host='0.0.0.0', port=8080)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
