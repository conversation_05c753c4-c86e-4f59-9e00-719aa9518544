#!/usr/bin/env python3
"""
Alternative approach to extract adverse events from ClinicalTrials.gov
"""

import asyncio
from crawl4ai import AsyncWebCrawler
import pandas as pd
import json
import re
from bs4 import BeautifulSoup

async def try_different_urls():
    """
    Try different URL variations to access the clinical trial data
    """
    
    base_nct = "NCT02673138"
    urls_to_try = [
        f"https://clinicaltrials.gov/study/{base_nct}",
        f"https://clinicaltrials.gov/study/{base_nct}?tab=results",
        f"https://clinicaltrials.gov/ct2/show/results/{base_nct}",
        f"https://clinicaltrials.gov/ct2/show/{base_nct}?sect=X70156",  # Results section
        f"https://clinicaltrials.gov/api/query/study_fields?expr={base_nct}&fields=NCTId,BriefTitle,OverallStatus,Phase,StudyType,PrimaryOutcomeMeasure,SecondaryOutcomeMeasure,OtherOutcomeMeasure,Condition,InterventionName,ArmGroupLabel,ArmGroupType,ArmGroupDescription,ArmGroupInterventionName,PrimaryOutcomeDescription,SecondaryOutcomeDescription,OtherOutcomeDescription,StudyFirstSubmitDate,StudyFirstPostDate,LastUpdateSubmitDate,LastUpdatePostDate,CompletionDate,PrimaryCompletionDate,StudyFirstSubmitQCDate,StudyFirstPostDateType,LastUpdateSubmitQCDate,LastUpdatePostDateType,CompletionDateType,PrimaryCompletionDateType,OverallOfficialName,OverallOfficialAffiliation,OverallOfficialRole,LeadSponsorName,LeadSponsorClass,CollaboratorName,CollaboratorClass,ResponsiblePartyType,ResponsiblePartyInvestigatorFullName,ResponsiblePartyInvestigatorTitle,ResponsiblePartyInvestigatorAffiliation,ResponsiblePartyOldNameTitle,ResponsiblePartyOldOrganization,LocationFacility,LocationCity,LocationState,LocationZip,LocationCountry,LocationStatus,LocationContactName,LocationContactRole,LocationContactPhone,LocationContactEMail,LocationContactBackupName,LocationContactBackupRole,LocationContactBackupPhone,LocationContactBackupEMail,CentralContactName,CentralContactRole,CentralContactPhone,CentralContactEMail,CentralContactBackupName,CentralContactBackupRole,CentralContactBackupPhone,CentralContactBackupEMail,BriefSummary,DetailedDescription,EligibilityCriteria,HealthyVolunteers,Gender,MinimumAge,MaximumAge,StdAge,StudyPopulation,SamplingMethod,Criteria,GenderBased,GenderDescription,MinimumAge,MaximumAge,StdAge,StudyPopulation,SamplingMethod,Criteria,GenderBased,GenderDescription&min_rnk=1&max_rnk=1&fmt=json",
    ]
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        for i, url in enumerate(urls_to_try):
            print(f"\n=== Trying URL {i+1}: {url} ===")
            
            try:
                result = await crawler.arun(
                    url=url,
                    word_count_threshold=10,
                    bypass_cache=True,
                    wait_for="networkidle",
                    js_code="""
                    // Wait for content to load
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    
                    // Try to click on results-related elements
                    const elements = document.querySelectorAll('*');
                    for (let element of elements) {
                        const text = element.textContent || '';
                        if (text.toLowerCase().includes('results') || 
                            text.toLowerCase().includes('adverse') ||
                            text.toLowerCase().includes('safety')) {
                            if (element.tagName === 'A' || element.tagName === 'BUTTON' || 
                                element.getAttribute('role') === 'tab' ||
                                element.classList.contains('tab') ||
                                element.classList.contains('button')) {
                                console.log('Clicking element:', text.substring(0, 50));
                                element.click();
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            }
                        }
                    }
                    
                    // Scroll to load more content
                    window.scrollTo(0, document.body.scrollHeight);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    """
                )
                
                if result.success:
                    print(f"Successfully crawled URL {i+1}. Content length: {len(result.html)}")
                    
                    # Save the HTML for inspection
                    filename = f"url_{i+1}_content.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(result.html)
                    print(f"Saved content to {filename}")
                    
                    # Parse and look for adverse events data
                    soup = BeautifulSoup(result.html, 'html.parser')
                    
                    # Look for tables
                    tables = soup.find_all('table')
                    print(f"Found {len(tables)} tables")
                    
                    # Look for JSON data (especially for API URLs)
                    if 'api' in url:
                        try:
                            json_data = json.loads(result.html)
                            print("Found JSON data:")
                            print(json.dumps(json_data, indent=2)[:500] + "...")
                            
                            # Save JSON data
                            json_filename = f"url_{i+1}_data.json"
                            with open(json_filename, 'w') as f:
                                json.dump(json_data, f, indent=2)
                            print(f"Saved JSON data to {json_filename}")
                            
                        except json.JSONDecodeError:
                            print("Content is not valid JSON")
                    
                    # Look for specific adverse events content
                    adverse_content = []
                    for table in tables:
                        table_text = table.get_text().lower()
                        if any(keyword in table_text for keyword in ['adverse', 'safety', 'side effect', 'event']):
                            print(f"Found table with potential adverse events content")
                            adverse_content.append(table)
                    
                    if adverse_content:
                        print(f"Found {len(adverse_content)} tables with adverse events content")
                        return url, adverse_content
                    
                    # Look for any structured data about adverse events
                    text_content = soup.get_text()
                    if 'adverse event' in text_content.lower():
                        print("Found text mentioning adverse events")
                        # Extract relevant sections
                        paragraphs = soup.find_all('p')
                        adverse_paragraphs = []
                        for p in paragraphs:
                            if 'adverse' in p.get_text().lower():
                                adverse_paragraphs.append(p.get_text().strip())
                        
                        if adverse_paragraphs:
                            print(f"Found {len(adverse_paragraphs)} paragraphs mentioning adverse events")
                            for j, para in enumerate(adverse_paragraphs[:3]):  # Show first 3
                                print(f"Paragraph {j+1}: {para[:200]}...")
                
                else:
                    print(f"Failed to crawl URL {i+1}: {result.error_message}")
                    
            except Exception as e:
                print(f"Error with URL {i+1}: {e}")
                continue
    
    return None, []

async def extract_from_clinicaltrials_api():
    """
    Try to extract data using the ClinicalTrials.gov API
    """
    
    nct_id = "NCT02673138"
    api_url = f"https://clinicaltrials.gov/api/query/full_studies?expr={nct_id}&fmt=json"
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        print(f"Trying ClinicalTrials.gov API: {api_url}")
        
        result = await crawler.arun(
            url=api_url,
            bypass_cache=True
        )
        
        if result.success:
            try:
                data = json.loads(result.html)
                print("Successfully retrieved API data")
                
                # Save the full API response
                with open('clinicaltrials_api_response.json', 'w') as f:
                    json.dump(data, f, indent=2)
                print("Saved API response to clinicaltrials_api_response.json")
                
                # Extract study information
                if 'FullStudiesResponse' in data and 'FullStudies' in data['FullStudiesResponse']:
                    studies = data['FullStudiesResponse']['FullStudies']
                    if studies:
                        study = studies[0]['Study']
                        print(f"Study Title: {study.get('ProtocolSection', {}).get('IdentificationModule', {}).get('BriefTitle', 'N/A')}")
                        
                        # Look for results data
                        if 'ResultsSection' in study:
                            results = study['ResultsSection']
                            print("Found ResultsSection in API data")
                            
                            # Look for adverse events
                            if 'AdverseEventsModule' in results:
                                adverse_events = results['AdverseEventsModule']
                                print("Found AdverseEventsModule!")
                                
                                # Save adverse events data
                                with open('adverse_events_api_data.json', 'w') as f:
                                    json.dump(adverse_events, f, indent=2)
                                print("Saved adverse events data to adverse_events_api_data.json")
                                
                                return adverse_events
                            else:
                                print("No AdverseEventsModule found in results")
                                print("Available modules in ResultsSection:", list(results.keys()))
                        else:
                            print("No ResultsSection found in study data")
                            print("Available sections:", list(study.keys()))
                
            except json.JSONDecodeError as e:
                print(f"Failed to parse API response as JSON: {e}")
                print("Response content:", result.html[:500])
        else:
            print(f"Failed to retrieve API data: {result.error_message}")
    
    return None

async def main():
    print("Trying alternative approaches to extract adverse events data...")
    
    # First try the API approach
    print("\n=== Approach 1: ClinicalTrials.gov API ===")
    api_data = await extract_from_clinicaltrials_api()
    
    if api_data:
        print("Successfully extracted adverse events data from API!")
        # Process the API data here
        return
    
    # If API doesn't work, try different URLs
    print("\n=== Approach 2: Different URL variations ===")
    successful_url, adverse_content = await try_different_urls()
    
    if successful_url and adverse_content:
        print(f"Successfully found adverse events content at: {successful_url}")
        # Process the content here
    else:
        print("Could not find adverse events data through any of the attempted methods")

if __name__ == "__main__":
    asyncio.run(main())
