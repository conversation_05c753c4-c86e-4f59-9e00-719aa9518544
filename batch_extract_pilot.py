#!/usr/bin/env python3
"""
Pilot test: Extract data for just a few studies to test the system
"""

import json
import os
import subprocess
import sys
from pathlib import Path
import shutil

def load_matching_studies():
    """Load the matching studies from our search"""
    try:
        with open('matching_studies.json', 'r') as f:
            studies = json.load(f)
        return studies
    except FileNotFoundError:
        print("❌ matching_studies.json not found. Run test_search_only.py first.")
        return []

def update_script_nct_id(script_path, new_nct_id):
    """Update the NCT ID in a script file"""
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Replace the current NCT ID with the new one
    updated_content = content.replace('NCT02324842', new_nct_id)
    
    with open(script_path, 'w') as f:
        f.write(updated_content)

def extract_single_study(nct_id, study_title, base_dir="pilot_studies"):
    """Extract data for a single study"""
    print(f"\n📁 Extracting {nct_id}: {study_title[:50]}...")
    
    # Create study directory
    study_dir = Path(base_dir) / nct_id
    study_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy extraction scripts to study directory
    script_files = [
        'extract_study_details.py',
        'extract_researcher_view.py', 
        'extract_results_posted.py',
        'extract2csv.py'
    ]
    
    original_dir = os.getcwd()
    
    try:
        # Copy and update scripts
        for script in script_files:
            if os.path.exists(script):
                # Copy script to study directory
                shutil.copy2(script, study_dir / script)
                
                # Update NCT ID in the copied script
                update_script_nct_id(study_dir / script, nct_id)
        
        # Change to study directory and run extractions
        os.chdir(study_dir)
        
        results = {}
        
        # 1. Extract Study Details
        print(f"  📋 Study Details...")
        result = subprocess.run([sys.executable, 'extract_study_details.py'], 
                              capture_output=True, text=True, timeout=60)
        results['study_details'] = result.returncode == 0
        if result.returncode != 0:
            print(f"    ❌ Error: {result.stderr[:100]}")
        
        # 2. Extract Researcher View
        print(f"  🔬 Researcher View...")
        result = subprocess.run([sys.executable, 'extract_researcher_view.py'], 
                              capture_output=True, text=True, timeout=60)
        results['researcher_view'] = result.returncode == 0
        if result.returncode != 0:
            print(f"    ❌ Error: {result.stderr[:100]}")
        
        # 3. Extract Results Posted
        print(f"  📊 Results Posted...")
        result = subprocess.run([sys.executable, 'extract_results_posted.py'], 
                              capture_output=True, text=True, timeout=60)
        results['results_posted'] = result.returncode == 0
        if result.returncode != 0:
            print(f"    ❌ Error: {result.stderr[:100]}")
        
        # 4. Extract Adverse Events CSV (only if results extraction succeeded)
        if results['results_posted']:
            print(f"  ⚠️  Adverse Events...")
            result = subprocess.run([sys.executable, 'extract2csv.py'], 
                                  capture_output=True, text=True, timeout=60)
            results['adverse_events'] = result.returncode == 0
            if result.returncode != 0:
                print(f"    ❌ Error: {result.stderr[:100]}")
        else:
            results['adverse_events'] = False
            print(f"    ⏭️  Skipped (no results data)")
        
        # Clean up script files
        for script in script_files:
            script_path = Path(script)
            if script_path.exists():
                script_path.unlink()
        
        # Check what files were created
        expected_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json', 'adverse_events.csv']
        created_files = [f for f in expected_files if os.path.exists(f)]
        
        success_count = sum(results.values())
        print(f"  ✅ Success: {success_count}/4 extractions, {len(created_files)} files created")
        
        return results, created_files
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {'error': str(e)}, []
    finally:
        os.chdir(original_dir)

def main():
    print("🧪 PILOT TEST: Batch Extraction")
    print("=" * 50)
    
    # Load matching studies
    studies = load_matching_studies()
    if not studies:
        return
    
    print(f"Found {len(studies)} matching studies")
    
    # Test on first 3 studies
    test_studies = studies[:3]
    
    print(f"\nTesting extraction on {len(test_studies)} studies:")
    for i, study in enumerate(test_studies, 1):
        print(f"{i}. {study['nct_id']}: {study['title'][:50]}...")
    
    response = input(f"\nProceed with pilot test? (y/N): ")
    if response.lower() != 'y':
        print("❌ Test cancelled.")
        return
    
    # Extract data for test studies
    results = []
    
    for i, study in enumerate(test_studies, 1):
        print(f"\n{'='*60}")
        print(f"PILOT TEST {i}/{len(test_studies)}: {study['nct_id']}")
        print(f"{'='*60}")
        
        extraction_results, created_files = extract_single_study(
            study['nct_id'], 
            study['title']
        )
        
        results.append({
            'nct_id': study['nct_id'],
            'title': study['title'],
            'extraction_results': extraction_results,
            'created_files': created_files
        })
    
    # Summary
    print(f"\n{'='*60}")
    print("PILOT TEST SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        nct_id = result['nct_id']
        extraction_results = result['extraction_results']
        created_files = result['created_files']
        
        if 'error' in extraction_results:
            print(f"❌ {nct_id}: ERROR - {extraction_results['error']}")
        else:
            success_count = sum(extraction_results.values())
            print(f"✅ {nct_id}: {success_count}/4 extractions, {len(created_files)} files")
    
    # Save results
    with open('pilot_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nPilot test completed! Check 'pilot_studies' directory.")
    print(f"Results saved to: pilot_test_results.json")

if __name__ == "__main__":
    main()
