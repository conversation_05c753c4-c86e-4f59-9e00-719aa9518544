#!/usr/bin/env python3
"""
Test script to check specific study NCT02065791 which should clearly match "diabetes"
"""

import json
import os
from pathlib import Path

# Import the function from ai_analyze_studies
import sys
sys.path.append('.')
from ai_analyze_studies import load_study_data, create_analysis_prompt

def test_specific_study():
    """Test NCT02065791 which has 'Diabetic Nephropathy' in title"""
    
    study_dir = Path("output/canagliflozin/NCT02065791")
    
    if not study_dir.exists():
        print(f"❌ Study directory not found: {study_dir}")
        return
    
    print(f"📁 Testing NCT02065791 - should match 'diabetes'")
    
    # Load study data
    study_data = load_study_data(study_dir)

    # Check researcher_view data (nested structure)
    researcher_view = study_data.get('researcher_view', {})
    full_study_data = researcher_view.get('fullStudyData', {})
    protocol_section = full_study_data.get('protocolSection', {})
    identification_module = protocol_section.get('identificationModule', {})

    brief_title = identification_module.get('briefTitle', 'N/A')
    official_title = identification_module.get('officialTitle', 'N/A')
    
    print(f"📋 Brief Title: {brief_title}")
    print(f"📋 Official Title: {official_title}")
    
    # Check if diabetes terms are in the titles
    diabetes_terms = ['diabetes', 'diabetic', 'diabetic nephropathy', 'type 1 diabetes', 'type 2 diabetes', 'T1DM', 'T2DM']
    
    found_diabetes = False
    for term in diabetes_terms:
        if term.lower() in brief_title.lower() or term.lower() in official_title.lower():
            print(f"✅ Found diabetes term: '{term}' in title")
            found_diabetes = True
    
    if not found_diabetes:
        print(f"❌ No diabetes terms found in titles")
    
    # Create the analysis prompt
    search_criteria = "diabetes, arrhythmia, fibrillation"
    nct_id = "NCT02065791"
    
    prompt = create_analysis_prompt(study_data, search_criteria, nct_id)
    
    # Check if the titles are correctly included in the prompt
    print(f"\n📝 Checking if titles are in the prompt:")
    if brief_title in prompt:
        print(f"✅ Brief title found in prompt")
    else:
        print(f"❌ Brief title NOT found in prompt")
        
    if official_title in prompt:
        print(f"✅ Official title found in prompt")
    else:
        print(f"❌ Official title NOT found in prompt")
    
    # Save the prompt for inspection
    with open('debug_prompt_NCT02065791.txt', 'w') as f:
        f.write(prompt)
    
    print(f"\n💾 Prompt saved to: debug_prompt_NCT02065791.txt")
    print(f"📏 Prompt length: {len(prompt)} characters")

if __name__ == "__main__":
    test_specific_study()
