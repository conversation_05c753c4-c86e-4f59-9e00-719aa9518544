#!/usr/bin/env python3
"""
Automated batch extraction (no user prompts) for web interface
"""

import json
import os
import subprocess
import sys
import argparse
from pathlib import Path
import shutil
from datetime import datetime

def load_matching_studies():
    """Load the matching studies from JSON file"""
    try:
        with open('matching_studies.json', 'r') as f:
            studies = json.load(f)
        print(f"✅ Loaded {len(studies)} matching studies")
        return studies
    except FileNotFoundError:
        print("❌ matching_studies.json not found. Run search first.")
        return []

def update_script_nct_id(script_path, new_nct_id):
    """Update the NCT ID in a script file"""
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Replace the current NCT ID with the new one
    updated_content = content.replace('NCT02324842', new_nct_id)
    
    with open(script_path, 'w') as f:
        f.write(updated_content)

def extract_single_study(nct_id, study_title, base_dir="studies"):
    """Extract data for a single study"""
    print(f"📁 {nct_id}: {study_title[:50]}...", flush=True)
    
    # Create study directory
    study_dir = Path(base_dir) / nct_id
    study_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy extraction scripts to study directory
    script_files = [
        'extract_study_details.py',
        'extract_researcher_view.py', 
        'extract_results_posted.py',
        'extract2csv.py'
    ]
    
    original_dir = os.getcwd()
    
    try:
        # Copy and update scripts
        for script in script_files:
            if os.path.exists(script):
                shutil.copy2(script, study_dir / script)
                update_script_nct_id(study_dir / script, nct_id)
        
        # Change to study directory and run extractions
        os.chdir(study_dir)
        
        results = {}
        
        # 1. Extract Study Details
        result = subprocess.run([sys.executable, 'extract_study_details.py'], 
                              capture_output=True, text=True, timeout=60)
        results['study_details'] = result.returncode == 0
        
        # 2. Extract Researcher View
        result = subprocess.run([sys.executable, 'extract_researcher_view.py'], 
                              capture_output=True, text=True, timeout=60)
        results['researcher_view'] = result.returncode == 0
        
        # 3. Extract Results Posted
        result = subprocess.run([sys.executable, 'extract_results_posted.py'], 
                              capture_output=True, text=True, timeout=60)
        results['results_posted'] = result.returncode == 0
        
        # 4. Extract Adverse Events CSV
        if results['results_posted']:
            result = subprocess.run([sys.executable, 'extract2csv.py'], 
                                  capture_output=True, text=True, timeout=60)
            results['adverse_events'] = result.returncode == 0
        else:
            results['adverse_events'] = False
        
        # Clean up script files
        for script in script_files:
            script_path = Path(script)
            if script_path.exists():
                script_path.unlink()
        
        # Check created files
        expected_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json', 'adverse_events.csv']
        created_files = [f for f in expected_files if os.path.exists(f)]
        
        success_count = sum(results.values())
        status = "✅" if success_count == 4 else "⚠️" if success_count >= 2 else "❌"
        print(f"  {status} {success_count}/4 extractions, {len(created_files)} files", flush=True)
        
        return results, created_files
        
    except Exception as e:
        print(f"  ❌ Error: {e}", flush=True)
        return {'error': str(e)}, []
    finally:
        os.chdir(original_dir)

def main(output_dir='studies'):
    print("🏥 CLINICAL TRIALS - AUTOMATED BATCH EXTRACTION")
    print("=" * 60)
    print("Extracting data from all completed studies with results")
    print("=" * 60)
    
    # Load matching studies
    studies = load_matching_studies()
    if not studies:
        return
    
    print(f"\nStudies to extract:")
    for i, study in enumerate(studies, 1):
        print(f"{i:2d}. {study['nct_id']}: {study['title'][:50]}...")
    
    print(f"\nThis will create:")
    print(f"- {len(studies)} study folders (named by NCT ID)")
    print(f"- 5 files per study (3 JSON + 1 intermediate JSON + 1 CSV)")
    print(f"- Total: ~{len(studies) * 5} files")
    
    # NO USER PROMPT - Auto proceed
    print(f"\n🚀 Auto-proceeding with batch extraction...")
    
    # Start batch extraction
    start_time = datetime.now()
    print(f"Started at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    successful = 0
    failed = 0
    
    for i, study in enumerate(studies, 1):
        print(f"\n[{i:2d}/{len(studies)}] ", end="", flush=True)

        extraction_results, created_files = extract_single_study(
            study['nct_id'],
            study['title'],
            output_dir
        )
        
        if 'error' not in extraction_results:
            success_count = sum(extraction_results.values())
            if success_count >= 2:  # At least 2 successful extractions
                successful += 1
            else:
                failed += 1
        else:
            failed += 1
        
        results.append({
            'nct_id': study['nct_id'],
            'title': study['title'],
            'results': extraction_results,
            'files': created_files
        })
    
    # Final summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n" + "=" * 60)
    print(f"BATCH EXTRACTION COMPLETED!")
    print(f"=" * 60)
    print(f"Duration: {duration}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total: {len(studies)}")
    print(f"Success rate: {successful/len(studies)*100:.1f}%")
    
    # Save summary
    summary = {
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'duration_seconds': duration.total_seconds(),
        'total_studies': len(studies),
        'successful': successful,
        'failed': failed,
        'success_rate': successful/len(studies)*100,
        'results': results
    }
    
    with open('batch_extraction_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📄 Summary saved to: batch_extraction_summary.json")
    print(f"📁 Study data saved to: {output_dir}/ directory")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Automated batch extraction')
    parser.add_argument('--output-dir', default='studies', help='Output directory for extracted studies')
    args = parser.parse_args()

    main(args.output_dir)
