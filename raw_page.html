<!DOCTYPE html>

<html lang="en" data-beasties-container>
<head><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <meta charset="UTF-8">
  <script type="text/javascript">
    window.ncbi_startTime = new Date();
  </script>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- Mobile properties -->
  <meta name="HandheldFriendly" content="True">
  <meta name="MobileOptimized" content="320">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1">

  <style>@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}</style>
  <style>@font-face{font-family:'Material Icons';font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');}.material-icons{font-family:'Material Icons';font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-feature-settings:'liga';-webkit-font-smoothing:antialiased;}</style>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC3PtSiQNBORX_rM0MRLHNA9VybME6zF1A&amp;libraries=places&amp;region=US&amp;language=en"></script>

  <title>ClinicalTrials.gov</title>
  <base href="/">

  <!-- Favicons -->
  <link rel="shortcut icon" type="image/svg" href="assets/favicons/favicon.svg">
  <link rel="icon" type="image/svg" href="assets/favicons/favicon.svg">

  <!-- 192x192, as recommended for Android
  http://updates.html5rocks.com/2014/11/Support-for-theme-color-in-Chrome-39-for-Android
  -->
  <link rel="icon" type="image/svg" sizes="192x192" href="assets/favicons/favicon.svg">

  <!-- 57x57 (precomposed) for iPhone 3GS, pre-2011 iPod Touch and older Android devices -->
  <link rel="apple-touch-icon-precomposed" href="assets/favicons/favicon.svg">
  <!-- 72x72 (precomposed) for 1st generation iPad, iPad 2 and iPad mini -->
  <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/favicons/favicon.svg">
  <!-- 114x114 (precomposed) for iPhone 4, 4S, 5 and post-2011 iPod Touch -->
  <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/favicons/favicon.svg">
  <!-- 144x144 (precomposed) for iPad 3rd and 4th generation -->
  <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/favicons/favicon.svg">


  <!-- Page description -->
  <meta name="description" content>

  <meta property="og:title" content>
  <meta property="og:description" content>

  <meta name="twitter:title" content>
  <meta name="twitter:description" content>
  <meta name="twitter:card" content="summary">

  <!-- robots -->
  <meta name="robots" content="index, follow">

  <!-- Pinger -->
  <meta name="ncbi_app" content="ctgov2">
  <meta name="ncbi_db" content>
  <meta name="ncbi_pinger_track_stat" content="false">
  <meta name="ncbi_pinger_ga_track" content="true">
  <meta name="ncbi_pinger_ga_tracking_id" content="UA-68648221-4">
  <meta name="ncbi_pinger_url_query_strip" content="lat,lng,locStr">
  <meta name="ncbi_pinger_click_use_capture" content="true">
  <meta name="ncbi_pinger_gtm_track" content="true">

  <meta name="ncbi_app_version" content>
<style>:root{--fa-style-family-brands:"Font Awesome 6 Brands";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands"}@font-face{font-family:"Font Awesome 6 Brands";font-style:normal;font-weight:400;font-display:block;src:url("./media/fa-brands-400-Q3XCMWHQ.woff2") format("woff2"),url("./media/fa-brands-400-R2XQZCET.ttf") format("truetype")}:root{--fa-style-family-classic:"Font Awesome 6 Free";--fa-font-regular:normal 400 1em/1 "Font Awesome 6 Free"}@font-face{font-family:"Font Awesome 6 Free";font-style:normal;font-weight:400;font-display:block;src:url("./media/fa-regular-400-QSNYFYRT.woff2") format("woff2"),url("./media/fa-regular-400-XUOPSR7E.ttf") format("truetype")}:root{--fa-style-family-classic:"Font Awesome 6 Free";--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free"}@font-face{font-family:"Font Awesome 6 Free";font-style:normal;font-weight:900;font-display:block;src:url("./media/fa-solid-900-5ZUYHGA7.woff2") format("woff2"),url("./media/fa-solid-900-PJNKLK6W.ttf") format("truetype")}html{--mat-sys-on-surface:initial}html{--mat-ripple-color:rgba(0, 0, 0, .1)}html{--mat-option-selected-state-label-text-color:#0071bc;--mat-option-label-text-color:rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color:rgba(0, 0, 0, .04)}html{--mat-optgroup-label-text-color:rgba(0, 0, 0, .87)}html{--mat-full-pseudo-checkbox-selected-icon-color:#448aff;--mat-full-pseudo-checkbox-selected-checkmark-color:#fafafa;--mat-full-pseudo-checkbox-unselected-icon-color:rgba(0, 0, 0, .54);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#fafafa;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:#b0b0b0;--mat-full-pseudo-checkbox-disabled-selected-icon-color:#b0b0b0}html{--mat-minimal-pseudo-checkbox-selected-checkmark-color:#448aff;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:#b0b0b0}html{--mat-app-background-color:#fafafa;--mat-app-text-color:rgba(0, 0, 0, .87);--mat-app-elevation-shadow-level-0:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-1:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-2:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-3:0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-4:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-5:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 5px 8px 0px rgba(0, 0, 0, .14), 0px 1px 14px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-6:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-7:0px 4px 5px -2px rgba(0, 0, 0, .2), 0px 7px 10px 1px rgba(0, 0, 0, .14), 0px 2px 16px 1px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-8:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-9:0px 5px 6px -3px rgba(0, 0, 0, .2), 0px 9px 12px 1px rgba(0, 0, 0, .14), 0px 3px 16px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-10:0px 6px 6px -3px rgba(0, 0, 0, .2), 0px 10px 14px 1px rgba(0, 0, 0, .14), 0px 4px 18px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-11:0px 6px 7px -4px rgba(0, 0, 0, .2), 0px 11px 15px 1px rgba(0, 0, 0, .14), 0px 4px 20px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-12:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-13:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 13px 19px 2px rgba(0, 0, 0, .14), 0px 5px 24px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-14:0px 7px 9px -4px rgba(0, 0, 0, .2), 0px 14px 21px 2px rgba(0, 0, 0, .14), 0px 5px 26px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-15:0px 8px 9px -5px rgba(0, 0, 0, .2), 0px 15px 22px 2px rgba(0, 0, 0, .14), 0px 6px 28px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-16:0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-17:0px 8px 11px -5px rgba(0, 0, 0, .2), 0px 17px 26px 2px rgba(0, 0, 0, .14), 0px 6px 32px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-18:0px 9px 11px -5px rgba(0, 0, 0, .2), 0px 18px 28px 2px rgba(0, 0, 0, .14), 0px 7px 34px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-19:0px 9px 12px -6px rgba(0, 0, 0, .2), 0px 19px 29px 2px rgba(0, 0, 0, .14), 0px 7px 36px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-20:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 20px 31px 3px rgba(0, 0, 0, .14), 0px 8px 38px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-21:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 21px 33px 3px rgba(0, 0, 0, .14), 0px 8px 40px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-22:0px 10px 14px -6px rgba(0, 0, 0, .2), 0px 22px 35px 3px rgba(0, 0, 0, .14), 0px 8px 42px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-23:0px 11px 14px -7px rgba(0, 0, 0, .2), 0px 23px 36px 3px rgba(0, 0, 0, .14), 0px 9px 44px 8px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-24:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mdc-elevated-card-container-shape:4px}html{--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}html{--mdc-elevated-card-container-color:white;--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12)}html{--mdc-outlined-card-container-color:white;--mdc-outlined-card-outline-color:rgba(0, 0, 0, .12);--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12)}html{--mat-card-subtitle-text-color:rgba(0, 0, 0, .54)}html{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}html{--mdc-plain-tooltip-container-shape:4px;--mdc-plain-tooltip-supporting-text-line-height:16px}html{--mdc-plain-tooltip-container-color:#616161;--mdc-plain-tooltip-supporting-text-color:#fff}html{--mdc-filled-text-field-active-indicator-height:1px;--mdc-filled-text-field-focus-active-indicator-height:2px;--mdc-filled-text-field-container-shape:4px}html{--mdc-outlined-text-field-outline-width:1px;--mdc-outlined-text-field-focus-outline-width:2px;--mdc-outlined-text-field-container-shape:4px}html{--mdc-filled-text-field-caret-color:#0071bc;--mdc-filled-text-field-focus-active-indicator-color:#0071bc;--mdc-filled-text-field-focus-label-text-color:rgba(0, 113, 188, .87);--mdc-filled-text-field-container-color:rgb(244.8, 244.8, 244.8);--mdc-filled-text-field-disabled-container-color:rgb(249.9, 249.9, 249.9);--mdc-filled-text-field-label-text-color:rgba(0, 0, 0, .6);--mdc-filled-text-field-hover-label-text-color:rgba(0, 0, 0, .6);--mdc-filled-text-field-disabled-label-text-color:rgba(0, 0, 0, .38);--mdc-filled-text-field-input-text-color:rgba(0, 0, 0, .87);--mdc-filled-text-field-disabled-input-text-color:rgba(0, 0, 0, .38);--mdc-filled-text-field-input-text-placeholder-color:rgba(0, 0, 0, .6);--mdc-filled-text-field-error-hover-label-text-color:#f44336;--mdc-filled-text-field-error-focus-label-text-color:#f44336;--mdc-filled-text-field-error-label-text-color:#f44336;--mdc-filled-text-field-error-caret-color:#f44336;--mdc-filled-text-field-active-indicator-color:rgba(0, 0, 0, .42);--mdc-filled-text-field-disabled-active-indicator-color:rgba(0, 0, 0, .06);--mdc-filled-text-field-hover-active-indicator-color:rgba(0, 0, 0, .87);--mdc-filled-text-field-error-active-indicator-color:#f44336;--mdc-filled-text-field-error-focus-active-indicator-color:#f44336;--mdc-filled-text-field-error-hover-active-indicator-color:#f44336}html{--mdc-outlined-text-field-caret-color:#0071bc;--mdc-outlined-text-field-focus-outline-color:#0071bc;--mdc-outlined-text-field-focus-label-text-color:rgba(0, 113, 188, .87);--mdc-outlined-text-field-label-text-color:rgba(0, 0, 0, .6);--mdc-outlined-text-field-hover-label-text-color:rgba(0, 0, 0, .6);--mdc-outlined-text-field-disabled-label-text-color:rgba(0, 0, 0, .38);--mdc-outlined-text-field-input-text-color:rgba(0, 0, 0, .87);--mdc-outlined-text-field-disabled-input-text-color:rgba(0, 0, 0, .38);--mdc-outlined-text-field-input-text-placeholder-color:rgba(0, 0, 0, .6);--mdc-outlined-text-field-error-caret-color:#f44336;--mdc-outlined-text-field-error-focus-label-text-color:#f44336;--mdc-outlined-text-field-error-label-text-color:#f44336;--mdc-outlined-text-field-error-hover-label-text-color:#f44336;--mdc-outlined-text-field-outline-color:rgba(0, 0, 0, .38);--mdc-outlined-text-field-disabled-outline-color:rgba(0, 0, 0, .06);--mdc-outlined-text-field-hover-outline-color:rgba(0, 0, 0, .87);--mdc-outlined-text-field-error-focus-outline-color:#f44336;--mdc-outlined-text-field-error-hover-outline-color:#f44336;--mdc-outlined-text-field-error-outline-color:#f44336}html{--mat-form-field-focus-select-arrow-color:rgba(0, 113, 188, .87);--mat-form-field-disabled-input-text-placeholder-color:rgba(0, 0, 0, .38);--mat-form-field-state-layer-color:rgba(0, 0, 0, .87);--mat-form-field-error-text-color:#f44336;--mat-form-field-select-option-text-color:inherit;--mat-form-field-select-disabled-option-text-color:GrayText;--mat-form-field-leading-icon-color:unset;--mat-form-field-disabled-leading-icon-color:unset;--mat-form-field-trailing-icon-color:unset;--mat-form-field-disabled-trailing-icon-color:unset;--mat-form-field-error-focus-trailing-icon-color:unset;--mat-form-field-error-hover-trailing-icon-color:unset;--mat-form-field-error-trailing-icon-color:unset;--mat-form-field-enabled-select-arrow-color:rgba(0, 0, 0, .54);--mat-form-field-disabled-select-arrow-color:rgba(0, 0, 0, .38);--mat-form-field-hover-state-layer-opacity:.04;--mat-form-field-focus-state-layer-opacity:.08}html{--mat-form-field-container-height:56px;--mat-form-field-filled-label-display:block;--mat-form-field-container-vertical-padding:16px;--mat-form-field-filled-with-label-container-padding-top:24px;--mat-form-field-filled-with-label-container-padding-bottom:8px}html{--mat-select-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, .38);--mat-select-placeholder-text-color:rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, .38);--mat-select-focused-arrow-color:rgba(0, 113, 188, .87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, .87)}html{--mat-select-arrow-transform:translateY(-8px)}html{--mat-autocomplete-container-shape:4px;--mat-autocomplete-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-autocomplete-background-color:white}html{--mdc-dialog-container-shape:4px}html{--mat-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12);--mat-dialog-container-max-width:80vw;--mat-dialog-container-small-max-width:80vw;--mat-dialog-container-min-width:0;--mat-dialog-actions-alignment:start;--mat-dialog-actions-padding:8px;--mat-dialog-content-padding:20px 24px;--mat-dialog-with-actions-content-padding:20px 24px;--mat-dialog-headline-padding:0 24px 9px}html{--mdc-dialog-container-color:white;--mdc-dialog-subhead-color:rgba(0, 0, 0, .87);--mdc-dialog-supporting-text-color:rgba(0, 0, 0, .6)}html{--mdc-switch-disabled-selected-icon-opacity:.38;--mdc-switch-disabled-track-opacity:.12;--mdc-switch-disabled-unselected-icon-opacity:.38;--mdc-switch-handle-height:20px;--mdc-switch-handle-shape:10px;--mdc-switch-handle-width:20px;--mdc-switch-selected-icon-size:18px;--mdc-switch-track-height:14px;--mdc-switch-track-shape:7px;--mdc-switch-track-width:36px;--mdc-switch-unselected-icon-size:18px;--mdc-switch-selected-focus-state-layer-opacity:.12;--mdc-switch-selected-hover-state-layer-opacity:.04;--mdc-switch-selected-pressed-state-layer-opacity:.1;--mdc-switch-unselected-focus-state-layer-opacity:.12;--mdc-switch-unselected-hover-state-layer-opacity:.04;--mdc-switch-unselected-pressed-state-layer-opacity:.1}html{--mdc-switch-selected-focus-state-layer-color:#3949ab;--mdc-switch-selected-handle-color:#3949ab;--mdc-switch-selected-hover-state-layer-color:#3949ab;--mdc-switch-selected-pressed-state-layer-color:#3949ab;--mdc-switch-selected-focus-handle-color:#112e51;--mdc-switch-selected-hover-handle-color:#112e51;--mdc-switch-selected-pressed-handle-color:#112e51;--mdc-switch-selected-focus-track-color:#80b8dd;--mdc-switch-selected-hover-track-color:#80b8dd;--mdc-switch-selected-pressed-track-color:#80b8dd;--mdc-switch-selected-track-color:#80b8dd;--mdc-switch-disabled-selected-handle-color:#424242;--mdc-switch-disabled-selected-icon-color:#fff;--mdc-switch-disabled-selected-track-color:#424242;--mdc-switch-disabled-unselected-handle-color:#424242;--mdc-switch-disabled-unselected-icon-color:#fff;--mdc-switch-disabled-unselected-track-color:#424242;--mdc-switch-handle-surface-color:#fff;--mdc-switch-selected-icon-color:#fff;--mdc-switch-unselected-focus-handle-color:#212121;--mdc-switch-unselected-focus-state-layer-color:#424242;--mdc-switch-unselected-focus-track-color:#e0e0e0;--mdc-switch-unselected-handle-color:#616161;--mdc-switch-unselected-hover-handle-color:#212121;--mdc-switch-unselected-hover-state-layer-color:#424242;--mdc-switch-unselected-hover-track-color:#e0e0e0;--mdc-switch-unselected-icon-color:#fff;--mdc-switch-unselected-pressed-handle-color:#212121;--mdc-switch-unselected-pressed-state-layer-color:#424242;--mdc-switch-unselected-pressed-track-color:#e0e0e0;--mdc-switch-unselected-track-color:#e0e0e0;--mdc-switch-handle-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-switch-disabled-handle-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12)}html{--mdc-switch-disabled-label-text-color:rgba(0, 0, 0, .38)}html{--mdc-switch-state-layer-size:40px}html{--mdc-radio-disabled-selected-icon-opacity:.38;--mdc-radio-disabled-unselected-icon-opacity:.38;--mdc-radio-state-layer-size:40px}html{--mdc-radio-state-layer-size:40px}html{--mat-radio-touch-target-display:block}html{--mdc-slider-active-track-height:6px;--mdc-slider-active-track-shape:9999px;--mdc-slider-handle-height:20px;--mdc-slider-handle-shape:50%;--mdc-slider-handle-width:20px;--mdc-slider-inactive-track-height:4px;--mdc-slider-inactive-track-shape:9999px;--mdc-slider-with-overlap-handle-outline-width:1px;--mdc-slider-with-tick-marks-active-container-opacity:.6;--mdc-slider-with-tick-marks-container-shape:50%;--mdc-slider-with-tick-marks-container-size:2px;--mdc-slider-with-tick-marks-inactive-container-opacity:.6;--mdc-slider-handle-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12)}html{--mat-slider-value-indicator-width:auto;--mat-slider-value-indicator-height:32px;--mat-slider-value-indicator-caret-display:block;--mat-slider-value-indicator-border-radius:4px;--mat-slider-value-indicator-padding:0 12px;--mat-slider-value-indicator-text-transform:none;--mat-slider-value-indicator-container-transform:translateX(-50%)}html{--mdc-slider-handle-color:#0071bc;--mdc-slider-focus-handle-color:#0071bc;--mdc-slider-hover-handle-color:#0071bc;--mdc-slider-active-track-color:#0071bc;--mdc-slider-inactive-track-color:#0071bc;--mdc-slider-with-tick-marks-inactive-container-color:#0071bc;--mdc-slider-with-tick-marks-active-container-color:white;--mdc-slider-disabled-active-track-color:#000;--mdc-slider-disabled-handle-color:#000;--mdc-slider-disabled-inactive-track-color:#000;--mdc-slider-label-container-color:#000;--mdc-slider-label-label-text-color:#fff;--mdc-slider-with-overlap-handle-outline-color:#fff;--mdc-slider-with-tick-marks-disabled-container-color:#000}html{--mat-slider-ripple-color:#0071bc;--mat-slider-hover-state-layer-color:rgba(0, 113, 188, .05);--mat-slider-focus-state-layer-color:rgba(0, 113, 188, .2);--mat-slider-value-indicator-opacity:.6}html{--mat-menu-container-shape:4px;--mat-menu-divider-bottom-spacing:0;--mat-menu-divider-top-spacing:0;--mat-menu-item-spacing:16px;--mat-menu-item-icon-size:24px;--mat-menu-item-leading-spacing:16px;--mat-menu-item-trailing-spacing:16px;--mat-menu-item-with-icon-leading-spacing:16px;--mat-menu-item-with-icon-trailing-spacing:16px;--mat-menu-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-menu-item-label-text-color:rgba(0, 0, 0, .87);--mat-menu-item-icon-color:rgba(0, 0, 0, .87);--mat-menu-item-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-menu-item-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-menu-container-color:white;--mat-menu-divider-color:rgba(0, 0, 0, .12)}html{--mdc-list-list-item-container-shape:0;--mdc-list-list-item-leading-avatar-shape:50%;--mdc-list-list-item-container-color:transparent;--mdc-list-list-item-selected-container-color:transparent;--mdc-list-list-item-leading-avatar-color:transparent;--mdc-list-list-item-leading-icon-size:24px;--mdc-list-list-item-leading-avatar-size:40px;--mdc-list-list-item-trailing-icon-size:24px;--mdc-list-list-item-disabled-state-layer-color:transparent;--mdc-list-list-item-disabled-state-layer-opacity:0;--mdc-list-list-item-disabled-label-text-opacity:.38;--mdc-list-list-item-disabled-leading-icon-opacity:.38;--mdc-list-list-item-disabled-trailing-icon-opacity:.38}html{--mat-list-active-indicator-color:transparent;--mat-list-active-indicator-shape:4px}html{--mdc-list-list-item-label-text-color:rgba(0, 0, 0, .87);--mdc-list-list-item-supporting-text-color:rgba(0, 0, 0, .54);--mdc-list-list-item-leading-icon-color:rgba(0, 0, 0, .38);--mdc-list-list-item-trailing-supporting-text-color:rgba(0, 0, 0, .38);--mdc-list-list-item-trailing-icon-color:rgba(0, 0, 0, .38);--mdc-list-list-item-selected-trailing-icon-color:rgba(0, 0, 0, .38);--mdc-list-list-item-disabled-label-text-color:black;--mdc-list-list-item-disabled-leading-icon-color:black;--mdc-list-list-item-disabled-trailing-icon-color:black;--mdc-list-list-item-hover-label-text-color:rgba(0, 0, 0, .87);--mdc-list-list-item-hover-leading-icon-color:rgba(0, 0, 0, .38);--mdc-list-list-item-hover-trailing-icon-color:rgba(0, 0, 0, .38);--mdc-list-list-item-focus-label-text-color:rgba(0, 0, 0, .87);--mdc-list-list-item-hover-state-layer-color:black;--mdc-list-list-item-hover-state-layer-opacity:.04;--mdc-list-list-item-focus-state-layer-color:black;--mdc-list-list-item-focus-state-layer-opacity:.12}html{--mdc-list-list-item-one-line-container-height:48px;--mdc-list-list-item-two-line-container-height:64px;--mdc-list-list-item-three-line-container-height:88px}html{--mat-list-list-item-leading-icon-start-space:16px;--mat-list-list-item-leading-icon-end-space:32px}html{--mat-paginator-container-text-color:rgba(0, 0, 0, .87);--mat-paginator-container-background-color:white;--mat-paginator-enabled-icon-color:rgba(0, 0, 0, .54);--mat-paginator-disabled-icon-color:rgba(0, 0, 0, .12)}html{--mat-paginator-container-size:56px;--mat-paginator-form-field-container-height:40px;--mat-paginator-form-field-container-vertical-padding:8px;--mat-paginator-touch-target-display:block}html{--mdc-secondary-navigation-tab-container-height:48px}html{--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0}html{--mat-tab-header-divider-color:transparent;--mat-tab-header-divider-height:0}html{--mdc-checkbox-disabled-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-state-layer-opacity:.16;--mdc-checkbox-selected-hover-state-layer-opacity:.04;--mdc-checkbox-selected-pressed-state-layer-opacity:.16;--mdc-checkbox-unselected-focus-state-layer-opacity:.16;--mdc-checkbox-unselected-hover-state-layer-opacity:.04;--mdc-checkbox-unselected-pressed-state-layer-opacity:.16}html{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color:white;--mdc-checkbox-selected-focus-icon-color:#448aff;--mdc-checkbox-selected-hover-icon-color:#448aff;--mdc-checkbox-selected-icon-color:#448aff;--mdc-checkbox-selected-pressed-icon-color:#448aff;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color:#448aff;--mdc-checkbox-selected-hover-state-layer-color:#448aff;--mdc-checkbox-selected-pressed-state-layer-color:#448aff;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}html{--mat-checkbox-disabled-label-color:rgba(0, 0, 0, .38);--mat-checkbox-label-text-color:rgba(0, 0, 0, .87)}html{--mdc-checkbox-state-layer-size:40px}html{--mat-checkbox-touch-target-display:block}html{--mdc-text-button-container-shape:4px;--mdc-text-button-keep-touch-target:false}html{--mdc-filled-button-container-shape:4px;--mdc-filled-button-keep-touch-target:false}html{--mdc-protected-button-container-shape:4px;--mdc-protected-button-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mdc-protected-button-disabled-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mdc-protected-button-focus-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mdc-protected-button-hover-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mdc-protected-button-pressed-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mdc-outlined-button-keep-touch-target:false;--mdc-outlined-button-outline-width:1px;--mdc-outlined-button-container-shape:4px}html{--mat-text-button-horizontal-padding:8px;--mat-text-button-with-icon-horizontal-padding:8px;--mat-text-button-icon-spacing:8px;--mat-text-button-icon-offset:0}html{--mat-filled-button-horizontal-padding:16px;--mat-filled-button-icon-spacing:8px;--mat-filled-button-icon-offset:-4px}html{--mat-protected-button-horizontal-padding:16px;--mat-protected-button-icon-spacing:8px;--mat-protected-button-icon-offset:-4px}html{--mat-outlined-button-horizontal-padding:15px;--mat-outlined-button-icon-spacing:8px;--mat-outlined-button-icon-offset:-4px}html{--mdc-text-button-label-text-color:black;--mdc-text-button-disabled-label-text-color:rgba(0, 0, 0, .38)}html{--mat-text-button-state-layer-color:black;--mat-text-button-disabled-state-layer-color:black;--mat-text-button-ripple-color:rgba(0, 0, 0, .1);--mat-text-button-hover-state-layer-opacity:.04;--mat-text-button-focus-state-layer-opacity:.12;--mat-text-button-pressed-state-layer-opacity:.12}html{--mdc-filled-button-container-color:white;--mdc-filled-button-label-text-color:black;--mdc-filled-button-disabled-container-color:rgba(0, 0, 0, .12);--mdc-filled-button-disabled-label-text-color:rgba(0, 0, 0, .38)}html{--mat-filled-button-state-layer-color:black;--mat-filled-button-disabled-state-layer-color:black;--mat-filled-button-ripple-color:rgba(0, 0, 0, .1);--mat-filled-button-hover-state-layer-opacity:.04;--mat-filled-button-focus-state-layer-opacity:.12;--mat-filled-button-pressed-state-layer-opacity:.12}html{--mdc-protected-button-container-color:white;--mdc-protected-button-label-text-color:black;--mdc-protected-button-disabled-container-color:rgba(0, 0, 0, .12);--mdc-protected-button-disabled-label-text-color:rgba(0, 0, 0, .38)}html{--mat-protected-button-state-layer-color:black;--mat-protected-button-disabled-state-layer-color:black;--mat-protected-button-ripple-color:rgba(0, 0, 0, .1);--mat-protected-button-hover-state-layer-opacity:.04;--mat-protected-button-focus-state-layer-opacity:.12;--mat-protected-button-pressed-state-layer-opacity:.12}html{--mdc-outlined-button-disabled-outline-color:rgba(0, 0, 0, .12);--mdc-outlined-button-disabled-label-text-color:rgba(0, 0, 0, .38);--mdc-outlined-button-label-text-color:black;--mdc-outlined-button-outline-color:rgba(0, 0, 0, .12)}html{--mat-outlined-button-state-layer-color:black;--mat-outlined-button-disabled-state-layer-color:black;--mat-outlined-button-ripple-color:rgba(0, 0, 0, .1);--mat-outlined-button-hover-state-layer-opacity:.04;--mat-outlined-button-focus-state-layer-opacity:.12;--mat-outlined-button-pressed-state-layer-opacity:.12}html{--mdc-text-button-container-height:36px}html{--mdc-filled-button-container-height:36px}html{--mdc-protected-button-container-height:36px}html{--mdc-outlined-button-container-height:36px}html{--mat-text-button-touch-target-display:block}html{--mat-filled-button-touch-target-display:block}html{--mat-protected-button-touch-target-display:block}html{--mat-outlined-button-touch-target-display:block}html{--mdc-icon-button-icon-size:24px}html{--mdc-icon-button-icon-color:inherit;--mdc-icon-button-disabled-icon-color:rgba(0, 0, 0, .38)}html{--mat-icon-button-state-layer-color:black;--mat-icon-button-disabled-state-layer-color:black;--mat-icon-button-ripple-color:rgba(0, 0, 0, .1);--mat-icon-button-hover-state-layer-opacity:.04;--mat-icon-button-focus-state-layer-opacity:.12;--mat-icon-button-pressed-state-layer-opacity:.12}html{--mat-icon-button-touch-target-display:block}html{--mdc-fab-container-shape:50%;--mdc-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html{--mdc-fab-small-container-shape:50%;--mdc-fab-small-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-small-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-small-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-small-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html{--mdc-extended-fab-container-height:48px;--mdc-extended-fab-container-shape:24px;--mdc-extended-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-extended-fab-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-extended-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-extended-fab-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html{--mdc-fab-container-color:white}html{--mat-fab-foreground-color:black;--mat-fab-state-layer-color:black;--mat-fab-disabled-state-layer-color:black;--mat-fab-ripple-color:rgba(0, 0, 0, .1);--mat-fab-hover-state-layer-opacity:.04;--mat-fab-focus-state-layer-opacity:.12;--mat-fab-pressed-state-layer-opacity:.12;--mat-fab-disabled-state-container-color:rgba(0, 0, 0, .12);--mat-fab-disabled-state-foreground-color:rgba(0, 0, 0, .38)}html{--mdc-fab-small-container-color:white}html{--mat-fab-small-foreground-color:black;--mat-fab-small-state-layer-color:black;--mat-fab-small-disabled-state-layer-color:black;--mat-fab-small-ripple-color:rgba(0, 0, 0, .1);--mat-fab-small-hover-state-layer-opacity:.04;--mat-fab-small-focus-state-layer-opacity:.12;--mat-fab-small-pressed-state-layer-opacity:.12;--mat-fab-small-disabled-state-container-color:rgba(0, 0, 0, .12);--mat-fab-small-disabled-state-foreground-color:rgba(0, 0, 0, .38)}html{--mat-fab-touch-target-display:block}html{--mat-fab-small-touch-target-display:block}html{--mdc-snackbar-container-shape:4px}html{--mdc-snackbar-container-color:#333333;--mdc-snackbar-supporting-text-color:rgba(255, 255, 255, .87)}html{--mat-snack-bar-button-color:#d0e5f3}html{--mat-table-row-item-outline-width:1px}html{--mat-table-background-color:white;--mat-table-header-headline-color:rgba(0, 0, 0, .87);--mat-table-row-item-label-text-color:rgba(0, 0, 0, .87);--mat-table-row-item-outline-color:rgba(0, 0, 0, .12)}html{--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px}html{--mdc-circular-progress-active-indicator-width:4px;--mdc-circular-progress-size:48px}html{--mdc-circular-progress-active-indicator-color:#0071bc}html{--mat-badge-container-shape:50%;--mat-badge-container-size:unset;--mat-badge-small-size-container-size:unset;--mat-badge-large-size-container-size:unset;--mat-badge-legacy-container-size:22px;--mat-badge-legacy-small-size-container-size:16px;--mat-badge-legacy-large-size-container-size:28px;--mat-badge-container-offset:-11px 0;--mat-badge-small-size-container-offset:-8px 0;--mat-badge-large-size-container-offset:-14px 0;--mat-badge-container-overlap-offset:-11px;--mat-badge-small-size-container-overlap-offset:-8px;--mat-badge-large-size-container-overlap-offset:-14px;--mat-badge-container-padding:0;--mat-badge-small-size-container-padding:0;--mat-badge-large-size-container-padding:0}html{--mat-badge-background-color:#0071bc;--mat-badge-text-color:white;--mat-badge-disabled-state-background-color:#b9b9b9;--mat-badge-disabled-state-text-color:rgba(0, 0, 0, .38)}html{--mat-bottom-sheet-container-shape:4px}html{--mat-bottom-sheet-container-text-color:rgba(0, 0, 0, .87);--mat-bottom-sheet-container-background-color:white}html{--mat-legacy-button-toggle-height:36px;--mat-legacy-button-toggle-shape:2px;--mat-legacy-button-toggle-focus-state-layer-opacity:1}html{--mat-standard-button-toggle-shape:4px;--mat-standard-button-toggle-hover-state-layer-opacity:.04;--mat-standard-button-toggle-focus-state-layer-opacity:.12}html{--mat-legacy-button-toggle-text-color:rgba(0, 0, 0, .38);--mat-legacy-button-toggle-state-layer-color:rgba(0, 0, 0, .12);--mat-legacy-button-toggle-selected-state-text-color:rgba(0, 0, 0, .54);--mat-legacy-button-toggle-selected-state-background-color:#e0e0e0;--mat-legacy-button-toggle-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-legacy-button-toggle-disabled-state-background-color:#eeeeee;--mat-legacy-button-toggle-disabled-selected-state-background-color:#bdbdbd}html{--mat-standard-button-toggle-text-color:rgba(0, 0, 0, .87);--mat-standard-button-toggle-background-color:white;--mat-standard-button-toggle-state-layer-color:black;--mat-standard-button-toggle-selected-state-background-color:#e0e0e0;--mat-standard-button-toggle-selected-state-text-color:rgba(0, 0, 0, .87);--mat-standard-button-toggle-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-standard-button-toggle-disabled-state-background-color:white;--mat-standard-button-toggle-disabled-selected-state-text-color:rgba(0, 0, 0, .87);--mat-standard-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-standard-button-toggle-divider-color:rgb(224.4, 224.4, 224.4)}html{--mat-standard-button-toggle-height:48px}html{--mat-datepicker-calendar-container-shape:4px;--mat-datepicker-calendar-container-touch-shape:4px;--mat-datepicker-calendar-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-container-touch-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#0071bc;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(0, 113, 188, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(0, 113, 188, .3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(0, 113, 188, .3);--mat-datepicker-toggle-active-state-icon-color:#0071bc;--mat-datepicker-calendar-date-in-range-state-background-color:rgba(0, 113, 188, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-toggle-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-body-label-text-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-period-button-text-color:black;--mat-datepicker-calendar-period-button-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-navigation-button-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-header-divider-color:rgba(0, 0, 0, .12);--mat-datepicker-calendar-header-text-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-date-today-outline-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-today-disabled-state-outline-color:rgba(0, 0, 0, .18);--mat-datepicker-calendar-date-text-color:rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-outline-color:transparent;--mat-datepicker-calendar-date-disabled-state-text-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-preview-state-outline-color:rgba(0, 0, 0, .24);--mat-datepicker-range-input-separator-color:rgba(0, 0, 0, .87);--mat-datepicker-range-input-disabled-state-separator-color:rgba(0, 0, 0, .38);--mat-datepicker-range-input-disabled-state-text-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-container-background-color:white;--mat-datepicker-calendar-container-text-color:rgba(0, 0, 0, .87)}html{--mat-divider-width:1px}html{--mat-divider-color:rgba(0, 0, 0, .12)}html{--mat-expansion-container-shape:4px;--mat-expansion-legacy-header-indicator-display:inline-block;--mat-expansion-header-indicator-display:none}html{--mat-expansion-container-background-color:white;--mat-expansion-container-text-color:rgba(0, 0, 0, .87);--mat-expansion-actions-divider-color:rgba(0, 0, 0, .12);--mat-expansion-header-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-expansion-header-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-expansion-header-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-expansion-header-text-color:rgba(0, 0, 0, .87);--mat-expansion-header-description-color:rgba(0, 0, 0, .54);--mat-expansion-header-indicator-color:rgba(0, 0, 0, .54)}html{--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px}html{--mat-icon-color:inherit}html{--mat-sidenav-container-shape:0;--mat-sidenav-container-elevation-shadow:0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-sidenav-container-width:auto}html{--mat-sidenav-container-divider-color:rgba(0, 0, 0, .12);--mat-sidenav-container-background-color:white;--mat-sidenav-container-text-color:rgba(0, 0, 0, .87);--mat-sidenav-content-background-color:#fafafa;--mat-sidenav-content-text-color:rgba(0, 0, 0, .87);--mat-sidenav-scrim-color:rgba(0, 0, 0, .6)}html{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#0071bc;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#0071bc;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#0071bc;--mat-stepper-header-edit-state-icon-foreground-color:white;--mat-stepper-container-color:white;--mat-stepper-line-color:rgba(0, 0, 0, .12);--mat-stepper-header-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-stepper-header-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-stepper-header-label-text-color:rgba(0, 0, 0, .54);--mat-stepper-header-optional-label-text-color:rgba(0, 0, 0, .54);--mat-stepper-header-selected-state-label-text-color:rgba(0, 0, 0, .87);--mat-stepper-header-error-state-label-text-color:#f44336;--mat-stepper-header-icon-background-color:rgba(0, 0, 0, .54);--mat-stepper-header-error-state-icon-foreground-color:#f44336;--mat-stepper-header-error-state-icon-background-color:transparent}html{--mat-stepper-header-height:72px}html{--mat-sort-arrow-color:rgb(117.3, 117.3, 117.3)}html{--mat-toolbar-container-background-color:whitesmoke;--mat-toolbar-container-text-color:rgba(0, 0, 0, .87)}html{--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px}html{--mat-tree-container-background-color:white;--mat-tree-node-text-color:rgba(0, 0, 0, .87)}html{--mat-tree-node-min-height:48px}html{--mat-timepicker-container-shape:4px;--mat-timepicker-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-timepicker-container-background-color:white}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}button,input{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button{text-transform:none}button{-webkit-appearance:button}button::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring{outline:1px dotted ButtonText}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}body{color:#1b1b1b;background-color:#fff;overflow-x:hidden}input:not([disabled]):focus,button:not([disabled]):focus{outline:.25rem solid #2491ff;outline-offset:0rem}[href]:focus{outline:.25rem solid #2491ff;outline-offset:0rem}*,*:before,*:after{box-sizing:inherit}html{font-feature-settings:"kern" 1;font-kerning:normal;font-family:Roboto,sans-serif;font-size:100%}.usa-sr-only{position:absolute;left:-999em;right:auto}.usa-dark-background{background-color:#3d4551}.usa-dark-background p,.usa-dark-background span,.usa-dark-background a,.usa-dark-background a:visited,.usa-dark-background a:hover,.usa-dark-background a:active{color:#fff}.usa-icon{display:inline-block;fill:currentColor;height:1em;position:relative;width:1em}input[type=search]{box-sizing:border-box;appearance:none}[type=search]{padding-bottom:0;padding-top:0;border-bottom-right-radius:0;border-right:none;border-top-right-radius:0;box-sizing:border-box;float:left;font-size:.93rem;height:2rem;margin:0}:root{--ctgl-color-base-lightest:#f0f0f0;--ctgl-color-base-lighter:#dfe1e2;--ctgl-color-base-light:#a9aeb1;--ctgl-color-base:#71767a;--ctgl-color-base-dark:#565c65;--ctgl-color-base-darker:#3d4551;--ctgl-color-base-darkest:#1b1b1b;--ctgl-color-primary-lighter:#d9e8f6;--ctgl-color-primary-light:#73b3e7;--ctgl-color-primary:#0071bc;--ctgl-color-primary-vivid:#0050d8;--ctgl-color-primary-dark:#1a4480;--ctgl-color-primary-darker:#205493;--ctgl-color-primary-darkest:#112e51;--ctgl-color-secondary-lighter:#f3e1e4;--ctgl-color-secondary-light:#f2938c;--ctgl-color-secondary:#d83933;--ctgl-color-secondary-vivid:#e41d3d;--ctgl-color-secondary-dark:#b50909;--ctgl-color-secondary-darker:#8b0a03;--ctgl-color-accent-warm-lighter:#f2e4d4;--ctgl-color-accent-warm-light:#ffbc78;--ctgl-color-accent-warm:#fa9441;--ctgl-color-accent-warm-dark:#c05600;--ctgl-color-accent-warm-darker:#775540;--ctgl-color-accent-cool-lighter:#e1f3f8;--ctgl-color-accent-cool-light:#97d4ea;--ctgl-color-accent-cool:#00bde3;--ctgl-color-accent-cool-dark:#28a0cb;--ctgl-color-accent-cool-darker:#07648d;--ctgl-color-error-lighter:#f4e3db;--ctgl-color-error-light:#f39268;--ctgl-color-error:#d54309;--ctgl-color-error-dark:#b50909;--ctgl-color-error-darker:#6f3331;--ctgl-color-warning-lighter:#faf3d1;--ctgl-color-warning-light:#fee685;--ctgl-color-warning:#ffbe2e;--ctgl-color-warning-dark:#e5a000;--ctgl-color-warning-darker:#936f38;--ctgl-color-success-lighter:#ecf3ec;--ctgl-color-success-light:#70e17b;--ctgl-color-success:#00a91c;--ctgl-color-success-dark:#008817;--ctgl-color-success-darker:#216e1f;--ctgl-color-info-lighter:#e7f6f8;--ctgl-color-info-light:#99deea;--ctgl-color-info:#00bde3;--ctgl-color-info-dark:#009ec1;--ctgl-color-info-darker:#2e6276;--ctgl-color-disabled-lighter:#c9c9c9;--ctgl-color-disabled-light:#919191;--ctgl-color-disabled:#757575;--ctgl-color-disabled-dark:#454545;--ctgl-color-disabled-darker:#1b1b1b;--ctgl-color-emergency:#9c3d10;--ctgl-color-emergency-dark:#332d29;--ctgl-color-red-cool-5:#f8eff1;--ctgl-color-red-cool-10:#f3e1e4;--ctgl-color-red-cool-20:#ecbec6;--ctgl-color-red-cool-30:#e09aa6;--ctgl-color-red-cool-40:#e16b80;--ctgl-color-red-cool-50:#cd425b;--ctgl-color-red-cool-60:#9e394b;--ctgl-color-red-cool-70:#68363f;--ctgl-color-red-cool-80:#40282c;--ctgl-color-red-cool-90:#1e1517;--ctgl-color-red-cool-5v:#fff2f5;--ctgl-color-red-cool-10v:#f8dfe2;--ctgl-color-red-cool-20v:#f8b9c5;--ctgl-color-red-cool-30v:#fd8ba0;--ctgl-color-red-cool-40v:#f45d79;--ctgl-color-red-cool-50v:#e41d3d;--ctgl-color-red-cool-60v:#b21d38;--ctgl-color-red-cool-70v:#822133;--ctgl-color-red-cool-80v:#4f1c24;--ctgl-color-red-5:#f9eeee;--ctgl-color-red-10:#f8e1de;--ctgl-color-red-20:#f7bbb1;--ctgl-color-red-30:#f2938c;--ctgl-color-red-40:#e9695f;--ctgl-color-red-50:#d83933;--ctgl-color-red-60:#a23737;--ctgl-color-red-70:#6f3331;--ctgl-color-red-80:#3e2927;--ctgl-color-red-90:#1b1616;--ctgl-color-red-5v:#fff3f2;--ctgl-color-red-10v:#fde0db;--ctgl-color-red-20v:#fdb8ae;--ctgl-color-red-30v:#ff8d7b;--ctgl-color-red-40v:#fb5a47;--ctgl-color-red-50v:#e52207;--ctgl-color-red-60v:#b50909;--ctgl-color-red-70v:#8b0a03;--ctgl-color-red-80v:#5c1111;--ctgl-color-red-warm-5:#f6efea;--ctgl-color-red-warm-10:#f4e3db;--ctgl-color-red-warm-20:#ecc0a7;--ctgl-color-red-warm-30:#dca081;--ctgl-color-red-warm-40:#d27a56;--ctgl-color-red-warm-50:#c3512c;--ctgl-color-red-warm-60:#805039;--ctgl-color-red-warm-70:#524236;--ctgl-color-red-warm-80:#332d29;--ctgl-color-red-warm-90:#1f1c18;--ctgl-color-red-warm-5v:#fff5ee;--ctgl-color-red-warm-10v:#fce1d4;--ctgl-color-red-warm-20v:#f6bd9c;--ctgl-color-red-warm-30v:#f39268;--ctgl-color-red-warm-40v:#ef5e25;--ctgl-color-red-warm-50v:#d54309;--ctgl-color-red-warm-60v:#9c3d10;--ctgl-color-red-warm-70v:#63340f;--ctgl-color-red-warm-80v:#3e2a1e;--ctgl-color-orange-warm-5:#faeee5;--ctgl-color-orange-warm-10:#fbe0d0;--ctgl-color-orange-warm-20:#f7bca2;--ctgl-color-orange-warm-30:#f3966d;--ctgl-color-orange-warm-40:#e17141;--ctgl-color-orange-warm-50:#bd5727;--ctgl-color-orange-warm-60:#914734;--ctgl-color-orange-warm-70:#633a32;--ctgl-color-orange-warm-80:#3d2925;--ctgl-color-orange-warm-90:#1c1615;--ctgl-color-orange-warm-5v:#fff3ea;--ctgl-color-orange-warm-10v:#ffe2d1;--ctgl-color-orange-warm-20v:#fbbaa7;--ctgl-color-orange-warm-30v:#fc906d;--ctgl-color-orange-warm-40v:#ff580a;--ctgl-color-orange-warm-50v:#cf4900;--ctgl-color-orange-warm-60v:#a72f10;--ctgl-color-orange-warm-70v:#782312;--ctgl-color-orange-warm-80v:#3d231d;--ctgl-color-orange-5:#f6efe9;--ctgl-color-orange-10:#f2e4d4;--ctgl-color-orange-20:#f3bf90;--ctgl-color-orange-30:#f09860;--ctgl-color-orange-40:#dd7533;--ctgl-color-orange-50:#a86437;--ctgl-color-orange-60:#775540;--ctgl-color-orange-70:#524236;--ctgl-color-orange-80:#332d27;--ctgl-color-orange-90:#1b1614;--ctgl-color-orange-5v:#fef2e4;--ctgl-color-orange-10v:#fce2c5;--ctgl-color-orange-20v:#ffbc78;--ctgl-color-orange-30v:#fa9441;--ctgl-color-orange-40v:#e66f0e;--ctgl-color-orange-50v:#c05600;--ctgl-color-orange-60v:#8c471c;--ctgl-color-orange-70v:#5f3617;--ctgl-color-orange-80v:#352313;--ctgl-color-gold-5:#f5f0e6;--ctgl-color-gold-10:#f1e5cd;--ctgl-color-gold-20:#dec69a;--ctgl-color-gold-30:#c7a97b;--ctgl-color-gold-40:#ad8b65;--ctgl-color-gold-50:#8e704f;--ctgl-color-gold-60:#6b5947;--ctgl-color-gold-70:#4d4438;--ctgl-color-gold-80:#322d26;--ctgl-color-gold-90:#191714;--ctgl-color-gold-5v:#fef0c8;--ctgl-color-gold-10v:#ffe396;--ctgl-color-gold-20v:#ffbe2e;--ctgl-color-gold-30v:#e5a000;--ctgl-color-gold-40v:#c2850c;--ctgl-color-gold-50v:#936f38;--ctgl-color-gold-60v:#7a591a;--ctgl-color-gold-70v:#5c410a;--ctgl-color-gold-80v:#3b2b15;--ctgl-color-yellow-5:#faf3d1;--ctgl-color-yellow-10:#f5e6af;--ctgl-color-yellow-20:#e6c74c;--ctgl-color-yellow-30:#c9ab48;--ctgl-color-yellow-40:#a88f48;--ctgl-color-yellow-50:#8a7237;--ctgl-color-yellow-60:#6b5a39;--ctgl-color-yellow-70:#504332;--ctgl-color-yellow-80:#332d27;--ctgl-color-yellow-90:#1a1614;--ctgl-color-yellow-5v:#fff5c2;--ctgl-color-yellow-10v:#fee685;--ctgl-color-yellow-20v:#face00;--ctgl-color-yellow-30v:#ddaa01;--ctgl-color-yellow-40v:#b38c00;--ctgl-color-yellow-50v:#947100;--ctgl-color-yellow-60v:#776017;--ctgl-color-yellow-70v:#5c4809;--ctgl-color-yellow-80v:#422d19;--ctgl-color-green-warm-5:#f1f4d7;--ctgl-color-green-warm-10:#e7eab7;--ctgl-color-green-warm-20:#cbd17a;--ctgl-color-green-warm-30:#a6b557;--ctgl-color-green-warm-40:#8a984b;--ctgl-color-green-warm-50:#6f7a41;--ctgl-color-green-warm-60:#5a5f38;--ctgl-color-green-warm-70:#45472f;--ctgl-color-green-warm-80:#2d2f21;--ctgl-color-green-warm-90:#171712;--ctgl-color-green-warm-5v:#f5fbc1;--ctgl-color-green-warm-10v:#e7f434;--ctgl-color-green-warm-20v:#c5d30a;--ctgl-color-green-warm-30v:#a3b72c;--ctgl-color-green-warm-40v:#7e9c1d;--ctgl-color-green-warm-50v:#6a7d00;--ctgl-color-green-warm-60v:#5a6613;--ctgl-color-green-warm-70v:#4b4e10;--ctgl-color-green-warm-80v:#38380b;--ctgl-color-green-5:#eaf4dd;--ctgl-color-green-10:#dfeacd;--ctgl-color-green-20:#b8d293;--ctgl-color-green-30:#9bb672;--ctgl-color-green-40:#7d9b4e;--ctgl-color-green-50:#607f35;--ctgl-color-green-60:#4c6424;--ctgl-color-green-70:#3c4a29;--ctgl-color-green-80:#293021;--ctgl-color-green-90:#161814;--ctgl-color-green-5v:#ddf9c7;--ctgl-color-green-10v:#c5ee93;--ctgl-color-green-20v:#98d035;--ctgl-color-green-30v:#7fb135;--ctgl-color-green-40v:#719f2a;--ctgl-color-green-50v:#538200;--ctgl-color-green-60v:#466c04;--ctgl-color-green-70v:#2f4a0b;--ctgl-color-green-80v:#243413;--ctgl-color-green-cool-5:#ecf3ec;--ctgl-color-green-cool-10:#dbebde;--ctgl-color-green-cool-20:#b4d0b9;--ctgl-color-green-cool-30:#86b98e;--ctgl-color-green-cool-40:#5e9f69;--ctgl-color-green-cool-50:#4d8055;--ctgl-color-green-cool-60:#446443;--ctgl-color-green-cool-70:#37493b;--ctgl-color-green-cool-80:#28312a;--ctgl-color-green-cool-90:#1a1f1a;--ctgl-color-green-cool-5v:#e3f5e1;--ctgl-color-green-cool-10v:#b7f5bd;--ctgl-color-green-cool-20v:#70e17b;--ctgl-color-green-cool-30v:#21c834;--ctgl-color-green-cool-40v:#00a91c;--ctgl-color-green-cool-50v:#008817;--ctgl-color-green-cool-60v:#216e1f;--ctgl-color-green-cool-70v:#154c21;--ctgl-color-green-cool-80v:#19311e;--ctgl-color-mint-5:#dbf6ed;--ctgl-color-mint-10:#c7efe2;--ctgl-color-mint-20:#92d9bb;--ctgl-color-mint-30:#5abf95;--ctgl-color-mint-40:#34a37e;--ctgl-color-mint-50:#2e8367;--ctgl-color-mint-60:#286846;--ctgl-color-mint-70:#204e34;--ctgl-color-mint-80:#193324;--ctgl-color-mint-90:#0d1a12;--ctgl-color-mint-5v:#c9fbeb;--ctgl-color-mint-10v:#83fcd4;--ctgl-color-mint-20v:#0ceda6;--ctgl-color-mint-30v:#04c585;--ctgl-color-mint-40v:#00a871;--ctgl-color-mint-50v:#008659;--ctgl-color-mint-60v:#146947;--ctgl-color-mint-70v:#0c4e29;--ctgl-color-mint-80v:#0d351e;--ctgl-color-mint-cool-5:#e0f7f6;--ctgl-color-mint-cool-10:#c4eeeb;--ctgl-color-mint-cool-20:#9bd4cf;--ctgl-color-mint-cool-30:#6fbab3;--ctgl-color-mint-cool-40:#4f9e99;--ctgl-color-mint-cool-50:#40807e;--ctgl-color-mint-cool-60:#376462;--ctgl-color-mint-cool-70:#2a4b45;--ctgl-color-mint-cool-80:#203131;--ctgl-color-mint-cool-90:#111818;--ctgl-color-mint-cool-5v:#d5fbf3;--ctgl-color-mint-cool-10v:#7efbe1;--ctgl-color-mint-cool-20v:#29e1cb;--ctgl-color-mint-cool-30v:#1dc2ae;--ctgl-color-mint-cool-40v:#00a398;--ctgl-color-mint-cool-50v:#008480;--ctgl-color-mint-cool-60v:#0f6460;--ctgl-color-mint-cool-70v:#0b4b3f;--ctgl-color-mint-cool-80v:#123131;--ctgl-color-cyan-5:#e7f6f8;--ctgl-color-cyan-10:#ccecf2;--ctgl-color-cyan-20:#99deea;--ctgl-color-cyan-30:#5dc0d1;--ctgl-color-cyan-40:#449dac;--ctgl-color-cyan-50:#168092;--ctgl-color-cyan-60:#2a646d;--ctgl-color-cyan-70:#2c4a4e;--ctgl-color-cyan-80:#203133;--ctgl-color-cyan-90:#111819;--ctgl-color-cyan-5v:#e5faff;--ctgl-color-cyan-10v:#a8f2ff;--ctgl-color-cyan-20v:#52daf2;--ctgl-color-cyan-30v:#00bde3;--ctgl-color-cyan-40v:#009ec1;--ctgl-color-cyan-50v:#0081a1;--ctgl-color-cyan-60v:#00687d;--ctgl-color-cyan-70v:#0e4f5c;--ctgl-color-cyan-80v:#093b44;--ctgl-color-blue-cool-5:#e7f2f5;--ctgl-color-blue-cool-10:#dae9ee;--ctgl-color-blue-cool-20:#adcfdc;--ctgl-color-blue-cool-30:#82b4c9;--ctgl-color-blue-cool-40:#6499af;--ctgl-color-blue-cool-50:#3a7d95;--ctgl-color-blue-cool-60:#2e6276;--ctgl-color-blue-cool-70:#224a58;--ctgl-color-blue-cool-80:#14333d;--ctgl-color-blue-cool-90:#0f191c;--ctgl-color-blue-cool-5v:#e1f3f8;--ctgl-color-blue-cool-10v:#c3ebfa;--ctgl-color-blue-cool-20v:#97d4ea;--ctgl-color-blue-cool-30v:#59b9de;--ctgl-color-blue-cool-40v:#28a0cb;--ctgl-color-blue-cool-50v:#0d7ea2;--ctgl-color-blue-cool-60v:#07648d;--ctgl-color-blue-cool-70v:#074b69;--ctgl-color-blue-cool-80v:#002d3f;--ctgl-color-blue-5:#eff6fb;--ctgl-color-blue-10:#d9e8f6;--ctgl-color-blue-20:#aacdec;--ctgl-color-blue-30:#73b3e7;--ctgl-color-blue-40:#4f97d1;--ctgl-color-blue-50:#2378c3;--ctgl-color-blue-60:#2c608a;--ctgl-color-blue-70:#274863;--ctgl-color-blue-80:#1f303e;--ctgl-color-blue-90:#11181d;--ctgl-color-blue-5v:#e8f5ff;--ctgl-color-blue-10v:#cfe8ff;--ctgl-color-blue-20v:#a1d3ff;--ctgl-color-blue-30v:#58b4ff;--ctgl-color-blue-40v:#2491ff;--ctgl-color-blue-50v:#0076d6;--ctgl-color-blue-60v:#005ea2;--ctgl-color-blue-70v:#0b4778;--ctgl-color-blue-80v:#112f4e;--ctgl-color-blue-warm-5:#ecf1f7;--ctgl-color-blue-warm-10:#e1e7f1;--ctgl-color-blue-warm-20:#bbcae4;--ctgl-color-blue-warm-30:#98afd2;--ctgl-color-blue-warm-40:#7292c7;--ctgl-color-blue-warm-50:#4a77b4;--ctgl-color-blue-warm-60:#345d96;--ctgl-color-blue-warm-70:#2f4668;--ctgl-color-blue-warm-80:#252f3e;--ctgl-color-blue-warm-90:#13171f;--ctgl-color-blue-warm-5v:#edf5ff;--ctgl-color-blue-warm-10v:#d4e5ff;--ctgl-color-blue-warm-20v:#adcdff;--ctgl-color-blue-warm-30v:#81aefc;--ctgl-color-blue-warm-40v:#5994f6;--ctgl-color-blue-warm-50v:#2672de;--ctgl-color-blue-warm-60v:#0050d8;--ctgl-color-blue-warm-70v:#1a4480;--ctgl-color-blue-warm-80v:#162e51;--ctgl-color-indigo-cool-5:#eef0f9;--ctgl-color-indigo-cool-10:#e1e6f9;--ctgl-color-indigo-cool-20:#bbc8f5;--ctgl-color-indigo-cool-30:#96abee;--ctgl-color-indigo-cool-40:#6b8ee8;--ctgl-color-indigo-cool-50:#496fd8;--ctgl-color-indigo-cool-60:#3f57a6;--ctgl-color-indigo-cool-70:#374274;--ctgl-color-indigo-cool-80:#292d42;--ctgl-color-indigo-cool-90:#151622;--ctgl-color-indigo-cool-5v:#edf0ff;--ctgl-color-indigo-cool-10v:#dee5ff;--ctgl-color-indigo-cool-20v:#b8c8ff;--ctgl-color-indigo-cool-30v:#94adff;--ctgl-color-indigo-cool-40v:#628ef4;--ctgl-color-indigo-cool-50v:#4866ff;--ctgl-color-indigo-cool-60v:#3e4ded;--ctgl-color-indigo-cool-70v:#222fbf;--ctgl-color-indigo-cool-80v:#1b2b85;--ctgl-color-indigo-5:#efeff8;--ctgl-color-indigo-10:#e5e4fa;--ctgl-color-indigo-20:#c5c5f3;--ctgl-color-indigo-30:#a5a8eb;--ctgl-color-indigo-40:#8889db;--ctgl-color-indigo-50:#676cc8;--ctgl-color-indigo-60:#4d52af;--ctgl-color-indigo-70:#3d4076;--ctgl-color-indigo-80:#2b2c40;--ctgl-color-indigo-90:#16171f;--ctgl-color-indigo-5v:#f0f0ff;--ctgl-color-indigo-10v:#e0e0ff;--ctgl-color-indigo-20v:#ccceff;--ctgl-color-indigo-30v:#a3a7fa;--ctgl-color-indigo-40v:#8289ff;--ctgl-color-indigo-50v:#656bd7;--ctgl-color-indigo-60v:#4a50c4;--ctgl-color-indigo-70v:#3333a3;--ctgl-color-indigo-80v:#212463;--ctgl-color-indigo-warm-5:#f1eff7;--ctgl-color-indigo-warm-10:#e7e3fa;--ctgl-color-indigo-warm-20:#cbc4f2;--ctgl-color-indigo-warm-30:#afa5e8;--ctgl-color-indigo-warm-40:#9287d8;--ctgl-color-indigo-warm-50:#7665d1;--ctgl-color-indigo-warm-60:#5e519e;--ctgl-color-indigo-warm-70:#453c7b;--ctgl-color-indigo-warm-80:#2e2c40;--ctgl-color-indigo-warm-90:#18161d;--ctgl-color-indigo-warm-5v:#f5f2ff;--ctgl-color-indigo-warm-10v:#e4deff;--ctgl-color-indigo-warm-20v:#cfc4fd;--ctgl-color-indigo-warm-30v:#b69fff;--ctgl-color-indigo-warm-40v:#967efb;--ctgl-color-indigo-warm-50v:#745fe9;--ctgl-color-indigo-warm-60v:#5942d2;--ctgl-color-indigo-warm-70v:#3d2c9d;--ctgl-color-indigo-warm-80v:#261f5b;--ctgl-color-violet-5:#f4f1f9;--ctgl-color-violet-10:#ebe3f9;--ctgl-color-violet-20:#d0c3e9;--ctgl-color-violet-30:#b8a2e3;--ctgl-color-violet-40:#9d84d2;--ctgl-color-violet-50:#8168b3;--ctgl-color-violet-60:#665190;--ctgl-color-violet-70:#4c3d69;--ctgl-color-violet-80:#312b3f;--ctgl-color-violet-90:#18161d;--ctgl-color-violet-5v:#f7f2ff;--ctgl-color-violet-10v:#ede3ff;--ctgl-color-violet-20v:#d5bfff;--ctgl-color-violet-30v:#c39deb;--ctgl-color-violet-40v:#ad79e9;--ctgl-color-violet-50v:#9355dc;--ctgl-color-violet-60v:#783cb9;--ctgl-color-violet-70v:#54278f;--ctgl-color-violet-80v:#39215e;--ctgl-color-violet-warm-5:#f8f0f9;--ctgl-color-violet-warm-10:#f6dff8;--ctgl-color-violet-warm-20:#e2bee4;--ctgl-color-violet-warm-30:#d29ad8;--ctgl-color-violet-warm-40:#bf77c8;--ctgl-color-violet-warm-50:#b04abd;--ctgl-color-violet-warm-60:#864381;--ctgl-color-violet-warm-70:#5c395a;--ctgl-color-violet-warm-80:#382936;--ctgl-color-violet-warm-90:#1b151b;--ctgl-color-violet-warm-5v:#fef2ff;--ctgl-color-violet-warm-10v:#fbdcff;--ctgl-color-violet-warm-20v:#f4b2ff;--ctgl-color-violet-warm-30v:#ee83ff;--ctgl-color-violet-warm-40v:#d85bef;--ctgl-color-violet-warm-50v:#be32d0;--ctgl-color-violet-warm-60v:#93348c;--ctgl-color-violet-warm-70v:#711e6c;--ctgl-color-violet-warm-80v:#481441;--ctgl-color-magenta-5:#f9f0f2;--ctgl-color-magenta-10:#f6e1e8;--ctgl-color-magenta-20:#f0bbcc;--ctgl-color-magenta-30:#e895b3;--ctgl-color-magenta-40:#e0699f;--ctgl-color-magenta-50:#c84281;--ctgl-color-magenta-60:#8b4566;--ctgl-color-magenta-70:#66364b;--ctgl-color-magenta-80:#402731;--ctgl-color-magenta-90:#1b1617;--ctgl-color-magenta-5v:#fff2f5;--ctgl-color-magenta-10v:#ffddea;--ctgl-color-magenta-20v:#ffb4cf;--ctgl-color-magenta-30v:#ff87b2;--ctgl-color-magenta-40v:#fd4496;--ctgl-color-magenta-50v:#d72d79;--ctgl-color-magenta-60v:#ab2165;--ctgl-color-magenta-70v:#731f44;--ctgl-color-magenta-80v:#4f172e;--ctgl-color-gray-cool-1:#fbfcfd;--ctgl-color-gray-cool-2:#f7f9fa;--ctgl-color-gray-cool-3:#f5f6f7;--ctgl-color-gray-cool-4:#f1f3f6;--ctgl-color-gray-cool-5:#edeff0;--ctgl-color-gray-cool-10:#dfe1e2;--ctgl-color-gray-cool-20:#c6cace;--ctgl-color-gray-cool-30:#a9aeb1;--ctgl-color-gray-cool-40:#8d9297;--ctgl-color-gray-cool-50:#71767a;--ctgl-color-gray-cool-60:#565c65;--ctgl-color-gray-cool-70:#3d4551;--ctgl-color-gray-cool-80:#2d2e2f;--ctgl-color-gray-cool-90:#1c1d1f;--ctgl-color-gray-1:#fcfcfc;--ctgl-color-gray-2:#f9f9f9;--ctgl-color-gray-3:#f6f6f6;--ctgl-color-gray-4:#f3f3f3;--ctgl-color-gray-5:#f0f0f0;--ctgl-color-gray-10:#e6e6e6;--ctgl-color-gray-20:#c9c9c9;--ctgl-color-gray-30:#adadad;--ctgl-color-gray-40:#919191;--ctgl-color-gray-50:#757575;--ctgl-color-gray-60:#5c5c5c;--ctgl-color-gray-70:#454545;--ctgl-color-gray-80:#2e2e2e;--ctgl-color-gray-90:#1b1b1b;--ctgl-color-gray-100:#000000;--ctgl-color-gray-warm-1:#fcfcfb;--ctgl-color-gray-warm-2:#f9f9f7;--ctgl-color-gray-warm-3:#f6f6f2;--ctgl-color-gray-warm-4:#f5f5f0;--ctgl-color-gray-warm-5:#f0f0ec;--ctgl-color-gray-warm-10:#e6e6e2;--ctgl-color-gray-warm-20:#cac9c0;--ctgl-color-gray-warm-30:#afaea2;--ctgl-color-gray-warm-40:#929285;--ctgl-color-gray-warm-50:#76766a;--ctgl-color-gray-warm-60:#5d5d52;--ctgl-color-gray-warm-70:#454540;--ctgl-color-gray-warm-80:#2e2e2a;--ctgl-color-gray-warm-90:#171716;--ctgl-color-black-transparent-5:rgba(0, 0, 0, .01);--ctgl-color-black-transparent-10:rgba(0, 0, 0, .1);--ctgl-color-black-transparent-20:rgba(0, 0, 0, .2);--ctgl-color-black-transparent-30:rgba(0, 0, 0, .3);--ctgl-color-black-transparent-40:rgba(0, 0, 0, .4);--ctgl-color-black-transparent-50:rgba(0, 0, 0, .5);--ctgl-color-black-transparent-60:rgba(0, 0, 0, .6);--ctgl-color-black-transparent-70:rgba(0, 0, 0, .7);--ctgl-color-black-transparent-80:rgba(0, 0, 0, .8);--ctgl-color-black-transparent-90:rgba(0, 0, 0, .9);--ctgl-color-white-transparent-5:rgba(255, 255, 255, .01);--ctgl-color-white-transparent-10:rgba(255, 255, 255, .1);--ctgl-color-white-transparent-20:rgba(255, 255, 255, .2);--ctgl-color-white-transparent-30:rgba(255, 255, 255, .3);--ctgl-color-white-transparent-40:rgba(255, 255, 255, .4);--ctgl-color-white-transparent-50:rgba(255, 255, 255, .5);--ctgl-color-white-transparent-60:rgba(255, 255, 255, .6);--ctgl-color-white-transparent-70:rgba(255, 255, 255, .7);--ctgl-color-white-transparent-80:rgba(255, 255, 255, .8);--ctgl-color-white-transparent-90:rgba(255, 255, 255, .9)}:root{--ctgl-breakpoint-card:10rem;--ctgl-breakpoint-card-lg:15rem;--ctgl-breakpoint-mobile:20rem;--ctgl-breakpoint-mobile-lg:30rem;--ctgl-breakpoint-tablet:40rem;--ctgl-breakpoint-tablet-lg:55rem;--ctgl-breakpoint-desktop:64rem;--ctgl-breakpoint-desktop-lg:75rem;--ctgl-breakpoint-widescreen:87.5rem}p{line-height:1.62!important}:root{font-family:Roboto,sans-serif;--font-family:Roboto, sans-serif}html{box-sizing:border-box}*,*:after,*:before{box-sizing:inherit}@media print{.glossary{display:none}.hide-glossary .usa-icon{height:1.5rem;width:1.5rem;margin-top:4px}}:root{--ctg-removed-background-color:#f4a4b1;--ctg-added-background-color:#99cfa2}.glossary{background:#112e51;color:#fff;width:300px;position:fixed;top:0;bottom:0;right:0;overflow-y:auto;padding:2.4rem;transition:right .2s;z-index:10001}@media screen and (min-width: 481px){.glossary{width:364px}}.glossary ul{padding:0}.glossary input[type=search]{box-sizing:inherit;margin-bottom:4rem;min-width:220px}.glossary label{margin-top:2.6rem}.glossary a{color:#fff;text-decoration:underline}.glossary[aria-hidden=true]{right:-300px}@media screen and (min-width: 481px){.glossary[aria-hidden=true]{right:-364px}}.glossary-heading{display:inline;font-size:22px;margin-top:0}.js-glossary-toggle{border-bottom:none;color:#0071bc;font-size:1.5rem;font-weight:700;line-height:initial;width:initial}.js-glossary-toggle:hover{color:#205493}.js-glossary-toggle svg{margin-right:2px}.js-glossary-close{border:none;font-size:20px;background-color:transparent;float:right;width:initial}.js-glossary-search::-webkit-search-cancel-button{-webkit-appearance:searchfield-cancel-button!important}.usa-sr-only{position:absolute;left:-999em}.clearable-input{position:relative;display:inline-block}.clearable-input>input{padding-right:1.4em}.clearable-input>input::-ms-clear{display:none}</style><link rel="stylesheet" href="styles-4O3OZYZ6.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles-4O3OZYZ6.css"></noscript></head>
<body>
<div id="glossary" class="glossary usa-dark-background" aria-describedby="glossary-result" aria-hidden="true">
  <button title="Close glossary" class="js-glossary-close button button--close js-glossary-toggle usa-button-unstyled hide-glossary"><span class="usa-sr-only">Hide glossary</span>
    <svg class="usa-icon" aria-hidden="true" focusable="false" role="img">
      <use href="/assets/uswds/img/sprite.svg#close"/>
    </svg>
  </button>
  <h4 class="glossary-heading">Glossary</h4>
  <p>
  Study record managers: refer to the <a href="/policy#data-element-definitions"><span style="text-decoration:underline">Data Element Definitions</span></a> if submitting registration or results information.
  </p>
  <p>
  <label for="glossary-search" class="label">Search for terms</label>
  </p>
  <span class="clearable-input">
  <input id="glossary-search" class="js-glossary-search" type="search">
  </span>
  <div class="glossary__content" id="glossary-result">
    <ul class="js-glossary-list"></ul>
  </div>
</div>

<app-root></app-root>

<script type="text/javascript" src="https://www.ncbi.nlm.nih.gov/core/pinger/pinger.js"></script>

<link rel="modulepreload" href="chunk-KIYK54SZ.js"><link rel="modulepreload" href="chunk-IMSVFUNF.js"><link rel="modulepreload" href="chunk-Z6TFQFEM.js"><link rel="modulepreload" href="chunk-PUEHDTZP.js"><link rel="modulepreload" href="chunk-NPZ7W4OC.js"><link rel="modulepreload" href="chunk-XG5PD4FB.js"><link rel="modulepreload" href="chunk-PTHZC6JK.js"><link rel="modulepreload" href="chunk-NW23TMQB.js"><link rel="modulepreload" href="chunk-MJXGVXMM.js"><link rel="modulepreload" href="chunk-NZQO23TG.js"><script src="polyfills-EONH2QZO.js" type="module"></script><script src="scripts-E6WOHXE5.js" defer></script><script src="main-GYNPEB4E.js" type="module"></script></body>

<!-- JavaScript -->

<script src="https://code.jquery.com/jquery-3.5.1.min.js" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous">
</script>
<script>
  var fallbackJquery = "https://www.ncbi.nlm.nih.gov/style-guide/static/base/js/jquery-3.5.0.min.js";
  window.jQuery || document.write("<script src=" + fallbackJquery + ">\x3C/script>")
</script>



<!--<script  type="text/javascript" src="assets/core/alerts/alerts.js"> </script>-->

<!--<script  type="text/javascript" src="https://www.ncbi.nlm.nih.gov/core/pinger/pinger.js"> </script>-->


<!-- - - - - -

- - - - - -->

<script type="text/javascript">
  var alertsUrl = "/core/alerts/alerts.js";
  if (typeof ncbiBaseUrl !== 'undefined') {
    alertsUrl = ncbiBaseUrl + alertsUrl;
  } else {
    alertsUrl = "https://www.ncbi.nlm.nih.gov" + alertsUrl;
  }
</script>

<!--
<script src="https://www.ncbi.nlm.nih.gov/coreutils/ncbi-banner/ncbi-banner-experiment.js"></script>
-->

</html>
