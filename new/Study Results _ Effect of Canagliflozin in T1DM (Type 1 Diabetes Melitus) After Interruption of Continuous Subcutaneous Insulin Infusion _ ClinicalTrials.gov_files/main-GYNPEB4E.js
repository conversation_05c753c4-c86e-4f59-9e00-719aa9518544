import{f as Jn,g as Je,h as kt,m as Zn,n as St,p as ei,u as ti}from"./chunk-KIYK54SZ.js";import{b as Tt}from"./chunk-IMSVFUNF.js";import{d as xi}from"./chunk-Z6TFQFEM.js";import{da as li}from"./chunk-PUEHDTZP.js";import"./chunk-NPZ7W4OC.js";import{a as si,b as gi}from"./chunk-XG5PD4FB.js";import{a as ui,b as fi}from"./chunk-PTHZC6JK.js";import{b as Et}from"./chunk-NW23TMQB.js";import{a as ci,e as pi}from"./chunk-MJXGVXMM.js";import{a as mi}from"./chunk-NZQO23TG.js";import"./chunk-T5FQAN5D.js";import{d as zn}from"./chunk-3BHRLMVD.js";import"./chunk-CV27EC5W.js";import{$ as Kn,V as Gn,Z as $n,_ as Wn,ba as Yn,ca as Qn,ra as wt}from"./chunk-JIMLWFGX.js";import{a as Re}from"./chunk-H4ZR7JKA.js";import{a as hi}from"./chunk-KIGZKE2V.js";import{b as bi,c as yi}from"./chunk-SADCLLN2.js";import{a as vi,b as _i}from"./chunk-EKGEVYQV.js";import{a as qe}from"./chunk-GCW4MX3W.js";import{a as ye,b as Pt}from"./chunk-3VE7O5U3.js";import{d as ne,e as Qt}from"./chunk-ZJOO3R7Y.js";import{Y as di,c as qn,d as Vn,e as jn,g as Ct,h as Xn,i as Yt,j as _e,l as ni,m as ii,n as Le,q as Ue,r as ri,s as He,t as be,u as oi,v as ai,w as se}from"./chunk-6DHUPXMX.js";import{$b as ve,Aa as En,Ac as c,Bc as z,Bd as Ln,Ca as ht,Cb as S,Cc as Bn,Cd as Ne,D as Wt,Da as Pn,Dc as Ie,Eb as Mn,Fc as Fn,G as pt,Ga as gt,Gc as Dn,Hb as k,Hc as In,I as yn,Jd as Un,Ka as Tn,Lc as Rn,Ma as De,Mc as Se,Nc as G,Nd as Hn,Rb as xe,Sa as We,Sb as Ye,Tb as v,Va as xt,Vb as pe,Zb as T,Zc as le,_b as On,ac as oe,ad as bt,bc as ae,ca as Kt,cc as o,d as K,da as wn,dc as l,e as zr,ec as d,ga as ge,hc as J,hd as Y,ib as Ce,ic as V,ja as Cn,kc as j,la as mt,lc as b,ma as y,mb as f,n as _n,na as kn,oa as ft,oc as ke,pb as vt,pc as Qe,rd as yt,sb as An,t as bn,ua as R,ub as Ke,va as N,wa as te,xa as Sn,xc as Xe,xd as Nn,yc as _t,zc as Z}from"./chunk-4YY6WBJZ.js";var Ee=K((es,Jt)=>{"use strict";typeof Object.create=="function"?Jt.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:Jt.exports=function(t,e){if(e){t.super_=e;var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t}}});var Pi=K(Mt=>{"use strict";Mt.byteLength=Zr;Mt.toByteArray=to;Mt.fromByteArray=ro;var ce=[],ee=[],Jr=typeof Uint8Array<"u"?Uint8Array:Array,Zt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(Pe=0,Si=Zt.length;Pe<Si;++Pe)ce[Pe]=Zt[Pe],ee[Zt.charCodeAt(Pe)]=Pe;var Pe,Si;ee[45]=62;ee[95]=63;function Ei(n){var t=n.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=n.indexOf("=");e===-1&&(e=t);var i=e===t?0:4-e%4;return[e,i]}function Zr(n){var t=Ei(n),e=t[0],i=t[1];return(e+i)*3/4-i}function eo(n,t,e){return(t+e)*3/4-e}function to(n){var t,e=Ei(n),i=e[0],r=e[1],a=new Jr(eo(n,i,r)),s=0,u=r>0?i-4:i,p;for(p=0;p<u;p+=4)t=ee[n.charCodeAt(p)]<<18|ee[n.charCodeAt(p+1)]<<12|ee[n.charCodeAt(p+2)]<<6|ee[n.charCodeAt(p+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=t&255;return r===2&&(t=ee[n.charCodeAt(p)]<<2|ee[n.charCodeAt(p+1)]>>4,a[s++]=t&255),r===1&&(t=ee[n.charCodeAt(p)]<<10|ee[n.charCodeAt(p+1)]<<4|ee[n.charCodeAt(p+2)]>>2,a[s++]=t>>8&255,a[s++]=t&255),a}function no(n){return ce[n>>18&63]+ce[n>>12&63]+ce[n>>6&63]+ce[n&63]}function io(n,t,e){for(var i,r=[],a=t;a<e;a+=3)i=(n[a]<<16&16711680)+(n[a+1]<<8&65280)+(n[a+2]&255),r.push(no(i));return r.join("")}function ro(n){for(var t,e=n.length,i=e%3,r=[],a=16383,s=0,u=e-i;s<u;s+=a)r.push(io(n,s,s+a>u?u:s+a));return i===1?(t=n[e-1],r.push(ce[t>>2]+ce[t<<4&63]+"==")):i===2&&(t=(n[e-2]<<8)+n[e-1],r.push(ce[t>>10]+ce[t>>4&63]+ce[t<<2&63]+"=")),r.join("")}});var Ti=K(en=>{"use strict";en.read=function(n,t,e,i,r){var a,s,u=r*8-i-1,p=(1<<u)-1,g=p>>1,h=-7,x=e?r-1:0,_=e?-1:1,C=n[t+x];for(x+=_,a=C&(1<<-h)-1,C>>=-h,h+=u;h>0;a=a*256+n[t+x],x+=_,h-=8);for(s=a&(1<<-h)-1,a>>=-h,h+=i;h>0;s=s*256+n[t+x],x+=_,h-=8);if(a===0)a=1-g;else{if(a===p)return s?NaN:(C?-1:1)*(1/0);s=s+Math.pow(2,i),a=a-g}return(C?-1:1)*s*Math.pow(2,a-i)};en.write=function(n,t,e,i,r,a){var s,u,p,g=a*8-r-1,h=(1<<g)-1,x=h>>1,_=r===23?Math.pow(2,-24)-Math.pow(2,-77):0,C=i?0:a-1,w=i?1:-1,P=t<0||t===0&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(u=isNaN(t)?1:0,s=h):(s=Math.floor(Math.log(t)/Math.LN2),t*(p=Math.pow(2,-s))<1&&(s--,p*=2),s+x>=1?t+=_/p:t+=_*Math.pow(2,1-x),t*p>=2&&(s++,p/=2),s+x>=h?(u=0,s=h):s+x>=1?(u=(t*p-1)*Math.pow(2,r),s=s+x):(u=t*Math.pow(2,x-1)*Math.pow(2,r),s=0));r>=8;n[e+C]=u&255,C+=w,u/=256,r-=8);for(s=s<<r|u,g+=r;g>0;n[e+C]=s&255,C+=w,s/=256,g-=8);n[e+C-w]|=P*128}});var qi=K(je=>{"use strict";var tn=Pi(),Ve=Ti(),Ai=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;je.Buffer=m;je.SlowBuffer=uo;je.INSPECT_MAX_BYTES=50;var Ot=2147483647;je.kMaxLength=Ot;m.TYPED_ARRAY_SUPPORT=oo();!m.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function oo(){try{var n=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(n,t),n.foo()===42}catch{return!1}}Object.defineProperty(m.prototype,"parent",{enumerable:!0,get:function(){if(m.isBuffer(this))return this.buffer}});Object.defineProperty(m.prototype,"offset",{enumerable:!0,get:function(){if(m.isBuffer(this))return this.byteOffset}});function me(n){if(n>Ot)throw new RangeError('The value "'+n+'" is invalid for option "size"');var t=new Uint8Array(n);return Object.setPrototypeOf(t,m.prototype),t}function m(n,t,e){if(typeof n=="number"){if(typeof t=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return an(n)}return Bi(n,t,e)}m.poolSize=8192;function Bi(n,t,e){if(typeof n=="string")return lo(n,t);if(ArrayBuffer.isView(n))return so(n);if(n==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof n);if(de(n,ArrayBuffer)||n&&de(n.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(de(n,SharedArrayBuffer)||n&&de(n.buffer,SharedArrayBuffer)))return rn(n,t,e);if(typeof n=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var i=n.valueOf&&n.valueOf();if(i!=null&&i!==n)return m.from(i,t,e);var r=co(n);if(r)return r;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof n[Symbol.toPrimitive]=="function")return m.from(n[Symbol.toPrimitive]("string"),t,e);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof n)}m.from=function(n,t,e){return Bi(n,t,e)};Object.setPrototypeOf(m.prototype,Uint8Array.prototype);Object.setPrototypeOf(m,Uint8Array);function Fi(n){if(typeof n!="number")throw new TypeError('"size" argument must be of type number');if(n<0)throw new RangeError('The value "'+n+'" is invalid for option "size"')}function ao(n,t,e){return Fi(n),n<=0?me(n):t!==void 0?typeof e=="string"?me(n).fill(t,e):me(n).fill(t):me(n)}m.alloc=function(n,t,e){return ao(n,t,e)};function an(n){return Fi(n),me(n<0?0:ln(n)|0)}m.allocUnsafe=function(n){return an(n)};m.allocUnsafeSlow=function(n){return an(n)};function lo(n,t){if((typeof t!="string"||t==="")&&(t="utf8"),!m.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var e=Di(n,t)|0,i=me(e),r=i.write(n,t);return r!==e&&(i=i.slice(0,r)),i}function nn(n){for(var t=n.length<0?0:ln(n.length)|0,e=me(t),i=0;i<t;i+=1)e[i]=n[i]&255;return e}function so(n){if(de(n,Uint8Array)){var t=new Uint8Array(n);return rn(t.buffer,t.byteOffset,t.byteLength)}return nn(n)}function rn(n,t,e){if(t<0||n.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(n.byteLength<t+(e||0))throw new RangeError('"length" is outside of buffer bounds');var i;return t===void 0&&e===void 0?i=new Uint8Array(n):e===void 0?i=new Uint8Array(n,t):i=new Uint8Array(n,t,e),Object.setPrototypeOf(i,m.prototype),i}function co(n){if(m.isBuffer(n)){var t=ln(n.length)|0,e=me(t);return e.length===0||n.copy(e,0,0,t),e}if(n.length!==void 0)return typeof n.length!="number"||sn(n.length)?me(0):nn(n);if(n.type==="Buffer"&&Array.isArray(n.data))return nn(n.data)}function ln(n){if(n>=Ot)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Ot.toString(16)+" bytes");return n|0}function uo(n){return+n!=n&&(n=0),m.alloc(+n)}m.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==m.prototype};m.compare=function(t,e){if(de(t,Uint8Array)&&(t=m.from(t,t.offset,t.byteLength)),de(e,Uint8Array)&&(e=m.from(e,e.offset,e.byteLength)),!m.isBuffer(t)||!m.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var i=t.length,r=e.length,a=0,s=Math.min(i,r);a<s;++a)if(t[a]!==e[a]){i=t[a],r=e[a];break}return i<r?-1:r<i?1:0};m.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};m.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return m.alloc(0);var i;if(e===void 0)for(e=0,i=0;i<t.length;++i)e+=t[i].length;var r=m.allocUnsafe(e),a=0;for(i=0;i<t.length;++i){var s=t[i];if(de(s,Uint8Array))a+s.length>r.length?m.from(s).copy(r,a):Uint8Array.prototype.set.call(r,s,a);else if(m.isBuffer(s))s.copy(r,a);else throw new TypeError('"list" argument must be an Array of Buffers');a+=s.length}return r};function Di(n,t){if(m.isBuffer(n))return n.length;if(ArrayBuffer.isView(n)||de(n,ArrayBuffer))return n.byteLength;if(typeof n!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof n);var e=n.length,i=arguments.length>2&&arguments[2]===!0;if(!i&&e===0)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":return on(n).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return e*2;case"hex":return e>>>1;case"base64":return Hi(n).length;default:if(r)return i?-1:on(n).length;t=(""+t).toLowerCase(),r=!0}}m.byteLength=Di;function po(n,t,e){var i=!1;if((t===void 0||t<0)&&(t=0),t>this.length||((e===void 0||e>this.length)&&(e=this.length),e<=0)||(e>>>=0,t>>>=0,e<=t))return"";for(n||(n="utf8");;)switch(n){case"hex":return wo(this,t,e);case"utf8":case"utf-8":return Ri(this,t,e);case"ascii":return bo(this,t,e);case"latin1":case"binary":return yo(this,t,e);case"base64":return vo(this,t,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Co(this,t,e);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(n+"").toLowerCase(),i=!0}}m.prototype._isBuffer=!0;function Te(n,t,e){var i=n[t];n[t]=n[e],n[e]=i}m.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)Te(this,e,e+1);return this};m.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)Te(this,e,e+3),Te(this,e+1,e+2);return this};m.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)Te(this,e,e+7),Te(this,e+1,e+6),Te(this,e+2,e+5),Te(this,e+3,e+4);return this};m.prototype.toString=function(){var t=this.length;return t===0?"":arguments.length===0?Ri(this,0,t):po.apply(this,arguments)};m.prototype.toLocaleString=m.prototype.toString;m.prototype.equals=function(t){if(!m.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:m.compare(this,t)===0};m.prototype.inspect=function(){var t="",e=je.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"};Ai&&(m.prototype[Ai]=m.prototype.inspect);m.prototype.compare=function(t,e,i,r,a){if(de(t,Uint8Array)&&(t=m.from(t,t.offset,t.byteLength)),!m.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),i===void 0&&(i=t?t.length:0),r===void 0&&(r=0),a===void 0&&(a=this.length),e<0||i>t.length||r<0||a>this.length)throw new RangeError("out of range index");if(r>=a&&e>=i)return 0;if(r>=a)return-1;if(e>=i)return 1;if(e>>>=0,i>>>=0,r>>>=0,a>>>=0,this===t)return 0;for(var s=a-r,u=i-e,p=Math.min(s,u),g=this.slice(r,a),h=t.slice(e,i),x=0;x<p;++x)if(g[x]!==h[x]){s=g[x],u=h[x];break}return s<u?-1:u<s?1:0};function Ii(n,t,e,i,r){if(n.length===0)return-1;if(typeof e=="string"?(i=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),e=+e,sn(e)&&(e=r?0:n.length-1),e<0&&(e=n.length+e),e>=n.length){if(r)return-1;e=n.length-1}else if(e<0)if(r)e=0;else return-1;if(typeof t=="string"&&(t=m.from(t,i)),m.isBuffer(t))return t.length===0?-1:Mi(n,t,e,i,r);if(typeof t=="number")return t=t&255,typeof Uint8Array.prototype.indexOf=="function"?r?Uint8Array.prototype.indexOf.call(n,t,e):Uint8Array.prototype.lastIndexOf.call(n,t,e):Mi(n,[t],e,i,r);throw new TypeError("val must be string, number or Buffer")}function Mi(n,t,e,i,r){var a=1,s=n.length,u=t.length;if(i!==void 0&&(i=String(i).toLowerCase(),i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")){if(n.length<2||t.length<2)return-1;a=2,s/=2,u/=2,e/=2}function p(C,w){return a===1?C[w]:C.readUInt16BE(w*a)}var g;if(r){var h=-1;for(g=e;g<s;g++)if(p(n,g)===p(t,h===-1?0:g-h)){if(h===-1&&(h=g),g-h+1===u)return h*a}else h!==-1&&(g-=g-h),h=-1}else for(e+u>s&&(e=s-u),g=e;g>=0;g--){for(var x=!0,_=0;_<u;_++)if(p(n,g+_)!==p(t,_)){x=!1;break}if(x)return g}return-1}m.prototype.includes=function(t,e,i){return this.indexOf(t,e,i)!==-1};m.prototype.indexOf=function(t,e,i){return Ii(this,t,e,i,!0)};m.prototype.lastIndexOf=function(t,e,i){return Ii(this,t,e,i,!1)};function mo(n,t,e,i){e=Number(e)||0;var r=n.length-e;i?(i=Number(i),i>r&&(i=r)):i=r;var a=t.length;i>a/2&&(i=a/2);for(var s=0;s<i;++s){var u=parseInt(t.substr(s*2,2),16);if(sn(u))return s;n[e+s]=u}return s}function fo(n,t,e,i){return Bt(on(t,n.length-e),n,e,i)}function ho(n,t,e,i){return Bt(Eo(t),n,e,i)}function go(n,t,e,i){return Bt(Hi(t),n,e,i)}function xo(n,t,e,i){return Bt(Po(t,n.length-e),n,e,i)}m.prototype.write=function(t,e,i,r){if(e===void 0)r="utf8",i=this.length,e=0;else if(i===void 0&&typeof e=="string")r=e,i=this.length,e=0;else if(isFinite(e))e=e>>>0,isFinite(i)?(i=i>>>0,r===void 0&&(r="utf8")):(r=i,i=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var a=this.length-e;if((i===void 0||i>a)&&(i=a),t.length>0&&(i<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return mo(this,t,e,i);case"utf8":case"utf-8":return fo(this,t,e,i);case"ascii":case"latin1":case"binary":return ho(this,t,e,i);case"base64":return go(this,t,e,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return xo(this,t,e,i);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}};m.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function vo(n,t,e){return t===0&&e===n.length?tn.fromByteArray(n):tn.fromByteArray(n.slice(t,e))}function Ri(n,t,e){e=Math.min(n.length,e);for(var i=[],r=t;r<e;){var a=n[r],s=null,u=a>239?4:a>223?3:a>191?2:1;if(r+u<=e){var p,g,h,x;switch(u){case 1:a<128&&(s=a);break;case 2:p=n[r+1],(p&192)===128&&(x=(a&31)<<6|p&63,x>127&&(s=x));break;case 3:p=n[r+1],g=n[r+2],(p&192)===128&&(g&192)===128&&(x=(a&15)<<12|(p&63)<<6|g&63,x>2047&&(x<55296||x>57343)&&(s=x));break;case 4:p=n[r+1],g=n[r+2],h=n[r+3],(p&192)===128&&(g&192)===128&&(h&192)===128&&(x=(a&15)<<18|(p&63)<<12|(g&63)<<6|h&63,x>65535&&x<1114112&&(s=x))}}s===null?(s=65533,u=1):s>65535&&(s-=65536,i.push(s>>>10&1023|55296),s=56320|s&1023),i.push(s),r+=u}return _o(i)}var Oi=4096;function _o(n){var t=n.length;if(t<=Oi)return String.fromCharCode.apply(String,n);for(var e="",i=0;i<t;)e+=String.fromCharCode.apply(String,n.slice(i,i+=Oi));return e}function bo(n,t,e){var i="";e=Math.min(n.length,e);for(var r=t;r<e;++r)i+=String.fromCharCode(n[r]&127);return i}function yo(n,t,e){var i="";e=Math.min(n.length,e);for(var r=t;r<e;++r)i+=String.fromCharCode(n[r]);return i}function wo(n,t,e){var i=n.length;(!t||t<0)&&(t=0),(!e||e<0||e>i)&&(e=i);for(var r="",a=t;a<e;++a)r+=To[n[a]];return r}function Co(n,t,e){for(var i=n.slice(t,e),r="",a=0;a<i.length-1;a+=2)r+=String.fromCharCode(i[a]+i[a+1]*256);return r}m.prototype.slice=function(t,e){var i=this.length;t=~~t,e=e===void 0?i:~~e,t<0?(t+=i,t<0&&(t=0)):t>i&&(t=i),e<0?(e+=i,e<0&&(e=0)):e>i&&(e=i),e<t&&(e=t);var r=this.subarray(t,e);return Object.setPrototypeOf(r,m.prototype),r};function F(n,t,e){if(n%1!==0||n<0)throw new RangeError("offset is not uint");if(n+t>e)throw new RangeError("Trying to access beyond buffer length")}m.prototype.readUintLE=m.prototype.readUIntLE=function(t,e,i){t=t>>>0,e=e>>>0,i||F(t,e,this.length);for(var r=this[t],a=1,s=0;++s<e&&(a*=256);)r+=this[t+s]*a;return r};m.prototype.readUintBE=m.prototype.readUIntBE=function(t,e,i){t=t>>>0,e=e>>>0,i||F(t,e,this.length);for(var r=this[t+--e],a=1;e>0&&(a*=256);)r+=this[t+--e]*a;return r};m.prototype.readUint8=m.prototype.readUInt8=function(t,e){return t=t>>>0,e||F(t,1,this.length),this[t]};m.prototype.readUint16LE=m.prototype.readUInt16LE=function(t,e){return t=t>>>0,e||F(t,2,this.length),this[t]|this[t+1]<<8};m.prototype.readUint16BE=m.prototype.readUInt16BE=function(t,e){return t=t>>>0,e||F(t,2,this.length),this[t]<<8|this[t+1]};m.prototype.readUint32LE=m.prototype.readUInt32LE=function(t,e){return t=t>>>0,e||F(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216};m.prototype.readUint32BE=m.prototype.readUInt32BE=function(t,e){return t=t>>>0,e||F(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])};m.prototype.readIntLE=function(t,e,i){t=t>>>0,e=e>>>0,i||F(t,e,this.length);for(var r=this[t],a=1,s=0;++s<e&&(a*=256);)r+=this[t+s]*a;return a*=128,r>=a&&(r-=Math.pow(2,8*e)),r};m.prototype.readIntBE=function(t,e,i){t=t>>>0,e=e>>>0,i||F(t,e,this.length);for(var r=e,a=1,s=this[t+--r];r>0&&(a*=256);)s+=this[t+--r]*a;return a*=128,s>=a&&(s-=Math.pow(2,8*e)),s};m.prototype.readInt8=function(t,e){return t=t>>>0,e||F(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]};m.prototype.readInt16LE=function(t,e){t=t>>>0,e||F(t,2,this.length);var i=this[t]|this[t+1]<<8;return i&32768?i|4294901760:i};m.prototype.readInt16BE=function(t,e){t=t>>>0,e||F(t,2,this.length);var i=this[t+1]|this[t]<<8;return i&32768?i|4294901760:i};m.prototype.readInt32LE=function(t,e){return t=t>>>0,e||F(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24};m.prototype.readInt32BE=function(t,e){return t=t>>>0,e||F(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]};m.prototype.readFloatLE=function(t,e){return t=t>>>0,e||F(t,4,this.length),Ve.read(this,t,!0,23,4)};m.prototype.readFloatBE=function(t,e){return t=t>>>0,e||F(t,4,this.length),Ve.read(this,t,!1,23,4)};m.prototype.readDoubleLE=function(t,e){return t=t>>>0,e||F(t,8,this.length),Ve.read(this,t,!0,52,8)};m.prototype.readDoubleBE=function(t,e){return t=t>>>0,e||F(t,8,this.length),Ve.read(this,t,!1,52,8)};function $(n,t,e,i,r,a){if(!m.isBuffer(n))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>r||t<a)throw new RangeError('"value" argument is out of bounds');if(e+i>n.length)throw new RangeError("Index out of range")}m.prototype.writeUintLE=m.prototype.writeUIntLE=function(t,e,i,r){if(t=+t,e=e>>>0,i=i>>>0,!r){var a=Math.pow(2,8*i)-1;$(this,t,e,i,a,0)}var s=1,u=0;for(this[e]=t&255;++u<i&&(s*=256);)this[e+u]=t/s&255;return e+i};m.prototype.writeUintBE=m.prototype.writeUIntBE=function(t,e,i,r){if(t=+t,e=e>>>0,i=i>>>0,!r){var a=Math.pow(2,8*i)-1;$(this,t,e,i,a,0)}var s=i-1,u=1;for(this[e+s]=t&255;--s>=0&&(u*=256);)this[e+s]=t/u&255;return e+i};m.prototype.writeUint8=m.prototype.writeUInt8=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,1,255,0),this[e]=t&255,e+1};m.prototype.writeUint16LE=m.prototype.writeUInt16LE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,2,65535,0),this[e]=t&255,this[e+1]=t>>>8,e+2};m.prototype.writeUint16BE=m.prototype.writeUInt16BE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=t&255,e+2};m.prototype.writeUint32LE=m.prototype.writeUInt32LE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t&255,e+4};m.prototype.writeUint32BE=m.prototype.writeUInt32BE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};m.prototype.writeIntLE=function(t,e,i,r){if(t=+t,e=e>>>0,!r){var a=Math.pow(2,8*i-1);$(this,t,e,i,a-1,-a)}var s=0,u=1,p=0;for(this[e]=t&255;++s<i&&(u*=256);)t<0&&p===0&&this[e+s-1]!==0&&(p=1),this[e+s]=(t/u>>0)-p&255;return e+i};m.prototype.writeIntBE=function(t,e,i,r){if(t=+t,e=e>>>0,!r){var a=Math.pow(2,8*i-1);$(this,t,e,i,a-1,-a)}var s=i-1,u=1,p=0;for(this[e+s]=t&255;--s>=0&&(u*=256);)t<0&&p===0&&this[e+s+1]!==0&&(p=1),this[e+s]=(t/u>>0)-p&255;return e+i};m.prototype.writeInt8=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=t&255,e+1};m.prototype.writeInt16LE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,2,32767,-32768),this[e]=t&255,this[e+1]=t>>>8,e+2};m.prototype.writeInt16BE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=t&255,e+2};m.prototype.writeInt32LE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,4,2147483647,-2147483648),this[e]=t&255,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4};m.prototype.writeInt32BE=function(t,e,i){return t=+t,e=e>>>0,i||$(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};function Ni(n,t,e,i,r,a){if(e+i>n.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function Li(n,t,e,i,r){return t=+t,e=e>>>0,r||Ni(n,t,e,4,34028234663852886e22,-34028234663852886e22),Ve.write(n,t,e,i,23,4),e+4}m.prototype.writeFloatLE=function(t,e,i){return Li(this,t,e,!0,i)};m.prototype.writeFloatBE=function(t,e,i){return Li(this,t,e,!1,i)};function Ui(n,t,e,i,r){return t=+t,e=e>>>0,r||Ni(n,t,e,8,17976931348623157e292,-17976931348623157e292),Ve.write(n,t,e,i,52,8),e+8}m.prototype.writeDoubleLE=function(t,e,i){return Ui(this,t,e,!0,i)};m.prototype.writeDoubleBE=function(t,e,i){return Ui(this,t,e,!1,i)};m.prototype.copy=function(t,e,i,r){if(!m.isBuffer(t))throw new TypeError("argument should be a Buffer");if(i||(i=0),!r&&r!==0&&(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<i&&(r=i),r===i||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(i<0||i>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-i&&(r=t.length-e+i);var a=r-i;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(e,i,r):Uint8Array.prototype.set.call(t,this.subarray(i,r),e),a};m.prototype.fill=function(t,e,i,r){if(typeof t=="string"){if(typeof e=="string"?(r=e,e=0,i=this.length):typeof i=="string"&&(r=i,i=this.length),r!==void 0&&typeof r!="string")throw new TypeError("encoding must be a string");if(typeof r=="string"&&!m.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(t.length===1){var a=t.charCodeAt(0);(r==="utf8"&&a<128||r==="latin1")&&(t=a)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<i)throw new RangeError("Out of range index");if(i<=e)return this;e=e>>>0,i=i===void 0?this.length:i>>>0,t||(t=0);var s;if(typeof t=="number")for(s=e;s<i;++s)this[s]=t;else{var u=m.isBuffer(t)?t:m.from(t,r),p=u.length;if(p===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(s=0;s<i-e;++s)this[s+e]=u[s%p]}return this};var ko=/[^+/0-9A-Za-z-_]/g;function So(n){if(n=n.split("=")[0],n=n.trim().replace(ko,""),n.length<2)return"";for(;n.length%4!==0;)n=n+"=";return n}function on(n,t){t=t||1/0;for(var e,i=n.length,r=null,a=[],s=0;s<i;++s){if(e=n.charCodeAt(s),e>55295&&e<57344){if(!r){if(e>56319){(t-=3)>-1&&a.push(239,191,189);continue}else if(s+1===i){(t-=3)>-1&&a.push(239,191,189);continue}r=e;continue}if(e<56320){(t-=3)>-1&&a.push(239,191,189),r=e;continue}e=(r-55296<<10|e-56320)+65536}else r&&(t-=3)>-1&&a.push(239,191,189);if(r=null,e<128){if((t-=1)<0)break;a.push(e)}else if(e<2048){if((t-=2)<0)break;a.push(e>>6|192,e&63|128)}else if(e<65536){if((t-=3)<0)break;a.push(e>>12|224,e>>6&63|128,e&63|128)}else if(e<1114112){if((t-=4)<0)break;a.push(e>>18|240,e>>12&63|128,e>>6&63|128,e&63|128)}else throw new Error("Invalid code point")}return a}function Eo(n){for(var t=[],e=0;e<n.length;++e)t.push(n.charCodeAt(e)&255);return t}function Po(n,t){for(var e,i,r,a=[],s=0;s<n.length&&!((t-=2)<0);++s)e=n.charCodeAt(s),i=e>>8,r=e%256,a.push(r),a.push(i);return a}function Hi(n){return tn.toByteArray(So(n))}function Bt(n,t,e,i){for(var r=0;r<i&&!(r+e>=t.length||r>=n.length);++r)t[r+e]=n[r];return r}function de(n,t){return n instanceof t||n!=null&&n.constructor!=null&&n.constructor.name!=null&&n.constructor.name===t.name}function sn(n){return n!==n}var To=function(){for(var n="0123456789abcdef",t=new Array(256),e=0;e<16;++e)for(var i=e*16,r=0;r<16;++r)t[i+r]=n[e]+n[r];return t}()});var we=K((cn,ji)=>{"use strict";var Ft=qi(),ue=Ft.Buffer;function Vi(n,t){for(var e in n)t[e]=n[e]}ue.from&&ue.alloc&&ue.allocUnsafe&&ue.allocUnsafeSlow?ji.exports=Ft:(Vi(Ft,cn),cn.Buffer=Ae);function Ae(n,t,e){return ue(n,t,e)}Ae.prototype=Object.create(ue.prototype);Vi(ue,Ae);Ae.from=function(n,t,e){if(typeof n=="number")throw new TypeError("Argument must not be a number");return ue(n,t,e)};Ae.alloc=function(n,t,e){if(typeof n!="number")throw new TypeError("Argument must be a number");var i=ue(n);return t!==void 0?typeof e=="string"?i.fill(t,e):i.fill(t):i.fill(0),i};Ae.allocUnsafe=function(n){if(typeof n!="number")throw new TypeError("Argument must be a number");return ue(n)};Ae.allocUnsafeSlow=function(n){if(typeof n!="number")throw new TypeError("Argument must be a number");return Ft.SlowBuffer(n)}});var Me=K((rs,Gi)=>{"use strict";var zi=we().Buffer;function Dt(n,t){this._block=zi.alloc(n),this._finalSize=t,this._blockSize=n,this._len=0}Dt.prototype.update=function(n,t){typeof n=="string"&&(t=t||"utf8",n=zi.from(n,t));for(var e=this._block,i=this._blockSize,r=n.length,a=this._len,s=0;s<r;){for(var u=a%i,p=Math.min(r-s,i-u),g=0;g<p;g++)e[u+g]=n[s+g];a+=p,s+=p,a%i===0&&this._update(e)}return this._len+=r,this};Dt.prototype.digest=function(n){var t=this._len%this._blockSize;this._block[t]=128,this._block.fill(0,t+1),t>=this._finalSize&&(this._update(this._block),this._block.fill(0));var e=this._len*8;if(e<=4294967295)this._block.writeUInt32BE(e,this._blockSize-4);else{var i=(e&4294967295)>>>0,r=(e-i)/4294967296;this._block.writeUInt32BE(r,this._blockSize-8),this._block.writeUInt32BE(i,this._blockSize-4)}this._update(this._block);var a=this._hash();return n?a.toString(n):a};Dt.prototype._update=function(){throw new Error("_update must be implemented by subclass")};Gi.exports=Dt});var Ki=K((os,Wi)=>{"use strict";var Ao=Ee(),$i=Me(),Mo=we().Buffer,Oo=[1518500249,1859775393,-1894007588,-899497514],Bo=new Array(80);function Ze(){this.init(),this._w=Bo,$i.call(this,64,56)}Ao(Ze,$i);Ze.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this};function Fo(n){return n<<5|n>>>27}function Do(n){return n<<30|n>>>2}function Io(n,t,e,i){return n===0?t&e|~t&i:n===2?t&e|t&i|e&i:t^e^i}Ze.prototype._update=function(n){for(var t=this._w,e=this._a|0,i=this._b|0,r=this._c|0,a=this._d|0,s=this._e|0,u=0;u<16;++u)t[u]=n.readInt32BE(u*4);for(;u<80;++u)t[u]=t[u-3]^t[u-8]^t[u-14]^t[u-16];for(var p=0;p<80;++p){var g=~~(p/20),h=Fo(e)+Io(g,i,r,a)+s+t[p]+Oo[g]|0;s=a,a=r,r=Do(i),i=e,e=h}this._a=e+this._a|0,this._b=i+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0};Ze.prototype._hash=function(){var n=Mo.allocUnsafe(20);return n.writeInt32BE(this._a|0,0),n.writeInt32BE(this._b|0,4),n.writeInt32BE(this._c|0,8),n.writeInt32BE(this._d|0,12),n.writeInt32BE(this._e|0,16),n};Wi.exports=Ze});var Xi=K((as,Qi)=>{"use strict";var Ro=Ee(),Yi=Me(),No=we().Buffer,Lo=[1518500249,1859775393,-1894007588,-899497514],Uo=new Array(80);function et(){this.init(),this._w=Uo,Yi.call(this,64,56)}Ro(et,Yi);et.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this};function Ho(n){return n<<1|n>>>31}function qo(n){return n<<5|n>>>27}function Vo(n){return n<<30|n>>>2}function jo(n,t,e,i){return n===0?t&e|~t&i:n===2?t&e|t&i|e&i:t^e^i}et.prototype._update=function(n){for(var t=this._w,e=this._a|0,i=this._b|0,r=this._c|0,a=this._d|0,s=this._e|0,u=0;u<16;++u)t[u]=n.readInt32BE(u*4);for(;u<80;++u)t[u]=Ho(t[u-3]^t[u-8]^t[u-14]^t[u-16]);for(var p=0;p<80;++p){var g=~~(p/20),h=qo(e)+jo(g,i,r,a)+s+t[p]+Lo[g]|0;s=a,a=r,r=Vo(i),i=e,e=h}this._a=e+this._a|0,this._b=i+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0};et.prototype._hash=function(){var n=No.allocUnsafe(20);return n.writeInt32BE(this._a|0,0),n.writeInt32BE(this._b|0,4),n.writeInt32BE(this._c|0,8),n.writeInt32BE(this._d|0,12),n.writeInt32BE(this._e|0,16),n};Qi.exports=et});var dn=K((ls,Zi)=>{"use strict";var zo=Ee(),Ji=Me(),Go=we().Buffer,$o=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Wo=new Array(64);function tt(){this.init(),this._w=Wo,Ji.call(this,64,56)}zo(tt,Ji);tt.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this};function Ko(n,t,e){return e^n&(t^e)}function Yo(n,t,e){return n&t|e&(n|t)}function Qo(n){return(n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10)}function Xo(n){return(n>>>6|n<<26)^(n>>>11|n<<21)^(n>>>25|n<<7)}function Jo(n){return(n>>>7|n<<25)^(n>>>18|n<<14)^n>>>3}function Zo(n){return(n>>>17|n<<15)^(n>>>19|n<<13)^n>>>10}tt.prototype._update=function(n){for(var t=this._w,e=this._a|0,i=this._b|0,r=this._c|0,a=this._d|0,s=this._e|0,u=this._f|0,p=this._g|0,g=this._h|0,h=0;h<16;++h)t[h]=n.readInt32BE(h*4);for(;h<64;++h)t[h]=Zo(t[h-2])+t[h-7]+Jo(t[h-15])+t[h-16]|0;for(var x=0;x<64;++x){var _=g+Xo(s)+Ko(s,u,p)+$o[x]+t[x]|0,C=Qo(e)+Yo(e,i,r)|0;g=p,p=u,u=s,s=a+_|0,a=r,r=i,i=e,e=_+C|0}this._a=e+this._a|0,this._b=i+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0,this._f=u+this._f|0,this._g=p+this._g|0,this._h=g+this._h|0};tt.prototype._hash=function(){var n=Go.allocUnsafe(32);return n.writeInt32BE(this._a,0),n.writeInt32BE(this._b,4),n.writeInt32BE(this._c,8),n.writeInt32BE(this._d,12),n.writeInt32BE(this._e,16),n.writeInt32BE(this._f,20),n.writeInt32BE(this._g,24),n.writeInt32BE(this._h,28),n};Zi.exports=tt});var tr=K((ss,er)=>{"use strict";var ea=Ee(),ta=dn(),na=Me(),ia=we().Buffer,ra=new Array(64);function It(){this.init(),this._w=ra,na.call(this,64,56)}ea(It,ta);It.prototype.init=function(){return this._a=3238371032,this._b=914150663,this._c=812702999,this._d=4144912697,this._e=4290775857,this._f=1750603025,this._g=1694076839,this._h=3204075428,this};It.prototype._hash=function(){var n=ia.allocUnsafe(28);return n.writeInt32BE(this._a,0),n.writeInt32BE(this._b,4),n.writeInt32BE(this._c,8),n.writeInt32BE(this._d,12),n.writeInt32BE(this._e,16),n.writeInt32BE(this._f,20),n.writeInt32BE(this._g,24),n};er.exports=It});var un=K((cs,sr)=>{"use strict";var oa=Ee(),lr=Me(),aa=we().Buffer,nr=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],la=new Array(160);function nt(){this.init(),this._w=la,lr.call(this,128,112)}oa(nt,lr);nt.prototype.init=function(){return this._ah=1779033703,this._bh=3144134277,this._ch=1013904242,this._dh=2773480762,this._eh=1359893119,this._fh=2600822924,this._gh=528734635,this._hh=1541459225,this._al=4089235720,this._bl=2227873595,this._cl=4271175723,this._dl=1595750129,this._el=2917565137,this._fl=725511199,this._gl=4215389547,this._hl=327033209,this};function ir(n,t,e){return e^n&(t^e)}function rr(n,t,e){return n&t|e&(n|t)}function or(n,t){return(n>>>28|t<<4)^(t>>>2|n<<30)^(t>>>7|n<<25)}function ar(n,t){return(n>>>14|t<<18)^(n>>>18|t<<14)^(t>>>9|n<<23)}function sa(n,t){return(n>>>1|t<<31)^(n>>>8|t<<24)^n>>>7}function ca(n,t){return(n>>>1|t<<31)^(n>>>8|t<<24)^(n>>>7|t<<25)}function da(n,t){return(n>>>19|t<<13)^(t>>>29|n<<3)^n>>>6}function ua(n,t){return(n>>>19|t<<13)^(t>>>29|n<<3)^(n>>>6|t<<26)}function D(n,t){return n>>>0<t>>>0?1:0}nt.prototype._update=function(n){for(var t=this._w,e=this._ah|0,i=this._bh|0,r=this._ch|0,a=this._dh|0,s=this._eh|0,u=this._fh|0,p=this._gh|0,g=this._hh|0,h=this._al|0,x=this._bl|0,_=this._cl|0,C=this._dl|0,w=this._el|0,P=this._fl|0,ie=this._gl|0,W=this._hl|0,E=0;E<32;E+=2)t[E]=n.readInt32BE(E*4),t[E+1]=n.readInt32BE(E*4+4);for(;E<160;E+=2){var L=t[E-30],U=t[E-15*2+1],he=sa(L,U),Oe=ca(U,L);L=t[E-2*2],U=t[E-2*2+1];var Be=da(L,U),rt=ua(U,L),Ht=t[E-7*2],qt=t[E-7*2+1],ot=t[E-16*2],at=t[E-16*2+1],M=Oe+qt|0,H=he+Ht+D(M,Oe)|0;M=M+rt|0,H=H+Be+D(M,rt)|0,M=M+at|0,H=H+ot+D(M,at)|0,t[E]=H,t[E+1]=M}for(var Q=0;Q<160;Q+=2){H=t[Q],M=t[Q+1];var Ge=rr(e,i,r),lt=rr(h,x,_),Vt=or(e,h),$e=or(h,e),st=ar(s,w),jt=ar(w,s),zt=nr[Q],ct=nr[Q+1],Gt=ir(s,u,p),dt=ir(w,P,ie),I=W+jt|0,X=g+st+D(I,W)|0;I=I+dt|0,X=X+Gt+D(I,dt)|0,I=I+ct|0,X=X+zt+D(I,ct)|0,I=I+M|0,X=X+H+D(I,M)|0;var ut=$e+lt|0,Fe=Vt+Ge+D(ut,$e)|0;g=p,W=ie,p=u,ie=P,u=s,P=w,w=C+I|0,s=a+X+D(w,C)|0,a=r,C=_,r=i,_=x,i=e,x=h,h=I+ut|0,e=X+Fe+D(h,I)|0}this._al=this._al+h|0,this._bl=this._bl+x|0,this._cl=this._cl+_|0,this._dl=this._dl+C|0,this._el=this._el+w|0,this._fl=this._fl+P|0,this._gl=this._gl+ie|0,this._hl=this._hl+W|0,this._ah=this._ah+e+D(this._al,h)|0,this._bh=this._bh+i+D(this._bl,x)|0,this._ch=this._ch+r+D(this._cl,_)|0,this._dh=this._dh+a+D(this._dl,C)|0,this._eh=this._eh+s+D(this._el,w)|0,this._fh=this._fh+u+D(this._fl,P)|0,this._gh=this._gh+p+D(this._gl,ie)|0,this._hh=this._hh+g+D(this._hl,W)|0};nt.prototype._hash=function(){var n=aa.allocUnsafe(64);function t(e,i,r){n.writeInt32BE(e,r),n.writeInt32BE(i,r+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),t(this._gh,this._gl,48),t(this._hh,this._hl,56),n};sr.exports=nt});var dr=K((ds,cr)=>{"use strict";var pa=Ee(),ma=un(),fa=Me(),ha=we().Buffer,ga=new Array(160);function Rt(){this.init(),this._w=ga,fa.call(this,128,112)}pa(Rt,ma);Rt.prototype.init=function(){return this._ah=3418070365,this._bh=1654270250,this._ch=2438529370,this._dh=355462360,this._eh=1731405415,this._fh=2394180231,this._gh=3675008525,this._hh=1203062813,this._al=3238371032,this._bl=914150663,this._cl=812702999,this._dl=4144912697,this._el=4290775857,this._fl=1750603025,this._gl=1694076839,this._hl=3204075428,this};Rt.prototype._hash=function(){var n=ha.allocUnsafe(48);function t(e,i,r){n.writeInt32BE(e,r),n.writeInt32BE(i,r+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),n};cr.exports=Rt});var pr=K((fe,ur)=>{"use strict";var fe=ur.exports=function(t){t=t.toLowerCase();var e=fe[t];if(!e)throw new Error(t+" is not supported (we accept pull requests)");return new e};fe.sha=Ki();fe.sha1=Xi();fe.sha224=tr();fe.sha256=dn();fe.sha384=dr();fe.sha512=un()});var Gr=(()=>{class n extends St{constructor(e,i,r){super(e,i,r)}ngOnDestroy(){this.flush()}static \u0275fac=function(i){return new(i||n)(mt(yt),mt(Je),mt(kt))};static \u0275prov=ge({token:n,factory:n.\u0275fac})}return n})();function $r(){return new Zn}function Wr(n,t,e){return new ti(n,t,e)}var wi=[{provide:kt,useFactory:$r},{provide:St,useClass:Gr},{provide:vt,useFactory:Wr,deps:[Ct,St,gt]}],$l=[{provide:Je,useClass:Jn},{provide:We,useValue:"NoopAnimations"},...wi],Kr=[{provide:Je,useFactory:()=>new ei},{provide:We,useFactory:()=>"BrowserAnimations"},...wi];function Ci(){return xt("NgEagerAnimations"),[...Kr]}var Yr="@",Qr=(()=>{class n{doc;delegate;zone;animationType;moduleImpl;_rendererFactoryPromise=null;scheduler=null;injector=y(En);loadingSchedulerFn=y(Xr,{optional:!0});_engine;constructor(e,i,r,a,s){this.doc=e,this.delegate=i,this.zone=r,this.animationType=a,this.moduleImpl=s}ngOnDestroy(){this._engine?.flush()}loadImpl(){let e=()=>this.moduleImpl??import("./chunk-UCLOVKCQ.js").then(r=>r),i;return this.loadingSchedulerFn?i=this.loadingSchedulerFn(e):i=e(),i.catch(r=>{throw new wn(5300,!1)}).then(({\u0275createEngine:r,\u0275AnimationRendererFactory:a})=>{this._engine=r(this.animationType,this.doc);let s=new a(this.delegate,this._engine,this.zone);return this.delegate=s,s})}createRenderer(e,i){let r=this.delegate.createRenderer(e,i);if(r.\u0275type===0)return r;typeof r.throwOnSyntheticProps=="boolean"&&(r.throwOnSyntheticProps=!1);let a=new Xt(r);return i?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(s=>{let u=s.createRenderer(e,i);a.use(u),this.scheduler??=this.injector.get(Pn,null,{optional:!0}),this.scheduler?.notify(10)}).catch(s=>{a.use(r)}),a}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}componentReplaced(e){this._engine?.flush(),this.delegate.componentReplaced?.(e)}static \u0275fac=function(i){An()};static \u0275prov=ge({token:n,factory:n.\u0275fac})}return n})(),Xt=class{delegate;replay=[];\u0275type=1;constructor(t){this.delegate=t}use(t){if(this.delegate=t,this.replay!==null){for(let e of this.replay)e(t);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(t,e){return this.delegate.createElement(t,e)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}get destroyNode(){return this.delegate.destroyNode}appendChild(t,e){this.delegate.appendChild(t,e)}insertBefore(t,e,i,r){this.delegate.insertBefore(t,e,i,r)}removeChild(t,e,i){this.delegate.removeChild(t,e,i)}selectRootElement(t,e){return this.delegate.selectRootElement(t,e)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,e,i,r){this.delegate.setAttribute(t,e,i,r)}removeAttribute(t,e,i){this.delegate.removeAttribute(t,e,i)}addClass(t,e){this.delegate.addClass(t,e)}removeClass(t,e){this.delegate.removeClass(t,e)}setStyle(t,e,i,r){this.delegate.setStyle(t,e,i,r)}removeStyle(t,e,i){this.delegate.removeStyle(t,e,i)}setProperty(t,e,i){this.shouldReplay(e)&&this.replay.push(r=>r.setProperty(t,e,i)),this.delegate.setProperty(t,e,i)}setValue(t,e){this.delegate.setValue(t,e)}listen(t,e,i,r){return this.shouldReplay(e)&&this.replay.push(a=>a.listen(t,e,i,r)),this.delegate.listen(t,e,i,r)}shouldReplay(t){return this.replay!==null&&t.startsWith(Yr)}},Xr=new Cn("");function ki(n="animations"){return xt("NgAsyncAnimations"),kn([{provide:vt,useFactory:(t,e,i)=>new Qr(t,e,i,n),deps:[yt,Ct,gt]},{provide:We,useValue:n==="noop"?"NoopAnimations":"BrowserAnimations"}])}var At={branch:"HEAD",tag:"v2.16.5",post:"undefined",hash:"undefined",versionText:"v2.16.5"};var Br=zr(pr());function mr(n){return typeof n?.onNavigation=="function"}var Nt=(()=>{class n{constructor(){this.fromBreadcrumbs=!1,this.states=new Map}getState(e){return this.states.get(e)}saveState(e){let i={scrollY:window.scrollY};return this.states.set(e,i),this.lastSavedUrl=e,i}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275prov=ge({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var ze=(()=>{class n{constructor(){this.fromLink$=new _n(n.currentUrlEncoded()),this.fromLink=Qt(this.fromLink$,{initialValue:""}),this.router=y(He),this.route=y(Ue),this.isHomePage=Y(()=>this.currentPath()==="/"),this.currentUrl=De(this.router.url),this.currentPath=Y(()=>this.currentUrl().split("?")[0]),this.queryParams=Qt(this.route.queryParamMap,{initialValue:this.route.snapshot.queryParamMap}),this.router.events.pipe(ne(),pt(e=>e instanceof Le)).subscribe(e=>{this.currentUrl.set(e.urlAfterRedirects),this.fromLink$.next(n.currentUrlEncoded())})}static currentUrlEncoded(){return encodeURIComponent(window.location.href)}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275prov=ge({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var xa=()=>({});function va(n,t){if(n&1){let e=V();o(0,"a",8),j("click",function(){R(e);let r=b(3);return N(r.onLinkClick())}),o(1,"span"),c(2),l()()}if(n&2){let e=b().$implicit;v("routerLink",e.url)("queryParams",e.queryParams?e.queryParams:Se(3,xa)),f(2),z(e.text)}}function _a(n,t){if(n&1&&(o(0,"span"),c(1),l()),n&2){let e=b().$implicit;f(),z(e.text)}}function ba(n,t){if(n&1&&(o(0,"li",6),k(1,va,3,4,"a",7)(2,_a,2,1,"span"),l()),n&2){let e=t.$implicit,i=t.$index,r=t.$count;pe("usa-current",i===r-1),xe("aria-current",i===r-1?"page":null),f(),T(e.url?1:-1),f(),T(e.url?-1:2)}}function ya(n,t){if(n&1&&(o(0,"div",0)(1,"div",1)(2,"div",2)(3,"nav",3)(4,"ol",4),oe(5,ba,3,5,"li",5,On),l()()()()()),n&2){let e=b();f(5),ae(e.paths())}}var wa=n=>n.replace(/_/g,"-"),fr=Object.entries(se).filter(n=>"breadcrumbs"in n[1]&&n[1].breadcrumbs!==void 0).reduce((n,[t,e])=>("breadcrumbs"in e&&n.set(wa(t),e.breadcrumbs),n),new Map),hr=(()=>{class n{constructor(){this.router=y(He),this.navigationService=y(Nt),this.paths=De([]),this.router.events.pipe(pt(e=>e instanceof Le),ne()).subscribe(()=>{this.paths.set(this.parsePath())})}ngOnInit(){this.paths.set(this.parsePath())}parsePath(){let e=this.router.parseUrl(this.router.url),i=e.root.children[ni];if(!i)return[];let r=i.segments.map(p=>p.path).filter(p=>fr.has(p)),a=r.length===0?"home":r[0],s=[];if(a==="search"||a==="study"){let p=e.queryParams;delete p.tab,delete p.a,delete p.b,s.push({text:"Search Results",url:"/search",queryParams:p}),r[1]==="my-saved-studies"&&(a=r[1])}else a==="find-studies"&&r.length>1?(a=r[1],s.push({text:"Find Studies",url:"/find-studies"})):a==="study-basics"&&r.length>1?(a=r[1],s.push({text:"Study Basics",url:"/study-basics"})):a==="submit-studies"&&r.length>1?(a=r[r.length-1],s.push({text:"Submit Studies",url:"/submit-studies"}),r.length>2&&r[1]==="prs-help"&&s.push({text:"PRS Help Resources",url:"/submit-studies/prs-help"}),r.length>2&&r[1]==="prs-accounts"&&s.push({text:"PRS Accounts",url:"/submit-studies/prs-accounts"})):a==="data-api"&&r.length>1?(a=r[r.length-1],s.push({text:"Data and API",url:"/data-api"}),r.length>2&&r[1]==="about-api"&&s.push({text:"About API",url:"/data-api/about-api"})):a==="policy"&&r.length>1?(a=r[1],s.push({text:"Policy",url:"/policy"})):a==="about-site"&&r.length>1&&(a=r[1],s.push({text:"About this site",url:"/about-site"}));let u=fr.get(a);return u&&s.push({text:u}),s.length===0?[]:(s.unshift({text:"Home",url:"/"}),delete s[s.length-1].url,s)}onLinkClick(){this.navigationService.fromBreadcrumbs=!0}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-breadcrumbs"]],decls:1,vars:1,consts:[[1,"grid-container","desktop:grid-container-widescreen"],[1,"grid-row"],[1,"mobile-lg:grid-col-8","text-left"],["aria-label","Breadcrumbs",1,"usa-breadcrumb"],[1,"usa-breadcrumb__list"],[1,"usa-breadcrumb__list-item",3,"usa-current"],[1,"usa-breadcrumb__list-item"],[1,"usa-breadcrumb__link",3,"routerLink","queryParams"],[1,"usa-breadcrumb__link",3,"click","routerLink","queryParams"]],template:function(i,r){i&1&&k(0,ya,7,0,"div",0),i&2&&T(r.paths().length?0:-1)},dependencies:[be],styles:[".usa-breadcrumb[_ngcontent-%COMP%]{background-color:transparent}@media print{[_nghost-%COMP%]{display:none}}"],changeDetection:0})}}return n})();var Lt=()=>["/study",""],Ut=n=>({a:null,b:null,rank:n});function Ca(n,t){n&1&&J(0)}function ka(n,t){if(n&1&&(o(0,"div",8)(1,"div",9),k(2,Ca,1,0,"ng-container",10),l()()),n&2){b(2);let e=Z(4);f(2),v("ngTemplateOutlet",e)}}function Sa(n,t){n&1&&J(0)}function Ea(n,t){n&1&&J(0)}function Pa(n,t){if(n&1&&(o(0,"div",9),k(1,Sa,1,0,"ng-container",10)(2,Ea,1,0,"ng-container",10),l()),n&2){b(2);let e=Z(6),i=Z(8);f(),v("ngTemplateOutlet",i),f(),v("ngTemplateOutlet",e)}}function Ta(n,t){if(n&1&&(o(0,"div",4)(1,"div",6)(2,"div",7),k(3,ka,3,1,"div",8)(4,Pa,3,2,"div",9),l()()()),n&2){let e=b();f(3),T(e.isSingleResult()?3:4)}}function Aa(n,t){n&1&&J(0)}function Ma(n,t){if(n&1&&(o(0,"div",13)(1,"div",15),k(2,Aa,1,0,"ng-container",10),l()()),n&2){b(2);let e=Z(4);f(2),v("ngTemplateOutlet",e)}}function Oa(n,t){n&1&&J(0)}function Ba(n,t){if(n&1){let e=V();o(0,"div",14)(1,"div",16),k(2,Oa,1,0,"ng-container",10),l(),o(3,"div",17)(4,"div",18)(5,"a",19)(6,"div"),c(7," Previous Study "),l()()(),o(8,"div",18)(9,"div",20)(10,"a",21),j("click",function(){R(e);let r=b(2);return N(r.returnToSearch())}),c(11,"Return to Search"),l()()(),o(12,"div")(13,"a",19)(14,"div"),c(15," Next Study "),l()()()()()}if(n&2){let e=b(2),i=Z(8);f(2),v("ngTemplateOutlet",i),f(3),pe("disabled-link",!e.showPreviousStudyButton()),v("routerLink",e.showPreviousStudyButton()?Se(13,Lt):void 0)("queryParamsHandling","merge")("queryParams",G(14,Ut,(e.rank()||0)-1))("skipLocationChange",!0),f(8),pe("disabled-link",!e.showNextStudyButton()),v("routerLink",e.showNextStudyButton()?Se(16,Lt):void 0)("queryParamsHandling","merge")("queryParams",G(17,Ut,(e.rank()||0)+1))("skipLocationChange",!0)}}function Fa(n,t){if(n&1&&(o(0,"div",5)(1,"div",11)(2,"div",12),k(3,Ma,3,1,"div",13)(4,Ba,16,19,"div",14),l()()()),n&2){let e=b();f(3),T(e.isSingleResult()?3:4)}}function Da(n,t){if(n&1){let e=V();o(0,"span",22),c(1),l(),o(2,"span",23)(3,"a",21),j("click",function(){R(e);let r=b();return N(r.returnToSearch())}),c(4,"Return to Search"),l()()}if(n&2){let e=b();f(),Ie("Record ",e.rank()," of ",e.totalResultDisplayed(),"")}}function Ia(n,t){n&1&&(te(),o(0,"svg",26),d(1,"use",29),l())}function Ra(n,t){n&1&&(te(),o(0,"svg",28),d(1,"use",30),l())}function Na(n,t){if(n&1){let e=V();o(0,"div",24)(1,"div",18)(2,"a",25),k(3,Ia,2,0,":svg:svg",26),c(4," Previous Study "),l()(),o(5,"div",18)(6,"a",27),j("click",function(){R(e);let r=b();return N(r.returnToSearch())}),c(7,"Return to Search"),l()(),o(8,"div")(9,"a",25),c(10," Next Study "),k(11,Ra,2,0,":svg:svg",28),l()()()}if(n&2){let e=b();f(2),pe("disabled-link",!e.showPreviousStudyButton()),v("routerLink",e.showPreviousStudyButton()?Se(14,Lt):void 0)("queryParamsHandling","merge")("queryParams",G(15,Ut,(e.rank()||0)-1))("skipLocationChange",!0),f(),T(e.showPreviousStudyButton()?3:-1),f(6),pe("disabled-link",!e.showNextStudyButton()),v("routerLink",e.showNextStudyButton()?Se(17,Lt):void 0)("queryParamsHandling","merge")("queryParams",G(18,Ut,(e.rank()||0)+1))("skipLocationChange",!0),f(2),T(e.showNextStudyButton()?11:-1)}}function La(n,t){if(n&1&&(o(0,"div",31),c(1),l()),n&2){let e=b();f(),Ie(" Record ",e.rank()," of ",e.totalResultDisplayed()," ")}}var gr=(()=>{class n{constructor(){this.studynavigateService=y(ui),this.totalResults=this.studynavigateService.resultCount,this.numberFormatter=Intl.NumberFormat(),this.isDesktop=y(qe).isDesktop,this.rank=this.studynavigateService.rank,this.showPreviousStudyButton=Y(()=>this.rank()>1),this.showNextStudyButton=Y(()=>this.rank()<this.totalResults()),this.isSingleResult=Y(()=>this.totalResults()==1),this.totalResultDisplayed=Y(()=>this.numberFormatter.format(this.totalResults()))}returnToSearch(){this.studynavigateService.goToSearch()}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-results-navigation"]],decls:9,vars:1,consts:[["singleResult",""],["buttons",""],["resultWithQuery",""],[1,"header-banner","font-body-2xs","desktop:font-body-sm"],[1,"grid-container","desktop:grid-container-widescreen"],[1,"grid-container"],[1,"grid-row","padding-y-05"],[1,"mobile-lg:grid-col-8","text-left","banner-content"],[1,"action-buttons"],[1,"display-flex","margin-top-1","margin-bottom-05"],[4,"ngTemplateOutlet"],[1,"grid-row"],[1,"grid-col-12"],[1,"banner-content"],[1,"banner-content","mobile-actions"],[1,"display-flex","flex-align-center"],[1,"margin-y-1"],[1,"grid-row","margin-bottom-1","no-print-view"],[1,"actions-right-border"],[1,"display-flex","flex-justify",3,"routerLink","queryParamsHandling","queryParams","skipLocationChange"],[1,"display-flex","flex-justify-center","flex-align-self-strech"],["href","javascript:void(0)",3,"click"],[1,"constrained-width","right-border"],[1,"no-print-view"],[1,"action-buttons","no-print-view","actions-container"],[1,"prev-next",3,"routerLink","queryParamsHandling","queryParams","skipLocationChange"],["aria-hidden","true","focusable","false","role","img",1,"usa-icon","margin-right-1"],["href","javascript:void(0)",1,"prev-next",3,"click"],["aria-hidden","true","focusable","false","role","img",1,"usa-icon","margin-left-1"],[0,"xlink","href","/assets/uswds/img/sprite.svg#arrow_back"],[0,"xlink","href","/assets/uswds/img/sprite.svg#arrow_forward"],[1,"margin-top-2px","constrained-width","right-border"]],template:function(i,r){i&1&&(o(0,"div",3),k(1,Ta,5,1,"div",4)(2,Fa,5,1,"div",5),l(),k(3,Da,5,2,"ng-template",null,0,le)(5,Na,12,20,"ng-template",null,1,le)(7,La,2,2,"ng-template",null,2,le)),i&2&&(f(),T(r.isDesktop()?1:2))},dependencies:[Ne,be],styles:[".header-banner[_ngcontent-%COMP%]{background:var(--ctgl-color-primary-darker)}.header-banner[_ngcontent-%COMP%]   .constrained-width[_ngcontent-%COMP%]{max-width:400px;font-weight:700}.header-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]{color:#fff}.header-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--ctgl-color-base-lightest)}.header-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--ctgl-color-base-lightest);text-decoration:underline}.header-banner[_ngcontent-%COMP%]   .banner-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#fff}.header-banner[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{color:var(--ctgl-color-base-lighter);font-weight:400}.header-banner[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .prev-next[_ngcontent-%COMP%]{display:inline-flex}.header-banner[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .prev-next[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%]{height:1.4rem;width:1.4rem}.header-banner[_ngcontent-%COMP%]   .mobile-actions[_ngcontent-%COMP%]{font-size:14px}.header-banner[_ngcontent-%COMP%]   .mobile-actions[_ngcontent-%COMP%]   .grid-row[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.header-banner[_ngcontent-%COMP%]   .top-border[_ngcontent-%COMP%]{border-top:2px solid white}@media (min-width: 64rem){.header-banner[_ngcontent-%COMP%]   .right-border[_ngcontent-%COMP%]{border-right:2px solid white;padding-right:16px;margin-right:16px}}.header-banner[_ngcontent-%COMP%]   .actions-right-border[_ngcontent-%COMP%]{border-right:1px solid var(--ctgl-color-base);padding-right:16px;margin-right:16px}.actions-container[_ngcontent-%COMP%]{display:inline-flex;justify-content:center}@media print{.no-print-view[_ngcontent-%COMP%]{display:none}.header-banner[_ngcontent-%COMP%]{margin-top:1rem}.header-banner[_ngcontent-%COMP%], .constrained-width[_ngcontent-%COMP%]{background:#fff;color:var(--ctgl-color-base-darkest)!important}}.disabled-link[_ngcontent-%COMP%]{text-decoration:none!important}"],changeDetection:0})}}return n})();var Ua=Object.getOwnPropertyNames,B=(n,t)=>function(){return t||(0,n[Ua(n)[0]])((t={exports:{}}).exports,t),t.exports},Ha=B({"node_modules/keyboardevent-key-polyfill/index.js"(n,t){(function(){var e={polyfill:a,keys:{3:"Cancel",6:"Help",8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",28:"Convert",29:"NonConvert",30:"Accept",31:"ModeChange",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",41:"Select",42:"Print",43:"Execute",44:"PrintScreen",45:"Insert",46:"Delete",48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],91:"OS",93:"ContextMenu",144:"NumLock",145:"ScrollLock",181:"VolumeMute",182:"VolumeDown",183:"VolumeUp",186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"'],224:"Meta",225:"AltGraph",246:"Attn",247:"CrSel",248:"ExSel",249:"EraseEof",250:"Play",251:"ZoomOut"}},i;for(i=1;i<25;i++)e.keys[111+i]="F"+i;var r="";for(i=65;i<91;i++)r=String.fromCharCode(i),e.keys[i]=[r.toLowerCase(),r.toUpperCase()];function a(){if(!("KeyboardEvent"in window)||"key"in KeyboardEvent.prototype)return!1;var s={get:function(u){var p=e.keys[this.which||this.keyCode];return Array.isArray(p)&&(p=p[+this.shiftKey]),p}};return Object.defineProperty(KeyboardEvent.prototype,"key",s),s}typeof define=="function"&&define.amd?define("keyboardevent-key-polyfill",e):typeof n<"u"&&typeof t<"u"?t.exports=e:window&&(window.keyboardeventKeyPolyfill=e)})()}}),xr=B({"node_modules/receptor/keymap/index.js"(n,t){Ha();var e={Alt:"altKey",Control:"ctrlKey",Ctrl:"ctrlKey",Shift:"shiftKey"},i="+",r=function(a,s){var u=a.key;if(s)for(var p in e)a[e[p]]===!0&&(u=[p,u].join(i));return u};t.exports=function(s){let u=Object.keys(s).some(function(p){return p.indexOf(i)>-1});return function(p){var g=r(p,u);return[g,g.toLowerCase()].reduce(function(h,x){return x in s&&(h=s[g].call(this,p)),h},void 0)}},t.exports.MODIFIERS=e}}),pn=B({"node_modules/object-assign/index.js"(n,t){"use strict";var e=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;function a(u){if(u==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(u)}function s(){try{if(!Object.assign)return!1;var u=new String("abc");if(u[5]="de",Object.getOwnPropertyNames(u)[0]==="5")return!1;for(var p={},g=0;g<10;g++)p["_"+String.fromCharCode(g)]=g;var h=Object.getOwnPropertyNames(p).map(function(_){return p[_]});if(h.join("")!=="0123456789")return!1;var x={};return"abcdefghijklmnopqrst".split("").forEach(function(_){x[_]=_}),Object.keys(Object.assign({},x)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}t.exports=s()?Object.assign:function(u,p){for(var g,h=a(u),x,_=1;_<arguments.length;_++){g=Object(arguments[_]);for(var C in g)i.call(g,C)&&(h[C]=g[C]);if(e){x=e(g);for(var w=0;w<x.length;w++)r.call(g,x[w])&&(h[x[w]]=g[x[w]])}}return h}}}),qa=B({"node_modules/element-closest/element-closest.js"(){(function(n){typeof n.matches!="function"&&(n.matches=n.msMatchesSelector||n.mozMatchesSelector||n.webkitMatchesSelector||function(e){for(var i=this,r=(i.document||i.ownerDocument).querySelectorAll(e),a=0;r[a]&&r[a]!==i;)++a;return!!r[a]}),typeof n.closest!="function"&&(n.closest=function(e){for(var i=this;i&&i.nodeType===1;){if(i.matches(e))return i;i=i.parentNode}return null})})(window.Element.prototype)}}),mn=B({"node_modules/receptor/delegate/index.js"(n,t){qa(),t.exports=function(i,r){return function(s){var u=s.target.closest(i);if(u)return r.call(u,s)}}}}),Va=B({"node_modules/receptor/compose/index.js"(n,t){t.exports=function(i){return function(r){return i.some(function(a){return a.call(this,r)===!1},this)}}}}),vr=B({"node_modules/receptor/delegateAll/index.js"(n,t){var e=mn(),i=Va(),r="*";t.exports=function(s){let u=Object.keys(s);if(u.length===1&&u[0]===r)return s[r];let p=u.reduce(function(g,h){return g.push(e(h,s[h])),g},[]);return i(p)}}}),_r=B({"node_modules/receptor/behavior/index.js"(n,t){var e=pn(),i=mn(),r=vr(),a=/^(.+):delegate\((.+)\)$/,s=" ",u=function(g,h){var x=g.match(a),_;x&&(g=x[1],_=x[2]);var C;typeof h=="object"&&(C={capture:p(h,"capture"),passive:p(h,"passive")});var w={selector:_,delegate:typeof h=="object"?r(h):_?i(_,h):h,options:C};return g.indexOf(s)>-1?g.split(s).map(function(P){return e({type:P},w)}):(w.type=g,[w])},p=function(g,h){var x=g[h];return delete g[h],x};t.exports=function(h,x){let _=Object.keys(h).reduce(function(C,w){var P=u(w,h[w]);return C.concat(P)},[]);return e({add:function(w){_.forEach(function(P){w.addEventListener(P.type,P.delegate,P.options)})},remove:function(w){_.forEach(function(P){w.removeEventListener(P.type,P.delegate,P.options)})}},x)}}}),fn=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/behavior.js"(n,t){"use strict";var e=pn(),i=_r(),r=(...a)=>function(u=document.body){a.forEach(p=>{typeof this[p]=="function"&&this[p].call(this,u)})};t.exports=(a,s)=>i(a,e({on:r("init","add"),off:r("teardown","remove")},s))}}),hn=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/select.js"(n,t){"use strict";var e=i=>i&&typeof i=="object"&&i.nodeType===1;t.exports=(i,r)=>{if(typeof i!="string")return[];(!r||!e(r))&&(r=window.document);let a=r.querySelectorAll(i);return Array.prototype.slice.call(a)}}}),br=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/toggle.js"(n,t){"use strict";var e="aria-expanded",i="aria-controls",r="hidden";t.exports=(a,s)=>{let u=s;typeof u!="boolean"&&(u=a.getAttribute(e)==="false"),a.setAttribute(e,u);let p=a.getAttribute(i),g=document.getElementById(p);if(!g)throw new Error(`No toggle target found with id: "${p}"`);return u?g.removeAttribute(r):g.setAttribute(r,""),u}}}),ja=B({"node_modules/receptor/ignore/index.js"(n,t){t.exports=function(i,r){return function(s){if(i!==s.target&&!i.contains(s.target))return r.call(this,s)}}}}),za=B({"node_modules/receptor/index.js"(n,t){t.exports={behavior:_r(),delegate:mn(),delegateAll:vr(),ignore:ja(),keymap:xr()}}}),Ga=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/active-element.js"(n,t){"use strict";t.exports=(e=document)=>e.activeElement}}),$a=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/focus-trap.js"(n,t){"use strict";var e=pn(),{keymap:i}=za(),r=fn(),a=hn(),s=Ga(),u='a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex="0"], [contenteditable]',p=g=>{let h=a(u,g),x=h[0],_=h[h.length-1];function C(P){s()===_&&(P.preventDefault(),x.focus())}function w(P){s()===x?(P.preventDefault(),_.focus()):h.includes(s())||(P.preventDefault(),x.focus())}return{firstTabStop:x,lastTabStop:_,tabAhead:C,tabBack:w}};t.exports=(g,h={})=>{let x=p(g),_=h,{Esc:C,Escape:w}=_;w&&!C&&(_.Esc=w);let P=i(e({Tab:x.tabAhead,"Shift+Tab":x.tabBack},h));return r({keydown:P},{init(){x.firstTabStop&&x.firstTabStop.focus()},update(W){W?this.on():this.off()}})}}}),Wa=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/is-in-viewport.js"(n,t){"use strict";function e(i,r=window,a=document.documentElement){let s=i.getBoundingClientRect();return s.top>=0&&s.left>=0&&s.bottom<=(r.innerHeight||a.clientHeight)&&s.right<=(r.innerWidth||a.clientWidth)}t.exports=e}}),yr=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/events.js"(n,t){"use strict";t.exports={CLICK:"click"}}}),wr=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/config.js"(n,t){"use strict";t.exports={prefix:"usa"}}}),Ka=B({"src/app/shared/uswds-integration/uswds3/usa-accordion/src/index.js"(n,t){"use strict";var e=hn(),i=fn(),r=br(),a=Wa(),{CLICK:s}=yr(),{prefix:u}=wr(),p=`.${u}-accordion, .${u}-accordion--bordered`,g=`.${u}-banner__button`,h=`.${u}-accordion__button[aria-controls]:not(${g})`,x="aria-expanded",_="data-allow-multiple",C=E=>e(h,E).filter(U=>U.closest(p)===E),w=(E,L)=>{let U=E.closest(p),he=L;if(!U)throw new Error(`${h} is missing outer ${p}`);he=r(E,L);let Oe=U.hasAttribute(_);he&&!Oe&&C(U).forEach(Be=>{Be!==E&&r(Be,!1)})},P=E=>w(E,!0),ie=E=>w(E,!1),W=i({[s]:{[h](){w(this),this.getAttribute(x)==="true"&&(a(this)||this.scrollIntoView())}}},{init(E){e(h,E).forEach(L=>{let U=L.getAttribute(x)==="true";w(L,U)})},ACCORDION:p,BUTTON:h,show:P,hide:ie,toggle:w,getButtons:C});t.exports=W}}),Ya=B({"src/app/shared/uswds-integration/uswds3/uswds-core/src/js/utils/scrollbar-width.js"(n,t){"use strict";t.exports=function(){let i=document.createElement("div");i.style.visibility="hidden",i.style.overflow="scroll",i.style.msOverflowStyle="scrollbar",document.body.appendChild(i);let r=document.createElement("div");i.appendChild(r);let a=`${i.offsetWidth-r.offsetWidth}px`;return i.parentNode.removeChild(i),a}}}),Qa=B({"src/app/shared/uswds-integration/uswds3/usa-header/src/index.js"(n,t){var e=xr(),i=fn(),r=hn(),a=br(),s=$a(),u=Ka(),p=Ya(),{CLICK:g}=yr(),{prefix:h}=wr(),x="body",_=`.${h}-header`,C=`.${h}-nav`,w=`.${h}-nav-container`,P=`.${h}-nav__primary`,ie=`.${h}-nav__primary-item`,W=`button.${h}-nav__link`,E=`${C} a`,L="data-nav-hidden",U=`.${h}-menu-btn`,he=`.${h}-nav__close`,Oe=`.${h}-overlay`,Be=`${he}, .${h}-overlay`,rt=[C,Oe].join(", "),Ht=`body *:not(${_}, ${w}, ${C}, ${C} *):not([aria-hidden])`,qt=`[${L}]`,ot="usa-js-mobile-nav--active",at="is-visible",M,H,Q,Ge=()=>document.body.classList.contains(ot),lt=navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome"),Vt=p(),$e=window.getComputedStyle(document.body).getPropertyValue("padding-right"),st=`${parseInt($e.replace(/px/,""),10)+parseInt(Vt.replace(/px/,""),10)}px`,jt=()=>{let A=document.querySelector(`${_}`).parentNode;Q=document.querySelectorAll(Ht),Q.forEach(q=>{q!==A&&(q.setAttribute("aria-hidden",!0),q.setAttribute(L,""))})},zt=()=>{Q=document.querySelectorAll(qt),Q&&Q.forEach(A=>{A.removeAttribute("aria-hidden"),A.removeAttribute(L)})},ct=A=>{A?jt():zt()},Gt=()=>{lt&&document.body.classList.add("is-safari")},dt=A=>{let q=`-${window.scrollY}px`;lt&&A.style.setProperty("--scrolltop",q)},I=A=>{let{body:q}=document,re=typeof A=="boolean"?A:!Ge();dt(q),q.classList.toggle(ot,re),r(rt).forEach(jr=>jr.classList.toggle(at,re)),M.focusTrap.update(re);let vn=q.querySelector(he),$t=document.querySelector(U);return q.style.paddingRight=q.style.paddingRight===st?$e:st,ct(re),re&&vn?vn.focus():!re&&$t&&getComputedStyle($t).display!=="none"&&$t.focus(),re},X=()=>{let A=document.body.querySelector(he);Ge()&&A&&A.getBoundingClientRect().width===0&&M.toggleNav.call(A,!1)},ut=()=>M.toggleNav.call(M,!1),Fe=()=>{H&&(a(H,!1),H=null)},qr=A=>{let q=A.target.closest(ie);if(!A.target.matches(W)){let re=q.querySelector(W);re&&re.focus()}},Vr=A=>{Fe(),qr(A)};M=i({[g]:{[W](){return H!==this&&Fe(),H||(H=this,a(H,!0)),!1},[x]:Fe,[U]:I,[Be]:I,[E](){let A=this.closest(u.ACCORDION);A&&u.getButtons(A).forEach(q=>u.hide(q)),Ge()&&M.toggleNav.call(M,!1)}},keydown:{[P]:e({Escape:Vr})},focusout:{[P](A){A.target.closest(P).contains(A.relatedTarget)||Fe()}}},{init(A){A&&(M.focusTrap=s(A,{Escape:ut})),Gt(),X(),window.addEventListener("resize",X,!1)},teardown(){window.removeEventListener("resize",X,!1),H=!1},focusTrap:null,toggleNav:I}),t.exports=M}}),gn=Qa();var Cr=(()=>{class n{constructor(){this.el=y(Tn)}ngAfterViewInit(){gn.on()}ngOnDestroy(){gn.off(this.el.nativeElement)}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275dir=Mn({type:n,selectors:[["",8,"usa-nav"]]})}}return n})();var Xa=n=>({"adjust-mobile-menu":n});function Ja(n,t){if(n&1&&(o(0,"button",36)(1,"span",18),c(2),l(),o(3,"span"),te(),o(4,"svg",38),d(5,"use",39),l()()()),n&2){let e=b();f(2),Ie("",e.savedStudiesLabel," (",e.savedCount()||0,")")}}function Za(n,t){if(n&1&&(o(0,"a",37)(1,"span",18),c(2),l(),o(3,"span"),te(),o(4,"svg",40),d(5,"use",39),l()()()),n&2){let e=b();f(2),Ie("",e.savedStudiesLabel," (",e.savedCount()||0," )")}}var kr=(()=>{class n{constructor(){this.isDesktop=y(qe).isDesktop,this.savedCount=y(pi).savedCount,this.currentUrlSvc=y(ze),this.savedStudiesLabel="My Saved Studies",this.showAltText=Y(()=>this.currentUrlSvc.isHomePage()?"ClinicalTrials.gov":"ClinicalTrials.gov return to the CTG homepage")}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-ctgl-header"]],decls:155,vars:39,consts:[[1,"usa-overlay"],[1,"usa-header"],[1,"desktop:grid-container-widescreen"],[1,"usa-nav-container","maxw-none"],[1,"usa-navbar","logo-menu","max-width"],["id","basic-logo",1,"desktop:margin-top-105","mobile-lg:margin-y-1","display-flex"],["href","/",1,"usa-logo","block-inline",3,"title"],["src","assets/images/ctg-header-logo.svg",1,"ctg-logo-image",3,"alt"],["id","local-menu-action-print",1,"usa-logo","block-inline"],["src","../../../assets/images/nih-nlm-ncbi-logo.png"],["title","Menu button","aria-label","Menu button","id","local-menu-action",1,"usa-menu-btn"],[1,"flex-justify-start","reduce-indentation"],["aria-label","Primary navigation",1,"max-width","usa-nav","flex-align-self-stretch",2,"height","100%"],["title","Menu close button","aria-label","Menu close button",1,"usa-nav__close","nav-close-button"],["src","/assets/images/close.png","role","img","alt","close"],[1,"usa-nav__primary","usa-accordion","adjust-menu-margins",3,"ngClass"],[1,"usa-nav__primary-item"],["aria-expanded","false","title","Find Studies","aria-label","Find Studies","aria-controls","basic-nav-section-five",1,"usa-accordion__button","usa-nav__link"],[1,"font-ui-md"],["id","basic-nav-section-five",1,"usa-nav__submenu","shadow-1"],[1,"usa-nav__submenu-item"],[3,"routerLink"],["aria-expanded","false","title","Study Basics","aria-label","Study Basics","aria-controls","basic-nav-section-two",1,"usa-accordion__button","usa-nav__link"],["id","basic-nav-section-two",1,"usa-nav__submenu","shadow-1"],["routerLink","/study-basics/learn-about-studies"],["routerLink","/study-basics/glossary"],["routerLink","/study-basics/patient-resources"],["aria-expanded","false","title","Submit Studies","aria-label","Submit Studies","aria-controls","basic-nav-section-four",1,"usa-accordion__button","usa-nav__link"],["id","basic-nav-section-four",1,"usa-nav__submenu","shadow-1"],["aria-expanded","false","title","Data and API","aria-label","Data About Studies","aria-controls","basic-nav-section-three",1,"usa-accordion__button","usa-nav__link"],["id","basic-nav-section-three",1,"usa-nav__submenu","shadow-1"],["aria-expanded","false","title","Policy","aria-label","Policy","aria-controls","basic-nav-section-six",1,"usa-accordion__button","usa-nav__link"],["id","basic-nav-section-six",1,"usa-nav__submenu","shadow-1"],["aria-expanded","false","title","About This Site","aria-label","About This Site","aria-controls","basic-nav-section-one",1,"usa-accordion__button","usa-nav__link"],["id","basic-nav-section-one",1,"usa-nav__submenu","shadow-1"],[1,"",3,"routerLink"],["aria-expanded","false","title","Go to My Saved Studies page","aria-label","Go to My Saved Studies page","routerLink","/search/my-saved-studies",1,"button-display"],["title","Go to My Saved Studies page","aria-label","Go to My Saved Studies page","routerLink","/search/my-saved-studies",1,"usa-link"],["aria-hidden","true","focusable","false","role","img",1,"usa-icon","desktop-icon-style"],[0,"xlink","href","/assets/uswds/img/sprite.svg#arrow_forward"],["aria-hidden","true","focusable","false","role","img",1,"usa-icon","mobile-my-list"]],template:function(i,r){i&1&&(d(0,"div",0),o(1,"header",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5)(6,"a",6),d(7,"img",7),l(),o(8,"a",8),d(9,"img",9),l()(),o(10,"button",10),c(11," Menu "),l()(),o(12,"div",11)(13,"nav",12)(14,"button",13),d(15,"img",14),l(),o(16,"ul",15)(17,"li",16)(18,"button",17)(19,"span",18),c(20,"Find Studies"),l()(),o(21,"ul",19)(22,"li",20)(23,"a",21),c(24,"Expert Search"),l()(),o(25,"li",20)(26,"a",21),c(27,"How to Search"),l()(),o(28,"li",20)(29,"a",21),c(30,"How to Use Search Results"),l()(),o(31,"li",20)(32,"a",21),c(33,"How to Search for Studies with Results"),l()(),o(34,"li",20)(35,"a",21),c(36,"Constructing Complex Search Queries"),l()(),o(37,"li",20)(38,"a",21),c(39,"RSS Feeds"),l()()()(),o(40,"li",16)(41,"button",22)(42,"span",18),c(43,"Study Basics"),l()(),o(44,"ul",23)(45,"li",20)(46,"a",24),c(47,"Learn About Studies"),l()(),o(48,"li",20)(49,"a",21),c(50,"How to Read a Study Record"),l()(),o(51,"li",20)(52,"a",21),c(53,"How to Read Study Results"),l()(),o(54,"li",20)(55,"a",25),c(56,"Glossary"),l()(),o(57,"li",20)(58,"a",26),c(59,"Patient Resources"),l()()()(),o(60,"li",16)(61,"button",27)(62,"span",18),c(63,"Submit Studies"),l()(),o(64,"ul",28)(65,"li",20)(66,"a",21),c(67,"PRS Accounts"),l()(),o(68,"li",20)(69,"a",21),c(70,"PRS Help Resources"),l()()()(),o(71,"li",16)(72,"button",29)(73,"span",18),c(74,"Data and API"),l()(),o(75,"ul",30)(76,"li",20)(77,"a",21),c(78,"ClinicalTrials.gov API"),l()(),o(79,"li",20)(80,"a",21),c(81,"About the API"),l()(),o(82,"li",20)(83,"a",21),c(84,"How to Download Study Records"),l()(),o(85,"li",20)(86,"a",21),c(87,"Access Data in FHIR"),l()()()(),o(88,"li",16)(89,"button",31)(90,"span",18),c(91,"Policy"),l()(),o(92,"ul",32)(93,"li",20)(94,"a",21),c(95,"FAQs"),l()(),o(96,"li",20)(97,"a",21),c(98,"Clinical Trial Reporting Requirements"),l()(),o(99,"li",20)(100,"a",21),c(101,"FDAAA 801 and the Final Rule"),l()(),o(102,"li",20)(103,"a",21),c(104,"Protocol Registration Definitions"),l()(),o(105,"li",20)(106,"a",21),c(107,"Results Definitions"),l()(),o(108,"li",20)(109,"a",21),c(110,"Expanded Access Definitions"),l()()()(),o(111,"li",16)(112,"button",33)(113,"span",18),c(114,"About"),l()(),o(115,"ul",34)(116,"li",20)(117,"a",21),c(118,"News and Updates"),l()(),o(119,"li",20)(120,"a",35),c(121,"About ClinicalTrials.gov"),l()(),o(122,"li",20)(123,"a",21),c(124,"Trends and Charts"),l()(),o(125,"li",20)(126,"a",21),c(127,"Modernization"),l()(),o(128,"li",20)(129,"a",21),c(130,"Modernization Top Questions"),l()(),o(131,"li",20)(132,"a",35),c(133,"Selected Publications"),l()(),o(134,"li",20)(135,"a",21),c(136,"Release Notes"),l()(),o(137,"li",20)(138,"a",21),c(139,"Planned Features on ClinicalTrials.gov"),l()(),o(140,"li",20)(141,"a",21),c(142,"Disclaimer"),l()(),o(143,"li",20)(144,"a",21),c(145,"Terms and Conditions"),l()(),o(146,"li",20)(147,"a",21),c(148,"Linking to This Site"),l()(),o(149,"li",20)(150,"a",21),c(151,"Accessibility"),l()()()(),o(152,"li"),k(153,Ja,6,2,"button",36)(154,Za,6,2,"a",37),l()()()()()()()),i&2&&(f(6),v("title",r.showAltText()),f(),v("alt",r.showAltText()),f(9),v("ngClass",G(37,Xa,!r.isDesktop())),f(7),v("routerLink","/expert-search"),f(3),v("routerLink","/find-studies/how-to-search"),f(3),v("routerLink","/find-studies/how-to-use-search-results"),f(3),v("routerLink","/find-studies/how-to-search-for-studies-with-results"),f(3),v("routerLink","/find-studies/constructing-complex-search-queries"),f(3),v("routerLink","/find-studies/rss"),f(11),v("routerLink","/study-basics/how-to-read-study-record"),f(3),v("routerLink","/study-basics/how-to-read-study-results"),f(14),v("routerLink","/submit-studies/prs-accounts"),f(3),v("routerLink","/submit-studies/prs-help"),f(8),v("routerLink","/data-api/api"),f(3),v("routerLink","/data-api/about-api"),f(3),v("routerLink","/data-api/how-download-study-records"),f(3),v("routerLink","/data-api/fhir"),f(8),v("routerLink","/policy/faq"),f(3),v("routerLink","/policy/reporting-requirements"),f(3),v("routerLink","/policy/fdaaa-801-final-rule"),f(3),v("routerLink","/policy/protocol-definitions"),f(3),v("routerLink","/policy/results-definitions"),f(3),v("routerLink","/policy/expanded-access-definitions"),f(8),v("routerLink","/about-site/news-and-updates"),f(3),v("routerLink","/about-site/about-ctg"),f(3),v("routerLink","/about-site/trends-charts"),f(3),v("routerLink","/about-site/modernization"),f(3),v("routerLink","/about-site/modernization-top-questions"),f(3),v("routerLink","/about-site/selected-publications"),f(3),v("routerLink","/about-site/release-notes"),f(3),v("routerLink","/about-site/planned-features"),f(3),v("routerLink","/about-site/disclaimer"),f(3),v("routerLink","/about-site/terms-conditions"),f(3),v("routerLink","/about-site/linking-to"),f(3),v("routerLink","/about-site/accessibility"),f(2),xe("class",r.isDesktop()?"usa-nav__primary-item display-flex flex-align-center last-item":"usa-nav__primary-item last-item"),f(),T(r.isDesktop()?153:154))},dependencies:[Nn,be,Cr,Et],styles:[".ctg-logo-image[_ngcontent-%COMP%]{max-height:2.5rem}@media (min-width: 64rem){.ctg-logo-image[_ngcontent-%COMP%]{max-height:4.5rem;height:85%;width:85%}}.block-inline[_ngcontent-%COMP%]{margin-top:0}.reduce-indentation[_ngcontent-%COMP%]{margin-left:-1rem}.adjust-mobile-menu[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%}@media (min-width: 64rem){.adjust-menu-margins[_ngcontent-%COMP%]{margin-top:-16px;margin-bottom:4px}}.desktop-icon-style[_ngcontent-%COMP%]{height:1.25em;width:1.25em;vertical-align:bottom}@media (min-width: 64rem){.last-item[_ngcontent-%COMP%]{display:inline-flex;margin-left:auto;margin-right:-18px}}.mobile-my-list[_ngcontent-%COMP%]{float:right;margin-top:2px;margin-right:-14px;height:1.25em;width:1.25em}@media (min-width: 64rem){.max-width[_ngcontent-%COMP%]{width:100%!important}}.usa-nav-container[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.usa-menu-btn[_ngcontent-%COMP%]{height:100%;font-size:14px;font-weight:700;background-color:var(--ctgl-color-primary-darkest);color:#fff;width:65px;text-transform:capitalize}.usa-menu-btn[_ngcontent-%COMP%]:hover{background-color:var(--ctgl-color-primary-dark)}.usa-nav__primary[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:var(--ctgl-color-primary-lighter)}.usa-nav__primary[_ngcontent-%COMP%]   button[aria-expanded=true][_ngcontent-%COMP%]{background-color:var(--ctgl-color-primary-light);color:var(--ctgl-color-base-darker)}.usa-nav__submenu[_ngcontent-%COMP%]{background-color:#fff;color:var(--ctgl-color-base-darker);padding-left:0;font-size:16px;font-weight:400}.usa-nav__submenu[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--ctgl-color-base-darker);padding-left:.5rem;padding-top:.5rem;padding-bottom:.5rem;border-left:5px solid white}.usa-nav__submenu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .usa-nav__submenu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:focus{border-left:5px solid blue}.usa-nav__link[_ngcontent-%COMP%]:hover:after{background-color:transparent}.text-small[_ngcontent-%COMP%]{font-family:Public Sans;font-size:22px;font-style:normal;font-weight:300;line-height:35.64px;letter-spacing:0;text-align:left;padding-top:24px}.ctg-header-content[_ngcontent-%COMP%]{height:568px}.content-detail[_ngcontent-%COMP%]{margin-top:2rem;margin-left:2em}.feature-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding-top:5px;padding-bottom:5px;margin-right:10%;color:#fff;background-color:transparent;box-shadow:inset 0 0 0 1px #fff}.ctg-content[_ngcontent-%COMP%]{width:50%}.custom-button[_ngcontent-%COMP%]{padding:20px}.header-right[_ngcontent-%COMP%]{text-align:right;margin:15px}.ctg-placeholder[_ngcontent-%COMP%]{margin-right:3em}.placeholder-logo[_ngcontent-%COMP%], .ctg-placeholder-logo[_ngcontent-%COMP%]{width:485px}.logo-menu[_ngcontent-%COMP%]{justify-content:space-between}.local-menu[_ngcontent-%COMP%]{margin-left:auto;padding:0}.local-menu[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}@media screen and (min-width: 1024px){.usa-header[_ngcontent-%COMP%]{border-bottom:1px solid var(--ctgl-color-primary-darker)}.usa-navbar[_ngcontent-%COMP%]{height:64px}}.usa-nav[_ngcontent-%COMP%]   .nav-close-button[_ngcontent-%COMP%]{margin:-.75rem -.75rem 1rem auto}@media print{#local-menu-action[_ngcontent-%COMP%], .usa-nav[_ngcontent-%COMP%]{display:none}#local-menu-action-print[_ngcontent-%COMP%]{display:block;padding-right:1rem}#basic-logo[_ngcontent-%COMP%]{width:100%}.usa-header--basic[_ngcontent-%COMP%]   .usa-navbar[_ngcontent-%COMP%]{position:relative;width:100%}.usa-navbar[_ngcontent-%COMP%]{border-bottom:none!important}}.no-underline[_ngcontent-%COMP%]{text-decoration:none}.button-display[_ngcontent-%COMP%]{width:100%;cursor:pointer}.button-display[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{content:none;background:none}"],changeDetection:0})}}return n})();var Sr=(()=>{class n{ngAfterViewInit(){typeof alertsUrl>"u"?(console.log("Note: alertsUrl is undefined, hence the NCBI Global Alert system will not work for this page"),ncbi.sg.ping({jsevent:"warning",warning:"alertsUrl is undefined"})):jQuery.getScript(alertsUrl,()=>{galert([".custom-alerts-placeholder",".ncbi-alerts-placeholder","header.ncbi-header","body > *:nth-child(1)"])})}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-ncbi-global-alert-banner"]],decls:2,vars:0,consts:[["data-section","Alerts"],[1,"ncbi-alerts-placeholder"]],template:function(i,r){i&1&&(o(0,"section",0),d(1,"div",1),l())},styles:[".ncbi-alert__shutdown-outer{padding-left:max(28px,(100vw - 85rem) / 2)!important;padding-right:max(28px,(100vw - 85rem) / 2)!important;background-position:max(28px,(100vw - 85rem) / 2) 20px!important}  .close{right:max(28px,(100vw - 85rem) / 2)!important}"],changeDetection:0})}}return n})();var Er=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-official-gov-banner"]],decls:49,vars:0,consts:[["aria-label","Official website of the United States government",1,"usa-banner"],[1,"usa-accordion"],[1,"usa-banner__header"],[1,"usa-banner__inner"],[1,"grid-col-auto"],["aria-hidden","true","src","/assets/uswds/img/us_flag_small.png","alt","",1,"usa-banner__header-flag"],["aria-hidden","true",1,"grid-col-fill","tablet:grid-col-auto"],[1,"usa-banner__header-text"],[1,"usa-banner__header-action"],["type","button","aria-expanded","false","aria-controls","gov-banner-default",1,"usa-accordion__button","usa-banner__button"],[1,"usa-banner__button-text"],["id","gov-banner-default",1,"usa-banner__content","usa-accordion__content"],[1,"grid-row","grid-gap-lg"],[1,"usa-banner__guidance","tablet:grid-col-6"],["src","/assets/uswds/img/icon-dot-gov.svg","role","img","alt","","aria-hidden","true",1,"usa-banner__icon","usa-media-block__img"],[1,"usa-media-block__body"],["src","/assets/uswds/img/icon-https.svg","role","img","alt","","aria-hidden","true",1,"usa-banner__icon","usa-media-block__img"],[1,"icon-lock"],["xmlns","http://www.w3.org/2000/svg","width","52","height","64","viewBox","0 0 52 64","role","img","aria-labelledby","banner-lock-description-default","focusable","false",1,"usa-banner__lock-image"],["id","banner-lock-title-default"],["id","banner-lock-description-default"],["fill","#000000","fill-rule","evenodd","d","M26 0c10.493 0 19 8.507 19 19v9h3a4 4 0 0 1 4 4v28a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V32a4 4 0 0 1 4-4h3v-9C7 8.507 15.507 0 26 0zm0 8c-5.979 0-10.843 4.77-10.996 10.712L15 19v9h22v-9c0-6.075-4.925-11-11-11z"]],template:function(i,r){i&1&&(o(0,"section",0)(1,"div",1)(2,"header",2)(3,"div",3)(4,"div",4),d(5,"img",5),l(),o(6,"div",6)(7,"p",7),c(8," An official website of the United States government "),l(),o(9,"p",8),c(10,"Here\u2019s how you know"),l()(),o(11,"button",9)(12,"span",10),c(13,"Here\u2019s how you know"),l()()()(),o(14,"div",11)(15,"div",12)(16,"div",13),d(17,"img",14),o(18,"div",15)(19,"p")(20,"strong"),c(21,"Official websites use .gov"),l(),d(22,"br"),c(23,"A "),o(24,"strong"),c(25,".gov"),l(),c(26," website belongs to an official government organization in the United States. "),l()()(),o(27,"div",13),d(28,"img",16),o(29,"div",15)(30,"p")(31,"strong"),c(32,"Secure .gov websites use HTTPS"),l(),d(33,"br"),c(34,"A "),o(35,"strong"),c(36,"lock"),l(),c(37," ( "),o(38,"span",17),te(),o(39,"svg",18)(40,"title",19),c(41,"Lock"),l(),o(42,"desc",20),c(43,"Locked padlock icon"),l(),d(44,"path",21),l()(),c(45,") or "),Sn(),o(46,"strong"),c(47,"https://"),l(),c(48," means you\u2019ve safely connected to the .gov website. Share sensitive information only on official, secure websites. "),l()()()()()()())},dependencies:[Et],styles:["@media all and (min-width: 64em){.usa-accordion[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto;max-width:87.5rem}}@media print{[_nghost-%COMP%]{display:none}}"],changeDetection:0})}}return n})();function el(n,t){n&1&&d(0,"ctg-results-navigation")}var Pr=(()=>{class n{constructor(){this.urlSvc=y(ze),this.showStudyBanner=Y(()=>this.urlSvc.currentPath()?.startsWith("/study")&&this.hasRankQueryParam()),this.hasRankQueryParam=Y(()=>this.urlSvc.queryParams().get("rank")!=null)}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-header"]],decls:8,vars:1,consts:[["href","#main-content",1,"usa-skipnav"]],template:function(i,r){i&1&&(d(0,"ctg-official-gov-banner"),o(1,"a",0),c(2,"Skip to main page content"),l(),d(3,"ctg-ncbi-global-alert-banner")(4,"ctg-ncbi-header")(5,"ctg-ctgl-header"),k(6,el,1,0,"ctg-results-navigation"),d(7,"ctg-breadcrumbs")),i&2&&(f(6),T(r.showStudyBanner()?6:-1))},dependencies:[Er,Sr,mi,kr,gr,hr],encapsulation:2,changeDetection:0})}}return n})();var xn=n=>({group:n});function tl(n,t){n&1&&(te(),o(0,"svg",17),d(1,"use",20),l())}function nl(n,t){n&1&&(te(),o(0,"svg",17),d(1,"use",21),l())}function il(n,t){if(n&1&&(o(0,"a",22),c(1),l()),n&2){let e=b().$implicit,i=b(2);ke("href",i.renderUrl(e.url),Ce),f(),z(e.text)}}function rl(n,t){if(n&1&&(o(0,"a",23),c(1),l()),n&2){let e=b().$implicit,i=b(2);Ye("aria-label","",e.text," (opens in a new tab)"),ke("href",i.renderUrl(e.url),Ce),Qe("title","",e.text," (opens in a new tab)"),f(),z(e.text)}}function ol(n,t){if(n&1&&(o(0,"div",19),k(1,il,2,2,"a",22)(2,rl,2,6,"a",23),l()),n&2){let e=t.$implicit,i=b(2);f(),T(!e.internal&&i.renderUrl(e.url).includes("clinicaltrials.gov")?1:2)}}function al(n,t){if(n&1){let e=V();o(0,"section",15)(1,"button",16),j("click",function(){let r=R(e).$index,a=b();return N(a.onGroupClick(r))}),k(2,tl,2,0,":svg:svg",17)(3,nl,2,0,":svg:svg",17),o(4,"span"),c(5),l()(),o(6,"div",18),oe(7,ol,3,1,"div",19,ve),l()()}if(n&2){let e=t.$implicit,i=t.$index,r=b();pe("group__expanded",r.expandedGroupIndex()===i),f(2),T(r.expandedGroupIndex()===i?2:-1),f(),T(r.expandedGroupIndex()!==i?3:-1),f(2),z(e.title),f(2),ae(e.links)}}function ll(n,t){n&1&&J(0)}function sl(n,t){n&1&&J(0)}function cl(n,t){n&1&&J(0)}function dl(n,t){if(n&1&&(o(0,"a",29),c(1),l()),n&2){let e=b().$implicit;v("routerLink",e.url),f(),z(e.text)}}function ul(n,t){if(n&1&&(o(0,"a",22),c(1),l()),n&2){let e=b().$implicit,i=b(2);ke("href",i.renderUrl(e.url),Ce),xe("aria-label",e.text),f(),z(e.text)}}function pl(n,t){if(n&1&&(o(0,"a",23),c(1),l()),n&2){let e=b().$implicit,i=b(2);Ye("aria-label","",e.text," (opens in a new tab)"),ke("href",i.renderUrl(e.url),Ce),Qe("title","",e.text," (opens in a new tab)"),f(),z(e.text)}}function ml(n,t){if(n&1&&(o(0,"li",28),k(1,dl,2,2,"a",29)(2,ul,2,3,"a",22)(3,pl,2,6,"a",23),l()),n&2){let e=t.$implicit,i=b(2);f(),T(e.internal?1:i.renderUrl(e.url).includes("clinicaltrials.gov")?2:3)}}function fl(n,t){if(n&1&&(o(0,"section",24)(1,"div",25),c(2),l(),d(3,"div",26),o(4,"ul",27),oe(5,ml,4,1,"li",28,ve),l()()),n&2){let e=t.group;f(2),z(e.title),f(3),ae(e.links)}}var Tr=(()=>{class n{constructor(){this.fromLink=y(ze).fromLink,this.linkGroups=[{title:"About",links:[{text:"About ClinicalTrials.gov",url:"/about-site/about-ctg",internal:!0},{text:"Release Notes",url:"/about-site/release-notes",internal:!0},{text:"Site Map",url:"/site-map",internal:!0}]},{title:"Help",links:[{text:"Give us feedback",url:{toString:()=>di+this.fromLink()},internal:!1},{text:"Glossary",url:"/study-basics/glossary",internal:!0},{text:"Customer Support",url:"https://support.nlm.nih.gov/knowledgebase/category/?id=CAT-01242&category=clinicaltrials.gov&hd_url=https%3A%2F%2Fclinicaltrials.gov",internal:!1}]},{title:"Legal",links:[{text:"Disclaimer",url:"/about-site/disclaimer",internal:!0},{text:"Terms and Conditions",url:"/about-site/terms-conditions",internal:!0}]}],this.expandedGroupIndex=De(-1)}onGroupClick(e){this.expandedGroupIndex.update(i=>i===e?-1:e)}renderUrl(e){return e?typeof e=="string"?e:e.toString():"javascript:void(0)"}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-ctg-footer"]],decls:24,vars:12,consts:[["block",""],[1,"usa-footer__primary-section","ctg-footer"],[1,"grid-container-widescreen"],[1,"grid-row","grid-gap"],[1,"mobile-lg:grid-col","footer-accordion"],[1,"group",3,"group__expanded"],[1,"grid-row","desktop:display-flex","desktop:flex-justify","width-full"],[1,"footer-list"],["aria-label","Footer navigation",1,"mobile-lg:grid-col-11"],[1,"grid-row"],[1,"mobile-lg:grid-col-4"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"tablet:grid-col","mobile-lg:grid-col","footer-contacts"],["src","assets/images/ctg-footer-logo.svg","alt","ClinicalTrials.gov logo",1,"ctg-logo"],[1,"address"],[1,"group"],[1,"group-title",3,"click"],["aria-hidden","true","focusable","false","role","img",1,"usa-icon"],[1,"group-content"],[1,"group-content-row"],[0,"xlink","href","/assets/uswds/img/sprite.svg#remove"],[0,"xlink","href","/assets/uswds/img/sprite.svg#add"],[1,"ctg-footer-link-text",3,"href"],["target","_blank","rel","noopener noreferrer",1,"ctg-footer-link-text",3,"href","title"],[1,"column","usa-footer__primary-content"],[1,"column-title"],[1,"separator"],[1,"usa-list","usa-list--unstyled"],[1,"usa-footer__secondary-link"],[1,"ctg-footer-link-text",3,"routerLink"]],template:function(i,r){if(i&1&&(o(0,"div",1)(1,"div",2)(2,"div",3)(3,"div",4),oe(4,al,9,5,"section",5,ve),l(),o(6,"div",6)(7,"div",7)(8,"nav",8)(9,"div",9)(10,"div",10),k(11,ll,1,0,"ng-container",11),l(),o(12,"div",10),k(13,sl,1,0,"ng-container",11),l(),o(14,"div",10),k(15,cl,1,0,"ng-container",11),l()()()(),o(16,"div",12),d(17,"img",13),o(18,"div",14),c(19," National Library of Medicine"),d(20,"br"),c(21," 8600 Rockville Pike, Bethesda, MD 20894 "),l()()()()()(),k(22,fl,7,1,"ng-template",null,0,le)),i&2){let a=Z(23);f(4),ae(r.linkGroups),f(7),v("ngTemplateOutlet",a)("ngTemplateOutletContext",G(6,xn,r.linkGroups[0])),f(2),v("ngTemplateOutlet",a)("ngTemplateOutletContext",G(8,xn,r.linkGroups[1])),f(2),v("ngTemplateOutlet",a)("ngTemplateOutletContext",G(10,xn,r.linkGroups[2]))}},dependencies:[Ne,be],styles:[".ctg-footer[_ngcontent-%COMP%]{background-color:#20558a;color:#fff;padding-bottom:30px}.footer-grid-gap[_ngcontent-%COMP%]{margin-right:5rem}.usa-list[_ngcontent-%COMP%]{margin-top:16px}.ctg-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .ctg-footer__primary-link[_ngcontent-%COMP%]{color:#fff}.address[_ngcontent-%COMP%]{font-size:14px}.ctg-footer-link-text[_ngcontent-%COMP%]{color:var(--ctgl-color-base-lighter);text-decoration:underline;font-size:18px}.ctg-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:visited{color:var(--ctgl-color-base-light)}.ctg-footer__logo[_ngcontent-%COMP%]{width:30%}.column[_ngcontent-%COMP%]{display:inline-block;margin-top:32px}.column-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700;line-height:26px;margin-bottom:16px}.column-content[_ngcontent-%COMP%]{font-size:18px;line-height:21px;margin-bottom:16px}.social-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700;margin:16px 0}.separator[_ngcontent-%COMP%]{width:38px;height:1px;border-top:1px solid white}.social-links[_ngcontent-%COMP%]{margin-top:12px}.social-distance[_ngcontent-%COMP%]{margin:0 8px 0 0;display:inline-block;width:38px;height:38px}.social-distance[_ngcontent-%COMP%]:hover{background-color:transparent!important}.ctg-logo[_ngcontent-%COMP%]{max-height:50px}.group-title[_ngcontent-%COMP%]{background:none;border-width:0 0 1px 0;border-color:#a6a6a6;border-radius:0;border-style:solid;box-shadow:none;color:var(--ctgl-color-base-lighter);font-size:16px;font-weight:700;line-height:21px;padding:8px 0;text-align:left;width:100%;display:flex;align-items:center}.group-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:20px}.group-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{text-decoration:underline}.group-title[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%]{width:20px;height:20px}.group[_ngcontent-%COMP%]:first-child > .group-title[_ngcontent-%COMP%]{border-top-width:1px;margin-top:8px}.group[_ngcontent-%COMP%] > .group-content[_ngcontent-%COMP%]{display:none}.group.group__expanded[_ngcontent-%COMP%] > .group-content[_ngcontent-%COMP%]{display:block}.group-content-row[_ngcontent-%COMP%]{font-size:18px;line-height:21px;padding:8px 0 8px 40px}.group-content-row[_ngcontent-%COMP%]:last-child{border-bottom:1px solid #a6a6a6}.footer-list[_ngcontent-%COMP%]{display:none}.footer-accordion[_ngcontent-%COMP%]{margin-bottom:16px}@media screen and (min-width: 640px){.footer-list[_ngcontent-%COMP%]{display:none}.footer-accordion[_ngcontent-%COMP%], .footer-contacts[_ngcontent-%COMP%]{display:inline-block;vertical-align:top;width:50%}.footer-accordion[_ngcontent-%COMP%]{padding:0 8px}}@media screen and (min-width: 1024px){.to-top[_ngcontent-%COMP%], .footer-accordion[_ngcontent-%COMP%]{display:none}.footer-list[_ngcontent-%COMP%]{display:inline-block;vertical-align:top;padding:8px 16px 0 0;width:62%}.footer-contacts[_ngcontent-%COMP%]{display:inline-block;vertical-align:top;margin-top:32px;width:38%;max-width:280px}.footer-contacts[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{font-size:16px}}@media print{[_nghost-%COMP%]{display:none}}"],changeDetection:0})}}return n})();function hl(n,t){if(n&1&&(o(0,"li",16)(1,"a",22),c(2),l()()),n&2){let e=t.$implicit;f(),Ye("aria-label","",e.text," (opens in a new tab)"),ke("href",e.url?e.url:"javascript:void(0)",Ce),Qe("title","",e.text," (opens in a new tab)"),xe("id",e.id?e.id:null),f(),z(e.text)}}var Ar=(()=>{class n{constructor(){this.links=[{text:"About HHS",url:"https://www.hhs.gov/about/index.html"},{text:"About NIH",url:"https://www.nih.gov/about-nih"},{text:"About NLM",url:"https://www.nlm.nih.gov/about"},{text:"About NCBI",url:"https://www.ncbi.nlm.nih.gov/home/<USER>"},{text:"Accessibility support",url:"https://www.nlm.nih.gov/accessibility.html"},{text:"FOIA requests",url:"https://www.nih.gov/icd/od/foia/index.htm"},{text:"No FEAR Act data",url:"https://www.edi.nih.gov/no-fear-act"},{text:"Office of the Inspector General",url:"https://oig.hhs.gov"},{text:"Privacy policy",url:"https://www.nlm.nih.gov/privacy.html"},{id:"vdp",text:"HHS Vulnerability Disclosure",url:"https://www.hhs.gov/vulnerability-disclosure-policy/index.html"}],this.ncbiLogoAltText="NIH National Library of Medicine, National Center for Biotechnology Information (opens in a new tab)"}versionText(){return At.versionText}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-hhs-footer"]],decls:34,vars:8,consts:[[1,"usa-identifier","hhs-footer"],["aria-label","Agency identifier",1,"usa-identifier__section","usa-identifier__section--masthead"],[1,"grid-container-widescreen"],[1,"grid-row","grid-gap"],[1,"desktop:grid-col","usa-identifier__logos"],["href","https://ncbi.nlm.nih.gov","target","_blank","rel","noopener noreferrer",1,"usa-identifier__logo",2,"flex","1",3,"title"],["src","assets/main/img/AgencyLogo.svg","role","img",1,"usa-identifier__logo-img",2,"width","100%",3,"alt"],["aria-label","Agency description",1,"desktop:grid-col",2,"padding-top","15px"],[1,"usa-identifier__identity-domain"],[1,"usa-identifier__identity-disclaimer"],["href","https://www.hhs.gov","linkText","U.S. Department of Health and Human Services",1,"hhs-footer-usa-link",3,"showIcon"],["href","https://www.nih.gov","linkText","National Institutes of Health",1,"hhs-footer-usa-link",3,"showIcon"],["href","https://www.nlm.nih.gov","linkText","National Library of Medicine",1,"hhs-footer-usa-link",3,"showIcon"],["href","https://www.ncbi.nlm.nih.gov","linkText","National Center for Biotechnology Information",1,"hhs-footer-usa-link",3,"showIcon"],["aria-label","Important links",1,"usa-identifier__section","usa-identifier__section--required-links"],[1,"usa-identifier__required-links-list"],[1,"usa-identifier__required-links-item","padding-y-05","margin-bottom-0"],["aria-label","Government information and services",1,"usa-identifier__section","usa-identifier__section--usagov"],[1,"usa-identifier__usagov-description"],["href","https://www.usa.gov/","linkText","Visit USA.gov",1,"usa-identifier__section--usagov","hhs-footer-usa-link",3,"showIcon"],[1,"margin-0"],[1,"version-info","text-base-light"],["target","_blank","rel","noopener noreferrer",1,"usa-identifier__required-link","usa-link",3,"href","title"]],template:function(i,r){i&1&&(o(0,"div",0)(1,"section",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"a",5),d(6,"img",6),l()(),o(7,"div",7)(8,"p",8),c(9,"ClinicalTrials.gov"),l(),o(10,"p",9),c(11,"An official website of the "),d(12,"ctg-external-link-ref",10),c(13,", "),d(14,"ctg-external-link-ref",11),c(15,", "),d(16,"ctg-external-link-ref",12),c(17,", and "),d(18,"ctg-external-link-ref",13),c(19,". "),l()()()()(),o(20,"nav",14)(21,"div",2)(22,"ul",15),oe(23,hl,3,7,"li",16,ve),l()()(),o(25,"section",17)(26,"div",2)(27,"div",18),c(28,"Looking for U.S. government information and services?"),l(),d(29,"ctg-external-link-ref",19),l()(),o(30,"section")(31,"div",20)(32,"p",21),c(33),l()()()()),i&2&&(f(5),v("title",r.ncbiLogoAltText),f(),v("alt",r.ncbiLogoAltText),f(6),v("showIcon",!1),f(2),v("showIcon",!1),f(2),v("showIcon",!1),f(2),v("showIcon",!1),f(5),ae(r.links),f(6),v("showIcon",!1),f(4),Bn("Revision: ",r.versionText(),""))},dependencies:[ye],styles:[".hhs-footer[_ngcontent-%COMP%]{background-color:var(--ctgl-color-blue-warm-80v);padding-right:1rem;color:#fff}.hhs-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{font-weight:700}.hhs-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:visited{color:#fff}.usa-identifier__usagov-description[_ngcontent-%COMP%]{margin-right:1ex}.usa-identifier__required-link[_ngcontent-%COMP%]{font-weight:400!important}.usa-identifier__required-link[_ngcontent-%COMP%]{color:var(--ctgl-color-base-light)!important}.usa-identifier__required-link[_ngcontent-%COMP%]:hover, .usa-identifier__required-link[_ngcontent-%COMP%]:visited{color:#fff!important}.usa-identifier__logos[_ngcontent-%COMP%]{display:flex;align-items:center;max-width:354px;margin:0}@media screen and (min-width: 640px){.usa-identifier__logos[_ngcontent-%COMP%]{margin-right:32px}}.version-info[_ngcontent-%COMP%]{text-align:right;padding-right:10rem;margin:0;font-size:.75rem}@media screen and (max-width: 600px){.version-info[_ngcontent-%COMP%]{padding-right:3rem}}@media print{[_nghost-%COMP%]{display:none}}"],changeDetection:0})}}return n})();var Mr=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-footer"]],decls:2,vars:0,template:function(i,r){i&1&&d(0,"ctg-ctg-footer")(1,"ctg-hhs-footer")},dependencies:[Tr,Ar],encapsulation:2,changeDetection:0})}}return n})();var gl=["desktopBackToTopButton"],xl=["mobileBackToTopButton"];function vl(n,t){if(n&1){let e=V();o(0,"a",5,0),j("click",function(){R(e);let r=b(2);return N(r.scrollToTop())}),o(2,"span",6),c(3,"Back to Top"),l()()}}function _l(n,t){if(n&1){let e=V();o(0,"a",7,1),j("click",function(){R(e);let r=b(2);return N(r.scrollToTop())}),l()}}function bl(n,t){if(n&1&&(o(0,"div",2),k(1,vl,4,0,"a",3)(2,_l,2,0,"a",4),l()),n&2){let e=b();f(),T(e.isDesktop()?1:2)}}var Or=(()=>{class n{constructor(){this.atTop=!0,this.enableAt=900,this.isDesktop=y(qe).isDesktop,this.feedbackButtonBottomValue=0,this.desktopBackToTopButton=Ke("desktopBackToTopButton"),this.mobileBackToTopButton=Ke("mobileBackToTopButton"),this.showScrollToTop=!1,this.shownScrollToTop=!1,Wt(window,"resize").pipe(Kt(100,void 0,{leading:!0,trailing:!0}),ne()).subscribe(()=>{this.onResize()}),Wt(window,"scroll").pipe(Kt(100,void 0,{leading:!0,trailing:!0}),ne()).subscribe(()=>{this.onScroll()})}ngAfterViewInit(){let e=1,i=setInterval(()=>{e>100&&(this.feedbackButtonBottomValue=0,clearInterval(i),setTimeout(()=>{this.onScroll()},100));let r=document.getElementById("QSIFeedbackButton-btn");if(r){clearInterval(i);let a=r.style.bottom;if(!a)return;this.feedbackButtonBottomValue=parseInt(a),setTimeout(()=>{this.onScroll()},100);return}e++},1e3)}scrollToTop(){return window.scrollTo(0,0),!1}onResize(){this.shownScrollToTop=!1,setTimeout(()=>{this.ngAfterViewInit()},100)}onScroll(){if(this.atTop=window.scrollY<this.enableAt,this.atTop){this.shownScrollToTop=!1;return}this.shownScrollToTop||setTimeout(()=>{if(!this.feedbackButtonBottomValue){this.showScrollToTop=!0,this.shownScrollToTop=!0;return}let e=110,i=this.desktopBackToTopButton();i&&(i.nativeElement.style.bottom=`${this.feedbackButtonBottomValue+e+54}px`,this.shownScrollToTop=!0);let r=this.mobileBackToTopButton();r&&(r.nativeElement.style.bottom=`${this.feedbackButtonBottomValue+e+4}px`,this.shownScrollToTop=!0),this.showScrollToTop=!0},10)}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-scroll-top"]],viewQuery:function(i,r){i&1&&(Xe(r.desktopBackToTopButton,gl,5),Xe(r.mobileBackToTopButton,xl,5)),i&2&&_t(2)},decls:1,vars:1,consts:[["desktopBackToTopButton",""],["mobileBackToTopButton",""],[1,"to-top"],["href","#","tabindex","0"],["href","#","tabindex","0",1,"scroll-icon"],["href","#","tabindex","0",3,"click"],[1,"to-top-text"],["href","#","tabindex","0",1,"scroll-icon",3,"click"]],template:function(i,r){i&1&&k(0,bl,3,1,"div",2),i&2&&T(!r.atTop&&r.showScrollToTop?0:-1)},styles:["a[_ngcontent-%COMP%]:hover{background-color:#277236;box-shadow:none;color:#fff}a[_ngcontent-%COMP%]{background:no-repeat 1rem .875rem/1rem;color:#fff;position:fixed;z-index:99999;right:0;margin:0;box-shadow:0 0 .625rem #0000004d;transition:background-color .15s,box-shadow .3s;font-size:.88rem;height:2.75rem;line-height:1.71875rem;border-radius:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:0;cursor:pointer;display:inline-block;font-weight:700;padding:.625rem 1.2rem .625rem 2.5rem;text-align:left;text-decoration:none;width:9.9rem}a[_ngcontent-%COMP%]:active, a[_ngcontent-%COMP%]:hover{background-color:#277236;box-shadow:none;color:#fff;outline:0}a[_ngcontent-%COMP%]:focus{outline:0}@media screen and (max-width: 600px){a[_ngcontent-%COMP%]{text-indent:-1000px;overflow:hidden;padding:0;width:2.75rem;height:2.75rem;background-position:.75rem .8125rem;background-size:1.375rem}}","a[_ngcontent-%COMP%]{z-index:9997;bottom:213px;transform:rotate(270deg);right:-47px;height:50px;font-size:1rem;width:9rem;background-size:21px;background-color:var(--ctgl-color-primary-darkest);background-image:url(/assets/images/arrow_forward.svg)}a[_ngcontent-%COMP%]:hover{background-color:var(--ctgl-color-primary-darkest)}a[_ngcontent-%COMP%]:focus{outline:.25rem solid var(--ctgl-color-blue-40v);outline-offset:0}.scroll-icon[_ngcontent-%COMP%]{position:fixed;transform:rotate(0);background-color:transparent;padding:0;margin:0;bottom:170px;right:-1px;width:52px;background-color:var(--ctgl-color-primary-darkest);background-image:url(/assets/images/arrow_upward.svg)}@media print{[_nghost-%COMP%]{display:none}}"]})}}return n})();function yl(n,t){n&1&&d(0,"ctg-header")}function wl(n,t){n&1&&d(0,"ctg-footer")(1,"ctg-scroll-top")}function Cl(n,t){if(n&1){let e=V();k(0,yl,1,0,"ctg-header"),o(1,"main",1)(2,"router-outlet",2),j("activate",function(r){R(e);let a=b(2);return N(a.onActivate(r))}),l()(),k(3,wl,2,0)}if(n&2){let e=b(2);T(e.needHeaderAndFooter()?0:-1),f(3),T(e.needHeaderAndFooter()?3:-1)}}function kl(n,t){if(n&1){let e=V();o(0,"div",0)(1,"div",3)(2,"div",4)(3,"header",5)(4,"h2",6),c(5,"Please enter invite code"),l()(),o(6,"div",7)(7,"form",8),j("ngSubmit",function(){R(e);let r=b(2);return N(r.onCodeCheck())}),o(8,"div",9)(9,"input",10),In("ngModelChange",function(r){R(e);let a=b(2);return Dn(a.inputCode,r)||(a.inputCode=r),N(r)}),l(),o(10,"button",11),c(11,"Check"),l()()()()(),o(12,"div",12)(13,"a",13),c(14,"HHS Vulnerability Disclosure"),l()()()()}if(n&2){let e=b(2);f(9),Fn("ngModel",e.inputCode)}}function Sl(n,t){if(n&1&&k(0,Cl,4,2)(1,kl,15,1,"div",0),n&2){let e=b();T(e.accessAllowed()?0:1)}}var El=["beta-dev.clinicaltrials.gov","beta-staging.clinicaltrials.gov","beta-ut.clinicaltrials.gov","qa.clinicaltrials.gov"],it="ctg_access",Pl=24*60*60*1e3,Fr=(()=>{class n{constructor(){this.router=y(He),this.pingerService=y(ci),this.navigationService=y(Nt),this.httpClient=y(qn),this.title="public-site",this.render=!1,this.isRestrictedHost=!1,this.storedCodeHash="",this.inputCode="",this.lastUrl="";let e=y(Ue),i=y(si),r=y(ht);this.router.events.pipe(ne()).subscribe(a=>{if(a instanceof ii)i.unsubscribe(),this.curUrl&&this.navigationService.saveState(this.curUrl),this.restoredNavigationId=a.restoredState?.navigationId;else if(a instanceof Le){this.curUrl=a.url;let s=this.curComponent;if(mr(s)){let u=s,p=this.restoredNavigationId!==void 0||u.restoreOnBreadcrumbs&&this.navigationService.fromBreadcrumbs?this.navigationService.getState(this.curUrl):void 0;u.onNavigation(p,this.navigationService.lastSavedUrl)}else{let u=a.urlAfterRedirects;if(!(this.lastUrl.startsWith("/study/")&&u.startsWith("/study/"))){let p=e.snapshot.fragment;p?i.scrollToFragment(p,r):window.scrollTo(0,0)}this.lastUrl=u}this.navigationService.fromBreadcrumbs=!1,this.pingerService.reset()}})}ngOnInit(){document.querySelector('meta[name="ncbi_app_version"]')?.setAttribute("content",At.versionText),window.onload=i=>{this.render=!0},this.isRestrictedHost=El.includes(window.location.hostname.toLowerCase());let e=+(localStorage.getItem(it+"_expire")||"");Date.now()>e?(localStorage.removeItem(it),this.storedCodeHash=""):this.storedCodeHash=localStorage.getItem(it)||""}accessAllowed(){return!this.isRestrictedHost||this.storedCodeHash==="true"}updateAccessCodeHash(e){let i=(0,Br.default)("sha256").update(e).digest("hex");this.httpClient.get("/api/int/access-check",{params:{code:i},responseType:"text"}).pipe(yn((r,a)=>bn("error"))).subscribe(r=>{r==="ok"&&(this.storedCodeHash="true",localStorage.setItem(it,this.storedCodeHash),localStorage.setItem(it+"_expire",(Date.now()+Pl).toString()))})}onCodeCheck(){this.updateAccessCodeHash(this.inputCode),this.inputCode=""}onActivate(e){this.curComponent=e}needHeaderAndFooter(){return!Re.reviewEnabled||!this.router.url.startsWith("/review")}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["app-root"]],decls:1,vars:1,consts:[[1,"grid-container-mobile-lg","margin-top-3"],["id","main-content"],[3,"activate"],[1,"usa-card","margin-left-auto","margin-right-auto"],[1,"usa-card__container"],[1,"usa-card__header"],[1,"usa-card__heading"],[1,"usa-card__body"],[1,"usa-form",3,"ngSubmit"],[1,"usa-form-group"],["name","code","type","password",1,"usa-input",3,"ngModelChange","ngModel"],["type","submit",1,"usa-button"],[1,"hhsvdp"],["aria-label","HHS Vulnerability Disclosure (opens in a new tab)","title","HHS Vulnerability Disclosure (opens in a new tab)","target","_blank","rel","noopener noreferrer","href","https://www.hhs.gov/vulnerability-disclosure-policy/index.html","id","vdp",1,"usa-link"]],template:function(i,r){i&1&&k(0,Sl,2,1),i&2&&T(r.render?0:-1)},dependencies:[ri,wt,Qn,Gn,$n,Wn,Yn,Kn,Pr,Mr,Or],styles:[".hhsvdp[_ngcontent-%COMP%]{text-align:center;margin-top:30px;font-size:10px}"]})}}return n})();var Dr=(()=>{class n{constructor(){this.radioStatus="all-studies"}ngOnInit(){}ngOnDestroy(){}onSearchEvent(e){}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-home-title-and-disclaimer"]],decls:5,vars:0,consts:[[1,"intro-text"],["id","disclaimer"]],template:function(i,r){i&1&&(o(0,"section")(1,"h3",0),c(2,"ClinicalTrials.gov is a place to learn about clinical studies from around the world."),l()(),o(3,"section",1),d(4,"ctg-disclaimer"),l())},dependencies:[fi],styles:[".grid-row-gap[_ngcontent-%COMP%]{height:50px}.intro-text[_ngcontent-%COMP%]{font-size:22px;font-weight:700;line-height:140%;margin-top:16px;margin-bottom:0}@media (min-width: 64rem){.intro-text[_ngcontent-%COMP%]{margin-bottom:16px;text-align:center}}#disclaimer[_ngcontent-%COMP%]{max-width:800px;margin:auto}"]})}}return n})();function Tl(n,t){n&1&&J(0)}function Al(n,t){if(n&1){let e=V();o(0,"div",10)(1,"div",11)(2,"button",12),j("click",function(){R(e);let r=b();return N(r.onSearch())}),c(3," Search "),l()()()}if(n&2){let e=b();f(2),v("disabled",e.searchButtonDisabled)}}var Ir=(()=>{class n{constructor(){this.searchButtonDisabled=!1,this.cd=y(bt),this.destroyRef=y(ht),this.searchForm=Ke.required(Tt)}onSearch(){this.searchForm().onSearch()}ngAfterViewInit(){this.searchForm().searchForm.statusChanges.pipe(ne(this.destroyRef)).subscribe(e=>{this.searchButtonDisabled=e!=="VALID"}),this.cd.detectChanges()}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-home-search-panel"]],viewQuery:function(i,r){i&1&&Xe(r.searchForm,Tt,5),i&2&&_t()},decls:14,vars:2,consts:[["form",""],["buttons",""],[1,"home-search-panel"],[1,"search-panel-header"],[1,"expert-search"],["link","/expert-search","linkText","Expert Search"],[1,"home-search-panel-filters"],[3,"homePage"],[1,"home-search-panel-footer"],[4,"ngTemplateOutlet"],[1,"grid-row","grid-gap","form-buttons"],[1,"grid-col"],[1,"usa-button","search-button","primary-button","height-full","float-right",3,"click","disabled"]],template:function(i,r){if(i&1&&(o(0,"div",2)(1,"div",3),c(2," Focus Your Search "),o(3,"span"),c(4,"(all filters optional)"),l(),o(5,"div",4),d(6,"ctg-internal-link-ref",5),l()(),o(7,"div",6),d(8,"ctg-search-filters-form",7,0),l(),o(10,"div",8),k(11,Tl,1,0,"ng-container",9),l()(),k(12,Al,4,1,"ng-template",null,1,le)),i&2){let a=Z(13);f(8),v("homePage",!0),f(3),v("ngTemplateOutlet",a)}},dependencies:[Un,Ne,Tt,Pt],styles:['.action-bar-widget[_ngcontent-%COMP%]{background:#fff;border:1px solid var(--ctgl-color-base-lighter);border-radius:0;display:flex;width:100%;justify-content:space-between;padding:.5rem 1rem}.action-bar-items[_ngcontent-%COMP%]{display:flex;flex-direction:row;gap:.5rem}.action-bar-widget[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;row-gap:0}.action-bar-widget[_ngcontent-%COMP%]   .action-bar-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:100%;gap:.5rem}.text-button[_ngcontent-%COMP%], .action-bar-button[_ngcontent-%COMP%]{border-radius:4px;border:2px solid var(--ctgl-color-primary-darkest);color:var(--ctgl-color-primary-darkest);gap:4px;height:40px;background:no-repeat .4rem .375rem/.5rem;appearance:none;cursor:pointer;display:inline-flex;align-items:center;font-weight:700;font-size:16px;text-decoration:none}.text-button[_ngcontent-%COMP%]:hover:not([disabled]), .action-bar-button[_ngcontent-%COMP%]:hover:not([disabled]){color:#fff;border:2px solid var(--ctgl-color-primary-dark);background-color:var(--ctgl-color-primary-dark)}.text-button[_ngcontent-%COMP%]:active, .action-bar-button[_ngcontent-%COMP%]:active{color:#fff;background-color:var(--ctgl-color-primary-darkest)}.text-button[_ngcontent-%COMP%]:disabled, .action-bar-button[_ngcontent-%COMP%]:disabled{cursor:auto;color:var(--ctgl-color-base-darker);border:2px solid var(--ctgl-color-disabled);background-color:transparent;pointer-events:none}.border-line[_ngcontent-%COMP%]{border:1px solid var(--ctgl-color-base);align-self:center;height:32px}.action-bar-button[_ngcontent-%COMP%]{padding:8px}.disabled-outline-button[_ngcontent-%COMP%]:before{margin-top:4px;content:url("./media/bookmark-outline-S5BQM6HX.svg");pointer-events:none}.bookmark-outline-button[_ngcontent-%COMP%]:before{height:41px;background:none;padding-top:6px;margin:4px 0 0;content:url("./media/bookmark-empty-primary-JZECWJL6.svg")}.bookmark-outline-button[_ngcontent-%COMP%]{background:none;margin:0}.bookmark-outline-button[_ngcontent-%COMP%]:hover{content:none}.bookmark-outline-button[_ngcontent-%COMP%]:hover:before{content:url("./media/bookmark-empty-primary-darker-XD3DB2TG.svg")}.button-group[_ngcontent-%COMP%]{outline:none!important;display:inline-flex;column-gap:0;margin-right:0;align-items:center;background:none;border:2px solid var(--ctgl-color-primary);color:var(--ctgl-color-primary)}.button-group[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%]{margin-right:2px}.button-group[_ngcontent-%COMP%]:first-child{border-radius:4px 0 0 4px}.button-group[_ngcontent-%COMP%]:last-child{border-radius:0 4px 4px 0}.button-group.card-btn[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%], .button-group.table-btn[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%]{font-size:18px}.button-group.active[_ngcontent-%COMP%]{cursor:auto;color:#fff;border:2px solid var(--ctgl-color-primary-darker);background-color:var(--ctgl-color-primary-darker)}.button-group[_ngcontent-%COMP%]:hover:not(.active){color:var(--ctgl-color-primary-dark);border:2px solid var(--ctgl-color-primary-dark);background-color:var(--ctgl-color-gray-warm-4)}.primary-button[_ngcontent-%COMP%]{border-radius:.25rem;border:2px solid var(--ctgl-color-primary);background:var(--ctgl-color-primary);color:#fff}.primary-button[_ngcontent-%COMP%]:hover:not([disabled]){background-color:var(--ctgl-color-primary-dark);color:#fff}.primary-button[_ngcontent-%COMP%]:active{background-color:var(--ctgl-color-primary-darker);color:#fff}.primary-button[_ngcontent-%COMP%]:disabled{background-color:var(--ctgl-color-gray-10);border:2px solid var(--ctgl-color-disabled-dark);color:var(--ctgl-color-disabled-dark);cursor:auto;pointer-events:none}.secondary-button[_ngcontent-%COMP%]{border-radius:.25rem;border:2px solid var(--ctgl-color-primary);color:var(--ctgl-color-primary);background:none}.secondary-button[_ngcontent-%COMP%]:hover:not([disabled]){border:2px solid var(--ctgl-color-primary-dark);color:var(--ctgl-color-primary-dark);background-color:var(--ctgl-color-gray-warm-4)}.secondary-button[_ngcontent-%COMP%]:active{border:2px solid var(--ctgl-color-primary-darker);color:var(--ctgl-color-primary-darker);background-color:var(--ctgl-color-gray-warm-5)}.secondary-button[_ngcontent-%COMP%]:disabled{border:2px solid var(--ctgl-color-disabled-dark);color:var(--ctgl-color-disabled-dark);cursor:auto;pointer-events:none;background:none}',"[_nghost-%COMP%]{display:block}.form-buttons[_ngcontent-%COMP%]   .usa-button[_ngcontent-%COMP%]{margin-right:0}.home-search-panel[_ngcontent-%COMP%]{background:#fff;border:1px solid var(--ctgl-color-base-lighter);margin:auto}@media (min-width: 64rem){.home-search-panel[_ngcontent-%COMP%]{width:800px}}.home-search-panel[_ngcontent-%COMP%]   .home-search-panel-filters[_ngcontent-%COMP%]   .caption[_ngcontent-%COMP%]{font-size:12px}.home-search-panel[_ngcontent-%COMP%]   .home-search-panel-filters[_ngcontent-%COMP%]   .close-filter[_ngcontent-%COMP%]{color:var(--ctgl-color-primary-darkest)}.home-search-panel-footer[_ngcontent-%COMP%]{position:sticky;bottom:0;z-index:200;background-color:var(--ctgl-color-gray-warm-2);border-top:1px solid var(--ctgl-color-base-light);border-radius:0 0 8px 8px;color:#fff;padding:.5rem 1rem}@media (min-width: 64rem){.home-search-panel-footer[_ngcontent-%COMP%]{padding:.5rem 2.5rem}}.home-search-panel-footer[_ngcontent-%COMP%]   .filter-button[_ngcontent-%COMP%]{padding-right:0}.home-search-panel-footer[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%]{width:100%}@media (min-width: 64rem){.home-search-panel-footer[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%]{width:99px;height:2.5rem}}.home-search-panel-footer[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .field-select-button[_ngcontent-%COMP%]{color:var(--ctgl-color-primary-darkest);font-weight:700;border:2px solid var(--ctgl-color-primary-darkest);border-radius:.25rem;width:100%;background-color:transparent;cursor:pointer;padding:10px 0}.home-search-panel-footer[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .field-select-button[_ngcontent-%COMP%]:hover{color:var(--ctgl-color-primary-dark);border:2px solid transparent;border:2px solid var(--ctgl-color-primary-dark)}@media (min-width: 64rem){.home-search-panel-footer[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .field-select-button[_ngcontent-%COMP%]{padding:0 1rem;width:auto;height:2.5rem;display:inline-flex;align-items:center}}.home-search-panel-footer[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .field-select-button[_ngcontent-%COMP%]   .usa-icon[_ngcontent-%COMP%]{height:1.5rem;width:1.5rem}.search-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px;background-color:var(--ctgl-color-gray-warm-2);border-radius:8px 8px 0 0;border-bottom:1px solid var(--ctgl-color-base-lighter);font-weight:700;font-size:16px;line-height:135%}.search-panel-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;color:var(--ctgl-color-base)}.search-panel-header[_ngcontent-%COMP%]   .expert-search[_ngcontent-%COMP%]{font-weight:400;font-size:14px}@media (min-width: 64rem){.search-panel-header[_ngcontent-%COMP%]   .expert-search[_ngcontent-%COMP%]{float:right;padding-right:4px}}@media (min-width: 64rem){.search-panel-header[_ngcontent-%COMP%]{padding:24px 40px 8px;font-size:18px}}"]})}}return n})();var Rr=(()=>{class n{constructor(){this.restoreOnBreadcrumbs=!1,y(_e).setTitle(se.home.title)}ngOnInit(){}onNavigation(e){requestAnimationFrame(()=>window.scrollTo(0,e?.scrollY||0))}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-home"]],decls:5,vars:0,consts:[[1,"home-search","padding-left-2","padding-right-2","desktop:padding-left-0","desktop:padding-right-0"],[1,"desktop:grid-container-widescreen","padding-top-1","padding-bottom-1"],[1,"desktop:grid-container-widescreen","padding-top-1","padding-bottom-2"]],template:function(i,r){i&1&&(o(0,"div",0)(1,"div",1),d(2,"ctg-home-title-and-disclaimer"),l(),o(3,"div",2),d(4,"ctg-home-search-panel"),l()())},dependencies:[Dr,Ir],styles:[".home-search[_ngcontent-%COMP%]{background-color:var(--ctgl-color-blue-5)}"]})}}return n})();var Ml=n=>({content:n});function Ol(n,t){n&1&&(o(0,"section",3)(1,"h2"),c(2,"Drug and device information from the Food and Drug Administration (FDA)"),l(),o(3,"ul")(4,"li"),d(5,"ctg-external-link-ref",4),c(6,": Regulated by the FDA Center for Drug Evaluation and Research (CDER) "),l(),o(7,"li"),d(8,"ctg-external-link-ref",5),c(9,": Regulated by the FDA Center for Devices and Radiological Health (CDRH) "),l(),o(10,"li"),d(11,"ctg-external-link-ref",6),c(12,": Regulated by the FDA Center for Biologics Evaluation and Research (CBER) "),l()()(),o(13,"section",7)(14,"h2"),c(15,"Drug action packages"),l(),o(16,"ul")(17,"li"),d(18,"ctg-external-link-ref",8),c(19,": Database of information about drug products approved by CDER "),l(),o(20,"li"),d(21,"ctg-external-link-ref",9),c(22," (Biologics): List of drug products approved by CBER "),l(),o(23,"li"),d(24,"ctg-external-link-ref",10),c(25,": List of biological products licensed by CBER "),l()()(),o(26,"section",11)(27,"h2"),c(28,"Device approval packages"),l(),o(29,"ul")(30,"li"),d(31,"ctg-external-link-ref",12),c(32,": Database of information about medical device PMAs approved by CDRH "),l(),o(33,"li"),d(34,"ctg-external-link-ref",13),c(35,": List of PMAs and humanitarian device exemptions approved by CBER "),l(),o(36,"li"),d(37,"ctg-external-link-ref",14),c(38,": Database of information about device premarket notifications cleared by CDRH "),l(),o(39,"li"),d(40,"ctg-external-link-ref",15),c(41,": List of 510(k) premarket notifications cleared by CBER "),l()()(),o(42,"section",16)(43,"h2"),c(44,"Drug and device safety Information"),l(),o(45,"ul")(46,"li"),d(47,"ctg-external-link-ref",17),c(48,": Lists the most recent Drug Safety Communications from FDA and provides links to Early Communications, Follow-Up Early Communications, and Information for Healthcare Professional sheets "),l(),o(49,"li"),d(50,"ctg-external-link-ref",18),c(51,": FDA gateway for clinically important safety information and for reporting serious problems with human medical products "),l(),o(52,"li"),d(53,"ctg-external-link-ref",19),c(54,": Links to postmarket drug safety information to improve transparency and communication with patients and health care providers "),l(),o(55,"li"),d(56,"ctg-external-link-ref",20),c(57,": Lists of recent medical device recalls and other FDA safety communications "),l(),o(58,"li"),d(59,"ctg-external-link-ref",21),c(60,": Lists safety communications and availability of biological products information from CBER "),l()()(),o(61,"section",22)(62,"h2"),c(63,"Other relevant information"),l(),o(64,"ul")(65,"li"),d(66,"ctg-external-link-ref",23),c(67,": Database of FDA advisory committee meeting materials that may discuss the efficacy and safety of drugs and devices and summarize the results of clinical trials "),l(),o(68,"li"),d(69,"ctg-external-link-ref",24),c(70,": Links to medical, statistical, and clinical pharmacology reviews of pediatric studies conducted in response to a Written Request issued under Section 505A(k)(1) of the Best Pharmaceuticals for Children Act of 2007 (BPCA) and pediatric assessments conducted under Section 505B(h)(1) of the Pediatric Research Equity Act of 2007 (PREA), as amended by FDAAA "),o(71,"ul")(72,"li"),d(73,"ctg-external-link-ref",25),c(74,": As of December 13, 2019 "),l()()(),o(75,"li"),d(76,"ctg-external-link-ref",26),c(77,": Highlights key pediatric information from the studies submitted in response to pediatric legislative initiatives, including BPCA and PREA; provided by CDER "),l(),o(78,"li"),d(79,"ctg-external-link-ref",27),c(80,": Labeling changes and reviews of pediatric studies conducted under PREA, in accordance with Section 505B of the Federal Food, Drug, and Cosmetic Act, as amended by FDAAA; provided by CBER "),l(),o(81,"li"),d(82,"ctg-external-link-ref",28),c(83,": A table of Preliminary Notices of Noncompliance (Pre-Notices) issued by FDA informing a responsible party of an applicable clinical trial (ACT) regarding potential noncompliance with the legal requirements for registering and submitting results information to ClinicalTrials.gov. Pre-Notices are posted on a quarterly basis. "),l(),o(84,"li"),d(85,"ctg-external-link-ref",29),c(86,": A table of Notices of Noncompliance sent by FDA and the amount of civil money penalties assessed, if any, for each responsible party of an applicable clinical trial (ACT) or submitter listed. FDA also has the authority to issue a Notice of Noncompliance to a submitter who has failed to submit or knowingly submitted a false certification to FDA. "),l(),o(87,"li"),d(88,"ctg-external-link-ref",30),c(89,": In a three-part webinar series, FDA provides a general overview of ClinicalTrials.gov and relevant definitions, laws, and regulations for complying with ClinicalTrials.gov registration and results information submission requirements. These presentations were part of the 2023 CDER Small Business and Industry Assistance Webinars and Conferences. "),o(90,"ul")(91,"li"),d(92,"ctg-external-link-ref",31),c(93," (12:17) "),l(),o(94,"li"),d(95,"ctg-external-link-ref",32),c(96," (15:32) "),l(),o(97,"li"),d(98,"ctg-external-link-ref",33),c(99," (16:32) "),l()()()()(),d(100,"div",34))}var Nr=(()=>{class n{constructor(){this.headerTitle="FDA Drug and Device Resources",this.navItems=[{label:"Drug and device information",anchor:"drug-and-device-info"},{label:"Drug action packages",anchor:"drug-action-packages"},{label:"Device approval packages",anchor:"device-approval-packages"},{label:"Drug and device safety information",anchor:"drug-device-safety-info"},{label:"Other relevant information",anchor:"other-relevant-info"}],this.changeDetectionRef=y(bt),this.activatedRoute=y(Ue),this.viewportScroller=y(Hn),y(_e).setTitle(se.fda_links.title)}ngAfterViewInit(){let e=this.activatedRoute.snapshot.fragment;e&&(this.changeDetectionRef.detectChanges(),this.viewportScroller.scrollToAnchor(e))}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-fda-links"]],features:[Rn([vi])],decls:4,vars:5,consts:[["fdaLinksTemplate",""],[3,"navItems","headerTitle"],["lastUpdateDate","2024-06-12",3,"infoData"],["ctgIntersectionObserver","","id","drug-and-device-info",1,"usa-prose","usa-paragraph"],["href","http://www.fda.gov/Drugs/default.htm","linkText","Drugs",1,"select-pub-link"],["href","http://www.fda.gov/MedicalDevices/default.htm","linkText","Medical Devices",1,"select-pub-link"],["href","http://www.fda.gov/BiologicsBloodVaccines/default.htm","linkText","Vaccines, Blood & Biologics",1,"select-pub-link"],["ctgIntersectionObserver","","id","drug-action-packages",1,"usa-prose","usa-paragraph"],["href","https://www.accessdata.fda.gov/scripts/cder/daf/index.cfm","linkText","Drugs@FDA",1,"select-pub-link"],["href","http://www.fda.gov/BiologicsBloodVaccines/BloodBloodProducts/ApprovedProducts/NewDrugApplicationsNDAs/ucm081717.htm","linkText","New Drug Applications with Supporting Documents",1,"select-pub-link"],["href","http://www.fda.gov/BiologicsBloodVaccines/ucm133705.htm","linkText","Licensed Biological Products with Supporting Documents",1,"select-pub-link"],["ctgIntersectionObserver","","id","device-approval-packages",1,"usa-prose","usa-paragraph"],["href","http://www.accessdata.fda.gov/scripts/cdrh/cfdocs/cfPMA/pma.cfm","linkText","Premarket Approval (PMA)",1,"select-pub-link"],["href","http://www.fda.gov/BiologicsBloodVaccines/BloodBloodProducts/ApprovedProducts/PremarketApprovalsPMAs/ucm089793.htm","linkText","Premarket Approvals and Humanitarian Device Exemptions with Supporting Documents",1,"select-pub-link"],["href","http://www.accessdata.fda.gov/scripts/cdrh/cfdocs/cfPMN/pmn.cfm","linkText","510(k) Premarket Notification",1,"select-pub-link"],["href","http://www.fda.gov/BiologicsBloodVaccines/BloodBloodProducts/ApprovedProducts/SubstantiallyEquivalent510kDeviceInformation/ucm063708.htm","linkText","Cleared 510(k) Submissions with Supporting Documents",1,"select-pub-link"],["ctgIntersectionObserver","","id","drug-device-safety-info",1,"usa-prose","usa-paragraph"],["href","http://www.fda.gov/Drugs/DrugSafety/ucm199082.htm","linkText","Drug Safety Communications",1,"select-pub-link"],["href","http://www.fda.gov/Safety/MedWatch/default.htm","linkText","MedWatch: The FDA Safety Information and Adverse Event Reporting Program",1,"select-pub-link"],["href","http://www.fda.gov/Drugs/DrugSafety/PostmarketDrugSafetyInformationforPatientsandProviders/default.htm","linkText","Postmarket Drug Safety Information for Patients and Providers",1,"select-pub-link"],["href","http://www.fda.gov/MedicalDevices/Safety/default.htm","linkText","Medical Device Safety",1,"select-pub-link"],["href","https://www.fda.gov/vaccines-blood-biologics/safety-availability-biologics","linkText","Safety & Availability (Biologics)",1,"select-pub-link"],["ctgIntersectionObserver","","id","other-relevant-info",1,"usa-prose","usa-paragraph"],["href","http://www.fda.gov/AdvisoryCommittees/CommitteesMeetingMaterials/default.htm","linkText","Committees & Meeting Materials",1,"select-pub-link"],["href","http://www.fda.gov/Drugs/DevelopmentApprovalProcess/DevelopmentResources/ucm049872.htm","linkText","Medical, Statistical, and Clinical Pharmacology Reviews of Pediatric Studies Conducted under Section 505A and 505B of the Federal Food, Drug, and Cosmetic Act (the Act), as amended by the FDA Amendments Act of 2007 (FDAAA)",1,"select-pub-link"],["href","https://www.fda.gov/drugs/development-resources/summaries-medical-and-clinical-pharmacology-reviews-pediatric-studies-availability","linkText","Summaries of Medical and Clinical Pharmacology Reviews",1,"select-pub-link"],["href","http://www.accessdata.fda.gov/scripts/sda/sdNavigation.cfm?sd=labelingdatabase","linkText","New Pediatric Labeling Information Database",1,"select-pub-link"],["href","http://www.fda.gov/AboutFDA/CentersOffices/OfficeofMedicalProductsandTobacco/CBER/ucm122938.htm","linkText","Biologics PREA Reviews and Labeling Changes",1,"select-pub-link"],["href","https://www.fda.gov/science-research/fdas-role-clinicaltrialsgov-information/pre-notices-potential-noncompliance","linkText","Pre-Notices for Potential Noncompliance",1,"select-pub-link"],["href","https://www.fda.gov/science-research/fdas-role-clinicaltrialsgov-information/clinicaltrialsgov-notices-noncompliance-and-civil-money-penalty-actions","linkText","ClinicalTrials.gov - Notices of Noncompliance and Civil Money Penalty Actions",1,"select-pub-link"],["href","https://www.fda.gov/drugs/news-events-human-drugs/clinicaltrialsgov-three-part-series","linkText","ClinicalTrials.gov - a Three-Part [Video] Series",1,"select-pub-link"],["href","https://www.youtube.com/watch?v=Seu8UtbV0QI","linkText","Part 1: Meeting Transparency and Reporting Requirements",1,"select-pub-link"],["href","https://www.youtube.com/watch?v=FiU89KN86nk","linkText","Part 2: Definitions, Laws, and Regulations",1,"select-pub-link"],["href","https://www.youtube.com/watch?v=wsC2RXSpKgc","linkText","Part 3: CDER's Compliance and Enforcement Activities",1,"select-pub-link"],[1,"margin-top-205"]],template:function(i,r){if(i&1&&(o(0,"ctg-side-nav-layout",1),d(1,"ctg-info-page-layout",2),l(),k(2,Ol,101,0,"ng-template",null,0,le)),i&2){let a=Z(3);v("navItems",r.navItems)("headerTitle",r.headerTitle),f(),v("infoData",G(3,Ml,a))}},dependencies:[ye,yi,bi,_i],styles:["[_nghost-%COMP%]{display:block}"]})}}return n})();var Bl=n=>({"min-width":n});function Fl(n,t){n&1&&(o(0,"span"),c(1,"*"),l())}function Dl(n,t){n&1&&(o(0,"p"),c(1,"* The cited violation includes failure to register a clinical trial, and therefore, no study record exists for this trial."),l())}function Il(n,t){if(n&1&&(o(0,"th",8),c(1),l()),n&2){let e=t.$implicit;v("ngStyle",G(2,Bl,e.width)),f(),z(e.name)}}var Lr=(()=>{class n{constructor(){this.columns=[{name:"Trial Title",width:"170px"},{name:"Responsible Party",width:"170px"},{name:"Unique Protocol ID number",width:"148px"},{name:"Date Available on Clinicaltrials.gov",width:"158px"},{name:"Date Issued by FDA",width:"140px"},{name:"Notice Type",width:"152px"},{name:"FDAAA 801 Notice",width:"211px"}],this.violations=[],y(_e).setTitle(se.violations_without_nct.title)}ngOnInit(){}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-fdaaa-violations-without-nct"]],decls:23,vars:2,consts:[[1,"header"],["term","fdaaa 801 violations","textToDisplay","FDAAA 801 Violations"],["term","nct number","textToDisplay","NCT Numbers"],[1,"fdaaa-content","grid-container-widescreen"],[1,"more-info-text"],["href","https://www.fda.gov/science-research/fdas-role-clinicaltrialsgov-information/clinicaltrialsgov-notices-noncompliance-and-civil-money-penalty-actions","linkText","Notices of Noncompliance [FDA]"],[1,"scroll-bar-global","overflow-x-auto"],[1,"usa-table","width-full"],[3,"ngStyle"],["colspan","7",1,"no-data"]],template:function(i,r){i&1&&(o(0,"div",0)(1,"h1"),c(2,"Information on "),o(3,"span"),d(4,"ctg-glossary-ref",1),l(),c(5," for Trials Without "),o(6,"span"),d(7,"ctg-glossary-ref",2),l(),k(8,Fl,2,0,"span"),l()(),o(9,"div",3),k(10,Dl,2,0,"p"),o(11,"div",4),d(12,"ctg-external-link-ref",5),l(),o(13,"div",6)(14,"table",7)(15,"thead")(16,"tr"),oe(17,Il,2,4,"th",8,ve),l()(),o(19,"tbody")(20,"tr")(21,"td",9),c(22,"No trials listed currently"),l()()()()()()),i&2&&(f(8),T(r.violations.length?8:-1),f(2),T(r.violations.length?10:-1),f(7),ae(r.columns))},dependencies:[hi,ye,Ln],styles:[".header[_ngcontent-%COMP%]{background:var(--ctgl-color-blue-5);padding:0}@media (min-width: 64rem){.header[_ngcontent-%COMP%]{padding:2rem 0}}h1[_ngcontent-%COMP%]{color:var(--ctgl-color-primary-darker);font-size:22px;margin:0}@media (min-width: 64rem){h1[_ngcontent-%COMP%]{font-size:42px}}h1[_ngcontent-%COMP%]{padding:1rem}@media (min-width: 64rem){h1[_ngcontent-%COMP%]{margin:auto;max-width:40rem;padding:1.5rem 0}}.fdaaa-content[_ngcontent-%COMP%]{padding:8px 16px 24px}@media (min-width: 64rem){.fdaaa-content[_ngcontent-%COMP%]{padding:1.5rem 2rem}}.fdaaa-content[_ngcontent-%COMP%]   .more-info-text[_ngcontent-%COMP%]{color:var(--ctgl-color-primary);font-weight:700;font-size:14px;text-align:left}@media (min-width: 64rem){.fdaaa-content[_ngcontent-%COMP%]   .more-info-text[_ngcontent-%COMP%]{font-size:16px}}.fdaaa-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border:.4px solid var(--ctgl-color-base-lighter);background-color:var(--ctgl-color-primary);color:#fff;font-size:14px}@media (min-width: 64rem){.fdaaa-content[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-size:16px}}.fdaaa-content[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border:.4px solid var(--ctgl-color-base-lighter);vertical-align:baseline}.fdaaa-content[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.no-data[_ngcontent-%COMP%]{color:var(--ctgl-color-base);font-size:14px}@media (min-width: 64rem){.fdaaa-content[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.no-data[_ngcontent-%COMP%]{font-size:16px}}"]})}}return n})();var Ur=(()=>{class n{constructor(){y(_e).setTitle(se.site_map.title)}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=S({type:n,selectors:[["ctg-site-map"]],decls:137,vars:0,consts:[[1,"header"],[1,"content","usa-paragraph","usa-prose"],["link","/find-studies","linkText","Find Studies"],["link","/expert-search","linkText","Expert Search"],["link","/find-studies/how-to-search","linkText","How to Search"],["link","/find-studies/how-to-use-search-results","linkText","How to Use Search Results"],["link","/find-studies/how-to-search-for-studies-with-results","linkText","How to Search for Studies with Results"],["link","/find-studies/constructing-complex-search-queries/","linkText","Constructing Complex Search Queries"],["link","/find-studies/rss","linkText","RSS Feeds"],["link","/study-basics","linkText","Study Basics"],["link","/study-basics/learn-about-studies","linkText","Learn About Studies"],["link","/study-basics/how-to-read-study-record","linkText","How to Read a Study Record"],["link","/study-basics/how-to-read-study-results","linkText","How to Read Study Results"],["link","/study-basics/glossary","linkText","Glossary"],["link","/study-basics/patient-resources","linkText","Patient Resources"],["link","/submit-studies","linkText","Submit Studies"],["link","/submit-studies/prs-accounts","linkText","PRS Accounts"],["link","/submit-studies/prs-accounts/how-to-apply","linkText","How to Apply for a PRS Account"],["href","https://register.clinicaltrials.gov/prs/html/account-organizations.html","linkText","Organizations with PRS Accounts"],["href","https://register.clinicaltrials.gov/prs/app/template/ContactOrgAdmin.vm","linkText","Administrator Contact Request Form"],["href","https://register.clinicaltrials.gov/prs/app/template/ApplyOrgAccount.vm","linkText","Apply for a PRS Organization Account"],["href","https://register.clinicaltrials.gov/prs/app/template/ApplyIndAccount.vm","linkText","Apply for a PRS Individual Account"],["link","/submit-studies/prs-help","linkText","PRS Help Resources"],["link","/submit-studies/prs-help/how-register-study","linkText","How to Register Your Study"],["link","/submit-studies/prs-help/how-edit-record","linkText","How to Edit Your Study Record"],["link","/submit-studies/prs-help/how-submit-results","linkText","How to Submit Results"],["link","/submit-studies/prs-help/user-guide","linkText","PRS User's Guide"],["link","/submit-studies/prs-help/prs-guided-tutorials","linkText","PRS Guided Tutorials"],["link","/submit-studies/prs-help/protocol-registration-quality-control-review-criteria","linkText","Protocol Registration Quality Control Review Criteria"],["link","/submit-studies/prs-help/results-quality-control-review-criteria","linkText","Results Quality Control Review Criteria"],["link","/submit-studies/prs-help/major-comments","linkText","ClinicalTrials.gov Major Comments"],["link","/submit-studies/prs-help/support-training-materials","linkText","Support and Training Materials"],["link","/submit-studies/prs-help/plain-language-guide-write-brief-summary","linkText","Plain Language Guide for Brief Summary"],["link","/data-api","linkText","Data and API"],["link","/data-api/api/","linkText","ClinicalTrials.gov API"],["link","/data-api/about-api","linkText","About the API"],["link","/data-api/about-api/api-migration","linkText","API Migration Guide"],["link","/data-api/about-api/study-data-structure","linkText","Study Data Structure"],["link","/data-api/about-api/search-areas","linkText","Search Areas"],["link","/data-api/about-api/csv-download","linkText","CSV Download"],["link","/data-api/about-api/ris-download","linkText","RIS Download"],["link","/data-api/how-download-study-records","linkText","How to Download Study Records"],["link","/data-api/fhir","linkText","Learn About FHIR"],["link","/policy","linkText","Policy"],["link","/policy/faq","linkText","Frequently Asked Questions"],["link","/policy/reporting-requirements","linkText","Clinical Trial Reporting Requirements"],["link","/policy/fdaaa-801-final-rule","linkText","FDAAA 801 and the Final Rule"],["link","/policy/protocol-definitions","linkText","Protocol Registration Data Element Definitions for Interventional and Observational Studies"],["link","/policy/results-definitions","linkText","Results Data Element Definitions for Interventional and Observational Studies"],["link","/policy/expanded-access-definitions","linkText","Expanded Access Data Element Definitions"],["link","/about-site","linkText","About This Site"],["link","/about-site/news-and-updates","linkText","News and Updates"],["link","/about-site/about-ctg","linkText","About ClinicalTrials.gov"],["link","/about-site/trends-charts","linkText","Trends and Charts"],["link","/about-site/modernization","linkText","ClinicalTrials.gov Modernization"],["link","/about-site/modernization-top-questions","linkText","Modernization Top Questions"],["link","/about-site/selected-publications","linkText","Selected Publications About ClinicalTrials.gov"],["link","/about-site/release-notes","linkText","Release Notes"],["link","/about-site/planned-features","linkText","Planned Features on ClinicalTrials.gov"],["link","/about-site/disclaimer","linkText","Disclaimer"],["link","/about-site/terms-conditions","linkText","Terms and Conditions"],["link","/about-site/linking-to","linkText","Linking to This Site"],["link","/about-site/accessibility","linkText","Accessibility"],["href","https://support.nlm.nih.gov/knowledgebase/category/?id=CAT-01242&category=clinicaltrials.gov&hd_url=https%3A%2F%2Fclinicaltrials.gov","linkText","Customer Support"]],template:function(i,r){i&1&&(o(0,"div",0)(1,"h1"),c(2,"Site Map"),l()(),o(3,"div",1)(4,"h2"),d(5,"ctg-internal-link-ref",2),l(),o(6,"ul")(7,"li"),d(8,"ctg-internal-link-ref",3),l(),o(9,"li"),d(10,"ctg-internal-link-ref",4),l(),o(11,"li"),d(12,"ctg-internal-link-ref",5),l(),o(13,"li"),d(14,"ctg-internal-link-ref",6),l(),o(15,"li"),d(16,"ctg-internal-link-ref",7),l(),o(17,"li"),d(18,"ctg-internal-link-ref",8),l()(),o(19,"h2"),d(20,"ctg-internal-link-ref",9),l(),o(21,"ul")(22,"li"),d(23,"ctg-internal-link-ref",10),l(),o(24,"li"),d(25,"ctg-internal-link-ref",11),l(),o(26,"li"),d(27,"ctg-internal-link-ref",12),l(),o(28,"li"),d(29,"ctg-internal-link-ref",13),l(),o(30,"li"),d(31,"ctg-internal-link-ref",14),l()(),o(32,"h2"),d(33,"ctg-internal-link-ref",15),l(),o(34,"ul")(35,"li"),d(36,"ctg-internal-link-ref",16),o(37,"ul")(38,"li"),d(39,"ctg-internal-link-ref",17),l(),o(40,"li"),d(41,"ctg-external-link-ref",18),l(),o(42,"li"),d(43,"ctg-external-link-ref",19),l(),o(44,"li"),d(45,"ctg-external-link-ref",20),l(),o(46,"li"),d(47,"ctg-external-link-ref",21),l()()(),o(48,"li"),d(49,"ctg-internal-link-ref",22),o(50,"ul")(51,"li"),d(52,"ctg-internal-link-ref",23),l(),o(53,"li"),d(54,"ctg-internal-link-ref",24),l(),o(55,"li"),d(56,"ctg-internal-link-ref",25),l(),o(57,"li"),d(58,"ctg-internal-link-ref",26),l(),o(59,"li"),d(60,"ctg-internal-link-ref",27),l(),o(61,"li"),d(62,"ctg-internal-link-ref",28),l(),o(63,"li"),d(64,"ctg-internal-link-ref",29),l(),o(65,"li"),d(66,"ctg-internal-link-ref",30),l(),o(67,"li"),d(68,"ctg-internal-link-ref",31),l(),o(69,"li"),d(70,"ctg-internal-link-ref",32),l()()()(),o(71,"h2"),d(72,"ctg-internal-link-ref",33),l(),o(73,"ul")(74,"li"),d(75,"ctg-internal-link-ref",34),l(),o(76,"li"),d(77,"ctg-internal-link-ref",35),o(78,"ul")(79,"li"),d(80,"ctg-internal-link-ref",36),l(),o(81,"li"),d(82,"ctg-internal-link-ref",37),l(),o(83,"li"),d(84,"ctg-internal-link-ref",38),l(),o(85,"li"),d(86,"ctg-internal-link-ref",39),l(),o(87,"li"),d(88,"ctg-internal-link-ref",40),l()()(),o(89,"li"),d(90,"ctg-internal-link-ref",41),l(),o(91,"li"),d(92,"ctg-internal-link-ref",42),l()(),o(93,"h2"),d(94,"ctg-internal-link-ref",43),l(),o(95,"ul")(96,"li"),d(97,"ctg-internal-link-ref",44),l(),o(98,"li"),d(99,"ctg-internal-link-ref",45),l(),o(100,"li"),d(101,"ctg-internal-link-ref",46),l(),o(102,"li"),d(103,"ctg-internal-link-ref",47),l(),o(104,"li"),d(105,"ctg-internal-link-ref",48),l(),o(106,"li"),d(107,"ctg-internal-link-ref",49),l()(),o(108,"h2"),d(109,"ctg-internal-link-ref",50),l(),o(110,"ul")(111,"li"),d(112,"ctg-internal-link-ref",51),l(),o(113,"li"),d(114,"ctg-internal-link-ref",52),l(),o(115,"li"),d(116,"ctg-internal-link-ref",53),l(),o(117,"li"),d(118,"ctg-internal-link-ref",54),l(),o(119,"li"),d(120,"ctg-internal-link-ref",55),l(),o(121,"li"),d(122,"ctg-internal-link-ref",56),l(),o(123,"li"),d(124,"ctg-internal-link-ref",57),l(),o(125,"li"),d(126,"ctg-internal-link-ref",58),l(),o(127,"li"),d(128,"ctg-internal-link-ref",59),l(),o(129,"li"),d(130,"ctg-internal-link-ref",60),l(),o(131,"li"),d(132,"ctg-internal-link-ref",61),l(),o(133,"li"),d(134,"ctg-internal-link-ref",62),l()(),o(135,"h2"),d(136,"ctg-external-link-ref",63),l()())},dependencies:[Pt,ye],styles:[".header[_ngcontent-%COMP%]{background:var(--ctgl-color-blue-5);padding:0}@media (min-width: 64rem){.header[_ngcontent-%COMP%]{padding:2rem 0}}h1[_ngcontent-%COMP%]{color:var(--ctgl-color-primary-dark);font-size:22px;margin:0;padding:1rem}@media (min-width: 64rem){h1[_ngcontent-%COMP%]{font-size:42px;margin:auto;max-width:41rem;padding:1.5rem 0}}p[_ngcontent-%COMP%]:first-child{margin-top:.25rem}p[_ngcontent-%COMP%]:last-child, ul[_ngcontent-%COMP%]:last-child{margin-bottom:.25rem}p[_ngcontent-%COMP%] + ul[_ngcontent-%COMP%]{margin-top:.25rem}ctg-enlarge-image[_ngcontent-%COMP%]{margin-top:1rem}h2[_ngcontent-%COMP%]{margin-bottom:0!important;max-width:68ex}h3[_ngcontent-%COMP%]{font-size:18px!important;line-height:1.35!important;margin-bottom:0!important;max-width:68ex}section[_ngcontent-%COMP%]{margin-top:2rem!important;margin-bottom:2rem!important}section[_ngcontent-%COMP%]:first-child{margin-top:0!important}section[_ngcontent-%COMP%]:last-child{margin-bottom:0!important}.download-button[_ngcontent-%COMP%]{border-radius:4px;border:2px solid var(--ctgl-color-primary-darkest);color:var(--ctgl-color-primary-darkest);gap:4px;padding:2px;margin-bottom:-5px}.overflow-table[_ngcontent-%COMP%]{overflow-x:auto}.summary-box[_ngcontent-%COMP%]{border-radius:8px;border:1px solid var(--ctgl-color-blue-20);background:var(--ctgl-color-blue-5);padding:16px 24px}",".content[_ngcontent-%COMP%]{padding:1rem}@media (min-width: 64rem){.content[_ngcontent-%COMP%]{margin:auto;max-width:41rem;padding:1.5rem 0}}.content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin-top:2rem}"]})}}return n})();var Hr=[{path:"search",loadChildren:()=>import("./chunk-Z3M2E27D.js"),providers:[ft(xi)]},{path:"expert-search",loadComponent:()=>import("./chunk-SLV4B52R.js").then(n=>n.ExpertSearchPageComponent)},{path:"study",loadChildren:()=>import("./chunk-JK272RUH.js")},...Re.reviewEnabled?[{path:"review",loadChildren:()=>import("./chunk-QPDBK73C.js")}]:[],{path:"find-studies",loadChildren:()=>import("./chunk-XRJVW3J5.js")},{path:"study-basics",loadChildren:()=>import("./chunk-W6GLW62P.js")},{path:"submit-studies",loadChildren:()=>import("./chunk-PJWUWWGH.js")},{path:"data-api",loadChildren:()=>import("./chunk-NIEH4JZD.js")},{path:"policy",loadChildren:()=>import("./chunk-5WJ5URLH.js")},{path:"about-site",loadChildren:()=>import("./chunk-3XEEAQGW.js")},{path:"fda-links",component:Nr},{path:"site-map",component:Ur},{path:"about",redirectTo:"about-site/about-ctg"},{path:"selected-publications",redirectTo:"about-site/selected-publications"},{path:"terms-conditions",redirectTo:"about-site/terms-conditions"},{path:"full-disclaimer",redirectTo:"about-site/disclaimer"},{path:"release-notes",redirectTo:"about-site/release-notes"},{path:"learn-about-api",redirectTo:"data-api/learn-about-api"},{path:"study-data-structure",redirectTo:"data-api/about-api/study-data-structure"},{path:"search-areas",redirectTo:"data-api/about-api/search-areas"},{path:"csv-download",redirectTo:"data-api/about-api/csv-download"},{path:"api-migration",redirectTo:"data-api/about-api/api-migration"},{path:"glossary",redirectTo:"study-basics/glossary"},{path:"about-studies",redirectTo:"study-basics/learn-about-studies"},{path:"violation-without-nct",redirectTo:"study-basics/violations-without-nct"},{path:"patient-resources",redirectTo:"study-basics/patient-resources"},{path:"data-about-studies/learn-about-api",redirectTo:"/data-api/api"},{path:"data-about-studies/study-data-structure",redirectTo:"/data-api/about-api/study-data-structure"},{path:"data-about-studies/search-areas",redirectTo:"/data-api/about-api/search-areas"},{path:"data-about-studies/csv-download",redirectTo:"/data-api/about-api/csv-download"},{path:"data-about-studies/api-migration",redirectTo:"/data-api/about-api/api-migration"},{path:"data-about-studies/fhir",redirectTo:"/data-api/fhir"},{path:"prs-info/protocol-definitions",redirectTo:"policy/protocol-definitions"},{path:"prs-info/results-definitions",redirectTo:"policy/results-definitions"},{path:"prs-info/expanded-access-definitions",redirectTo:"policy/expanded-access-definitions"},{path:"violations-without-nct",component:Lr},{path:"",component:Rr},{path:"**",component:gi}];Re.production&&void 0;Xn(Fr,{providers:[ki(),li({theme:{preset:"saga-blue",options:{darkModeSelector:".ctg-dark"}}}),ft(Yt,wt,zn),Ci(),Vn(jn()),oi(Hr,ai({anchorScrolling:"enabled",scrollPositionRestoration:"enabled"}))]}).catch(n=>console.error(n));
