
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"8",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":false},{"function":"__c","vtp_value":"google.com.tw"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_cross_domain","priority":15,"tag_id":110},{"function":"__ogt_ip_mark","priority":5,"vtp_instanceOrder":0,"vtp_paramValue":"internal","vtp_ruleResult":["macro",1],"tag_id":105},{"function":"__ogt_ip_mark","priority":5,"vtp_instanceOrder":1,"vtp_paramValue":"internal","vtp_ruleResult":["macro",2],"tag_id":107},{"function":"__ogt_ip_mark","priority":5,"vtp_instanceOrder":2,"vtp_paramValue":"internal","vtp_ruleResult":["macro",3],"tag_id":108},{"function":"__ogt_ip_mark","priority":5,"vtp_instanceOrder":3,"vtp_paramValue":"internal","vtp_ruleResult":["macro",4],"tag_id":109},{"function":"__ogt_dma","priority":5,"vtp_delegationMode":"ON","vtp_dmaDefault":"DENIED","tag_id":111},{"function":"__ogt_1p_data_v2","priority":5,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":113},{"function":"__ccd_ga_first","priority":4,"vtp_instanceDestinationId":"G-DP2X732JSX","tag_id":118},{"function":"__set_product_settings","priority":3,"vtp_instanceDestinationId":"G-DP2X732JSX","vtp_foreignTldMacroResult":["macro",5],"vtp_isChinaVipRegionMacroResult":["macro",6],"tag_id":117},{"function":"__ccd_ga_regscope","priority":2,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-DP2X732JSX","tag_id":116},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-DP2X732JSX","tag_id":115},{"function":"__gct","vtp_trackingId":"G-DP2X732JSX","vtp_sessionDuration":0,"tag_id":102},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-DP2X732JSX","tag_id":114}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init_consent"}],
  "rules":[[["if",0],["add",11]],[["if",1],["add",1,2,3,4,0,6,12,10,9,8,7]],[["if",2],["add",5]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"M"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"O"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"P"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"V"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"W"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EE"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AA"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CQ"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AB"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_cross_domain",[46,"a"],[52,"b",[15,"__module_convertDomainConditions"]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[22,[17,[15,"a"],"rules"],[46,[53,[41,"e"],[3,"e",[30,["c"],[7]]],[65,"f",[15,"e"],[46,[53,[41,"g"],[3,"g",[17,[15,"a"],"rules"]],["d",[15,"f"],"cross_domain_conditions",[17,[15,"a"],"rules"]],[22,[17,[15,"g"],"length"],[46,[53,[3,"g",[2,[15,"b"],"A",[7,[15,"g"]]]],["d",[15,"f"],"linker",[8,"domains",[15,"g"],"decorate_forms",true,"accept_incoming",true,"url_position","query"]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_dma",[46,"a"],[52,"b",["require","internal.declareConsentState"]],[52,"c",["require","internal.isDmaRegion"]],[52,"d",["require","internal.setDelegatedConsentType"]],[22,[1,[20,[17,[15,"a"],"delegationMode"],"ON"],["c"]],[46,[53,["d","ad_user_data","ad_storage"]]]],[22,[20,[17,[15,"a"],"dmaDefault"],"GRANTED"],[46,[53,["b",[8,"ad_user_data","granted"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_ip_mark",[46,"a"],[52,"b",["require","internal.appendRemoteConfigParameter"]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.sortRemoteConfigParameters"]],[52,"e",[8,"instance_order",[17,[15,"a"],"instanceOrder"],"traffic_type",[17,[15,"a"],"paramValue"],"rule_result",[17,[15,"a"],"ruleResult"]]],[41,"f"],[3,"f",[30,["c"],[7]]],[65,"g",[15,"f"],[46,[53,["b",[15,"g"],"internal_traffic_results",[15,"e"]],["d",[15,"g"],"internal_traffic_results",[8,"sortKey","instance_order"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",47],[52,"h",42],[52,"i",43],[52,"j",44],[52,"k",45],[52,"l",46],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",199],[52,"w",212],[36,[8,"DW",[15,"r"],"Y",[15,"b"],"AA",[15,"c"],"AB",[15,"d"],"AC",[15,"e"],"AI",[15,"f"],"AL",[15,"h"],"AM",[15,"i"],"AN",[15,"j"],"AO",[15,"k"],"AP",[15,"l"],"AK",[15,"g"],"EJ",[15,"u"],"AU",[15,"m"],"EA",[15,"s"],"EE",[15,"t"],"ET",[15,"v"],"CD",[15,"n"],"CQ",[15,"o"],"DD",[15,"p"],"FG",[15,"w"],"DO",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_convertDomainConditions",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"e",[46,"g"],[36,[2,[15,"g"],"replace",[7,[15,"d"],"\\$&"]]]],[50,"f",[46,"g"],[52,"h",[7]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"g"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[41,"j"],[22,[20,["c",[16,[15,"g"],[15,"i"]]],"object"],[46,[53,[52,"l",[16,[16,[15,"g"],[15,"i"]],"matchType"]],[52,"m",[16,[16,[15,"g"],[15,"i"]],"matchValue"]],[38,[15,"l"],[46,"BEGINS_WITH","ENDS_WITH","EQUALS","REGEX","CONTAINS"],[46,[5,[46,[3,"j",[0,"^",["e",[15,"m"]]]],[4]]],[5,[46,[3,"j",[0,["e",[15,"m"]],"$"]],[4]]],[5,[46,[3,"j",[0,[0,"^",["e",[15,"m"]]],"$"]],[4]]],[5,[46,[3,"j",[15,"m"]],[4]]],[5,[46]],[9,[46,[3,"j",["e",[15,"m"]]],[4]]]]]]],[46,[53,[3,"j",[16,[15,"g"],[15,"i"]]]]]],[41,"k"],[22,[15,"j"],[46,[53,[3,"k",["b",[15,"j"]]]]]],[22,[15,"k"],[46,[53,[2,[15,"h"],"push",[7,[15,"k"]]]]]]]]]],[36,[15,"h"]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","getType"]],[52,"d",["b","[.*+\\-?^${}()|[\\]\\\\]","g"]],[36,[8,"A",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"m","n","o"],[50,"t",[46,"v"],[52,"w",[16,[15,"i"],[15,"v"]]],[22,[28,[15,"w"]],[46,[36]]],[53,[41,"x"],[3,"x",0],[63,[7,"x"],[23,[15,"x"],[17,[15,"w"],"length"]],[33,[15,"x"],[3,"x",[0,[15,"x"],1]]],[46,[53,[52,"y",[16,[15,"w"],[15,"x"]]],["q",[15,"p"],[17,[15,"y"],"name"],[17,[15,"y"],"value"]]]]]]],[50,"u",[46,"v"],[22,[30,[28,[15,"r"]],[21,[17,[15,"r"],"length"],2]],[46,[53,[36,false]]]],[41,"w"],[3,"w",[16,[15,"v"],[15,"s"]]],[22,[20,[15,"w"],[44]],[46,[53,[3,"w",[16,[15,"v"],[15,"r"]]]]]],[36,[28,[28,[15,"w"]]]]],[22,[28,[15,"n"]],[46,[36]]],[52,"p",[30,[17,[15,"m"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"q",["h",[15,"f"],[15,"o"]]],[52,"r",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"o"]]],["$0"]]],[52,"s",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"o"]]],["$0"]]],[53,[41,"v"],[3,"v",0],[63,[7,"v"],[23,[15,"v"],[17,[15,"n"],"length"]],[33,[15,"v"],[3,"v",[0,[15,"v"],1]]],[46,[53,[52,"w",[16,[15,"n"],[15,"v"]]],[22,[30,[17,[15,"w"],"disallowAllRegions"],["u",[17,[15,"w"],"disallowedRegions"]]],[46,[53,["t",[17,[15,"w"],"redactFieldGroup"]]]]]]]]]],[50,"k",[46,"m"],[52,"n",[8]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[53,[6]]]],[43,[15,"n"],[15,"q"],true]]]]],[36,[15,"n"]]],[50,"l",[46,"m"],[22,[28,[17,[15,"m"],"settingsTable"]],[46,[36,[7]]]],[52,"n",[8]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[17,[15,"m"],"settingsTable"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[17,[15,"m"],"settingsTable"],[15,"o"]]],[52,"q",[17,[15,"p"],"redactFieldGroup"]],[22,[28,[16,[15,"i"],[15,"q"]]],[46,[6]]],[43,[15,"n"],[15,"q"],[8,"redactFieldGroup",[15,"q"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"r",[16,[15,"n"],[15,"q"]]],[22,[17,[15,"p"],"disallowAllRegions"],[46,[53,[43,[15,"r"],"disallowAllRegions",true],[6]]]],[43,[15,"r"],"disallowedRegions",["k",[17,[15,"p"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"n"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[52,"i",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"j"],"B",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__ogt_cross_domain":{"2":true,"5":true}
,
"__ogt_dma":{"2":true,"5":true}
,
"__ogt_ip_mark":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"8"}
,"permissions":{
"__c":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__ogt_cross_domain":{}
,
"__ogt_dma":{"access_consent":{"consentTypes":[{"consentType":"ad_user_data","read":false,"write":true},{"consentType":"ad_storage","read":true,"write":false}]}}
,
"__ogt_ip_mark":{}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_conversion_marking"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__ogt_cross_domain"
,
"__ogt_dma"
,
"__ogt_ip_mark"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ca(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if(typeof Object.setPrototypeOf=="function")ka=Object.setPrototypeOf;else{var na;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;na=pa.a;break a}catch(a){}na=!1}ka=na?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=ka,ra=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Dq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:sa(l(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.Dq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Br=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map=new Map;this.C=new Set};k=Ca.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.ql=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Da=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ca.prototype.wa=function(){return Da(this,1)};Ca.prototype.Zb=function(){return Da(this,2)};Ca.prototype.Ib=function(){return Da(this,3)};var Ea=function(){this.map={};this.C={}};k=Ea.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.ql=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Fa=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ea.prototype.wa=function(){return Fa(this,1)};Ea.prototype.Zb=function(){return Fa(this,2)};Ea.prototype.Ib=function(){return Fa(this,3)};var Ga=function(){};Ga.prototype.reset=function(){};var Ha=[],Ia={};function Ja(a){return Ha[a]===void 0?!1:Ha[a]};var Ka=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.tb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ja(15)?new Ca:new Ea};k=Ka.prototype;k.add=function(a,b){this.tb||this.values.set(a,b)};k.nh=function(a,b){this.tb||this.values.ql(a,b)};k.set=function(a,b){this.tb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ka(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Kd(this.N);return a};k.Bd=function(){return this.P};k.Nb=function(a){this.C=a};k.am=function(){return this.C};k.Vc=function(a){this.H=a};k.bj=function(){return this.H};k.Ua=function(){this.tb=!0};k.Kd=function(a){this.N=a};k.sb=function(){return this.N};var La=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.tb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.nh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.tb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.tb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new La(this.ba,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Kd(this.P);return a};k.Bd=function(){return this.ba};k.Nb=function(a){this.H=a};k.am=function(){return this.H};k.Vc=function(a){this.N=a};k.bj=function(){return this.N};k.Ua=function(){this.tb=!0};k.Kd=function(a){this.P=a};k.sb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.om=a;this.Sl=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=new Map;function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ra(a,e.value),c instanceof Ba);e=d.next());return c}function Ra(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f,g=String(d);f=Ja(17)?Pa.has(g)?Pa.get(g):a.get(g):a.get(g);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(m){var h=a.am();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Sa=function(){this.H=new Ga;this.C=Ja(16)?new La(this.H):new Ka(this.H)};k=Sa.prototype;k.Bd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Cj([a].concat(ua(ya.apply(1,arguments))))};k.Cj=function(){for(var a,b=l(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ra(this.C,c.value);return a};
k.fo=function(a){var b=ya.apply(1,arguments),c=this.C.rb();c.Kd(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ra(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ta=function(){this.Ca=!1;this.aa=new Ea};k=Ta.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};function Ua(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Xa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,Za;function $a(a){Wa=Wa||Xa();Za=Za||Ua();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function ab(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Za[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Xa();Za=Za||Ua();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var bb={};function cb(a,b){bb[a]=bb[a]||[];bb[a][b]=!0}function db(){bb.GTAG_EVENT_FEATURE_CHANNEL=eb}function fb(a){var b=bb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return $a(c.join("")).replace(/\.+$/,"")}function gb(){for(var a=[],b=bb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function hb(){}function ib(a){return typeof a==="function"}function jb(a){return typeof a==="string"}function kb(a){return typeof a==="number"&&!isNaN(a)}function lb(a){return Array.isArray(a)?a:[a]}function mb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function nb(a,b){if(!kb(a)||!kb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function ob(a,b){for(var c=new pb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function qb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function rb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function tb(a){return Math.round(Number(a))||0}function ub(a){return"false"===String(a).toLowerCase()?!1:!!a}
function vb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function wb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function xb(){return new Date(Date.now())}function yb(){return xb().getTime()}var pb=function(){this.prefix="gtm.";this.values={}};pb.prototype.set=function(a,b){this.values[this.prefix+a]=b};pb.prototype.get=function(a){return this.values[this.prefix+a]};pb.prototype.contains=function(a){return this.get(a)!==void 0};
function zb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Ab(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Bb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Cb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Db(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Eb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Fb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Gb=/^\w{1,9}$/;function Ib(a,b){a=a||{};b=b||",";var c=[];qb(a,function(d,e){Gb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Nb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ua(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ob=globalThis.trustedTypes,Pb;function Qb(){var a=null;if(!Ob)return a;try{var b=function(c){return c};a=Ob.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Rb(){Pb===void 0&&(Pb=Qb());return Pb};var Sb=function(a){this.C=a};Sb.prototype.toString=function(){return this.C+""};function Tb(a){var b=a,c=Rb(),d=c?c.createScriptURL(b):b;return new Sb(d)}function Ub(a){if(a instanceof Sb)return a.C;throw Error("");};var Vb=wa([""]),Wb=va(["\x00"],["\\0"]),Xb=va(["\n"],["\\n"]),Yb=va(["\x00"],["\\u0000"]);function Zb(a){return a.toString().indexOf("`")===-1}Zb(function(a){return a(Vb)})||Zb(function(a){return a(Wb)})||Zb(function(a){return a(Xb)})||Zb(function(a){return a(Yb)});var $b=function(a){this.C=a};$b.prototype.toString=function(){return this.C};var bc=function(a){this.Tp=a};function cc(a){return new bc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var dc=[cc("data"),cc("http"),cc("https"),cc("mailto"),cc("ftp"),new bc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ec(a){var b;b=b===void 0?dc:b;if(a instanceof $b)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof bc&&d.Tp(a))return new $b(a)}}var hc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ic(a){var b;if(a instanceof $b)if(a instanceof $b)b=a.C;else throw Error("");else b=hc.test(a)?a:void 0;return b};function jc(a,b){var c=ic(b);c!==void 0&&(a.action=c)};function kc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var lc=function(a){this.C=a};lc.prototype.toString=function(){return this.C+""};var nc=function(){this.C=mc[0].toLowerCase()};nc.prototype.toString=function(){return this.C};function oc(a,b){var c=[new nc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof nc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var pc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function qc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,rc=window.history,z=document,sc=navigator;function tc(){var a;try{a=sc.serviceWorker}catch(b){return}return a}var uc=z.currentScript,vc=uc&&uc.src;function wc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function xc(a){return(sc.userAgent||"").indexOf(a)!==-1}function yc(){return xc("Firefox")||xc("FxiOS")}function zc(){return(xc("GSA")||xc("GoogleApp"))&&(xc("iPhone")||xc("iPad"))}function Ac(){return xc("Edg/")||xc("EdgA/")||xc("EdgiOS/")}
var Bc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Cc={onload:1,src:1,width:1,height:1,style:1};function Dc(a,b,c){b&&qb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Ec(a,b,c,d,e){var f=z.createElement("script");Dc(f,d,Bc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Tb(qc(a));f.src=Ub(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Fc(){if(vc){var a=vc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Gc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Dc(g,c,Cc);d&&qb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Hc(a,b,c,d){return Ic(a,b,c,d)}function Jc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Kc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Lc(a){x.setTimeout(a,0)}function Mc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Nc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Oc(a){var b=z.createElement("div"),c=b,d,e=qc("A<div>"+a+"</div>"),f=Rb(),g=f?f.createHTML(e):e;d=new lc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof lc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Pc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Qc(a,b,c){var d;try{d=sc.sendBeacon&&sc.sendBeacon(a)}catch(e){cb("TAGGING",15)}d?b==null||b():Ic(a,b,c)}function Rc(a,b){try{return sc.sendBeacon(a,b)}catch(c){cb("TAGGING",15)}return!1}var Sc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Uc(a,b,c,d,e){if(Vc()){var f=Object.assign({},Sc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),!1;if(b){var h=
Rc(a,b);h?d==null||d():e==null||e();return h}Wc(a,d,e);return!0}function Vc(){return typeof x.fetch==="function"}function Xc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Yc(){var a=x.performance;if(a&&ib(a.now))return a.now()}
function Zc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function $c(){return x.performance||void 0}function ad(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Ic=function(a,b,c,d){var e=new Image(1,1);Dc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Wc=Qc;function bd(a,b){return this.evaluate(a)&&this.evaluate(b)}function cd(a,b){return this.evaluate(a)===this.evaluate(b)}function dd(a,b){return this.evaluate(a)||this.evaluate(b)}function ed(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function fd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function gd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ta&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var hd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,id=function(a){if(a==null)return String(a);var b=hd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},jd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},kd=function(a){if(!a||id(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!jd(a,"constructor")&&!jd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
jd(a,b)},ld=function(a,b){var c=b||(id(a)=="array"?[]:{}),d;for(d in a)if(jd(a,d)){var e=a[d];id(e)=="array"?(id(c[d])!="array"&&(c[d]=[]),c[d]=ld(e,c[d])):kd(e)?(kd(c[d])||(c[d]={}),c[d]=ld(e,c[d])):c[d]=e}return c};function md(a){if(a==void 0||Array.isArray(a)||kd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function nd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var od=function(a){a=a===void 0?[]:a;this.aa=new Ea;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(nd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=od.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof od?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!nd(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else nd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():nd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Zb=function(){for(var a=this.aa.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Ib=function(){for(var a=this.aa.Ib(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){nd(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new od(this.values.splice(a)):new od(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};k.has=function(a){return nd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.tb=function(){return this.Ca};
function pd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var qd=function(a,b){this.functionName=a;this.ye=b;this.aa=new Ea;this.Ca=!1};k=qd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new od(this.wa())};k.invoke=function(a){return this.ye.call.apply(this.ye,[new rd(this,a)].concat(ua(ya.apply(1,arguments))))};k.Lb=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};
k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};var sd=function(a,b){qd.call(this,a,b)};ra(sd,qd);var td=function(a,b){qd.call(this,a,b)};ra(td,qd);var rd=function(a,b){this.ye=a;this.K=b};
rd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ra(b,a):a};rd.prototype.getName=function(){return this.ye.getName()};rd.prototype.Bd=function(){return this.K.Bd()};var ud=function(){this.map=new Map};ud.prototype.set=function(a,b){this.map.set(a,b)};ud.prototype.get=function(a){return this.map.get(a)};var vd=function(){this.keys=[];this.values=[]};vd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};vd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function wd(){try{return Map?new ud:new vd}catch(a){return new vd}};var xd=function(a){if(a instanceof xd)return a;if(md(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};xd.prototype.getValue=function(){return this.value};xd.prototype.toString=function(){return String(this.value)};var zd=function(a){this.promise=a;this.Ca=!1;this.aa=new Ea;this.aa.set("then",yd(this));this.aa.set("catch",yd(this,!0));this.aa.set("finally",yd(this,!1,!0))};k=zd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};
var yd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new sd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof sd||(d=void 0);e instanceof sd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new xd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new zd(h)})};zd.prototype.Ua=function(){this.Ca=!0};zd.prototype.tb=function(){return this.Ca};function Ad(a,b,c){var d=wd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof od){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof zd)return g.promise.then(function(u){return Ad(u,b,1)},function(u){return Promise.reject(Ad(u,b,1))});if(g instanceof Ta){var q={};d.set(g,q);e(g,q);return q}if(g instanceof sd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(u[w],b,c);var y=new Ka(b?b.Bd():new Ga);b&&y.Kd(b.sb());return f(g.invoke.apply(g,[y].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof xd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Bd(a,b,c){var d=wd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||rb(g)){var m=new od;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(kd(g)){var p=new Ta;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new sd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Ad(this.evaluate(u[w]),b,c);return f(this.K.bj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new xd(g)};return f(a)};var Cd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof od)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new od(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new od(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new od(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=pd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new od(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=pd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var Dd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ed=new Ba("break"),Fd=new Ba("continue");function Gd(a,b){return this.evaluate(a)+this.evaluate(b)}function Hd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof od))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Ad(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Dd.hasOwnProperty(e)){var m=2;m=1;var n=Ad(f,void 0,m);return Bd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof od){if(d.has(e)){var p=d.get(String(e));if(p instanceof sd){var q=pd(f);return p.invoke.apply(p,[this.K].concat(ua(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(Cd.supportedMethods.indexOf(e)>=
0){var r=pd(f);return Cd[e].call.apply(Cd[e],[d,this.K].concat(ua(r)))}}if(d instanceof sd||d instanceof Ta||d instanceof zd){if(d.has(e)){var t=d.get(e);if(t instanceof sd){var u=pd(f);return t.invoke.apply(t,[this.K].concat(ua(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof sd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof xd&&e==="toString")return d.toString();throw Oa(Error("TypeError: Object has no '"+
e+"' property."));}function Jd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Kd(){var a=ya.apply(0,arguments),b=this.K.rb(),c=Qa(b,a);if(c instanceof Ba)return c}function Ld(){return Ed}function Md(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Nd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Od(){return Fd}function Pd(a,b){return new Ba(a,this.evaluate(b))}function Qd(a,b){for(var c=ya.apply(2,arguments),d=new od,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.K.add(a,this.evaluate(g))}function Rd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Sd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof xd,f=d instanceof xd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Td(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ud(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Vd(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(f){return f},c);if(b instanceof Ta||b instanceof zd||b instanceof od||b instanceof sd){var d=b.wa(),e=d.length;return Ud(a,function(){return e},function(f){return d[f]},c)}}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){g.set(d,h);return g},e,f)}
function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.nh(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){g.set(d,h);return g},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.nh(d,h);return m},e,f)}function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function $d(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof od)return Ud(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ce(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof od))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Ra(m,b);){var n=Qa(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Ra(p,c);m=p}}
function de(a,b){var c=ya.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof od))throw Error("Error: non-List value given for Fn argument names.");return new sd(a,function(){return function(){var f=ya.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Kd(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new od(h));var r=Qa(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function ee(a){var b=this.evaluate(a),c=this.K;if(fe&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ge(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ta||d instanceof zd||d instanceof od||d instanceof sd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:nd(e)&&(c=d[e]);else if(d instanceof xd)return;return c}function he(a,b){return this.evaluate(a)>this.evaluate(b)}function ie(a,b){return this.evaluate(a)>=this.evaluate(b)}
function je(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof xd&&(c=c.getValue());d instanceof xd&&(d=d.getValue());return c===d}function ke(a,b){return!je.call(this,a,b)}function le(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.K,d);if(e instanceof Ba)return e}var fe=!1;
function me(a,b){return this.evaluate(a)<this.evaluate(b)}function ne(a,b){return this.evaluate(a)<=this.evaluate(b)}function oe(){for(var a=new od,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function pe(){for(var a=new Ta,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function qe(a,b){return this.evaluate(a)%this.evaluate(b)}
function re(a,b){return this.evaluate(a)*this.evaluate(b)}function se(a){return-this.evaluate(a)}function te(a){return!this.evaluate(a)}function ue(a,b){return!Sd.call(this,a,b)}function ve(){return null}function we(a,b){return this.evaluate(a)||this.evaluate(b)}function xe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ye(a){return this.evaluate(a)}function ze(){return ya.apply(0,arguments)}function Ae(a){return new Ba("return",this.evaluate(a))}
function Be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof sd||d instanceof od||d instanceof Ta)&&d.set(String(e),f);return f}function Ce(a,b){return this.evaluate(a)-this.evaluate(b)}
function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function Ee(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Fe(a){var b=this.evaluate(a);return b instanceof sd?"function":typeof b}function Ge(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function He(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.K,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.K,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ie(a){return~Number(this.evaluate(a))}function Je(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Le(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Pe(){}
function Qe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Na&&h.Sl))throw h;var e=this.K.rb();a!==""&&(h instanceof Na&&(h=h.om),e.add(a,new xd(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Ba)return g}}function Re(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Sl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Te=function(){this.C=new Sa;Se(this)};Te.prototype.execute=function(a){return this.C.Cj(a)};var Se=function(a){var b=function(c,d){var e=new td(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Pa.set(f,e)};b("map",pe);b("and",bd);b("contains",ed);b("equals",cd);b("or",dd);b("startsWith",fd);b("variable",gd)};Te.prototype.Nb=function(a){this.C.Nb(a)};var Ve=function(){this.H=!1;this.C=new Sa;Ue(this);this.H=!0};Ve.prototype.execute=function(a){return We(this.C.Cj(a))};var Xe=function(a,b,c){return We(a.C.fo(b,c))};Ve.prototype.Ua=function(){this.C.Ua()};
var Ue=function(a){var b=function(c,d){var e=String(c),f=new td(e,d);f.Ua();a.C.C.set(e,f);Pa.set(e,f)};b(0,Gd);b(1,Hd);b(2,Id);b(3,Jd);b(56,Me);b(57,Je);b(58,Ie);b(59,Oe);b(60,Ke);b(61,Le);b(62,Ne);b(53,Kd);b(4,Ld);b(5,Md);b(68,Qe);b(52,Nd);b(6,Od);b(49,Pd);b(7,oe);b(8,pe);b(9,Md);b(50,Qd);b(10,Rd);b(12,Sd);b(13,Td);b(67,Re);b(51,de);b(47,Wd);b(54,Xd);b(55,Yd);b(63,ce);b(64,Zd);b(65,ae);b(66,be);b(15,ee);b(16,ge);b(17,ge);b(18,he);b(19,ie);b(20,je);b(21,ke);b(22,le);b(23,me);b(24,ne);b(25,qe);b(26,
re);b(27,se);b(28,te);b(29,ue);b(45,ve);b(30,we);b(32,xe);b(33,xe);b(34,ye);b(35,ye);b(46,ze);b(36,Ae);b(43,Be);b(37,Ce);b(38,De);b(39,Ee);b(40,Fe);b(44,Pe);b(41,Ge);b(42,He)};Ve.prototype.Bd=function(){return this.C.Bd()};Ve.prototype.Nb=function(a){this.C.Nb(a)};Ve.prototype.Vc=function(a){this.C.Vc(a)};
function We(a){if(a instanceof Ba||a instanceof sd||a instanceof od||a instanceof Ta||a instanceof zd||a instanceof xd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ye=function(a){this.message=a};function Ze(a){a.Ir=!0;return a};var $e=Ze(function(a){return typeof a==="string"});function af(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ye("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function bf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var cf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function df(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+af(e)+c}a<<=2;d||(a|=32);return c=""+af(a|b)+c}
function ef(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+df(1,1)+af(d<<2|e));var f=a.Rl,g=a.Mo,h="4"+c+(f?""+df(2,1)+af(f):"")+(g?""+df(12,1)+af(g):""),m,n=a.Dj;m=n&&cf.test(n)?""+df(3,2)+n:"";var p,q=a.zj;p=q?""+df(4,1)+af(q):"";var r;var t=a.ctid;if(t&&b){var u=df(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+af(1+y.length)+(a.fm||0)+y}}else r="";var A=a.Cq,D=a.ve,E=a.Ma,G=a.Mr,I=h+m+p+r+(A?""+df(6,1)+af(A):"")+(D?""+df(7,3)+af(D.length)+
D:"")+(E?""+df(8,3)+af(E.length)+E:"")+(G?""+df(9,3)+af(G.length)+G:""),M;var S=a.Tl;S=S===void 0?{}:S;for(var ea=[],U=l(Object.keys(S)),ta=U.next();!ta.done;ta=U.next()){var W=ta.value;ea[Number(W)]=S[W]}if(ea.length){var da=df(10,3),Y;if(ea.length===0)Y=af(0);else{for(var X=[],ma=0,ja=!1,la=0;la<ea.length;la++){ja=!0;var Va=la%6;ea[la]&&(ma|=1<<Va);Va===5&&(X.push(af(ma)),ma=0,ja=!1)}ja&&X.push(af(ma));Y=X.join("")}var Ya=Y;M=""+da+af(Ya.length)+Ya}else M="";var sb=a.qm,ac=a.sq;return I+M+(sb?""+
df(11,3)+af(sb.length)+sb:"")+(ac?""+df(13,3)+af(ac.length)+ac:"")};var ff=function(){function a(b){return{toString:function(){return b}}}return{Qm:a("consent"),Sj:a("convert_case_to"),Tj:a("convert_false_to"),Uj:a("convert_null_to"),Vj:a("convert_true_to"),Wj:a("convert_undefined_to"),Oq:a("debug_mode_metadata"),Ra:a("function"),zi:a("instance_name"),jo:a("live_only"),ko:a("malware_disabled"),METADATA:a("metadata"),no:a("original_activity_id"),jr:a("original_vendor_template_id"),ir:a("once_on_load"),mo:a("once_per_event"),sl:a("once_per_load"),lr:a("priority_override"),
qr:a("respected_consent_types"),Cl:a("setup_tags"),kh:a("tag_id"),Kl:a("teardown_tags")}}();var Cf;var Df=[],Ef=[],Ff=[],Gf=[],Hf=[],Jf,Kf,Lf;function Mf(a){Lf=Lf||a}
function Nf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Df.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Gf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ff.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Of(p[r])}Ef.push(p)}}
function Of(a){}var Pf,Qf=[],Rf=[];function Sf(a,b){var c={};c[ff.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Tf(a,b,c){try{return Kf(Uf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Uf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Vf(a[e],b,c));return d},Vf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Vf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Df[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ff.zi]);try{var m=Uf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Wf(m,{event:b,index:f,type:2,
name:h});Pf&&(d=Pf.Po(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Vf(a[n],b,c)]=Vf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Vf(a[q],b,c);Lf&&(p=p||Lf.Qp(r));d.push(r)}return Lf&&p?Lf.Uo(d):d.join("");case "escape":d=Vf(a[1],b,c);if(Lf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Lf.Rp(a))return Lf.hq(d);d=String(d);for(var t=2;t<a.length;t++)nf[a[t]]&&(d=nf[a[t]](d));return d;
case "tag":var u=a[1];if(!Gf[u])throw Error("Unable to resolve tag reference "+u+".");return{Xl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[ff.Ra]=a[1];var w=Tf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Wf=function(a,b){var c=a[ff.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Jf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Qf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Db(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Df[q];break;case 1:r=Gf[q];break;default:n="";break a}var t=r&&r[ff.zi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Rf.indexOf(c)===-1){Rf.push(c);
var y=yb();u=e(g);var A=yb()-y,D=yb();v=Cf(c,h,b);w=A-(yb()-D)}else if(e&&(u=e(g)),!e||f)v=Cf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),md(u)?(Array.isArray(u)?Array.isArray(v):kd(u)?kd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Xf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Xf,Error);Xf.prototype.getMessage=function(){return this.message};function Yf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Yf(a[c],b[c])}};function Zf(){return function(a,b){var c;var d=$f;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function $f(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)kb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function ag(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=bg(a),f=0;f<Ef.length;f++){var g=Ef[f],h=cg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Gf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function cg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function bg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Tf(Ff[c],a));return b[c]}};function dg(a,b){b[ff.Sj]&&typeof a==="string"&&(a=b[ff.Sj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ff.Uj)&&a===null&&(a=b[ff.Uj]);b.hasOwnProperty(ff.Wj)&&a===void 0&&(a=b[ff.Wj]);b.hasOwnProperty(ff.Vj)&&a===!0&&(a=b[ff.Vj]);b.hasOwnProperty(ff.Tj)&&a===!1&&(a=b[ff.Tj]);return a};var eg=function(){this.C={}},gg=function(a,b){var c=fg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function hg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Xf(c,d,g);}}
function ig(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));hg(e,b,d,g);hg(f,b,d,g)}}}};var mg=function(){var a=data.permissions||{},b=jg.ctid,c=this;this.H={};this.C=new eg;var d={},e={},f=ig(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});qb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw kg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};qb(h,function(p,q){var r=lg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Pl&&!e[p]&&(e[p]=r.Pl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw kg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},ng=function(a){return fg.H[a]||function(){}};
function lg(a,b){var c=Sf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=kg;try{return Wf(c)}catch(d){return{assert:function(e){throw new Xf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Xf(a,{},"Permission "+a+" is unknown.");}}}}function kg(a,b,c){return new Xf(a,b,c)};var og=!1;var pg={};pg.Jm=ub('');pg.fp=ub('');
var tg=function(a){var b={},c=0;qb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(qg.hasOwnProperty(e))b[qg[e]]=g;else if(rg.hasOwnProperty(e)){var h=rg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=sg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];qb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
qg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},rg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},sg=["ca",
"c2","c3","c4","c5"];function ug(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var vg=[];function wg(a){switch(a){case 1:return 0;case 38:return 12;case 53:return 1;case 54:return 2;case 52:return 6;case 211:return 17;case 75:return 3;case 103:return 13;case 197:return 14;case 203:return 15;case 114:return 11;case 116:return 4;case 209:return 16;case 135:return 8;case 136:return 5}}function xg(a,b){vg[a]=b;var c=wg(a);c!==void 0&&(Ha[c]=b)}function B(a){xg(a,!0)}
B(39);B(34);B(35);B(36);
B(56);B(145);B(153);B(144);B(120);
B(5);B(111);B(139);B(87);
B(92);B(159);B(132);
B(20);B(72);B(113);
B(154);B(116);B(143);
xg(23,!1),B(24);
Ia[1]=ug('1',6E4);Ia[3]=ug('10',1);Ia[2]=ug('',50);B(29);
yg(26,25);
B(37);B(9);
B(91);B(123);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(95);B(38);B(103);B(112);
B(63);
B(152);
B(101);B(122);B(121);
B(108);
B(134);B(31);B(22);
B(97);B(19);B(90);B(59);
B(13);
B(175);B(176);B(185);B(186);
B(187);
B(192);
B(199);B(200);B(201);function C(a){return!!vg[a]}
function yg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};
var zg=function(){this.events=[];this.C="";this.ra={};this.baseUrl="";this.N=0;this.P=this.H=!1;this.endpoint=0;C(89)&&(this.P=!0)};zg.prototype.add=function(a){return this.R(a)?(this.events.push(a),this.C=a.H,this.ra=a.ra,this.baseUrl=a.baseUrl,this.N+=a.P,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ba=a.eventId,this.ka=a.priorityId,!0):!1};zg.prototype.R=function(a){return this.events.length?this.events.length>=20||a.P+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.Ba(a):!0};zg.prototype.Ba=function(a){var b=this;if(!this.P)return this.C===a.H;var c=Object.keys(this.ra);return c.length===Object.keys(a.ra).length&&c.every(function(d){return a.ra.hasOwnProperty(d)&&String(b.ra[d])===String(a.ra[d])})};var Ag={},Bg=(Ag.uaa=!0,Ag.uab=!0,Ag.uafvl=!0,Ag.uamb=!0,Ag.uam=!0,Ag.uap=!0,Ag.uapv=!0,Ag.uaw=!0,Ag);
var Eg=function(a,b){var c=a.events;if(c.length===1)return Cg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)qb(c[f].Ld,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};qb(e,function(t,u){var v,w=-1,y=0;qb(u,function(A,D){y+=D;var E=(A.length+t.length+2)*(D-1);E>w&&(v=A,w=E)});y===c.length&&(g[t]=v)});Dg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={sj:void 0},p++){var q=[];n.sj={};qb(c[p].Ld,function(t){return function(u,
v){g[u]!==""+v&&(t.sj[u]=v)}}(n));c[p].C&&q.push(c[p].C);Dg(n.sj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Cg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Dg(a.Ld,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Dg=function(a,b){qb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Fg=function(a){var b=[];qb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Gg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.ra=a.ra;this.Ld=a.Ld;this.Zi=a.Zi;this.N=d;this.H=Fg(a.ra);this.C=Fg(a.Zi);this.P=this.C.length;if(e&&this.P>16384)throw Error("EVENT_TOO_LARGE");};
var Jg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Hg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Ig.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Db(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Ig=/^[a-z$_][\w-$]*$/i,Hg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Kg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Lg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Mg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Ng=new pb;function Og(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Ng.get(e);f||(f=new RegExp(b,d),Ng.set(e,f));return f.test(a)}catch(g){return!1}}function Pg(a,b){return String(a).indexOf(String(b))>=0}
function Qg(a,b){return String(a)===String(b)}function Rg(a,b){return Number(a)>=Number(b)}function Sg(a,b){return Number(a)<=Number(b)}function Tg(a,b){return Number(a)>Number(b)}function Ug(a,b){return Number(a)<Number(b)}function Vg(a,b){return Db(String(a),String(b))};var bh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ch={Fn:"function",PixieMap:"Object",List:"Array"};
function dh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=bh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof sd?n="Fn":m instanceof od?n="List":m instanceof Ta?n="PixieMap":m instanceof zd?n="PixiePromise":m instanceof xd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ch[n]||n)+", which does not match required type ")+
((ch[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof sd?d.push("function"):g instanceof od?d.push("Array"):g instanceof Ta?d.push("Object"):g instanceof zd?d.push("Promise"):g instanceof xd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function eh(a){return a instanceof Ta}function fh(a){return eh(a)||a===null||gh(a)}
function hh(a){return a instanceof sd}function ih(a){return hh(a)||a===null||gh(a)}function jh(a){return a instanceof od}function kh(a){return a instanceof xd}function lh(a){return typeof a==="string"}function mh(a){return lh(a)||a===null||gh(a)}function nh(a){return typeof a==="boolean"}function oh(a){return nh(a)||gh(a)}function ph(a){return nh(a)||a===null||gh(a)}function qh(a){return typeof a==="number"}function gh(a){return a===void 0};function rh(a){return""+a}
function sh(a,b){var c=[];return c};function th(a,b){var c=new sd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Ua();return c}
function uh(a,b){var c=new Ta,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ib(e)?c.set(d,th(a+"_"+d,e)):kd(e)?c.set(d,uh(a+"_"+d,e)):(kb(e)||jb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function vh(a,b){if(!lh(a))throw F(this.getName(),["string"],arguments);if(!mh(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Ta;return d=uh("AssertApiSubject",
c)};function wh(a,b){if(!mh(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof zd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ta;return d=uh("AssertThatSubject",c)};function xh(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Ad(b[e],d));return Bd(a.apply(null,c))}}function yh(){for(var a=Math,b=zh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=xh(a[e].bind(a)))}return c};function Ah(a){return a!=null&&Db(a,"__cvt_")};function Bh(a){var b;return b};function Ch(a){var b;return b};function Dh(a){try{return encodeURI(a)}catch(b){}};function Eh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Fh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Gh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Fh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Fh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Ih=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Gh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Hh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Hh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Ih(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Og(d(c[0]),d(c[1]),!1);case 5:return Qg(d(c[0]),d(c[1]));case 6:return Vg(d(c[0]),d(c[1]));case 7:return Lg(d(c[0]),d(c[1]));case 8:return Pg(d(c[0]),d(c[1]));case 9:return Ug(d(c[0]),d(c[1]));case 10:return Sg(d(c[0]),d(c[1]));case 11:return Tg(d(c[0]),d(c[1]));case 12:return Rg(d(c[0]),d(c[1]));case 13:return Mg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Jh(a){if(!mh(a))throw F(this.getName(),["string|undefined"],arguments);};function Kh(a,b){if(!qh(a)||!qh(b))throw F(this.getName(),["number","number"],arguments);return nb(a,b)};function Lh(){return(new Date).getTime()};function Mh(a){if(a===null)return"null";if(a instanceof od)return"array";if(a instanceof sd)return"function";if(a instanceof xd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Nh(a){function b(c){return function(d){try{return c(d)}catch(e){(og||pg.Jm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Bd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Ad(c))}),publicName:"JSON"}};function Oh(a){return tb(Ad(a,this.K))};function Ph(a){return Number(Ad(a,this.K))};function Qh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Rh(a,b,c){var d=null,e=!1;return e?d:null};var zh="floor ceil round max min abs pow sqrt".split(" ");function Sh(){var a={};return{tp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Em:function(b,c){a[b]=c},reset:function(){a={}}}}function Th(a,b){return function(){return sd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Uh(a,b){if(!lh(a))throw F(this.getName(),["string","any"],arguments);}
function Vh(a,b){if(!lh(a)||!eh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Wh={};var Xh=function(a){var b=new Ta;if(a instanceof od)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof sd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Wh.keys=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.wa());return new od};
Wh.values=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.Zb());return new od};
Wh.entries=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.Ib().map(function(b){return new od(b)}));return new od};
Wh.freeze=function(a){(a instanceof Ta||a instanceof zd||a instanceof od||a instanceof sd)&&a.Ua();return a};Wh.delete=function(a,b){if(a instanceof Ta&&!a.tb())return a.remove(b),!0;return!1};function H(a,b){var c=ya.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.oq){try{d.Ql.apply(null,[b].concat(ua(c)))}catch(e){throw cb("TAGGING",21),e;}return}d.Ql.apply(null,[b].concat(ua(c)))};var Yh=function(){this.H={};this.C={};this.N=!0;};Yh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Yh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Yh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:ib(b)?th(a,b):uh(a,b)};function Zh(a,b){var c=void 0;return c};function $h(){var a={};return a};var J={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ia:"analytics_storage",fc:"region",ja:"consent_updated",qg:"wait_for_update",bn:"app_remove",dn:"app_store_refund",fn:"app_store_subscription_cancel",gn:"app_store_subscription_convert",hn:"app_store_subscription_renew",jn:"consent_update",bk:"add_payment_info",dk:"add_shipping_info",Od:"add_to_cart",Pd:"remove_from_cart",ek:"view_cart",Xc:"begin_checkout",Qd:"select_item",jc:"view_item_list",Gc:"select_promotion",kc:"view_promotion",
kb:"purchase",Rd:"refund",xb:"view_item",fk:"add_to_wishlist",kn:"exception",ln:"first_open",mn:"first_visit",qa:"gtag.config",Cb:"gtag.get",nn:"in_app_purchase",Yc:"page_view",on:"screen_view",pn:"session_start",qn:"source_update",rn:"timing_complete",sn:"track_social",Sd:"user_engagement",tn:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",mc:"gclgb",lb:"gclid",gk:"gclid_len",Td:"gclgs",Ud:"gcllp",Vd:"gclst",za:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
Zc:"gclid_url",hk:"gclsrc",Pe:"gbraid",Wd:"wbraid",Ea:"allow_ad_personalization_signals",xg:"allow_custom_scripts",Qe:"allow_direct_google_requests",yg:"allow_display_features",zg:"allow_enhanced_conversions",Ob:"allow_google_signals",mb:"allow_interest_groups",un:"app_id",vn:"app_installer_id",wn:"app_name",xn:"app_version",Pb:"auid",yn:"auto_detection_enabled",bd:"aw_remarketing",Oh:"aw_remarketing_only",Ag:"discount",Bg:"aw_feed_country",Cg:"aw_feed_language",sa:"items",Dg:"aw_merchant_id",ik:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Qb:"client_id",jk:"rnd",Ph:"consent_update_type",zn:"content_group",An:"content_type",Rb:"conversion_cookie_prefix",Ye:"conversion_id",Oa:"conversion_linker",Qh:"conversion_linker_disabled",dd:"conversion_api",Eg:"cookie_deprecation",nb:"cookie_domain",ob:"cookie_expires",yb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",Xd:"country",
Va:"currency",Rh:"customer_buyer_stage",Ze:"customer_lifetime_value",Sh:"customer_loyalty",Th:"customer_ltv_bucket",af:"custom_map",Uh:"gcldc",fd:"dclid",kk:"debug_mode",oa:"developer_id",Bn:"disable_merchant_reported_purchases",gd:"dc_custom_params",Cn:"dc_natural_search",lk:"dynamic_event_settings",mk:"affiliation",Fg:"checkout_option",Vh:"checkout_step",nk:"coupon",bf:"item_list_name",Wh:"list_name",Dn:"promotions",cf:"shipping",Xh:"tax",Gg:"engagement_time_msec",Hg:"enhanced_client_id",Yh:"enhanced_conversions",
pk:"enhanced_conversions_automatic_settings",Ig:"estimated_delivery_date",Zh:"euid_logged_in_state",df:"event_callback",En:"event_category",Tb:"event_developer_id_string",Gn:"event_label",hd:"event",Jg:"event_settings",Kg:"event_timeout",Hn:"description",In:"fatal",Jn:"experiments",ai:"firebase_id",Yd:"first_party_collection",Lg:"_x_20",oc:"_x_19",qk:"fledge_drop_reason",rk:"fledge",sk:"flight_error_code",tk:"flight_error_message",uk:"fl_activity_category",vk:"fl_activity_group",bi:"fl_advertiser_id",
wk:"fl_ar_dedupe",ef:"match_id",xk:"fl_random_number",yk:"tran",zk:"u",Mg:"gac_gclid",Zd:"gac_wbraid",Ak:"gac_wbraid_multiple_conversions",Bk:"ga_restrict_domain",di:"ga_temp_client_id",Kn:"ga_temp_ecid",jd:"gdpr_applies",Ck:"geo_granularity",Ic:"value_callback",qc:"value_key",rc:"google_analysis_params",ae:"_google_ng",be:"google_signals",Dk:"google_tld",ff:"gpp_sid",hf:"gpp_string",Ng:"groups",Ek:"gsa_experiment_id",jf:"gtag_event_feature_usage",Fk:"gtm_up",Jc:"iframe_state",kf:"ignore_referrer",
ei:"internal_traffic_results",Gk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Og:"is_passthrough",kd:"_lps",zb:"language",Pg:"legacy_developer_id_string",Pa:"linker",ce:"accept_incoming",sc:"decorate_forms",ma:"domains",Mc:"url_position",de:"merchant_feed_label",ee:"merchant_feed_language",fe:"merchant_id",Hk:"method",Ln:"name",Ik:"navigation_type",lf:"new_customer",Qg:"non_interaction",Mn:"optimize_id",Jk:"page_hostname",nf:"page_path",Wa:"page_referrer",Db:"page_title",Kk:"passengers",
Lk:"phone_conversion_callback",Nn:"phone_conversion_country_code",Mk:"phone_conversion_css_class",On:"phone_conversion_ids",Nk:"phone_conversion_number",Ok:"phone_conversion_options",Pn:"_platinum_request_status",Qn:"_protected_audience_enabled",pf:"quantity",Rg:"redact_device_info",fi:"referral_exclusion_definition",Rq:"_request_start_time",Vb:"restricted_data_processing",Rn:"retoken",Sn:"sample_rate",gi:"screen_name",Nc:"screen_resolution",Pk:"_script_source",Tn:"search_term",pb:"send_page_view",
ld:"send_to",md:"server_container_url",qf:"session_duration",Sg:"session_engaged",hi:"session_engaged_time",uc:"session_id",Tg:"session_number",rf:"_shared_user_id",tf:"delivery_postal_code",Sq:"_tag_firing_delay",Tq:"_tag_firing_time",Uq:"temporary_client_id",ii:"_timezone",ji:"topmost_url",Un:"tracking_id",ki:"traffic_type",Xa:"transaction_id",vc:"transport_url",Qk:"trip_type",od:"update",Eb:"url_passthrough",Rk:"uptgs",uf:"_user_agent_architecture",vf:"_user_agent_bitness",wf:"_user_agent_full_version_list",
xf:"_user_agent_mobile",yf:"_user_agent_model",zf:"_user_agent_platform",Af:"_user_agent_platform_version",Bf:"_user_agent_wow64",eb:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",wc:"user_data_mode",Ug:"user_data_settings",Qa:"user_id",Wb:"user_properties",Sk:"_user_region",Cf:"us_privacy_string",Fa:"value",Tk:"wbraid_multiple_conversions",rd:"_fpm_parameters",xi:"_host_name",fl:"_in_page_command",
il:"_ip_override",nl:"_is_passthrough_cid",xc:"non_personalized_ads",Ji:"_sst_parameters",nc:"conversion_label",Aa:"page_location",Ub:"global_developer_id_string",nd:"tc_privacy_string"}};var ai={},bi=(ai[J.m.ja]="gcu",ai[J.m.mc]="gclgb",ai[J.m.lb]="gclaw",ai[J.m.gk]="gclid_len",ai[J.m.Td]="gclgs",ai[J.m.Ud]="gcllp",ai[J.m.Vd]="gclst",ai[J.m.Pb]="auid",ai[J.m.Ag]="dscnt",ai[J.m.Bg]="fcntr",ai[J.m.Cg]="flng",ai[J.m.Dg]="mid",ai[J.m.ik]="bttype",ai[J.m.Qb]="gacid",ai[J.m.nc]="label",ai[J.m.dd]="capi",ai[J.m.Eg]="pscdl",ai[J.m.Va]="currency_code",ai[J.m.Rh]="clobs",ai[J.m.Ze]="vdltv",ai[J.m.Sh]="clolo",ai[J.m.Th]="clolb",ai[J.m.kk]="_dbg",ai[J.m.Ig]="oedeld",ai[J.m.Tb]="edid",ai[J.m.qk]=
"fdr",ai[J.m.rk]="fledge",ai[J.m.Mg]="gac",ai[J.m.Zd]="gacgb",ai[J.m.Ak]="gacmcov",ai[J.m.jd]="gdpr",ai[J.m.Ub]="gdid",ai[J.m.ae]="_ng",ai[J.m.ff]="gpp_sid",ai[J.m.hf]="gpp",ai[J.m.Ek]="gsaexp",ai[J.m.jf]="_tu",ai[J.m.Jc]="frm",ai[J.m.Og]="gtm_up",ai[J.m.kd]="lps",ai[J.m.Pg]="did",ai[J.m.de]="fcntr",ai[J.m.ee]="flng",ai[J.m.fe]="mid",ai[J.m.lf]=void 0,ai[J.m.Db]="tiba",ai[J.m.Vb]="rdp",ai[J.m.uc]="ecsid",ai[J.m.rf]="ga_uid",ai[J.m.tf]="delopc",ai[J.m.nd]="gdpr_consent",ai[J.m.Xa]="oid",ai[J.m.Rk]=
"uptgs",ai[J.m.uf]="uaa",ai[J.m.vf]="uab",ai[J.m.wf]="uafvl",ai[J.m.xf]="uamb",ai[J.m.yf]="uam",ai[J.m.zf]="uap",ai[J.m.Af]="uapv",ai[J.m.Bf]="uaw",ai[J.m.li]="ec_lat",ai[J.m.mi]="ec_meta",ai[J.m.ni]="ec_m",ai[J.m.oi]="ec_sel",ai[J.m.ri]="ec_s",ai[J.m.wc]="ec_mode",ai[J.m.Qa]="userId",ai[J.m.Cf]="us_privacy",ai[J.m.Fa]="value",ai[J.m.Tk]="mcov",ai[J.m.xi]="hn",ai[J.m.fl]="gtm_ee",ai[J.m.xc]="npa",ai[J.m.Ye]=null,ai[J.m.Nc]=null,ai[J.m.zb]=null,ai[J.m.sa]=null,ai[J.m.Aa]=null,ai[J.m.Wa]=null,ai[J.m.ji]=
null,ai[J.m.rd]=null,ai[J.m.Le]=null,ai[J.m.Me]=null,ai[J.m.rc]=null,ai);function ci(a,b){if(a){var c=a.split("x");c.length===2&&(di(b,"u_w",c[0]),di(b,"u_h",c[1]))}}
function ei(a){var b=fi;b=b===void 0?gi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(hi(q.value)),r.push(hi(q.quantity)),r.push(hi(q.item_id)),r.push(hi(q.start_date)),r.push(hi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function gi(a){return ii(a.item_id,a.id,a.item_name)}function ii(){for(var a=l(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ji(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function di(a,b,c){c===void 0||c===null||c===""&&!Bg[b]||(a[b]=c)}function hi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ki={},mi={uq:li};function ni(a,b){var c=ki[b],d=c.Gm;if(!(ki[b].active||ki[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;mi.uq(a,b)}}function li(a,b){var c=ki[b];if(!(nb(0,9999)<c.percent*2*100))return a;oi(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function oi(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=nb(0,1)===0,e=nb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var K={J:{Mj:"call_conversion",W:"conversion",Vn:"floodlight",Ef:"ga_conversion",Fi:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};function ri(a){return si?z.querySelectorAll(a):null}
function ti(a,b){if(!si)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ui=!1;
if(z.querySelectorAll)try{var vi=z.querySelectorAll(":root");vi&&vi.length==1&&vi[0]==z.documentElement&&(ui=!0)}catch(a){}var si=ui;function wi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function xi(){this.blockSize=-1};function yi(a,b){this.blockSize=-1;this.blockSize=64;this.N=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=za.Int32Array?new Int32Array(64):Array(64);zi===void 0&&(za.Int32Array?zi=new Int32Array(Ai):zi=Ai);this.reset()}Aa(yi,xi);for(var Bi=[],Ci=0;Ci<63;Ci++)Bi[Ci]=0;var Di=[].concat(128,Bi);
yi.prototype.reset=function(){this.P=this.H=0;var a;if(za.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ei=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(zi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
yi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ei(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ei(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};yi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Di,56-this.H):this.update(Di,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ei(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ai=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],zi;function Fi(){yi.call(this,8,Gi)}Aa(Fi,yi);var Gi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Hi=/^[0-9A-Fa-f]{64}$/;function Ii(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Ji(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Hi.test(a))return Promise.resolve(a);try{var d=Ii(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ki(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ki(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Li=[],Mi;function Ni(a){Mi?Mi(a):Li.push(a)}function Oi(a,b){if(!C(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Ni(a),b):c}function Pi(a,b){if(!C(190))return b;var c=Qi(a,"");return c!==b?(Ni(a),b):c}function Qi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ri(a,b){if(!C(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Ni(a),b)}function Si(){Mi=Ti;for(var a=l(Li),b=a.next();!b.done;b=a.next())Mi(b.value);Li.length=0};var Ui={Xm:'512',Ym:'0',Zm:'1000',Zn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',ao:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',xo:Pi(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104839054~104839056~104885889~104885891')},Vi={ap:Number(Ui.Xm)||0,bp:Number(Ui.Ym)||0,ep:Number(Ui.Zm)||0,xp:Ui.Zn.split("~"),yp:Ui.ao.split("~"),Lq:Ui.xo};Object.assign({},Vi);function L(a){cb("GTM",a)};
var $i=function(a,b){var c=["tv.1"],d=Wi(a);if(d)return c.push(d),{Za:!1,Ej:c.join("~"),mg:{}};var e={},f=0;var g=Xi(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).Za;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{Za:g,Ej:h,mg:m,cp:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Yi():Zi()}:{Za:g,Ej:h,mg:m}},bj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=aj(a);return Xi(b,function(){}).Za},Xi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=cj[g.name];if(h){var m=dj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,fj:c}},dj=function(a){var b=ej(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(fj.test(e)||
Hi.test(e))}return d},ej=function(a){return gj.indexOf(a)!==-1},Zi=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BD6dP0RV5GhwQa1pObjmBnl4cmUONRwMVPviFiMRmYLCETOfGaVUgPIounrK8INuZgpnuLWau3tmsAgDckuYkMM\x3d\x22,\x22version\x22:0},\x22id\x22:\x22baa4e8be-b125-4ce4-a0c2-bf556a893ef4\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BL/yzBlWWmd9oa0xNGwna1cAmsrfLyvdtz5fg+5JF+3rrk0qmAHVRj6OCuMll0oTBZ3zVrevL88d/TCvJFLziO4\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a73df72c-528c-4872-adc8-2ad5aed787a1\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCMPMLMxwSxccypHORFYkNbjBgqjoB9oQVwWvWe7MZW5bSK98s++Tj+MZpF2GPPZh4zlZSyEqSyNx+eGbFzxmRQ\x3d\x22,\x22version\x22:0},\x22id\x22:\x226e566fec-f826-4006-b302-30af1c9c44ae\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBuAw1Advm2qdY6vnNWIKDoZSpLD+itgKWpXOsh+U5M1GB7GvnYz1zC3ddtoyeriRcCP3mYSVHcQT1JNY8fPlMI\x3d\x22,\x22version\x22:0},\x22id\x22:\x224351ac64-5ea1-4762-adef-4fbe3e802392\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBWRsTHiHHGeX9wbntoN87J6FFTc31AdEAZhDNR/XwE5slgsqDT040PMZltVl0+DeRQVwiCeRRld/RK/74e+DDk\x3d\x22,\x22version\x22:0},\x22id\x22:\x222e5ed546-6c33-44c8-8b24-c479e770cd53\x22}]}'},jj=function(a){if(x.Promise){var b=void 0;return b}},oj=function(a,b,c,d,e){if(x.Promise)try{var f=aj(a),g=kj(f,e).then(lj);return g}catch(p){}},qj=function(a){try{return lj(pj(aj(a)))}catch(b){}},ij=function(a,b){var c=void 0;return c},lj=function(a){var b=a.Tc,c=a.time,d=["tv.1"],e=Wi(b);if(e)return d.push(e),{Kb:encodeURIComponent(d.join("~")),fj:!1,Za:!1,time:c,ej:!0};var f=b.filter(function(n){return!dj(n)}),g=Xi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.fj,m=g.Za;return{Kb:encodeURIComponent(d.join("~")),fj:h,Za:m,time:c,ej:!1}},Wi=function(a){if(a.length===1&&a[0].name==="error_code")return cj.error_code+
"."+a[0].value},nj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(cj[d.name]&&d.value)return!0}return!1},aj=function(a){function b(r,t,u,v){var w=rj(r);w!==""&&(Hi.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(jb(u)||Array.isArray(u)){u=lb(r);for(var v=0;v<u.length;++v){var w=rj(u[v]),y=Hi.test(w);t&&!y&&L(89);!t&&y&&L(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
sj[t];r[v]&&(r[t]&&L(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=lb(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){L(64);return r(t)}}var h=[];if(x.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",tj);e(a,"phone_number",uj);e(a,"first_name",g(vj));e(a,"last_name",g(vj));var m=a.home_address||{};e(m,"street",g(wj));e(m,"city",g(wj));e(m,"postal_code",g(xj));e(m,"region",
g(wj));e(m,"country",g(xj));for(var n=lb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",vj,p);f(q,"last_name",vj,p);f(q,"street",wj,p);f(q,"city",wj,p);f(q,"postal_code",xj,p);f(q,"region",wj,p);f(q,"country",xj,p)}return h},yj=function(a){var b=a?aj(a):[];return lj({Tc:b})},zj=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?aj(a).some(function(b){return b.value&&ej(b.name)&&!Hi.test(b.value)}):!1},rj=function(a){return a==null?"":jb(a)?wb(String(a)):"e0"},xj=function(a){return a.replace(Aj,
"")},vj=function(a){return wj(a.replace(/\s/g,""))},wj=function(a){return wb(a.replace(Bj,"").toLowerCase())},uj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Cj.test(a)?a:"e0"},tj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Dj.test(c))return c}return"e0"},pj=function(a){var b=Yc();try{a.forEach(function(e){if(e.value&&ej(e.name)){var f;var g=e.value,h=x;if(g===""||
g==="e0"||Hi.test(g))f=g;else try{var m=new Fi;m.update(Ii(g));f=Ki(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Yc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},kj=function(a,b){if(!a.some(function(d){return d.value&&ej(d.name)}))return Promise.resolve({Tc:a});if(!x.Promise)return Promise.resolve({Tc:[]});var c=b?Yc():void 0;return Promise.all(a.map(function(d){return d.value&&ej(d.name)?Ji(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Tc:a};if(c!==void 0){var e=Yc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},Bj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Dj=/^\S+@\S+\.\S+$/,Cj=/^\+\d{10,15}$/,Aj=/[.~]/g,fj=/^[0-9A-Za-z_-]{43}$/,Ej={},cj=(Ej.email="em",Ej.phone_number="pn",Ej.first_name="fn",Ej.last_name="ln",Ej.street="sa",Ej.city="ct",Ej.region="rg",Ej.country="co",Ej.postal_code="pc",Ej.error_code="ec",Ej),Fj={},sj=(Fj.email="sha256_email_address",Fj.phone_number="sha256_phone_number",
Fj.first_name="sha256_first_name",Fj.last_name="sha256_last_name",Fj.street="sha256_street",Fj);var gj=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Gj={},Hj=(Gj[J.m.mb]=1,Gj[J.m.md]=2,Gj[J.m.vc]=2,Gj[J.m.za]=3,Gj[J.m.Ze]=4,Gj[J.m.xg]=5,Gj[J.m.Hc]=6,Gj[J.m.cb]=6,Gj[J.m.nb]=6,Gj[J.m.ed]=6,Gj[J.m.Sb]=6,Gj[J.m.yb]=6,Gj[J.m.ob]=7,Gj[J.m.Vb]=9,Gj[J.m.yg]=10,Gj[J.m.Ob]=11,Gj),Ij={},Jj=(Ij.unknown=13,Ij.standard=14,Ij.unique=15,Ij.per_session=16,Ij.transactions=17,Ij.items_sold=18,Ij);var eb=[];function Kj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Hj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Hj[f],h=b;h=h===void 0?!1:h;cb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(eb[g]=!0)}}};var Lj=function(){this.C=new Set;this.H=new Set},Nj=function(a){var b=Mj.R;a=a===void 0?[]:a;var c=[].concat(ua(b.C)).concat([].concat(ua(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Oj=function(){var a=[].concat(ua(Mj.R.C));a.sort(function(b,c){return b-c});return a},Pj=function(){var a=Mj.R,b=Vi.Lq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Qj={},Rj=Pi(14,"5720"),Sj=Ri(15,Number("0")),Tj=Pi(19,"pingerDataLayer");Pi(20,"");Pi(16,"ChAI8IOzwwYQnJyErMiZz5oyEiUAx0A2QuT6i/l4DXEkTz9+OXinLpO1nPLG+bXAxsBbsQ0vOm3JGgL2DQ\x3d\x3d");var Uj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Vj={__paused:1,__tg:1},Wj;for(Wj in Uj)Uj.hasOwnProperty(Wj)&&(Vj[Wj]=1);var Xj=Oi(11,ub("")),Yj=!1,Zj,ak=!1;ak=!0;
Zj=ak;var bk,ck=!1;bk=ck;Qj.vg=Pi(3,"www.googletagmanager.com");var dk=""+Qj.vg+(Zj?"/gtag/js":"/gtm.js"),ek=null,fk=null,gk={},hk={};Qj.Rm=Oi(2,ub(""));var ik="";Qj.Ki=ik;
var Mj=new function(){this.R=new Lj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.qb=this.P="";this.ba=this.ka=!1};function jk(){var a;a=a===void 0?[]:a;return Nj(a).join("~")}function kk(){var a=Mj.P.length;return Mj.P[a-1]==="/"?Mj.P.substring(0,a-1):Mj.P}function lk(){return Mj.C?C(84)?Mj.H===0:Mj.H!==1:!1}function mk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var nk=new pb,ok={},pk={},sk={name:Tj,set:function(a,b){ld(Fb(a,b),ok);qk()},get:function(a){return rk(a,2)},reset:function(){nk=new pb;ok={};qk()}};function rk(a,b){return b!=2?nk.get(a):tk(a)}function tk(a,b){var c=a.split(".");b=b||[];for(var d=ok,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function uk(a,b){pk.hasOwnProperty(a)||(nk.set(a,b),ld(Fb(a,b),ok),qk())}
function vk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=rk(c,1);if(Array.isArray(d)||kd(d))d=ld(d,null);pk[c]=d}}function qk(a){qb(pk,function(b,c){nk.set(b,c);ld(Fb(b),ok);ld(Fb(b,c),ok);a&&delete pk[b]})}function wk(a,b){var c,d=(b===void 0?2:b)!==1?tk(a):nk.get(a);id(d)==="array"||id(d)==="object"?c=ld(d,null):c=d;return c};
var yk=function(a){for(var b=[],c=Object.keys(xk),d=0;d<c.length;d++){var e=c[d],f=xk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},zk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},Ak=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!Db(w,"#")&&!Db(w,".")}),h=0;h<g.length;h++){var m=g[h];if(Db(m,"dataLayer."))f=rk(m.substring(10));
else{var n=m.split(".");f=x[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&si)try{var q=ri(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Nc(q[r])||wb(q[r].value));f=f.length===1?f[0]:f}}catch(w){L(149)}if(C(60)){for(var t,u=0;u<g.length&&(t=rk(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=zk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},Bk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=Ak(c,"email",
a.email,b)||d;d=Ak(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=Ak(g,"first_name",e[f].first_name,b)||d;d=Ak(g,"last_name",e[f].last_name,b)||d;d=Ak(g,"street",e[f].street,b)||d;d=Ak(g,"city",e[f].city,b)||d;d=Ak(g,"region",e[f].region,b)||d;d=Ak(g,"country",e[f].country,b)||d;d=Ak(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},Ck=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&kd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&cb("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Bk(a[J.m.pk])}},Dk=function(a){return kd(a)?!!a.enable_code:!1},xk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Gk=/:[0-9]+$/,Hk=/^\d+\.fls\.doubleclick\.net$/;function Ik(a,b,c,d){var e=Jk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Jk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=sa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Kk(a){try{return decodeURIComponent(a)}catch(b){}}function Lk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Mk(a.protocol)||Mk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Gk,"").toLowerCase());return Nk(a,b,c,d,e)}
function Nk(a,b,c,d,e){var f,g=Mk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Ok(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Gk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||cb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Ik(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Mk(a){return a?a.replace(":","").toLowerCase():""}function Ok(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Pk={},Qk=0;
function Rk(a){var b=Pk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||cb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Gk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Qk<5&&(Pk[a]=b,Qk++)}return b}function Sk(a,b,c){var d=Rk(a);return Lb(b,d,c)}
function Tk(a){var b=Rk(x.location.href),c=Lk(b,"host",!1);if(c&&c.match(Hk)){var d=Lk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Uk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Vk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Wk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Rk(""+c+b).href}}function Xk(a,b){if(lk()||Mj.N)return Wk(a,b)}
function Yk(){return!!Qj.Ki&&Qj.Ki.split("@@").join("")!=="SGTM_TOKEN"}function Zk(a){for(var b=l([J.m.md,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function $k(a,b,c){c=c===void 0?"":c;if(!lk())return a;var d=b?Uk[a]||"":"";d==="/gs"&&(c="");return""+kk()+d+c}function al(a){if(!lk())return a;for(var b=l(Vk),c=b.next();!c.done;c=b.next())if(Db(a,""+kk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function bl(a){var b=String(a[ff.Ra]||"").replace(/_/g,"");return Db(b,"cvt")?"cvt":b}var cl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var dl={qq:Ri(27,Number("0.005000")),Yo:Ri(42,Number("0.010000"))},el=Math.random(),fl=cl||el<Number(dl.qq),gl=cl||el>=1-Number(dl.Yo);var hl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},il=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var jl,kl;a:{for(var ll=["CLOSURE_FLAGS"],ml=za,nl=0;nl<ll.length;nl++)if(ml=ml[ll[nl]],ml==null){kl=null;break a}kl=ml}var ol=kl&&kl[610401301];jl=ol!=null?ol:!1;function pl(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var ql,rl=za.navigator;ql=rl?rl.userAgentData||null:null;function sl(a){if(!jl||!ql)return!1;for(var b=0;b<ql.brands.length;b++){var c=ql.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function tl(a){return pl().indexOf(a)!=-1};function ul(){return jl?!!ql&&ql.brands.length>0:!1}function vl(){return ul()?!1:tl("Opera")}function wl(){return tl("Firefox")||tl("FxiOS")}function xl(){return ul()?sl("Chromium"):(tl("Chrome")||tl("CriOS"))&&!(ul()?0:tl("Edge"))||tl("Silk")};var yl=function(a){yl[" "](a);return a};yl[" "]=function(){};var zl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Al(){return jl?!!ql&&!!ql.platform:!1}function Bl(){return tl("iPhone")&&!tl("iPod")&&!tl("iPad")}function Cl(){Bl()||tl("iPad")||tl("iPod")};vl();ul()||tl("Trident")||tl("MSIE");tl("Edge");!tl("Gecko")||pl().toLowerCase().indexOf("webkit")!=-1&&!tl("Edge")||tl("Trident")||tl("MSIE")||tl("Edge");pl().toLowerCase().indexOf("webkit")!=-1&&!tl("Edge")&&tl("Mobile");Al()||tl("Macintosh");Al()||tl("Windows");(Al()?ql.platform==="Linux":tl("Linux"))||Al()||tl("CrOS");Al()||tl("Android");Bl();tl("iPad");tl("iPod");Cl();pl().toLowerCase().indexOf("kaios");var Dl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{yl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},El=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Fl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Gl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Dl(b.top)?1:2},Hl=function(a){a=a===void 0?document:a;return a.createElement("img")},Il=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Dl(a)&&(b=a);return b};function Jl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Kl(){return Jl("join-ad-interest-group")&&ib(sc.joinAdInterestGroup)}
function Ll(a,b,c){var d=Ia[3]===void 0?1:Ia[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ia[2]===void 0?50:Ia[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&yb()-q<(Ia[1]===void 0?6E4:Ia[1])?(cb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ml(f[0]);else{if(n)return cb("TAGGING",10),!1}else f.length>=d?Ml(f[0]):n&&Ml(m[0]);Gc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:yb()});return!0}function Ml(a){try{a.parentNode.removeChild(a)}catch(b){}};function Nl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Ol=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};wl();Bl()||tl("iPod");tl("iPad");!tl("Android")||xl()||wl()||vl()||tl("Silk");xl();!tl("Safari")||xl()||(ul()?0:tl("Coast"))||vl()||(ul()?0:tl("Edge"))||(ul()?sl("Microsoft Edge"):tl("Edg/"))||(ul()?sl("Opera"):tl("OPR"))||wl()||tl("Silk")||tl("Android")||Cl();var Pl={},Ql=null,Rl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Ql){Ql={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Pl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Ql[q]===void 0&&(Ql[q]=p)}}}for(var r=Pl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],D=b[v+2],E=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|D>>6],M=r[D&63];t[w++]=""+E+G+I+M}var S=0,ea=u;switch(b.length-v){case 2:S=b[v+1],ea=r[(S&15)<<2]||u;case 1:var U=b[v];t[w]=""+r[U>>2]+r[(U&3)<<4|S>>4]+ea+u}return t.join("")};var Sl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Tl=/#|$/,Ul=function(a,b){var c=a.search(Tl),d=Sl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return zl(a.slice(d,e!==-1?e:0))},Vl=/[?&]($|#)/,Wl=function(a,b,c){for(var d,e=a.search(Tl),f=0,g,h=[];(g=Sl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Vl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Xl(a,b,c,d,e,f,g){var h=Ul(c,"fmt");if(d){var m=Ul(c,"random"),n=Ul(c,"label")||"";if(!m)return!1;var p=Rl(zl(n)+":"+zl(m));if(!Nl(a,p,d))return!1}h&&Number(h)!==4&&(c=Wl(c,"rfmt",h));var q=Wl(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||g.H();Ec(q,function(){g==null||g.C();a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||g.C();e==null||e()},f,r||void 0);return!0};var Yl={},Zl=(Yl[1]={},Yl[2]={},Yl[3]={},Yl[4]={},Yl);function $l(a,b,c){var d=am(b,c);if(d){var e=Zl[b][d];e||(e=Zl[b][d]=[]);e.push(Object.assign({},a))}}function bm(a,b){var c=am(a,b);if(c){var d=Zl[a][c];d&&(Zl[a][c]=d.filter(function(e){return!e.Am}))}}function cm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function am(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function dm(a){var b=ya.apply(1,arguments);gl&&($l(a,2,b[0]),$l(a,3,b[0]));Qc.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);gl&&$l(a,2,b[0]);return Rc.apply(null,ua(b))}function fm(a){var b=ya.apply(1,arguments);gl&&$l(a,3,b[0]);Hc.apply(null,ua(b))}
function gm(a){var b=ya.apply(1,arguments),c=b[0];gl&&($l(a,2,c),$l(a,3,c));return Uc.apply(null,ua(b))}function hm(a){var b=ya.apply(1,arguments);gl&&$l(a,1,b[0]);Ec.apply(null,ua(b))}function im(a){var b=ya.apply(1,arguments);b[0]&&gl&&$l(a,4,b[0]);Gc.apply(null,ua(b))}function jm(a){var b=ya.apply(1,arguments);gl&&$l(a,1,b[2]);return Xl.apply(null,ua(b))}function km(a){var b=ya.apply(1,arguments);gl&&$l(a,4,b[0]);Ll.apply(null,ua(b))};var lm=/gtag[.\/]js/,mm=/gtm[.\/]js/,nm=!1;function om(a){if(nm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(lm.test(c))return"3";if(mm.test(c))return"2"}return"0"};function pm(a,b,c){var d=qm(),e=rm().container[a];e&&e.state!==3||(rm().container[a]={state:1,context:b,parent:d},sm({ctid:a,isDestination:!1},c))}function sm(a,b){var c=rm();c.pending||(c.pending=[]);mb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function tm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var um=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=tm()};function rm(){var a=wc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new um,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=tm());return c};var vm={},jg={ctid:Pi(5,"G-DP2X732JSX"),canonicalContainerId:Pi(6,"84263385"),sm:Pi(10,"G-DP2X732JSX"),tm:Pi(9,"G-DP2X732JSX")};vm.pe=Oi(7,ub(""));function wm(){return vm.pe&&xm().some(function(a){return a===jg.ctid})}function ym(){return jg.canonicalContainerId||"_"+jg.ctid}function zm(){return jg.sm?jg.sm.split("|"):[jg.ctid]}
function xm(){return jg.tm?jg.tm.split("|").filter(function(a){return C(108)?a.indexOf("GTM-")!==0:!0}):[]}function Am(){var a=Bm(qm()),b=a&&a.parent;if(b)return Bm(b)}function Cm(){var a=Bm(qm());if(a){for(;a.parent;){var b=Bm(a.parent);if(!b)break;a=b}return a}}function Bm(a){var b=rm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Dm(){var a=rm();if(a.pending){for(var b,c=[],d=!1,e=zm(),f=xm(),g={},h=0;h<a.pending.length;g={jg:void 0},h++)g.jg=a.pending[h],mb(g.jg.target.isDestination?f:e,function(m){return function(n){return n===m.jg.target.ctid}}(g))?d||(b=g.jg.onLoad,d=!0):c.push(g.jg);a.pending=c;if(b)try{b(ym())}catch(m){}}}
function Em(){for(var a=jg.ctid,b=zm(),c=xm(),d=function(n,p){var q={canonicalContainerId:jg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};uc&&(q.scriptElement=uc);vc&&(q.scriptSource=vc);if(Am()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Mj.C,y=Rk(v),A=w?y.pathname:""+y.hostname+y.pathname,D=z.scripts,E="",G=0;G<D.length;++G){var I=D[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}E=String(G)}}if(E){t=E;break b}}t=void 0}var M=t;if(M){nm=!0;r=M;break a}}var S=[].slice.call(z.scripts);r=q.scriptElement?String(S.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=om(q)}var ea=p?e.destination:e.container,U=ea[n];U?(p&&U.state===0&&L(93),Object.assign(U,q)):ea[n]=q},e=rm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[ym()]={};Dm()}function Fm(){var a=ym();return!!rm().canonical[a]}function Gm(a){return!!rm().container[a]}function Hm(a){var b=rm().destination[a];return!!b&&!!b.state}function qm(){return{ctid:jg.ctid,isDestination:vm.pe}}function Im(){var a=rm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Jm(){var a={};qb(rm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Km(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Lm(){for(var a=rm(),b=l(zm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Mm={Ia:{ie:0,oe:1,Gi:2}};Mm.Ia[Mm.Ia.ie]="FULL_TRANSMISSION";Mm.Ia[Mm.Ia.oe]="LIMITED_TRANSMISSION";Mm.Ia[Mm.Ia.Gi]="NO_TRANSMISSION";var Nm={X:{Fb:0,Da:1,Fc:2,Oc:3}};Nm.X[Nm.X.Fb]="NO_QUEUE";Nm.X[Nm.X.Da]="ADS";Nm.X[Nm.X.Fc]="ANALYTICS";Nm.X[Nm.X.Oc]="MONITORING";function Om(){var a=wc("google_tag_data",{});return a.ics=a.ics||new Pm}var Pm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Pm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;cb("TAGGING",19);b==null?cb("TAGGING",18):Qm(this,a,b==="granted",c,d,e,f,g)};Pm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Qm(this,a[d],void 0,void 0,"","",b,c)};
var Qm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&jb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(cb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Pm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Rm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Rm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&jb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,ye:b})};var Rm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.vm=!0)}};Pm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.vm){d.vm=!1;try{d.ye({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Sm=!1,Tm=!1,Um={},Vm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Um.ad_storage=1,Um.analytics_storage=1,Um.ad_user_data=1,Um.ad_personalization=1,Um),usedContainerScopedDefaults:!1};function Wm(a){var b=Om();b.accessedAny=!0;return(jb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Vm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Xm(a){var b=Om();b.accessedAny=!0;return b.getConsentState(a,Vm)}function Ym(a){var b=Om();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function Zm(){if(!Ja(7))return!1;var a=Om();a.accessedAny=!0;if(a.active)return!0;if(!Vm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Vm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Vm.containerScopedDefaults[c.value]!==1)return!0;return!1}function $m(a,b){Om().addListener(a,b)}
function an(a,b){Om().notifyListeners(a,b)}function bn(a,b){function c(){for(var e=0;e<b.length;e++)if(!Ym(b[e]))return!0;return!1}if(c()){var d=!1;$m(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function cn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Wm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=jb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),$m(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var dn={},en=(dn[Nm.X.Fb]=Mm.Ia.ie,dn[Nm.X.Da]=Mm.Ia.ie,dn[Nm.X.Fc]=Mm.Ia.ie,dn[Nm.X.Oc]=Mm.Ia.ie,dn),fn=function(a,b){this.C=a;this.consentTypes=b};fn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Wm(a)});case 1:return this.consentTypes.some(function(a){return Wm(a)});default:kc(this.C,"consentsRequired had an unknown type")}};
var gn={},hn=(gn[Nm.X.Fb]=new fn(0,[]),gn[Nm.X.Da]=new fn(0,["ad_storage"]),gn[Nm.X.Fc]=new fn(0,["analytics_storage"]),gn[Nm.X.Oc]=new fn(1,["ad_storage","analytics_storage"]),gn);var kn=function(a){var b=this;this.type=a;this.C=[];$m(hn[a].consentTypes,function(){jn(b)||b.flush()})};kn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var jn=function(a){return en[a.type]===Mm.Ia.Gi&&!hn[a.type].isConsentGranted()},ln=function(a,b){jn(a)?a.C.push(b):b()},mn=new Map;function nn(a){mn.has(a)||mn.set(a,new kn(a));return mn.get(a)};var on={Z:{Om:"aw_user_data_cache",Kh:"cookie_deprecation_label",wg:"diagnostics_page_id",Wn:"fl_user_data_cache",Yn:"ga4_user_data_cache",Ff:"ip_geo_data_cache",Ai:"ip_geo_fetch_in_progress",rl:"nb_data",po:"page_experiment_ids",Nf:"pt_data",tl:"pt_listener_set",Bl:"service_worker_endpoint",Dl:"shared_user_id",El:"shared_user_id_requested",jh:"shared_user_id_source"}};var pn=function(a){return Ze(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(on.Z);
function qn(a,b){b=b===void 0?!1:b;if(pn(a)){var c,d,e=(d=(c=wc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function rn(a,b){var c=qn(a,!0);c&&c.set(b)}function sn(a){var b;return(b=qn(a))==null?void 0:b.get()}function tn(a){var b={},c=qn(a);if(!c){c=qn(a,!0);if(!c)return;c.set(b)}return c.get()}function un(a,b){if(typeof b==="function"){var c;return(c=qn(a,!0))==null?void 0:c.subscribe(b)}}function vn(a,b){var c=qn(a);return c?c.unsubscribe(b):!1};var wn="https://"+Pi(21,"www.googletagmanager.com"),xn="/td?id="+jg.ctid,yn={},zn=(yn.tdp=1,yn.exp=1,yn.pid=1,yn.dl=1,yn.seq=1,yn.t=1,yn.v=1,yn),An=["mcc"],Bn={},Cn={},Dn=!1,En=void 0;function Fn(a,b,c){Cn[a]=b;(c===void 0||c)&&Gn(a)}function Gn(a,b){Bn[a]!==void 0&&(b===void 0||!b)||Db(jg.ctid,"GTM-")&&a==="mcc"||(Bn[a]=!0)}
function Hn(a){a=a===void 0?!1:a;var b=Object.keys(Bn).filter(function(c){return Bn[c]===!0&&Cn[c]!==void 0&&(a||!An.includes(c))}).map(function(c){var d=Cn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+$k(wn)+xn+(""+b+"&z=0")}function In(){Object.keys(Bn).forEach(function(a){zn[a]||(Bn[a]=!1)})}
function Jn(a){a=a===void 0?!1:a;if(Mj.ba&&gl&&jg.ctid){var b=nn(Nm.X.Oc);if(jn(b))Dn||(Dn=!0,ln(b,Jn));else{var c=Hn(a),d={destinationId:jg.ctid,endpoint:61};a?gm(d,c,void 0,{Ch:!0},void 0,function(){fm(d,c+"&img=1")}):fm(d,c);In();Dn=!1}}}var Kn={};
function Ln(a){var b=String(a);Kn.hasOwnProperty(b)||(Kn[b]=!0,Fn("csp",Object.keys(Kn).join("~")),Gn("csp",!0),En===void 0&&C(171)&&(En=x.setTimeout(function(){var c=Bn.csp;Bn.csp=!0;Bn.seq=!1;var d=Hn(!1);Bn.csp=c;Bn.seq=!0;Ec(d+"&script=1");En=void 0},500)))}function Mn(){Object.keys(Bn).filter(function(a){return Bn[a]&&!zn[a]}).length>0&&Jn(!0)}var Nn;
function On(){if(sn(on.Z.wg)===void 0){var a=function(){rn(on.Z.wg,nb());Nn=0};a();x.setInterval(a,864E5)}else un(on.Z.wg,function(){Nn=0});Nn=0}function Pn(){On();Fn("v","3");Fn("t","t");Fn("pid",function(){return String(sn(on.Z.wg))});Fn("seq",function(){return String(++Nn)});Fn("exp",jk());Jc(x,"pagehide",Mn)};var Qn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Rn=[J.m.md,J.m.vc,J.m.Yd,J.m.Qb,J.m.uc,J.m.Qa,J.m.Pa,J.m.cb,J.m.nb,J.m.Sb],Sn=!1,Tn=!1,Un={},Vn={};function Wn(){!Tn&&Sn&&(Qn.some(function(a){return Vm.containerScopedDefaults[a]!==1})||Xn("mbc"));Tn=!0}function Xn(a){gl&&(Fn(a,"1"),Jn())}function Yn(a,b){if(!Un[b]&&(Un[b]=!0,Vn[b]))for(var c=l(Rn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){Xn("erc");break}};function Zn(a){cb("HEALTH",a)};var $n={rp:Pi(22,"eyIwIjoiVFciLCIxIjoiVFctVEFPIiwiMiI6ZmFsc2UsIjMiOiJnb29nbGUuY29tLnR3IiwiNCI6IiIsIjUiOnRydWUsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0")},ao={},bo=!1;function co(){function a(){c!==void 0&&vn(on.Z.Ff,c);try{var e=sn(on.Z.Ff);ao=JSON.parse(e)}catch(f){L(123),Zn(2),ao={}}bo=!0;b()}var b=eo,c=void 0,d=sn(on.Z.Ff);d?a(d):(c=un(on.Z.Ff,a),fo())}
function fo(){function a(c){rn(on.Z.Ff,c||"{}");rn(on.Z.Ai,!1)}if(!sn(on.Z.Ai)){rn(on.Z.Ai,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function go(){var a=$n.rp;try{return JSON.parse(ab(a))}catch(b){return L(123),Zn(2),{}}}function ho(){return ao["0"]||""}function io(){return ao["1"]||""}function jo(){var a=!1;a=!!ao["2"];return a}function ko(){return ao["6"]!==!1}function lo(){var a="";a=ao["4"]||"";return a}
function mo(){var a=!1;a=!!ao["5"];return a}function no(){var a="";a=ao["3"]||"";return a};var oo={},po=Object.freeze((oo[J.m.Ea]=1,oo[J.m.yg]=1,oo[J.m.zg]=1,oo[J.m.Ob]=1,oo[J.m.sa]=1,oo[J.m.nb]=1,oo[J.m.ob]=1,oo[J.m.yb]=1,oo[J.m.ed]=1,oo[J.m.Sb]=1,oo[J.m.cb]=1,oo[J.m.Hc]=1,oo[J.m.af]=1,oo[J.m.oa]=1,oo[J.m.lk]=1,oo[J.m.df]=1,oo[J.m.Jg]=1,oo[J.m.Kg]=1,oo[J.m.Yd]=1,oo[J.m.Bk]=1,oo[J.m.rc]=1,oo[J.m.be]=1,oo[J.m.Dk]=1,oo[J.m.Ng]=1,oo[J.m.ei]=1,oo[J.m.Kc]=1,oo[J.m.Lc]=1,oo[J.m.Pa]=1,oo[J.m.fi]=1,oo[J.m.Vb]=1,oo[J.m.pb]=1,oo[J.m.ld]=1,oo[J.m.md]=1,oo[J.m.qf]=1,oo[J.m.hi]=1,oo[J.m.tf]=1,oo[J.m.vc]=
1,oo[J.m.od]=1,oo[J.m.Ug]=1,oo[J.m.Wb]=1,oo[J.m.rd]=1,oo[J.m.Ji]=1,oo));Object.freeze([J.m.Aa,J.m.Wa,J.m.Db,J.m.zb,J.m.gi,J.m.Qa,J.m.ai,J.m.zn]);
var qo={},ro=Object.freeze((qo[J.m.bn]=1,qo[J.m.dn]=1,qo[J.m.fn]=1,qo[J.m.gn]=1,qo[J.m.hn]=1,qo[J.m.ln]=1,qo[J.m.mn]=1,qo[J.m.nn]=1,qo[J.m.pn]=1,qo[J.m.Sd]=1,qo)),so={},to=Object.freeze((so[J.m.bk]=1,so[J.m.dk]=1,so[J.m.Od]=1,so[J.m.Pd]=1,so[J.m.ek]=1,so[J.m.Xc]=1,so[J.m.Qd]=1,so[J.m.jc]=1,so[J.m.Gc]=1,so[J.m.kc]=1,so[J.m.kb]=1,so[J.m.Rd]=1,so[J.m.xb]=1,so[J.m.fk]=1,so)),uo=Object.freeze([J.m.Ea,J.m.Qe,J.m.Ob,J.m.Hc,J.m.Yd,J.m.kf,J.m.pb,J.m.od]),vo=Object.freeze([].concat(ua(uo))),wo=Object.freeze([J.m.ob,
J.m.Kg,J.m.qf,J.m.hi,J.m.Gg]),xo=Object.freeze([].concat(ua(wo))),yo={},zo=(yo[J.m.U]="1",yo[J.m.ia]="2",yo[J.m.V]="3",yo[J.m.La]="4",yo),Ao={},Bo=Object.freeze((Ao.search="s",Ao.youtube="y",Ao.playstore="p",Ao.shopping="h",Ao.ads="a",Ao.maps="m",Ao));function Co(a){return typeof a!=="object"||a===null?{}:a}function Do(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Eo(a){if(a!==void 0&&a!==null)return Do(a)}function Fo(a){return typeof a==="number"?a:Eo(a)};function Go(a){return a&&a.indexOf("pending:")===0?Ho(a.substr(8)):!1}function Ho(a){if(a==null||a.length===0)return!1;var b=Number(a),c=yb();return b<c+3E5&&b>c-9E5};var Io=!1,Jo=!1,Ko=!1,Lo=0,Mo=!1,No=[];function Oo(a){if(Lo===0)Mo&&No&&(No.length>=100&&No.shift(),No.push(a));else if(Po()){var b=Pi(41,'google.tagmanager.ta.prodqueue'),c=wc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Qo(){Ro();Kc(z,"TAProdDebugSignal",Qo)}function Ro(){if(!Jo){Jo=!0;So();var a=No;No=void 0;a==null||a.forEach(function(b){Oo(b)})}}
function So(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Ho(a)?Lo=1:!Go(a)||Io||Ko?Lo=2:(Ko=!0,Jc(z,"TAProdDebugSignal",Qo,!1),x.setTimeout(function(){Ro();Io=!0},200))}function Po(){if(!Mo)return!1;switch(Lo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var To=!1;function Uo(a,b){var c=zm(),d=xm();if(Po()){var e=Vo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Oo(e)}}
function Wo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=Po()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Vo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Oo(h)}}function Xo(a){Po()&&Wo(a())}
function Vo(a,b){b=b===void 0?{}:b;b.groupId=Yo;var c,d=b,e={publicId:Zo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'8',messageType:a};c.containerProduct=To?"OGT":"GTM";c.key.targetRef=$o;return c}var Zo="",$o={ctid:"",isDestination:!1},Yo;
function ap(a){var b=jg.ctid,c=wm();Lo=0;Mo=!0;So();Yo=a;Zo=b;To=Zj;$o={ctid:b,isDestination:c}};var bp=[J.m.U,J.m.ia,J.m.V,J.m.La],cp,dp;function ep(a){var b=a[J.m.fc];b||(b=[""]);for(var c={Zf:0};c.Zf<b.length;c={Zf:c.Zf},++c.Zf)qb(a,function(d){return function(e,f){if(e!==J.m.fc){var g=Do(f),h=b[d.Zf],m=ho(),n=io();Tm=!0;Sm&&cb("TAGGING",20);Om().declare(e,g,h,m,n)}}}(c))}
function fp(a){Wn();!dp&&cp&&Xn("crc");dp=!0;var b=a[J.m.qg];b&&L(41);var c=a[J.m.fc];c?L(40):c=[""];for(var d={cg:0};d.cg<c.length;d={cg:d.cg},++d.cg)qb(a,function(e){return function(f,g){if(f!==J.m.fc&&f!==J.m.qg){var h=Eo(g),m=c[e.cg],n=Number(b),p=ho(),q=io();n=n===void 0?0:n;Sm=!0;Tm&&cb("TAGGING",20);Om().default(f,h,m,p,q,n,Vm)}}}(d))}
function gp(a){Vm.usedContainerScopedDefaults=!0;var b=a[J.m.fc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(io())&&!c.includes(ho()))return}qb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Vm.usedContainerScopedDefaults=!0;Vm.containerScopedDefaults[d]=e==="granted"?3:2})}
function hp(a,b){Wn();cp=!0;qb(a,function(c,d){var e=Do(d);Sm=!0;Tm&&cb("TAGGING",20);Om().update(c,e,Vm)});an(b.eventId,b.priorityId)}function ip(a){a.hasOwnProperty("all")&&(Vm.selectedAllCorePlatformServices=!0,qb(Bo,function(b){Vm.corePlatformServices[b]=a.all==="granted";Vm.usedCorePlatformServices=!0}));qb(a,function(b,c){b!=="all"&&(Vm.corePlatformServices[b]=c==="granted",Vm.usedCorePlatformServices=!0)})}function O(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Wm(b)})}
function jp(a,b){$m(a,b)}function kp(a,b){cn(a,b)}function lp(a,b){bn(a,b)}function mp(){var a=[J.m.U,J.m.La,J.m.V];Om().waitForUpdate(a,500,Vm)}function np(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Om().clearTimeout(d,void 0,Vm)}an()}function op(){if(!bk)for(var a=ko()?mk(Mj.Sa):mk(Mj.qb),b=0;b<bp.length;b++){var c=bp[b],d=c,e=a[c]?"granted":"denied";Om().implicit(d,e)}};var pp=!1,qp=[];function rp(){if(!pp){pp=!0;for(var a=qp.length-1;a>=0;a--)qp[a]();qp=[]}};var sp=x.google_tag_manager=x.google_tag_manager||{};function tp(a,b){return sp[a]=sp[a]||b()}function up(){var a=jg.ctid,b=vp;sp[a]=sp[a]||b}function wp(){var a=sp.sequence||1;sp.sequence=a+1;return a};function xp(){if(sp.pscdl!==void 0)sn(on.Z.Kh)===void 0&&rn(on.Z.Kh,sp.pscdl);else{var a=function(c){sp.pscdl=c;rn(on.Z.Kh,c)},b=function(){a("error")};try{sc.cookieDeprecationLabel?(a("pending"),sc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var yp=0;function zp(a){gl&&a===void 0&&yp===0&&(Fn("mcc","1"),yp=1)};var Ap={Df:{Sm:"cd",Tm:"ce",Um:"cf",Vm:"cpf",Wm:"cu"}};var Bp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Cp=/\s/;
function Dp(a,b){if(jb(a)){a=wb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Bp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Cp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Ep(a,b){for(var c={},d=0;d<a.length;++d){var e=Dp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Fp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Gp={},Fp=(Gp[0]=0,Gp[1]=1,Gp[2]=2,Gp[3]=0,Gp[4]=1,Gp[5]=0,Gp[6]=0,Gp[7]=0,Gp);var Hp=Number('')||500,Ip={},Jp={},Kp={initialized:11,complete:12,interactive:13},Lp={},Mp=Object.freeze((Lp[J.m.pb]=!0,Lp)),Np=void 0;function Op(a,b){if(b.length&&gl){var c;(c=Ip)[a]!=null||(c[a]=[]);Jp[a]!=null||(Jp[a]=[]);var d=b.filter(function(e){return!Jp[a].includes(e)});Ip[a].push.apply(Ip[a],ua(d));Jp[a].push.apply(Jp[a],ua(d));!Np&&d.length>0&&(Gn("tdc",!0),Np=x.setTimeout(function(){Jn();Ip={};Np=void 0},Hp))}}
function Pp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Qp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;id(t)==="object"?u=t[r]:id(t)==="array"&&(u=t[r]);return u===void 0?Mp[r]:u},f=Pp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=id(m)==="object"||id(m)==="array",q=id(n)==="object"||id(n)==="array";if(p&&q)Qp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Rp(){Fn("tdc",function(){Np&&(x.clearTimeout(Np),Np=void 0);var a=[],b;for(b in Ip)Ip.hasOwnProperty(b)&&a.push(b+"*"+Ip[b].join("."));return a.length?a.join("!"):void 0},!1)};var Sp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Tp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l(Tp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Up=function(a){for(var b={},c=Tp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Sp.prototype.getMergedValues=function(a,b,c){function d(n){kd(n)&&qb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Tp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Vp=function(a){for(var b=[J.m.Ve,J.m.Re,J.m.Se,J.m.Te,J.m.Ue,J.m.We,J.m.Xe],c=Tp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Wp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Xp=function(a,
b){a.H=b;return a},Yp=function(a,b){a.R=b;return a},Zp=function(a,b){a.C=b;return a},$p=function(a,b){a.N=b;return a},aq=function(a,b){a.ba=b;return a},bq=function(a,b){a.P=b;return a},cq=function(a,b){a.eventMetadata=b||{};return a},dq=function(a,b){a.onSuccess=b;return a},eq=function(a,b){a.onFailure=b;return a},fq=function(a,b){a.isGtmEvent=b;return a},gq=function(a){return new Sp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={A:{Jj:"accept_by_default",pg:"add_tag_timing",Gh:"allow_ad_personalization",Lj:"batch_on_navigation",Nj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Nq:"consent_state",ja:"consent_updated",Wc:"conversion_linker_enabled",ya:"cookie_options",sg:"create_dc_join",tg:"create_fpm_geo_join",ug:"create_fpm_signals_join",Nd:"create_google_join",Ke:"em_event",Qq:"endpoint_for_debug",Zj:"enhanced_client_id_source",Nh:"enhanced_match_result",he:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Xk:"event_usage",Wg:"extra_tag_experiment_ids",Xq:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",Xg:"send_as_iframe",Yq:"parameter_order",Yg:"parsed_target",Xn:"ga4_collection_subdomain",al:"gbraid_cookie_marked",fa:"hit_type",sd:"hit_type_override",co:"is_config_command",Gf:"is_consent_update",Hf:"is_conversion",jl:"is_ecommerce",ud:"is_external_event",Bi:"is_fallback_aw_conversion_ping_allowed",If:"is_first_visit",kl:"is_first_visit_conversion",Zg:"is_fl_fallback_conversion_flow_allowed",
je:"is_fpm_encryption",ah:"is_fpm_split",ke:"is_gcp_conversion",Ci:"is_google_signals_allowed",vd:"is_merchant_center",ml:"is_new_join_id_required",bh:"is_new_to_site",eh:"is_server_side_destination",me:"is_session_start",ol:"is_session_start_conversion",er:"is_sgtm_ga_ads_conversion_study_control_group",gr:"is_sgtm_prehit",pl:"is_sgtm_service_worker",Di:"is_split_conversion",eo:"is_syn",ne:"join_id",Ei:"join_elapsed",Jf:"join_timer_sec",qe:"tunnel_updated",kr:"prehit_for_retry",mr:"promises",nr:"record_aw_latency",
yc:"redact_ads_data",se:"redact_click_ids",qo:"remarketing_only",zl:"send_ccm_parallel_ping",ih:"send_fledge_experiment",rr:"send_ccm_parallel_test_ping",Of:"send_to_destinations",Ii:"send_to_targets",Al:"send_user_data_hit",hb:"source_canonical_id",Ha:"speculative",Fl:"speculative_in_message",Gl:"suppress_script_load",Hl:"syn_or_mod",Ll:"transient_ecsid",Pf:"transmission_type",ib:"user_data",wr:"user_data_from_automatic",xr:"user_data_from_automatic_getter",ue:"user_data_from_code",mh:"user_data_from_manual",
Nl:"user_data_mode",Qf:"user_id_updated"}};var hq={Nm:Number("5"),Or:Number("")},iq=[],jq=!1;function kq(a){iq.push(a)}var lq="?id="+jg.ctid,mq=void 0,nq={},oq=void 0,pq=new function(){var a=5;hq.Nm>0&&(a=hq.Nm);this.H=a;this.C=0;this.N=[]},qq=1E3;
function rq(a,b){var c=mq;if(c===void 0)if(b)c=wp();else return"";for(var d=[$k("https://www.googletagmanager.com"),"/a",lq],e=l(iq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Md:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function sq(){if(Mj.ba&&(oq&&(x.clearTimeout(oq),oq=void 0),mq!==void 0&&tq)){var a=nn(Nm.X.Oc);if(jn(a))jq||(jq=!0,ln(a,sq));else{var b;if(!(b=nq[mq])){var c=pq;b=c.C<c.H?!1:yb()-c.N[c.C%c.H]<1E3}if(b||qq--<=0)L(1),nq[mq]=!0;else{var d=pq,e=d.C++%d.H;d.N[e]=yb();var f=rq(!0);fm({destinationId:jg.ctid,endpoint:56,eventId:mq},f);jq=tq=!1}}}}function uq(){if(fl&&Mj.ba){var a=rq(!0,!0);fm({destinationId:jg.ctid,endpoint:56,eventId:mq},a)}}var tq=!1;
function vq(a){nq[a]||(a!==mq&&(sq(),mq=a),tq=!0,oq||(oq=x.setTimeout(sq,500)),rq().length>=2022&&sq())}var wq=nb();function xq(){wq=nb()}function yq(){return[["v","3"],["t","t"],["pid",String(wq)]]};var zq={};function Aq(a,b,c){fl&&a!==void 0&&(zq[a]=zq[a]||[],zq[a].push(c+b),vq(a))}function Bq(a){var b=a.eventId,c=a.Md,d=[],e=zq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete zq[b];return d};function Cq(a,b,c,d){var e=Dp(a,!0);e&&Dq.register(e,b,c,d)}function Eq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&(Yj&&(d.deferrable=!0),Dq.push("event",[b,a],e,d))}function Fq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&Dq.push("get",[a,b],e,d)}function Gq(a){var b=Dp(a,!0),c;b?c=Hq(Dq,b).C:c={};return c}function Iq(a,b){var c=Dp(a,!0);c&&Jq(Dq,c,b)}
var Kq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Lq=function(a,b,c,d){this.H=yb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Mq=function(){this.destinations={};this.C={};this.commands=[]},Hq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Kq},Nq=function(a,b,c,d){if(d.C){var e=Hq(a,d.C),f=e.ba;if(f){var g=ld(c,null),h=ld(e.R[d.C.id],null),m=ld(e.P,null),n=ld(e.C,null),p=ld(a.C,null),q={};if(fl)try{q=
ld(ok,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Aq(d.messageContext.eventId,r,w)},u=gq(fq(eq(dq(cq(aq($p(bq(Zp(Yp(Xp(new Wp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Aq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(gl&&w==="config"){var A,D=(A=Dp(y))==null?void 0:A.ids;if(!(D&&D.length>1)){var E,G=wc("google_tag_data",{});G.td||(G.td={});E=G.td;var I=ld(u.P);ld(u.C,I);var M=[],S;for(S in E)E.hasOwnProperty(S)&&Qp(E[S],I).length&&M.push(S);M.length&&(Op(y,M),cb("TAGGING",Kp[z.readyState]||14));E[y]=I}}f(d.C.id,b,d.H,u)}catch(ea){Aq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():ln(e.ka,v)}}};
Mq.prototype.register=function(a,b,c,d){var e=Hq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=nn(c),Jq(this,a,d||{}),this.flush())};
Mq.prototype.push=function(a,b,c,d){c!==void 0&&(Hq(this,c).status===1&&(Hq(this,c).status=2,this.push("require",[{}],c,{})),Hq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.A.Of]||(d.eventMetadata[P.A.Of]=[c.destinationId]),d.eventMetadata[P.A.Ii]||(d.eventMetadata[P.A.Ii]=[c.id]));this.commands.push(new Lq(a,c,b,d));d.deferrable||this.flush()};
Mq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,rh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Hq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Hq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];qb(h,function(t,u){ld(Fb(t,u),b.C)});Kj(h,!0);break;case "config":var m=Hq(this,g);
e.Qc={};qb(f.args[0],function(t){return function(u,v){ld(Fb(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.od];delete e.Qc[J.m.od];var p=g.destinationId===g.id;Kj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Nq(this,J.m.qa,e.Qc,f);m.N=!0;p?ld(e.Qc,m.P):(ld(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.rh={};qb(f.args[0],function(t){return function(u,v){ld(Fb(u,v),t.rh)}}(e));Kj(e.rh);Nq(this,f.args[1],e.rh,f);break;case "get":var q={},r=(q[J.m.qc]=f.args[0],q[J.m.Ic]=f.args[1],q);Nq(this,J.m.Cb,r,f)}this.commands.shift();
Oq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Oq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Hq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Jq=function(a,b,c){var d=ld(c,null);ld(Hq(a,b).C,d);Hq(a,b).C=d},Dq=new Mq;function Pq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Qq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Rq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Hl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=pc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Qq(e,"load",f);Qq(e,"error",f)};Pq(e,"load",f);Pq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Sq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";El(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Tq(c,b)}
function Tq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Rq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Uq=function(){this.ba=this.ba;this.P=this.P};Uq.prototype.ba=!1;Uq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Uq.prototype[Symbol.dispose]=function(){this.dispose()};Uq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Uq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Vq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Wq=function(a,b){b=b===void 0?{}:b;Uq.call(this);this.C=null;this.ka={};this.qb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Dr)!=null?d:!1};ra(Wq,Uq);Wq.prototype.N=function(){this.ka={};this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Uq.prototype.N.call(this)};var Yq=function(a){return typeof a.H.__tcfapi==="function"||Xq(a)!=null};
Wq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=il(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Vq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Zq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Wq.prototype.removeEventListener=function(a){a&&a.listenerId&&Zq(this,"removeEventListener",null,a.listenerId)};
var ar=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=$q(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&$q(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?$q(a.purpose.legitimateInterests,
b)&&$q(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},$q=function(a,b){return!(!a||!a[b])},Zq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Xq(a)){br(a);var g=++a.qb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Xq=function(a){if(a.C)return a.C;a.C=Fl(a.H,"__tcfapiLocator");return a.C},br=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Pq(a.H,"message",b)}},cr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Vq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Sq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var dr={1:0,3:0,4:0,7:3,9:3,10:3};function er(){return tp("tcf",function(){return{}})}var fr=function(){return new Wq(x,{timeoutMs:-1})};
function gr(){var a=er(),b=fr();Yq(b)&&!hr()&&!ir()&&L(124);if(!a.active&&Yq(b)){hr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Om().active=!0,a.tcString="tcunavailable");mp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)jr(a),np([J.m.U,J.m.La,J.m.V]),Om().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,ir()&&(a.active=!0),!kr(c)||hr()||ir()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in dr)dr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(kr(c)){var g={},h;for(h in dr)if(dr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={qp:!0};p=p===void 0?{}:p;m=cr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.qp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?ar(n,"1",0):!0:!1;g["1"]=m}else g[h]=ar(c,h,dr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(np([J.m.U,J.m.La,J.m.V]),Om().active=!0):(r[J.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":np([J.m.V]),hp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:lr()||""}))}}else np([J.m.U,J.m.La,J.m.V])})}catch(c){jr(a),np([J.m.U,J.m.La,J.m.V]),Om().active=!0}}}
function jr(a){a.type="e";a.tcString="tcunavailable"}function kr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function hr(){return x.gtag_enable_tcf_support===!0}function ir(){return er().enableAdvertiserConsentMode===!0}function lr(){var a=er();if(a.active)return a.tcString}function mr(){var a=er();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function nr(a){if(!dr.hasOwnProperty(String(a)))return!0;var b=er();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var or=[J.m.U,J.m.ia,J.m.V,J.m.La],pr={},qr=(pr[J.m.U]=1,pr[J.m.ia]=2,pr);function rr(a){if(a===void 0)return 0;switch(N(a,J.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function sr(){return(C(183)?Vi.xp:Vi.yp).indexOf(io())!==-1&&sc.globalPrivacyControl===!0}function tr(a){if(sr())return!1;var b=rr(a);if(b===3)return!1;switch(Xm(J.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function ur(){return Zm()||!Wm(J.m.U)||!Wm(J.m.ia)}function vr(){var a={},b;for(b in qr)qr.hasOwnProperty(b)&&(a[qr[b]]=Xm(b));return"G1"+bf(a[1]||0)+bf(a[2]||0)}var wr={},xr=(wr[J.m.U]=0,wr[J.m.ia]=1,wr[J.m.V]=2,wr[J.m.La]=3,wr);function yr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function zr(a){for(var b="1",c=0;c<or.length;c++){var d=b,e,f=or[c],g=Vm.delegatedConsentTypes[f];e=g===void 0?0:xr.hasOwnProperty(g)?12|xr[g]:8;var h=Om();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|yr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[yr(m.declare)<<4|yr(m.default)<<2|yr(m.update)])}var n=b,p=(sr()?1:0)<<3,q=(Zm()?1:0)<<2,r=rr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Vm.containerScopedDefaults.ad_storage<<4|Vm.containerScopedDefaults.analytics_storage<<2|Vm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Vm.usedContainerScopedDefaults?1:0)<<2|Vm.containerScopedDefaults.ad_personalization]}
function Ar(){if(!Wm(J.m.V))return"-";for(var a=Object.keys(Bo),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Vm.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Bo[m])}(Vm.usedCorePlatformServices?Vm.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Br(){return ko()||(hr()||ir())&&mr()==="1"?"1":"0"}function Cr(){return(ko()?!0:!(!hr()&&!ir())&&mr()==="1")||!Wm(J.m.V)}
function Dr(){var a="0",b="0",c;var d=er();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=er();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;ko()&&(h|=1);mr()==="1"&&(h|=2);hr()&&(h|=4);var m;var n=er();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Om().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Er(){return io()==="US-CO"};var fg;function Fr(){var a=!1;return a}function Gr(){C(212)&&Zj&&gg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Hr;function Ir(){if(vc===null)return 0;var a=$c();if(!a)return 0;var b=a.getEntriesByName(vc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Jr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Kr(a){a=a===void 0?{}:a;var b=jg.ctid.split("-")[0].toUpperCase(),c={ctid:jg.ctid,zj:Sj,Dj:Rj,fm:vm.pe?2:1,Cq:a.Dm,ve:jg.canonicalContainerId};if(C(210)){var d;c.sq=(d=Cm())==null?void 0:d.canonicalContainerId}if(C(204)){var e;c.Mo=(e=Hr)!=null?e:Hr=Ir()}c.ve!==a.Ma&&(c.Ma=a.Ma);var f=Am();c.qm=f?f.canonicalContainerId:void 0;Zj?(c.Uc=Jr[b],c.Uc||(c.Uc=0)):c.Uc=bk?13:10;Mj.C?(c.Sc=0,c.Rl=2):Mj.N?c.Sc=1:Fr()?c.Sc=2:c.Sc=3;var g={6:!1};Mj.H===2?g[7]=!0:Mj.H===1&&(g[2]=!0);if(vc){var h=Lk(Rk(vc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Tl=g;return ef(c,a.oh)}
function Lr(){if(!C(192))return Kr();if(C(193))return ef({zj:Sj,Dj:Rj});var a=jg.ctid.split("-")[0].toUpperCase(),b={ctid:jg.ctid,zj:Sj,Dj:Rj,fm:vm.pe?2:1,ve:jg.canonicalContainerId},c=Am();b.qm=c?c.canonicalContainerId:void 0;Zj?(b.Uc=Jr[a],b.Uc||(b.Uc=0)):b.Uc=bk?13:10;Mj.C?(b.Sc=0,b.Rl=2):Mj.N?b.Sc=1:Fr()?b.Sc=2:b.Sc=3;var d={6:!1};Mj.H===2?d[7]=!0:Mj.H===1&&(d[2]=!0);if(vc){var e=Lk(Rk(vc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Tl=d;return ef(b)};function Mr(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||yb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var Nr=["ad_storage","ad_user_data"];function Or(a,b){if(!a)return cb("TAGGING",32),10;if(b===null||b===void 0||b==="")return cb("TAGGING",33),11;var c=Pr(!1);if(c.error!==0)return cb("TAGGING",34),c.error;if(!c.value)return cb("TAGGING",35),2;c.value[a]=b;var d=Qr(c);d!==0&&cb("TAGGING",36);return d}
function Rr(a){if(!a)return cb("TAGGING",27),{error:10};var b=Pr();if(b.error!==0)return cb("TAGGING",29),b;if(!b.value)return cb("TAGGING",30),{error:2};if(!(a in b.value))return cb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(cb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Pr(a){a=a===void 0?!0:a;if(!Wm(Nr))return cb("TAGGING",43),{error:3};try{if(!x.localStorage)return cb("TAGGING",44),{error:1}}catch(f){return cb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return cb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return cb("TAGGING",47),{error:12}}}catch(f){return cb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return cb("TAGGING",49),{error:4};
if(b.version!==1)return cb("TAGGING",50),{error:5};try{var e=Sr(b);a&&e&&Qr({value:b,error:0})}catch(f){return cb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Sr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,cb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Sr(a[e.value])||c;return c}return!1}
function Qr(a){if(a.error)return a.error;if(!a.value)return cb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return cb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return cb("TAGGING",53),7}return 0};var Tr={pj:"value",Gb:"conversionCount"},Ur=[Tr,{dm:9,xm:10,pj:"timeouts",Gb:"timeouts"}];function Vr(){var a=Tr;if(!Wr(a))return{};var b=Xr(Ur),c=b[a.Gb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Gb]=c+1,d));return Yr(e)?e:b}
function Xr(a){var b;a:{var c=Rr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Wr(m)){var n=e[m.pj];n===void 0||Number.isNaN(n)?f[m.Gb]=-1:f[m.Gb]=Number(n)}else f[m.Gb]=-1}return f}
function Yr(a,b){b=b||{};for(var c=yb(),d=Mr(b,c,!0),e={},f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Gb];m!==void 0&&m!==-1&&(e[h.pj]=m)}e.creationTimeMs=c;return Or("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Wr(a){return Wm(["ad_storage","ad_user_data"])?!a.xm||Ja(a.xm):!1}function Zr(a){return Wm(["ad_storage","ad_user_data"])?!a.dm||Ja(a.dm):!1};function $r(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var as={O:{ro:0,Kj:1,rg:2,Qj:3,Ih:4,Oj:5,Pj:6,Rj:7,Jh:8,Vk:9,Uk:10,si:11,Wk:12,Vg:13,Zk:14,Lf:15,oo:16,te:17,Ni:18,Oi:19,Pi:20,Jl:21,Qi:22,Lh:23,Yj:24}};as.O[as.O.ro]="RESERVED_ZERO";as.O[as.O.Kj]="ADS_CONVERSION_HIT";as.O[as.O.rg]="CONTAINER_EXECUTE_START";as.O[as.O.Qj]="CONTAINER_SETUP_END";as.O[as.O.Ih]="CONTAINER_SETUP_START";as.O[as.O.Oj]="CONTAINER_BLOCKING_END";as.O[as.O.Pj]="CONTAINER_EXECUTE_END";as.O[as.O.Rj]="CONTAINER_YIELD_END";as.O[as.O.Jh]="CONTAINER_YIELD_START";as.O[as.O.Vk]="EVENT_EXECUTE_END";
as.O[as.O.Uk]="EVENT_EVALUATION_END";as.O[as.O.si]="EVENT_EVALUATION_START";as.O[as.O.Wk]="EVENT_SETUP_END";as.O[as.O.Vg]="EVENT_SETUP_START";as.O[as.O.Zk]="GA4_CONVERSION_HIT";as.O[as.O.Lf]="PAGE_LOAD";as.O[as.O.oo]="PAGEVIEW";as.O[as.O.te]="SNIPPET_LOAD";as.O[as.O.Ni]="TAG_CALLBACK_ERROR";as.O[as.O.Oi]="TAG_CALLBACK_FAILURE";as.O[as.O.Pi]="TAG_CALLBACK_SUCCESS";as.O[as.O.Jl]="TAG_EXECUTE_END";as.O[as.O.Qi]="TAG_EXECUTE_START";as.O[as.O.Lh]="CUSTOM_PERFORMANCE_START";as.O[as.O.Yj]="CUSTOM_PERFORMANCE_END";var bs=[],cs={},ds={};var es=["1"];function fs(a){return a.origin!=="null"};function gs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ja(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function hs(a,b,c,d){if(!is(d))return[];if(bs.includes("1")){var e;(e=$c())==null||e.mark("1-"+as.O.Lh+"-"+(ds["1"]||0))}var f=gs(a,String(b||js()),c);if(bs.includes("1")){var g="1-"+as.O.Yj+"-"+(ds["1"]||0),h={start:"1-"+as.O.Lh+"-"+(ds["1"]||0),end:g},m;(m=$c())==null||m.mark(g);var n,p,q=(p=(n=$c())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(ds["1"]=(ds["1"]||0)+1,cs["1"]=q+(cs["1"]||0))}return f}
function ks(a,b,c,d,e){if(is(e)){var f=ls(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=ms(f,function(g){return g.Zo},b);if(f.length===1)return f[0];f=ms(f,function(g){return g.bq},c);return f[0]}}}function ns(a,b,c,d){var e=js(),f=window;fs(f)&&(f.document.cookie=a);var g=js();return e!==g||c!==void 0&&hs(b,g,!1,d).indexOf(c)>=0}
function os(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!is(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ps(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Xp);g=e(g,"samesite",c.tq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=qs(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!rs(u,c.path)&&ns(v,a,b,c.Dc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return rs(n,c.path)?1:ns(g,a,b,c.Dc)?0:1}function ss(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return os(a,b,c)}
function ms(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ls(a,b,c){for(var d=[],e=hs(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Qo:e[f],Ro:g.join("."),Zo:Number(n[0])||1,bq:Number(n[1])||1})}}}return d}function ps(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ts=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,us=/(^|\.)doubleclick\.net$/i;function rs(a,b){return a!==void 0&&(us.test(window.document.location.hostname)||b==="/"&&ts.test(a))}function vs(a){if(!a)return 1;var b=a;Ja(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ws(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function xs(a,b){var c=""+vs(a),d=ws(b);d>1&&(c+="-"+d);return c}
var js=function(){return fs(window)?window.document.cookie:""},is=function(a){return a&&Ja(7)?(Array.isArray(a)?a:[a]).every(function(b){return Ym(b)&&Wm(b)}):!0},qs=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;us.test(e)||ts.test(e)||a.push("none");return a};function ys(a){var b=Math.round(Math.random()*2147483647);return a?String(b^$r(a)&2147483647):String(b)}function zs(a){return[ys(a),Math.round(yb()/1E3)].join(".")}function As(a,b,c,d,e){var f=vs(b),g;return(g=ks(a,f,ws(c),d,e))==null?void 0:g.Ro};var Bs;function Cs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ds,d=Es,e=Fs();if(!e.init){Jc(z,"mousedown",a);Jc(z,"keyup",a);Jc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Gs(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Fs().decorators.push(f)}
function Hs(a,b,c){for(var d=Fs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Bb(e,g.callback())}}return e}
function Fs(){var a=wc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Is=/(.*?)\*(.*?)\*(.*)/,Js=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ks=/^(?:www\.|m\.|amp\.)+/,Ls=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ms(a){var b=Ls.exec(a);if(b)return{vj:b[1],query:b[2],fragment:b[3]}}function Ns(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Os(a,b){var c=[sc.userAgent,(new Date).getTimezoneOffset(),sc.userLanguage||sc.language,Math.floor(yb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Bs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Bs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Bs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ps(a){return function(b){var c=Rk(x.location.href),d=c.search.replace("?",""),e=Ik(d,"_gl",!1,!0)||"";b.query=Qs(e)||{};var f=Lk(c,"fragment"),g;var h=-1;if(Db(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Qs(g||"")||{};a&&Rs(c,d,f)}}function Ss(a,b){var c=Ns(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Rs(a,b,c){function d(g,h){var m=Ss("_gl",g);m.length&&(m=h+m);return m}if(rc&&rc.replaceState){var e=Ns("_gl");if(e.test(b)||e.test(c)){var f=Lk(a,"path");b=d(b,"?");c=d(c,"#");rc.replaceState({},"",""+f+b+c)}}}function Ts(a,b){var c=Ps(!!b),d=Fs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Bb(e,f.query),a&&Bb(e,f.fragment));return e}
var Qs=function(a){try{var b=Us(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ab(d[e+1]);c[f]=g}cb("TAGGING",6);return c}}catch(h){cb("TAGGING",8)}};function Us(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Is.exec(d);if(f){c=f;break a}d=Kk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Os(h,p)){m=!0;break a}m=!1}if(m)return h;cb("TAGGING",7)}}}
function Vs(a,b,c,d,e){function f(p){p=Ss(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ms(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.vj+h+m}
function Ws(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push($a(String(y))))}var A=v.join("*");u=["1",Os(A),A].join("*");d?(Ja(3)||Ja(1)||!p)&&Xs("_gl",u,a,p,q):Ys("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Hs(b,1,d),f=Hs(b,2,d),g=Hs(b,4,d),h=Hs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ja(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Zs(m,h[m],a)}function Zs(a,b,c){c.tagName.toLowerCase()==="a"?Ys(a,b,c):c.tagName.toLowerCase()==="form"&&Xs(a,b,c)}function Ys(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ja(4)||d)){var h=x.location.href,m=Ms(c.href),n=Ms(h);g=!(m&&n&&m.vj===n.vj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Vs(a,b,c.href,d,e);hc.test(p)&&(c.href=p)}}
function Xs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Vs(a,b,f,d,e);hc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ds(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Ws(e,e.hostname)}}catch(g){}}function Es(a){try{var b=a.getAttribute("action");if(b){var c=Lk(Rk(b),"host");Ws(a,c)}}catch(d){}}function $s(a,b,c,d){Cs();var e=c==="fragment"?2:1;d=!!d;Gs(a,b,e,d,!1);e===2&&cb("TAGGING",23);d&&cb("TAGGING",24)}
function at(a,b){Cs();Gs(a,[Nk(x.location,"host",!0)],b,!0,!0)}function bt(){var a=z.location.hostname,b=Js.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Kk(f[2])||"":Kk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ks,""),m=e.replace(Ks,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ct(a,b){return a===!1?!1:a||b||bt()};var dt=["1"],et={},ft={};function gt(a,b){b=b===void 0?!0:b;var c=ht(a.prefix);if(et[c])it(a);else if(jt(c,a.path,a.domain)){var d=ft[ht(a.prefix)]||{id:void 0,Ah:void 0};b&&kt(a,d.id,d.Ah);it(a)}else{var e=Tk("auiddc");if(e)cb("TAGGING",17),et[c]=e;else if(b){var f=ht(a.prefix),g=zs();lt(f,g,a);jt(c,a.path,a.domain);it(a,!0)}}}
function it(a,b){if((b===void 0?0:b)&&Wr(Tr)){var c=Pr(!1);c.error!==0?cb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Qr(c)!==0&&cb("TAGGING",41)):cb("TAGGING",40):cb("TAGGING",39)}if(Zr(Tr)&&Xr([Tr])[Tr.Gb]===-1){for(var d={},e=(d[Tr.Gb]=0,d),f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Tr&&Zr(h)&&(e[h.Gb]=0)}Yr(e,a)}}
function kt(a,b,c){var d=ht(a.prefix),e=et[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(yb()/1E3)));lt(d,h,a,g*1E3)}}}}function lt(a,b,c,d){var e;e=["1",xs(c.domain,c.path),b].join(".");var f=Mr(c,d);f.Dc=mt();ss(a,e,f)}function jt(a,b,c){var d=As(a,b,c,dt,mt());if(!d)return!1;nt(a,d);return!0}
function nt(a,b){var c=b.split(".");c.length===5?(et[a]=c.slice(0,2).join("."),ft[a]={id:c.slice(2,4).join("."),Ah:Number(c[4])||0}):c.length===3?ft[a]={id:c.slice(0,2).join("."),Ah:Number(c[2])||0}:et[a]=b}function ht(a){return(a||"_gcl")+"_au"}function ot(a){function b(){Wm(c)&&a()}var c=mt();bn(function(){b();Wm(c)||cn(b,c)},c)}
function pt(a){var b=Ts(!0),c=ht(a.prefix);ot(function(){var d=b[c];if(d){nt(c,d);var e=Number(et[c].split(".")[1])*1E3;if(e){cb("TAGGING",16);var f=Mr(a,e);f.Dc=mt();var g=["1",xs(a.domain,a.path),d].join(".");ss(c,g,f)}}})}function qt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=As(a,e.path,e.domain,dt,mt());h&&(g[a]=h);return g};ot(function(){$s(f,b,c,d)})}function mt(){return["ad_storage","ad_user_data"]};function rt(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Gj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function st(a,b){var c=rt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Gj]||(d[c[e].Gj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Gj].push(g)}}return d};var tt={},ut=(tt.k={da:/^[\w-]+$/},tt.b={da:/^[\w-]+$/,Aj:!0},tt.i={da:/^[1-9]\d*$/},tt.h={da:/^\d+$/},tt.t={da:/^[1-9]\d*$/},tt.d={da:/^[A-Za-z0-9_-]+$/},tt.j={da:/^\d+$/},tt.u={da:/^[1-9]\d*$/},tt.l={da:/^[01]$/},tt.o={da:/^[1-9]\d*$/},tt.g={da:/^[01]$/},tt.s={da:/^.+$/},tt);var vt={},At=(vt[5]={Fh:{2:wt},oj:"2",ph:["k","i","b","u"]},vt[4]={Fh:{2:wt,GCL:xt},oj:"2",ph:["k","i","b"]},vt[2]={Fh:{GS2:wt,GS1:zt},oj:"GS2",ph:"sogtjlhd".split("")},vt);function Bt(a,b,c){var d=At[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Fh[e];if(f)return f(a,b)}}}
function wt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=At[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=ut[p];r&&(r.Aj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ct(a,b,c){var d=At[b];if(d)return[d.oj,c||"1",Dt(a,b)].join(".")}
function Dt(a,b){var c=At[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=ut[g];if(h){var m=a[g];if(m!==void 0)if(h.Aj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function xt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function zt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Et=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ft(a,b,c){if(At[b]){for(var d=[],e=hs(a,void 0,void 0,Et.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Bt(g.value,b,c);h&&d.push(Gt(h))}return d}}function Ht(a,b,c,d,e){d=d||{};var f=xs(d.domain,d.path),g=Ct(b,c,f);if(!g)return 1;var h=Mr(d,e,void 0,Et.get(c));return ss(a,g,h)}function It(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Gt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=ut[e];d.Tf?d.Tf.Aj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return It(h,g.Tf)}}(d)):void 0:typeof f==="string"&&It(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Jt=function(){this.value=0};Jt.prototype.set=function(a){return this.value|=1<<a};var Kt=function(a,b){b<=0||(a.value|=1<<b-1)};Jt.prototype.get=function(){return this.value};Jt.prototype.clear=function(a){this.value&=~(1<<a)};Jt.prototype.clearAll=function(){this.value=0};Jt.prototype.equals=function(a){return this.value===a.value};function Lt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Mt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Nt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a($r((""+b+e).toLowerCase()))};var Ot={},Pt=(Ot.gclid=!0,Ot.dclid=!0,Ot.gbraid=!0,Ot.wbraid=!0,Ot),Qt=/^\w+$/,Rt=/^[\w-]+$/,St={},Tt=(St.aw="_aw",St.dc="_dc",St.gf="_gf",St.gp="_gp",St.gs="_gs",St.ha="_ha",St.ag="_ag",St.gb="_gb",St),Ut=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Vt=/^www\.googleadservices\.com$/;function Wt(){return["ad_storage","ad_user_data"]}function Xt(a){return!Ja(7)||Wm(a)}function Yt(a,b){function c(){var d=Xt(b);d&&a();return d}bn(function(){c()||cn(c,b)},b)}
function Zt(a){return $t(a).map(function(b){return b.gclid})}function au(a){return bu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function bu(a){var b=cu(a.prefix),c=du("gb",b),d=du("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=$t(c).map(e("gb")),g=eu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function fu(a,b,c,d,e,f){var g=mb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Ed=f),g.labels=gu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Ed:f})}function eu(a){for(var b=Ft(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=hu(f);h&&fu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function $t(a){for(var b=[],c=hs(a,z.cookie,void 0,Wt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=iu(e.value);if(f!=null){var g=f;fu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ju(b)}function ku(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function lu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Jt,q=(n=b.Ka)!=null?n:new Jt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Ed=b.Ed);d.labels=ku(d.labels||[],b.labels||[]);d.Bb=ku(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function mu(a){if(!a)return new Jt;var b=new Jt;if(a===1)return Kt(b,2),Kt(b,3),b;Kt(b,a);return b}
function nu(){var a=Rr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Rt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Jt;typeof e==="number"?g=mu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Bb:[2]}}catch(h){return null}}
function ou(){var a=Rr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Rt))return b;var f=new Jt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Bb:[2]});return b},[])}catch(b){return null}}
function pu(a){for(var b=[],c=hs(a,z.cookie,void 0,Wt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=iu(e.value);f!=null&&(f.Ed=void 0,f.Ka=new Jt,f.Bb=[1],lu(b,f))}var g=nu();g&&(g.Ed=void 0,g.Bb=g.Bb||[2],lu(b,g));if(Ja(13)){var h=ou();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Ed=void 0;p.Bb=p.Bb||[2];lu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ju(b)}
function gu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function cu(a){return a&&typeof a==="string"&&a.match(Qt)?a:"_gcl"}function qu(a,b){if(a){var c={value:a,Ka:new Jt};Kt(c.Ka,b);return c}}
function ru(a,b,c){var d=Rk(a),e=Lk(d,"query",!1,void 0,"gclsrc"),f=qu(Lk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=qu(Ik(g,"gclid",!1),3));e||(e=Ik(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function su(a,b){var c=Rk(a),d=Lk(c,"query",!1,void 0,"gclid"),e=Lk(c,"query",!1,void 0,"gclsrc"),f=Lk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Lk(c,"query",!1,void 0,"gbraid"),h=Lk(c,"query",!1,void 0,"gad_source"),m=Lk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Ik(n,"gclid",!1);e=e||Ik(n,"gclsrc",!1);f=f||Ik(n,"wbraid",!1);g=g||Ik(n,"gbraid",!1);h=h||Ik(n,"gad_source",!1)}return tu(d,e,m,f,g,h)}function uu(){return su(x.location.href,!0)}
function tu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Rt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Rt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Rt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Rt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function vu(a){for(var b=uu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=su(x.document.referrer,!1),b.gad_source=void 0);wu(b,!1,a)}
function xu(a){vu(a);var b=ru(x.location.href,!0,!1);b.length||(b=ru(x.document.referrer,!1,!0));a=a||{};yu(a);if(b.length){var c=b[0],d=yb(),e=Mr(a,d,!0),f=Wt(),g=function(){Xt(f)&&e.expires!==void 0&&Or("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};bn(function(){g();Xt(f)||cn(g,f)},f)}}
function yu(a){var b;if(b=Ja(14)){var c=zu();b=Ut.test(c)||Vt.test(c)||Au()}if(b){var d;a:{for(var e=Rk(x.location.href),f=Jk(Lk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Pt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Lt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Mt(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,D=y,E=A,G=D&7;if(D>>3===16382){if(G!==0)break;var I=Mt(t,E);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var S=void 0,ea=t,U=E;switch(G){case 0:M=(S=Mt(ea,U))==null?void 0:S[1];break d;case 1:M=U+8;break d;case 2:var ta=Mt(ea,U);if(ta===void 0)break;var W=l(ta),da=W.next().value;M=W.next().value+da;break d;case 5:M=U+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Bu(Y,7,a)}}
function Bu(a,b,c){c=c||{};var d=yb(),e=Mr(c,d,!0),f=Wt(),g=function(){if(Xt(f)&&e.expires!==void 0){var h=ou()||[];lu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:mu(b)},!0);Or("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};bn(function(){Xt(f)?g():cn(g,f)},f)}
function wu(a,b,c,d,e){c=c||{};e=e||[];var f=cu(c.prefix),g=d||yb(),h=Math.round(g/1E3),m=Wt(),n=!1,p=!1,q=function(){if(Xt(m)){var r=Mr(c,g,!0);r.Dc=m;for(var t=function(S,ea){var U=du(S,f);U&&(ss(U,ea,r),S!=="gb"&&(n=!0))},u=function(S){var ea=["GCL",h,S];e.length>0&&ea.push(e.join("."));return ea.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],D=du("gb",f);!b&&$t(D).some(function(S){return S.gclid===A&&S.labels&&
S.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&Xt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,G=du("ag",f);if(b||!eu(G).some(function(S){return S.gclid===E&&S.labels&&S.labels.length>0})){var I={},M=(I.k=E,I.i=""+h,I.b=e,I);Ht(G,M,5,c,g)}}Cu(a,f,g,c)};bn(function(){q();Xt(m)||cn(q,m)},m)}
function Cu(a,b,c,d){if(a.gad_source!==void 0&&Xt("ad_storage")){var e=Zc();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=du("gs",b);if(g){var h=Math.floor((yb()-(Yc()||0))/1E3),m,n=Nt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Ht(g,m,5,d,c)}}}}
function Du(a,b){var c=Ts(!0);Yt(function(){for(var d=cu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Tt[f]!==void 0){var g=du(f,d),h=c[g];if(h){var m=Math.min(Eu(h),yb()),n;b:{for(var p=m,q=hs(g,z.cookie,void 0,Wt()),r=0;r<q.length;++r)if(Eu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Mr(b,m,!0);t.Dc=Wt();ss(g,h,t)}}}}wu(tu(c.gclid,c.gclsrc),!1,b)},Wt())}
function Fu(a){var b=["ag"],c=Ts(!0),d=cu(a.prefix);Yt(function(){for(var e=0;e<b.length;++e){var f=du(b[e],d);if(f){var g=c[f];if(g){var h=Bt(g,5);if(h){var m=hu(h);m||(m=yb());var n;a:{for(var p=m,q=Ft(f,5),r=0;r<q.length;++r)if(hu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ht(f,h,5,a,m)}}}}},["ad_storage"])}function du(a,b){var c=Tt[a];if(c!==void 0)return b+c}function Eu(a){return Gu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function hu(a){return a?(Number(a.i)||0)*1E3:0}function iu(a){var b=Gu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Gu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Rt.test(a[2])?[]:a}
function Hu(a,b,c,d,e){if(Array.isArray(b)&&fs(x)){var f=cu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=du(a[m],f);if(n){var p=hs(n,z.cookie,void 0,Wt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Yt(function(){$s(g,b,c,d)},Wt())}}
function Iu(a,b,c,d){if(Array.isArray(a)&&fs(x)){var e=["ag"],f=cu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=du(e[m],f);if(!n)return{};var p=Ft(n,5);if(p.length){var q=p.sort(function(r,t){return hu(t)-hu(r)})[0];h[n]=Ct(q,5)}}return h};Yt(function(){$s(g,a,b,c)},["ad_storage"])}}function ju(a){return a.filter(function(b){return Rt.test(b.gclid)})}
function Ju(a,b){if(fs(x)){for(var c=cu(b.prefix),d={},e=0;e<a.length;e++)Tt[a[e]]&&(d[a[e]]=Tt[a[e]]);Yt(function(){qb(d,function(f,g){var h=hs(c+g,z.cookie,void 0,Wt());h.sort(function(t,u){return Eu(u)-Eu(t)});if(h.length){var m=h[0],n=Eu(m),p=Gu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Gu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];wu(q,!0,b,n,p)}})},Wt())}}
function Ku(a){var b=["ag"],c=["gbraid"];Yt(function(){for(var d=cu(a.prefix),e=0;e<b.length;++e){var f=du(b[e],d);if(!f)break;var g=Ft(f,5);if(g.length){var h=g.sort(function(q,r){return hu(r)-hu(q)})[0],m=hu(h),n=h.b,p={};p[c[e]]=h.k;wu(p,!0,a,m,n)}}},["ad_storage"])}function Lu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Mu(a){function b(h,m,n){n&&(h[m]=n)}if(Zm()){var c=uu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Ts(!1)._gs);if(Lu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);at(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);at(function(){return g},1)}}}function Au(){var a=Rk(x.location.href);return Lk(a,"query",!1,void 0,"gad_source")}
function Nu(a){if(!Ja(1))return null;var b=Ts(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ja(2)){b=Au();if(b!=null)return b;var c=uu();if(Lu(c,a))return"0"}return null}function Ou(a){var b=Nu(a);b!=null&&at(function(){var c={};return c.gad_source=b,c},4)}function Pu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Qu(a,b,c,d){var e=[];c=c||{};if(!Xt(Wt()))return e;var f=$t(a),g=Pu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Mr(c,p,!0);r.Dc=Wt();ss(a,q,r)}return e}
function Ru(a,b){var c=[];b=b||{};var d=bu(b),e=Pu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=cu(b.prefix),n=du(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Ht(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),D=Mr(b,u,!0);D.Dc=Wt();ss(n,A,D)}}return c}
function Su(a,b){var c=cu(b),d=du(a,c);if(!d)return 0;var e;e=a==="ag"?eu(d):$t(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Tu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Uu(a){var b=Math.max(Su("aw",a),Tu(Xt(Wt())?st():{})),c=Math.max(Su("gb",a),Tu(Xt(Wt())?st("_gac_gb",!0):{}));c=Math.max(c,Su("ag",a));return c>b}
function zu(){return z.referrer?Lk(Rk(z.referrer),"host"):""};
var Vu=function(a,b){b=b===void 0?!1:b;var c=tp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},Wu=function(a){return Sk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},cv=function(a,b,c,d,e){var f=cu(a.prefix);if(Vu(f,!0)){var g=uu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=Xu(),r=q.Xf,t=q.Zl;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,zd:p});n&&h.push({gclid:n,zd:"ds"});h.length===2&&L(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,zd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",zd:"aw.ds"});Yu(function(){var u=O(Zu());if(u){gt(a);var v=[],w=u?et[ht(a.prefix)]:void 0;w&&v.push("auid="+w);if(O(J.m.V)){e&&v.push("userId="+e);var y=sn(on.Z.Dl);if(y===void 0)rn(on.Z.El,!0);else{var A=sn(on.Z.jh);v.push("ga_uid="+A+"."+y)}}var D=zu(),E=u||!d?h:[];E.length===0&&(Ut.test(D)||Vt.test(D))&&E.push({gclid:"",zd:""});if(E.length!==0||r!==void 0){D&&v.push("ref="+encodeURIComponent(D));var G=$u();v.push("url="+
encodeURIComponent(G));v.push("tft="+yb());var I=Yc();I!==void 0&&v.push("tfd="+Math.round(I));var M=Gl(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var S={};c=gq(Xp(new Wp(0),(S[J.m.Ea]=Dq.C[J.m.Ea],S)))}v.push("gtm="+Kr({Ma:b}));ur()&&v.push("gcs="+vr());v.push("gcd="+zr(c));Cr()&&v.push("dma_cps="+Ar());v.push("dma="+Br());tr(c)?v.push("npa=0"):v.push("npa=1");Er()&&v.push("_ng=1");Yq(fr())&&
v.push("tcfd="+Dr());var ea=mr();ea&&v.push("gdpr="+ea);var U=lr();U&&v.push("gdpr_consent="+U);C(23)&&v.push("apve=0");C(123)&&Ts(!1)._up&&v.push("gtm_up=1");jk()&&v.push("tag_exp="+jk());if(E.length>0)for(var ta=0;ta<E.length;ta++){var W=E[ta],da=W.gclid,Y=W.zd;if(!av(a.prefix,Y+"."+da,w!==void 0)){var X=bv+"?"+v.join("&");da!==""?X=Y==="gb"?X+"&wbraid="+da:X+"&gclid="+da+"&gclsrc="+Y:Y==="aw.ds"&&(X+="&gclsrc=aw.ds");Qc(X)}}else if(r!==void 0&&!av(a.prefix,"gad",w!==void 0)){var ma=bv+"?"+v.join("&");
Qc(ma)}}}})}},av=function(a,b,c){var d=tp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},Xu=function(){var a=Rk(x.location.href),b=void 0,c=void 0,d=Lk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(dv);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Xf:b,Zl:c}},$u=function(){var a=Gl(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},ev=function(a){var b=[];qb(a,function(c,d){d=ju(d);for(var e=
[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},gv=function(a,b){return fv("dc",a,b)},hv=function(a,b){return fv("aw",a,b)},fv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Tk("gcl"+a);if(d)return d.split(".")}var e=cu(b);if(e==="_gcl"){var f=!O(Zu())&&c,g;g=uu()[a]||[];if(g.length>0)return f?["0"]:g}var h=du(a,e);return h?Zt(h):[]},Yu=function(a){var b=Zu();lp(function(){a();O(b)||cn(a,b)},b)},Zu=function(){return[J.m.U,J.m.V]},bv=Pi(36,
'https://adservice.google.com/pagead/regclk'),dv=/^gad_source[_=](\d+)$/;function iv(){return tp("dedupe_gclid",function(){return zs()})};var jv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,kv=/^www.googleadservices.com$/;function lv(a){a||(a=mv());return a.Kq?!1:a.Fp||a.Gp||a.Jp||a.Hp||a.Xf||a.pp||a.Ip||a.vp?!0:!1}function mv(){var a={},b=Ts(!0);a.Kq=!!b._up;var c=uu();a.Fp=c.aw!==void 0;a.Gp=c.dc!==void 0;a.Jp=c.wbraid!==void 0;a.Hp=c.gbraid!==void 0;a.Ip=c.gclsrc==="aw.ds";a.Xf=Xu().Xf;var d=z.referrer?Lk(Rk(z.referrer),"host"):"";a.vp=jv.test(d);a.pp=kv.test(d);return a};function nv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function ov(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function pv(){return["ad_storage","ad_user_data"]}function qv(a){if(C(38)&&!sn(on.Z.rl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{nv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(rn(on.Z.rl,function(d){d.gclid&&Bu(d.gclid,5,a)}),ov(c)||L(178))})}catch(c){L(177)}};bn(function(){Xt(pv())?b():cn(b,pv())},pv())}};var rv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function sv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?rn(on.Z.Nf,{gadSource:a.data.gadSource}):L(173)}
function tv(a,b){if(C(a)){if(sn(on.Z.Nf))return L(176),on.Z.Nf;if(sn(on.Z.tl))return L(170),on.Z.Nf;var c=Il();if(!c)L(171);else if(c.opener){var d=function(g){if(rv.includes(g.origin)){a===119?sv(g):a===200&&(sv(g),g.data.gclid&&Bu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Qq(c,"message",d)}else L(172)};if(Pq(c,"message",d)){rn(on.Z.tl,!0);for(var e=l(rv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return on.Z.Nf}L(175)}}}
;var uv=function(){this.C=this.gppString=void 0};uv.prototype.reset=function(){this.C=this.gppString=void 0};var vv=new uv;var wv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),xv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,yv=/^\d+\.fls\.doubleclick\.net$/,zv=/;gac=([^;?]+)/,Av=/;gacgb=([^;?]+)/;
function Bv(a,b){if(yv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(wv)?Kk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Cv(a,b,c){for(var d=Xt(Wt())?st("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Qu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{op:f?e.join(";"):"",np:Bv(d,Av)}}function Dv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(xv)?b[1]:void 0}
function Ev(a){var b={},c,d,e;yv.test(z.location.host)&&(c=Dv("gclgs"),d=Dv("gclst"),e=Dv("gcllp"));if(c&&d&&e)b.sh=c,b.uh=d,b.th=e;else{var f=yb(),g=eu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Ed});h.length>0&&m.length>0&&n.length>0&&(b.sh=h.join("."),b.uh=m.join("."),b.th=n.join("."))}return b}
function Fv(a,b,c,d){d=d===void 0?!1:d;if(yv.test(z.location.host)){var e=Dv(c);if(e){if(d){var f=new Jt;Kt(f,2);Kt(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?pu(g):$t(g)}if(b==="wbraid")return $t((a||"_gcl")+"_gb");if(b==="braids")return bu({prefix:a})}return[]}function Gv(a){return yv.test(z.location.host)?!(Dv("gclaw")||Dv("gac")):Uu(a)}
function Hv(a,b,c){var d;d=c?Ru(a,b):Qu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Iv(){var a=x.__uspapi;if(ib(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Nv=function(a){if(a.eventName===J.m.qa&&Q(a,P.A.fa)===K.J.Ga)if(C(24)){R(a,P.A.se,N(a.D,J.m.za)!=null&&N(a.D,J.m.za)!==!1&&!O([J.m.U,J.m.V]));var b=Jv(a),c=N(a.D,J.m.Oa)!==!1;c||T(a,J.m.Qh,"1");var d=cu(b.prefix),e=Q(a,P.A.eh);if(!Q(a,P.A.ja)&&!Q(a,P.A.Qf)&&!Q(a,P.A.qe)){var f=N(a.D,J.m.Eb),g=N(a.D,J.m.Pa)||{};Kv({we:c,Ce:g,Ge:f,Rc:b});if(!e&&!Vu(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{T(a,J.m.hd,J.m.Yc);if(Q(a,P.A.ja))T(a,J.m.hd,J.m.jn),T(a,J.m.ja,"1");else if(Q(a,P.A.Qf))T(a,J.m.hd,
J.m.tn);else if(Q(a,P.A.qe))T(a,J.m.hd,J.m.qn);else{var h=uu();T(a,J.m.Zc,h.gclid);T(a,J.m.fd,h.dclid);T(a,J.m.hk,h.gclsrc);Lv(a,J.m.Zc)||Lv(a,J.m.fd)||(T(a,J.m.Wd,h.wbraid),T(a,J.m.Pe,h.gbraid));T(a,J.m.Wa,zu());T(a,J.m.Aa,$u());if(C(27)&&vc){var m=Lk(Rk(vc),"host");m&&T(a,J.m.Pk,m)}if(!Q(a,P.A.qe)){var n=Xu(),p=n.Zl;T(a,J.m.Ne,n.Xf);T(a,J.m.Oe,p)}T(a,J.m.Jc,Gl(!0));var q=mv();lv(q)&&T(a,J.m.kd,"1");T(a,J.m.jk,iv());Ts(!1)._up==="1"&&T(a,J.m.Fk,"1")}Sn=!0;T(a,J.m.Db);T(a,J.m.Pb);var r=O([J.m.U,J.m.V]);
r&&(T(a,J.m.Db,Mv()),c&&(gt(b),T(a,J.m.Pb,et[ht(b.prefix)])));T(a,J.m.mc);T(a,J.m.lb);if(!Lv(a,J.m.Zc)&&!Lv(a,J.m.fd)&&Gv(d)){var t=au(b);t.length>0&&T(a,J.m.mc,t.join("."))}else if(!Lv(a,J.m.Wd)&&r){var u=Zt(d+"_aw");u.length>0&&T(a,J.m.lb,u.join("."))}C(31)&&T(a,J.m.Ik,Zc());a.D.isGtmEvent&&(a.D.C[J.m.Ea]=Dq.C[J.m.Ea]);tr(a.D)?T(a,J.m.xc,!1):T(a,J.m.xc,!0);R(a,P.A.pg,!0);var v=Iv();v!==void 0&&T(a,J.m.Cf,v||"error");var w=mr();w&&T(a,J.m.jd,w);if(C(137))try{var y=Intl.DateTimeFormat().resolvedOptions().timeZone;
T(a,J.m.ii,y||"-")}catch(G){T(a,J.m.ii,"e")}var A=lr();A&&T(a,J.m.nd,A);var D=vv.gppString;D&&T(a,J.m.hf,D);var E=vv.C;E&&T(a,J.m.ff,E);R(a,P.A.Ha,!1)}}else a.isAborted=!0},Jv=function(a){var b={prefix:N(a.D,J.m.Rb)||N(a.D,J.m.cb),domain:N(a.D,J.m.nb),Bc:N(a.D,J.m.ob),flags:N(a.D,J.m.yb)};a.D.isGtmEvent&&(b.path=N(a.D,J.m.Sb));return b},Ov=function(a,b){var c,d,e,f,g,h,m,n;c=a.we;d=a.Ce;e=a.Ge;f=a.Ma;g=a.D;h=a.De;m=a.Fr;n=a.Lm;Kv({we:c,Ce:d,Ge:e,Rc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,cv(b,
f,g,h,n))},Pv=function(a,b){if(!Q(a,P.A.qe)){var c=tv(119);if(c){var d=sn(c),e=function(g){R(a,P.A.qe,!0);var h=Lv(a,J.m.Ne),m=Lv(a,J.m.Oe);T(a,J.m.Ne,String(g.gadSource));T(a,J.m.Oe,6);R(a,P.A.ja);R(a,P.A.Qf);T(a,J.m.ja);b();T(a,J.m.Ne,h);T(a,J.m.Oe,m);R(a,P.A.qe,!1)};if(d)e(d);else{var f=void 0;f=un(c,function(g,h){e(h);vn(c,f)})}}}},Kv=function(a){var b,c,d,e;b=a.we;c=a.Ce;d=a.Ge;e=a.Rc;b&&(ct(c[J.m.ce],!!c[J.m.ma])&&(Du(Qv,e),Fu(e),pt(e)),Gl()!==2?(xu(e),qv(e),tv(200,e)):vu(e),Ju(Qv,e),Ku(e));
c[J.m.ma]&&(Hu(Qv,c[J.m.ma],c[J.m.Mc],!!c[J.m.sc],e.prefix),Iu(c[J.m.ma],c[J.m.Mc],!!c[J.m.sc],e.prefix),qt(ht(e.prefix),c[J.m.ma],c[J.m.Mc],!!c[J.m.sc],e),qt("FPAU",c[J.m.ma],c[J.m.Mc],!!c[J.m.sc],e));d&&(C(101)?Mu(Rv):Mu(Sv));Ou(Sv)},Tv=function(a,b,c,d){var e,f,g;e=a.Mm;f=a.callback;g=a.hm;if(typeof f==="function")if(e===J.m.lb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===J.m.Pb?(L(65),gt(b,!1),f(et[ht(b.prefix)])):f(g)},Uv=function(a,b){Array.isArray(b)||
(b=[b]);var c=Q(a,P.A.fa);return b.indexOf(c)>=0},Qv=["aw","dc","gb"],Sv=["aw","dc","gb","ag"],Rv=["aw","dc","gb","ag","gad_source"];function Vv(a){var b=N(a.D,J.m.Lc),c=N(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Sd&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function Wv(a){var b=O(J.m.U)?sp.pscdl:"denied";b!=null&&T(a,J.m.Eg,b)}function Xv(a){var b=Gl(!0);T(a,J.m.Jc,b)}function Yv(a){Er()&&T(a,J.m.ae,1)}
function Mv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Kk(a.substring(0,b))===void 0;)b--;return Kk(a.substring(0,b))||""}function Zv(a){$v(a,Ap.Df.Tm,N(a.D,J.m.ob))}function $v(a,b,c){Lv(a,J.m.rd)||T(a,J.m.rd,{});Lv(a,J.m.rd)[b]=c}function aw(a){R(a,P.A.Pf,Nm.X.Da)}function bw(a){var b=fb("GTAG_EVENT_FEATURE_CHANNEL");b&&(T(a,J.m.jf,b),db())}function cw(a){var b=a.D.getMergedValues(J.m.rc);b&&a.mergeHitDataForKey(J.m.rc,b)}
function dw(a,b){b=b===void 0?!1:b;if(C(108)){var c=Q(a,P.A.Of);if(c)if(c.indexOf(a.target.destinationId)<0){if(R(a,P.A.Jj,!1),b||!ew(a,"custom_event_accept_rules",!1))a.isAborted=!0}else R(a,P.A.Jj,!0)}}function fw(a){gl&&(Sn=!0,a.eventName===J.m.qa?Yn(a.D,a.target.id):(Q(a,P.A.Ke)||(Vn[a.target.id]=!0),zp(Q(a,P.A.hb))))};function pw(a,b,c,d){var e=Fc(),f;if(e===1)a:{var g=dk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Bw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Lv(a,b)},setHitData:function(b,c){T(a,b,c)},setHitDataIfNotDefined:function(b,c){Lv(a,b)===void 0&&T(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return Q(a,b)},setMetadata:function(b,c){R(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Ab:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return kd(c)?a.mergeHitDataForKey(b,c):!1}}};var Dw=function(a){var b=Cw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Bw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Ew=function(a,b){var c=Cw[a];c||(c=Cw[a]=[]);c.push(b)},Cw={};function Gw(a,b){return arguments.length===1?Hw("set",a):Hw("set",a,b)}function Iw(a,b){return arguments.length===1?Hw("config",a):Hw("config",a,b)}function Jw(a,b,c){c=c||{};c[J.m.ld]=a;return Hw("event",b,c)}function Hw(){return arguments};var Lw=function(){this.messages=[];this.C=[]};Lw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Lw.prototype.listen=function(a){this.C.push(a)};
Lw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Lw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Mw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.A.hb]=jg.canonicalContainerId;Nw().enqueue(a,b,c)}
function Ow(){var a=Pw;Nw().listen(a)}function Nw(){return tp("mb",function(){return new Lw})};var Qw,Rw=!1;function Sw(){Rw=!0;Qw=Qw||{}}function Tw(a){Rw||Sw();return Qw[a]};function Uw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Vw(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var ex=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+dx.test(a.la)},sx=function(a){a=a||{Ae:!0,Be:!0,Eh:void 0};a.Yb=a.Yb||{email:!0,phone:!1,address:!1};var b=fx(a),c=gx[b];if(c&&yb()-c.timestamp<200)return c.result;var d=hx(),e=d.status,f=[],g,h,m=[];if(!C(33)){if(a.Yb&&a.Yb.email){var n=ix(d.elements);f=jx(n,a&&a.Uf);g=kx(f);n.length>10&&(e="3")}!a.Eh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(lx(f[p],!!a.Ae,!!a.Be));m=m.slice(0,10)}else if(a.Yb){}g&&(h=lx(g,!!a.Ae,!!a.Be));var G={elements:m,
yj:h,status:e};gx[b]={timestamp:yb(),result:G};return G},tx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},vx=function(a){var b=ux(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},ux=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},rx=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=wx(d));c&&(e.isVisible=!Vw(d));return e},lx=function(a,b,c){return rx({element:a.element,la:a.la,xa:qx.hc},b,c)},fx=function(a){var b=!(a==null||!a.Ae)+"."+!(a==null||!a.Be);a&&a.Uf&&a.Uf.length&&(b+="."+a.Uf.join("."));a&&a.Yb&&(b+="."+a.Yb.email+"."+a.Yb.phone+"."+a.Yb.address);return b},kx=function(a){if(a.length!==0){var b;b=xx(a,function(c){return!yx.test(c.la)});b=xx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=xx(b,function(c){return!Vw(c.element)});return b[0]}},jx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&ti(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},xx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},wx=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=wx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},ix=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(zx);if(f){var g=f[0],h;if(x.location){var m=Nk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},hx=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Ax.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Bx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||C(33)&&Cx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},zx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,dx=/@(gmail|googlemail)\./i,yx=/support|noreply/i,Ax="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Bx=
["BR"],Dx=ug('',2),qx={hc:"1",xd:"2",pd:"3",wd:"4",Je:"5",Mf:"6",fh:"7",Mi:"8",Hh:"9",Hi:"10"},gx={},Cx=["INPUT","SELECT"],Ex=ux(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var cy=function(a,b,c){var d={};a.mergeHitDataForKey(J.m.Ji,(d[b]=c,d))},dy=function(a,b){var c=ew(a,J.m.Jg,a.D.H[J.m.Jg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},ey=function(a){var b=Q(a,P.A.ib);if(kd(b))return b},fy=function(a){if(Q(a,P.A.vd)||!Zk(a.D))return!1;if(!N(a.D,J.m.md)){var b=N(a.D,J.m.Yd);return b===!0||b==="true"}return!0},gy=function(a){return ew(a,J.m.be,N(a.D,J.m.be))||!!ew(a,"google_ng",!1)};var hy=Number('')||5,iy=Number('')||50,jy=nb();
var ly=function(a,b){a&&(ky("sid",a.targetId,b),ky("cc",a.clientCount,b),ky("tl",a.totalLifeMs,b),ky("hc",a.heartbeatCount,b),ky("cl",a.clientLifeMs,b))},ky=function(a,b,c){b!=null&&c.push(a+"="+b)},my=function(){var a=z.referrer;if(a){var b;return Lk(Rk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},ny="https://"+Pi(21,"www.googletagmanager.com")+"/a?",py=function(){this.R=oy;this.N=0};py.prototype.H=function(a,b,c,d){var e=my(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&ky("si",a.fg,g);ky("m",0,g);ky("iss",f,g);ky("if",c,g);ly(b,g);d&&ky("fm",encodeURIComponent(d.substring(0,iy)),g);this.P(g);};py.prototype.C=function(a,b,c,d,e){var f=[];ky("m",1,f);ky("s",a,f);ky("po",my(),f);b&&(ky("st",b.state,f),ky("si",b.fg,f),ky("sm",b.lg,f));ly(c,f);ky("c",d,f);e&&ky("fm",encodeURIComponent(e.substring(0,
iy)),f);this.P(f);};py.prototype.P=function(a){a=a===void 0?[]:a;!fl||this.N>=hy||(ky("pid",jy,a),ky("bc",++this.N,a),a.unshift("ctid="+jg.ctid+"&t=s"),this.R(""+ny+a.join("&")))};var qy=Number('')||500,ry=Number('')||5E3,sy=Number('20')||10,ty=Number('')||5E3;function uy(a){return a.performance&&a.performance.now()||Date.now()}
var vy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{km:function(){},lm:function(){},jm:function(){},onFailure:function(){}}:h;this.yo=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.wo=0;this.gh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.fg=uy(this.C);this.lg=uy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
fg:Math.round(uy(this.C)-this.fg),lg:Math.round(uy(this.C)-this.lg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.lg=uy(this.C))};e.prototype.Il=function(){return String(this.wo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Il(),maxDelay:this.hh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>sy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.vo();var n,p;(p=(n=f.N).jm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Ml();else{if(f.heartbeatCount>g.stats.heartbeatCount+sy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.gh){var u,v;(v=(u=f.N).lm)==null||v.call(u)}else{f.gh=!0;var w,y;(y=(w=f.N).km)==null||y.call(w)}f.ba=0;f.zo();f.Ml()}}})};e.prototype.hh=function(){return this.state===2?
ry:qy};e.prototype.Ml=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.hh()-(uy(this.C)-this.ka)))};e.prototype.Co=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Il(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Kf(t,7)},(p=f.maxDelay)!=null?p:ty),r={request:f,Bm:g,wm:m,Wp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=uy(this.C);f.wm=!1;this.yo(f.request)};e.prototype.zo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.wm&&this.sendRequest(h)}};e.prototype.vo=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Kf(this.H[g.value],this.R)};e.prototype.Kf=function(f,g){this.qb(f);var h=f.request;h.failure={failureType:g};f.Bm(h)};e.prototype.qb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Wp)};e.prototype.Dp=function(f){this.ka=uy(this.C);var g=this.H[f.requestId];if(g)this.qb(g),g.Bm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var wy;
var xy=function(){wy||(wy=new py);return wy},oy=function(a){ln(nn(Nm.X.Oc),function(){Ic(a)})},yy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},zy=function(a){var b=a,c=Mj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ay=function(a){var b=sn(on.Z.Bl);return b&&b[a]},By=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.To(a);x.setTimeout(function(){f.initialize()},1E3);Lc(function(){f.Np(a,b,e)})};k=By.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),fg:this.initTime,lg:Math.round(yb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Co(a,b,c)};k.getState=function(){return this.N.getState().state};k.Np=function(a,b,c){var d=x.location.origin,e=this,
f=Gc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?yy(h):"",p;C(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Gc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Dp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.To=function(a){var b=this,c=vy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{km:function(){b.P=!0;b.H.H(c.getState(),c.stats)},lm:function(){},jm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Cy(){var a=ig(fg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Dy(a,b){var c=Math.round(yb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Cy()||C(168))return;lk()&&(a=""+d+kk()+"/_/service_worker");var e=zy(a);if(e===null||Ay(e.origin))return;if(!tc()){xy().H(void 0,void 0,6);return}var f=new By(e,!!a,c||Math.round(yb()),xy(),b);tn(on.Z.Bl)[e.origin]=f;}
var Ey=function(a,b,c,d){var e;if((e=Ay(a))==null||!e.delegate){var f=tc()?16:6;xy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ay(a).delegate(b,c,d);};
function Fy(a,b,c,d,e){var f=zy();if(f===null){d(tc()?16:6);return}var g,h=(g=Ay(f.origin))==null?void 0:g.initTime,m=Math.round(yb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Ey(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Gy(a,b,c,d){var e=zy(a);if(e===null){d("_is_sw=f"+(tc()?16:6)+"te");return}var f=b?1:0,g=Math.round(yb()),h,m=(h=Ay(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;C(169)&&(p=!0);Ey(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Ay(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Hy(a){if(C(10)||lk()||Mj.N||Zk(a.D)||C(168))return;Dy(void 0,C(131));};var Iy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Jy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ky(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Ly(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function My(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ny(a){if(!My(a))return null;var b=Jy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Iy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Py=function(a,b){if(a)for(var c=Oy(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;T(b,f,c[f])}},Oy=function(a){var b={};b[J.m.uf]=a.architecture;b[J.m.vf]=a.bitness;a.fullVersionList&&(b[J.m.wf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.xf]=a.mobile?"1":"0";b[J.m.yf]=a.model;b[J.m.zf]=a.platform;b[J.m.Af]=a.platformVersion;b[J.m.Bf]=a.wow64?"1":"0";return b},Qy=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=Ky(d);if(e)c(e);else{var f=Ly(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.gg||(c.gg=!0,L(106),c(null,Error("Timeout")))},b);f.then(function(h){c.gg||(c.gg=!0,L(104),d.clearTimeout(g),c(h))}).catch(function(h){c.gg||(c.gg=!0,L(105),d.clearTimeout(g),c(null,h))})}else c(null)}},Sy=function(){var a=x;if(My(a)&&(Ry=yb(),!Ly(a))){var b=Ny(a);b&&(b.then(function(){L(95)}),b.catch(function(){L(96)}))}},Ry;function Ty(a){var b=a.location.href;if(a===a.top)return{url:b,Sp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Sp:c}};var Hz=function(){return C(90)?lo():""},Iz=function(){var a;C(90)&&lo()!==""&&(a=lo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Jz=function(){var a="www";C(90)&&lo()&&(a=lo());return"https://"+a+".google-analytics.com/g/collect"};function Kz(a,b){var c=!!lk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?kk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?C(187)?Hz()?Iz():""+kk()+"/ag/g/c":Hz().toLowerCase()==="region1"?""+kk()+"/r1ag/g/c":""+kk()+"/ag/g/c":Iz();case 16:if(c){if(C(187))return Hz()?Jz():
""+kk()+"/ga/g/c";var d=Hz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+kk()+d}return Jz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?kk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?kk()+"/d/pagead/form-data":C(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Do+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?kk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(C(207)?c:c&&b.zh)?kk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?kk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(C(207)?c:c&&b.zh)?kk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";
case 55:return c?kk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return C(205)?"https://www.google.com/measurement/conversion/":c?kk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(C(207)?c:c&&b.zh)?kk()+"/d/ccm/form-data":C(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:kc(a,"Unknown endpoint")}};function Lz(a){a=a===void 0?[]:a;return Nj(a).join("~")}function Mz(){if(!C(118))return"";var a,b;return(((a=Bm(qm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Nz(a,b){b&&qb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Pz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Lv(a,g),m=Oz[g];m&&h!==void 0&&h!==""&&(!Q(a,P.A.se)||g!==J.m.Zc&&g!==J.m.fd&&g!==J.m.Wd&&g!==J.m.Pe||(h="0"),d(m,h))}d("gtm",Kr({Ma:Q(a,P.A.hb)}));ur()&&d("gcs",vr());d("gcd",zr(a.D));Cr()&&d("dma_cps",Ar());d("dma",Br());Yq(fr())&&d("tcfd",Dr());Lz()&&d("tag_exp",Lz());Mz()&&d("ptag_exp",Mz());if(Q(a,P.A.pg)){d("tft",
yb());var n=Yc();n!==void 0&&d("tfd",Math.round(n))}C(24)&&d("apve","1");(C(25)||C(26))&&d("apvf",Vc()?C(26)?"f":"sb":"nf");en[Nm.X.Da]!==Mm.Ia.oe||hn[Nm.X.Da].isConsentGranted()||(c.limited_ads="1");b(c)},Qz=function(a,b,c){var d=b.D;Wo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Ya:{eventId:d.eventId,priorityId:d.priorityId},qh:{eventId:Q(b,P.A.He),priorityId:Q(b,P.A.Ie)}})},Rz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};Qz(a,b,c);gm(d,a,void 0,{Ch:!0,method:"GET"},function(){},function(){fm(d,a+"&img=1")})},Sz=function(a){var b=Ac()||yc()?"www.google.com":"www.googleadservices.com",c=[];qb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Tz=function(a){Pz(a,function(b){if(Q(a,P.A.fa)===K.J.Ga){var c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);
qb(b,function(r,t){c.push(r+"="+t)});var d=O([J.m.U,J.m.V])?45:46,e=Kz(d)+"?"+c.join("&");Qz(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(C(26)&&Vc()){gm(g,e,void 0,{Ch:!0},function(){},function(){fm(g,e+"&img=1")});var h=O([J.m.U,J.m.V]),m=Lv(a,J.m.kd)==="1",n=Lv(a,J.m.Qh)==="1";if(h&&m&&!n){var p=Sz(b),q=Ac()||yc()?58:57;Rz(p,a,q)}}else em(g,e)||fm(g,e+"&img=1");if(ib(a.D.onSuccess))a.D.onSuccess()}})},Uz={},Oz=(Uz[J.m.ja]="gcu",
Uz[J.m.mc]="gclgb",Uz[J.m.lb]="gclaw",Uz[J.m.Ne]="gad_source",Uz[J.m.Oe]="gad_source_src",Uz[J.m.Zc]="gclid",Uz[J.m.hk]="gclsrc",Uz[J.m.Pe]="gbraid",Uz[J.m.Wd]="wbraid",Uz[J.m.Pb]="auid",Uz[J.m.jk]="rnd",Uz[J.m.Qh]="ncl",Uz[J.m.Uh]="gcldc",Uz[J.m.fd]="dclid",Uz[J.m.Tb]="edid",Uz[J.m.hd]="en",Uz[J.m.jd]="gdpr",Uz[J.m.Ub]="gdid",Uz[J.m.ae]="_ng",Uz[J.m.ff]="gpp_sid",Uz[J.m.hf]="gpp",Uz[J.m.jf]="_tu",Uz[J.m.Fk]="gtm_up",Uz[J.m.Jc]="frm",Uz[J.m.kd]="lps",Uz[J.m.Pg]="did",Uz[J.m.Ik]="navt",Uz[J.m.Aa]=
"dl",Uz[J.m.Wa]="dr",Uz[J.m.Db]="dt",Uz[J.m.Pk]="scrsrc",Uz[J.m.rf]="ga_uid",Uz[J.m.nd]="gdpr_consent",Uz[J.m.ii]="u_tz",Uz[J.m.Qa]="uid",Uz[J.m.Cf]="us_privacy",Uz[J.m.xc]="npa",Uz);var Vz={};Vz.O=as.O;var Wz={hr:"L",so:"S",yr:"Y",Mq:"B",Wq:"E",ar:"I",vr:"TC",Zq:"HTC"},Xz={so:"S",Vq:"V",Pq:"E",ur:"tag"},Yz={},Zz=(Yz[Vz.O.Oi]="6",Yz[Vz.O.Pi]="5",Yz[Vz.O.Ni]="7",Yz);function $z(){function a(c,d){var e=fb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var aA=!1;
function tA(a){}function uA(a){}
function vA(){}function wA(a){}
function xA(a){}function yA(a){}
function zA(){}function AA(a,b){}
function BA(a,b,c){}
function CA(){};var DA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function EA(a,b,c,d,e,f,g){var h=Object.assign({},DA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});FA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():C(128)&&(b+="&_z=retryFetch",c?em(a,b,c):dm(a,b))})};var GA=function(a){this.P=a;this.C=""},HA=function(a,b){a.H=b;return a},IA=function(a,b){a.N=b;return a},FA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}JA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},KA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};JA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},JA=function(a,b){b&&(LA(b.send_pixel,b.options,a.P),LA(b.create_iframe,b.options,a.H),LA(b.fetch,b.options,a.N))};function MA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function LA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=kd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var BB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),CB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},DB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},EB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function FB(){var a=rk("gtm.allowlist")||rk("gtm.whitelist");a&&L(9);Zj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);BB.test(x.location&&x.location.hostname)&&(Zj?L(116):(L(117),GB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Cb(vb(a),CB),c=rk("gtm.blocklist")||rk("gtm.blacklist");c||(c=rk("tagTypeBlacklist"))&&L(3);c?L(8):c=[];BB.test(x.location&&x.location.hostname)&&(c=vb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
vb(c).indexOf("google")>=0&&L(2);var d=c&&Cb(vb(c),DB),e={};return function(f){var g=f&&f[ff.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=hk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Zj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=ob(d,h||[]);t&&L(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Zj&&h.indexOf("cmpPartners")>=0?!HB():b&&b.indexOf("sandboxedScripts")!==-1?0:ob(d,EB))&&(u=!0);return e[g]=u}}function HB(){var a=ig(fg.C,jg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var GB=!1;GB=!0;function IB(a,b,c,d,e){if(!JB()&&!Gm(a)){d.loadExperiments=Oj();pm(a,d,e);var f=KB(a),g=function(){rm().container[a]&&(rm().container[a].state=3);LB()},h={destinationId:a,endpoint:0};if(lk())hm(h,kk()+"/"+f,void 0,g);else{var m=Db(a,"GTM-"),n=Yk(),p=c?"/gtag/js":"/gtm.js",q=Xk(b,p+f);if(!q){var r=Qj.vg+p;n&&vc&&m&&(r=vc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=pw("https://","http://",r+f)}hm(h,q,void 0,g)}}}
function LB(){Im()||qb(Jm(),function(a,b){MB(a,b.transportUrl,b.context);L(92)})}
function MB(a,b,c,d){if(!JB()&&!Hm(a))if(c.loadExperiments||(c.loadExperiments=Oj()),Im()){var e;(e=rm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:qm()});rm().destination[a].state=0;sm({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=rm().destination)[a]!=null||(f[a]={context:c,state:1,parent:qm()});rm().destination[a].state=1;sm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(lk())hm(g,kk()+("/gtd"+KB(a,!0)));else{var h="/gtag/destination"+KB(a,!0),
m=Xk(b,h);m||(m=pw("https://","http://",Qj.vg+h));hm(g,m)}}}function KB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Tj!=="dataLayer"&&(c+="&l="+Tj);if(!Db(a,"GTM-")||b)c=C(130)?c+(lk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Lr();Yk()&&(c+="&sign="+Qj.Ki);var d=Mj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!C(191)&&Oj().join("~")&&(c+="&tag_exp="+Oj().join("~"));return c}
function JB(){if(Fr()){return!0}return!1};var NB=function(){this.H=0;this.C={}};NB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Fe:c};return d};NB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var PB=function(a,b){var c=[];qb(OB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Fe===void 0||b.indexOf(e.Fe)>=0)&&c.push(e.listener)});return c};function QB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:jg.ctid}};function RB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var TB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;SB(this,a,b)},UB=function(a,b,c,d){if(Vj.hasOwnProperty(b)||b==="__zone")return-1;var e={};kd(d)&&(e=ld(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},VB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},WB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},SB=function(a,b,c){b!==void 0&&a.Rf(b);c&&x.setTimeout(function(){WB(a)},
Number(c))};TB.prototype.Rf=function(a){var b=this,c=Ab(function(){Lc(function(){a(jg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var XB=function(a){a.N++;return Ab(function(){a.H++;a.R&&a.H>=a.N&&WB(a)})},YB=function(a){a.R=!0;a.H>=a.N&&WB(a)};var ZB={};function $B(){return x[aC()]}
function aC(){return x.GoogleAnalyticsObject||"ga"}function dC(){var a=jg.ctid;}
function eC(a,b){return function(){var c=$B(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var kC=["es","1"],lC={},mC={};function nC(a,b){if(fl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";lC[a]=[["e",c],["eid",a]];vq(a)}}function oC(a){var b=a.eventId,c=a.Md;if(!lC[b])return[];var d=[];mC[b]||d.push(kC);d.push.apply(d,ua(lC[b]));c&&(mC[b]=!0);return d};var pC={},qC={},rC={};function sC(a,b,c,d){fl&&C(120)&&((d===void 0?0:d)?(rC[b]=rC[b]||0,++rC[b]):c!==void 0?(qC[a]=qC[a]||{},qC[a][b]=Math.round(c)):(pC[a]=pC[a]||{},pC[a][b]=(pC[a][b]||0)+1))}function tC(a){var b=a.eventId,c=a.Md,d=pC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete pC[b];return e.length?[["md",e.join(".")]]:[]}
function uC(a){var b=a.eventId,c=a.Md,d=qC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete qC[b];return e.length?[["mtd",e.join(".")]]:[]}function vC(){for(var a=[],b=l(Object.keys(rC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+rC[d])}return a.length?[["mec",a.join(".")]]:[]};var wC={},xC={};function yC(a,b,c){if(fl&&b){var d=bl(b);wC[a]=wC[a]||[];wC[a].push(c+d);var e=b[ff.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Jf[e]?"1":"2")+d;xC[a]=xC[a]||[];xC[a].push(f);vq(a)}}function zC(a){var b=a.eventId,c=a.Md,d=[],e=wC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=xC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete wC[b],delete xC[b]);return d};function AC(a,b,c){c=c===void 0?!1:c;BC().addRestriction(0,a,b,c)}function CC(a,b,c){c=c===void 0?!1:c;BC().addRestriction(1,a,b,c)}function DC(){var a=ym();return BC().getRestrictions(1,a)}var EC=function(){this.container={};this.C={}},FC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
EC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=FC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
EC.prototype.getRestrictions=function(a,b){var c=FC(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
EC.prototype.getExternalRestrictions=function(a,b){var c=FC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};EC.prototype.removeExternalRestrictions=function(a){var b=FC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function BC(){return tp("r",function(){return new EC})};function GC(a,b,c,d){var e=Gf[a],f=HC(a,b,c,d);if(!f)return null;var g=Vf(e[ff.Cl],c,[]);if(g&&g.length){var h=g[0];f=GC(h.index,{onSuccess:f,onFailure:h.Xl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function HC(a,b,c,d){function e(){function w(){Zn(3);var M=yb()-I;yC(c.id,f,"7");VB(c.Pc,E,"exception",M);C(109)&&BA(c,f,Vz.O.Ni);G||(G=!0,h())}if(f[ff.ko])h();else{var y=Uf(f,c,[]),A=y[ff.Qm];if(A!=null)for(var D=0;D<A.length;D++)if(!O(A[D])){h();return}var E=UB(c.Pc,String(f[ff.Ra]),Number(f[ff.kh]),y[ff.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=yb()-I;yC(c.id,Gf[a],"5");VB(c.Pc,E,"success",M);C(109)&&BA(c,f,Vz.O.Pi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=yb()-
I;yC(c.id,Gf[a],"6");VB(c.Pc,E,"failure",M);C(109)&&BA(c,f,Vz.O.Oi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);yC(c.id,f,"1");C(109)&&AA(c,f);var I=yb();try{Wf(y,{event:c,index:a,type:1})}catch(M){w(M)}C(109)&&BA(c,f,Vz.O.Jl)}}var f=Gf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Vf(f[ff.Kl],c,[]);if(n&&n.length){var p=n[0],q=GC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Xl===
2?m:q}if(f[ff.sl]||f[ff.mo]){var r=f[ff.sl]?Hf:c.Eq,t=g,u=h;if(!r[a]){var v=IC(a,r,Ab(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function IC(a,b,c){var d=[],e=[];b[a]=JC(d,e,c);return{onSuccess:function(){b[a]=KC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=LC;for(var f=0;f<e.length;f++)e[f]()}}}function JC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function KC(a){a()}function LC(a,b){b()};var OC=function(a,b){for(var c=[],d=0;d<Gf.length;d++)if(a[d]){var e=Gf[d];var f=XB(b.Pc);try{var g=GC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ff.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Jf[h];c.push({Im:d,priorityOverride:(m?m.priorityOverride||0:0)||RB(e[ff.Ra],1)||0,execute:g})}else MC(d,b),f()}catch(p){f()}}c.sort(NC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function PC(a,b){if(!OB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=PB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=XB(b);try{d[e](a,f)}catch(g){f()}}return!0}function NC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Im,h=b.Im;f=g>h?1:g<h?-1:0}return f}
function MC(a,b){if(fl){var c=function(d){var e=b.isBlocked(Gf[d])?"3":"4",f=Vf(Gf[d][ff.Cl],b,[]);f&&f.length&&c(f[0].index);yC(b.id,Gf[d],e);var g=Vf(Gf[d][ff.Kl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var QC=!1,OB;function RC(){OB||(OB=new NB);return OB}
function SC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(C(109)){}if(d==="gtm.js"){if(QC)return!1;QC=!0}var e=!1,f=DC(),g=ld(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}nC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:TC(g,e),Eq:[],logMacroError:function(){L(6);Zn(0)},cachedModelValues:UC(),Pc:new TB(function(){if(C(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};C(120)&&fl&&(n.reportMacroDiscrepancy=sC);C(109)&&xA(n.id);var p=ag(n);C(109)&&yA(n.id);e&&(p=VC(p));C(109)&&wA(b);var q=OC(p,n),r=PC(a,n.Pc);YB(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||dC();return WC(p,q)||r}function UC(){var a={};a.event=wk("event",1);a.ecommerce=wk("ecommerce",1);a.gtm=wk("gtm");a.eventModel=wk("eventModel");return a}
function TC(a,b){var c=FB();return function(d){if(c(d))return!0;var e=d&&d[ff.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=ym();f=BC().getRestrictions(0,g);var h=a;b&&(h=ld(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=hk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function VC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Gf[c][ff.Ra]);if(Uj[d]||Gf[c][ff.no]!==void 0||RB(d,2))b[c]=!0}return b}function WC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Gf[c]&&!Vj[String(Gf[c][ff.Ra])])return!0;return!1};function XC(){RC().addListener("gtm.init",function(a,b){Mj.ba=!0;Jn();b()})};var YC=!1,ZC=0,$C=[];function aD(a){if(!YC){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){YC=!0;for(var e=0;e<$C.length;e++)Lc($C[e])}$C.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)Lc(f[g]);return 0}}}function bD(){if(!YC&&ZC<140){ZC++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");aD()}catch(c){x.setTimeout(bD,50)}}}
function cD(){var a=x;YC=!1;ZC=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")aD();else{Jc(z,"DOMContentLoaded",aD);Jc(z,"readystatechange",aD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&bD()}Jc(a,"load",aD)}}function dD(a){YC?a():$C.push(a)};var eD={},fD={};function gD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={xj:void 0,dj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.xj=Dp(g,b),e.xj){var h=xm();mb(h,function(r){return function(t){return r.xj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=eD[g]||[];e.dj={};m.forEach(function(r){return function(t){r.dj[t]=!0}}(e));for(var n=zm(),p=0;p<n.length;p++)if(e.dj[n[p]]){c=c.concat(xm());break}var q=fD[g]||[];q.length&&(c=c.concat(q))}}return{rj:c,Yp:d}}
function hD(a){qb(eD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function iD(a){qb(fD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var jD=!1,kD=!1;function lD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ld(b,null),b[J.m.df]&&(d.eventCallback=b[J.m.df]),b[J.m.Kg]&&(d.eventTimeout=b[J.m.Kg]));return d}function mD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:wp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function nD(a,b){var c=a&&a[J.m.ld];c===void 0&&(c=rk(J.m.ld,2),c===void 0&&(c="default"));if(jb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?jb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=gD(d,b.isGtmEvent),f=e.rj,g=e.Yp;if(g.length)for(var h=oD(a),m=0;m<g.length;m++){var n=Dp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=rm().destination[q];r&&r.state===0||MB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{rj:Ep(f,b.isGtmEvent),
Eo:Ep(t,b.isGtmEvent)}}}var pD=void 0,qD=void 0;function rD(a,b,c){var d=ld(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=ld(b,null);ld(c,e);Mw(Iw(zm()[0],e),a.eventId,d)}function oD(a){for(var b=l([J.m.md,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Dq.C[d];if(e)return e}}
var sD={config:function(a,b){var c=mD(a,b);if(!(a.length<2)&&jb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!kd(a[2])||a.length>3)return;d=a[2]}var e=Dp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!vm.pe){var m=Bm(qm());if(Km(m)){var n=m.parent,p=n.isDestination;h={aq:Bm(n),Up:p};break a}}h=void 0}var q=h;q&&(f=q.aq,g=q.Up);nC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?xm().indexOf(r)===-1:zm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=oD(d);if(t)MB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;pD?rD(b,v,pD):qD||(qD=ld(v,null))}else IB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;qD?(rD(b,qD,y),w=!1):(!y[J.m.od]&&Xj&&pD||(pD=ld(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}gl&&(yp===1&&(Bn.mcc=!1),yp=2);if(Xj&&!t&&!d[J.m.od]){var A=kD;kD=!0;if(A)return}jD||L(43);if(!b.noTargetGroup)if(t){iD(e.id);
var D=e.id,E=d[J.m.Ng]||"default";E=String(E).split(",");for(var G=0;G<E.length;G++){var I=fD[E[G]]||[];fD[E[G]]=I;I.indexOf(D)<0&&I.push(D)}}else{hD(e.id);var M=e.id,S=d[J.m.Ng]||"default";S=S.toString().split(",");for(var ea=0;ea<S.length;ea++){var U=eD[S[ea]]||[];eD[S[ea]]=U;U.indexOf(M)<0&&U.push(M)}}delete d[J.m.Ng];var ta=b.eventMetadata||{};ta.hasOwnProperty(P.A.ud)||(ta[P.A.ud]=!b.fromContainerExecution);b.eventMetadata=ta;delete d[J.m.df];for(var W=t?[e.id]:xm(),da=0;da<W.length;da++){var Y=
d,X=W[da],ma=ld(b,null),ja=Dp(X,ma.isGtmEvent);ja&&Dq.push("config",[Y],ja,ma)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=mD(a,b),d=a[1],e={},f=Co(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.qg?Array.isArray(h)?NaN:Number(h):g===J.m.fc?(Array.isArray(h)?h:[h]).map(Do):Eo(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.La]&&L(140));d==="default"?fp(e):d==="update"?hp(e,c):d==="declare"&&b.fromContainerExecution&&ep(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&jb(c)){var d=void 0;if(a.length>2){if(!kd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=lD(c,d),f=mD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=nD(d,b);if(m){var n=m.rj,p=m.Eo,q,r,t;if(C(108)){q=p.map(function(M){return M.id});r=p.map(function(M){return M.destinationId});t=n.map(function(M){return M.id});for(var u=l(xm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(M){return M.id}),r=n.map(function(M){return M.destinationId}),t=q;nC(g,c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var D=A.value,E=ld(b,null),G=ld(d,null);delete G[J.m.df];var I=E.eventMetadata||{};I.hasOwnProperty(P.A.ud)||(I[P.A.ud]=!E.fromContainerExecution);I[P.A.Ii]=q.slice();I[P.A.Of]=r.slice();E.eventMetadata=I;Eq(c,G,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.ld]=q.join(","):delete e.eventModel[J.m.ld];jD||L(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[P.A.Hl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&jb(a[1])&&jb(a[2])&&ib(a[3])){var c=Dp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){jD||L(43);var f=oD();if(mb(xm(),function(h){return c.destinationId===h})){mD(a,b);var g={};ld((g[J.m.qc]=d,g[J.m.Ic]=e,g),null);Fq(d,function(h){Lc(function(){e(h)})},c.id,b)}else MB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){jD=!0;var c=mD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&jb(a[1])&&ib(a[2])){if(gg(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](jg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&kd(a[1])?c=ld(a[1],null):a.length===3&&jb(a[1])&&(c={},kd(a[2])||Array.isArray(a[2])?
c[a[1]]=ld(a[2],null):c[a[1]]=a[2]);if(c){var d=mD(a,b),e=d.eventId,f=d.priorityId;ld(c,null);var g=ld(c,null);Dq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},tD={policy:!0};var vD=function(a){if(uD(a))return a;this.value=a};vD.prototype.getUntrustedMessageValue=function(){return this.value};var uD=function(a){return!a||id(a)!=="object"||kd(a)?!1:"getUntrustedMessageValue"in a};vD.prototype.getUntrustedMessageValue=vD.prototype.getUntrustedMessageValue;var wD=!1,xD=[];function yD(){if(!wD){wD=!0;for(var a=0;a<xD.length;a++)Lc(xD[a])}}function zD(a){wD?Lc(a):xD.push(a)};var AD=0,BD={},CD=[],DD=[],ED=!1,FD=!1;function GD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function HD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return ID(a)}function JD(a,b){if(!kb(b)||b<0)b=0;var c=sp[Tj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function KD(a,b){var c=a._clear||b.overwriteModelFields;qb(a,function(e,f){e!=="_clear"&&(c&&uk(e),uk(e,f))});ek||(ek=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=wp(),a["gtm.uniqueEventId"]=d,uk("gtm.uniqueEventId",d));return SC(a)}function LD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(rb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function MD(){var a;if(DD.length)a=DD.shift();else if(CD.length)a=CD.shift();else return;var b;var c=a;if(ED||!LD(c.message))b=c;else{ED=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=wp(),f=wp(),c.message["gtm.uniqueEventId"]=wp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};CD.unshift(n,c);b=h}return b}
function ND(){for(var a=!1,b;!FD&&(b=MD());){FD=!0;delete ok.eventModel;qk();var c=b,d=c.message,e=c.messageContext;if(d==null)FD=!1;else{e.fromContainerExecution&&vk();try{if(ib(d))try{d.call(sk)}catch(u){}else if(Array.isArray(d)){if(jb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=rk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(rb(d))a:{if(d.length&&jb(d[0])){var p=sD[d[0]];if(p&&(!e.fromContainerExecution||!tD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=KD(n,e)||a)}}finally{e.fromContainerExecution&&qk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=BD[String(q)]||[],t=0;t<r.length;t++)DD.push(OD(r[t]));r.length&&DD.sort(GD);delete BD[String(q)];q>AD&&(AD=q)}FD=!1}}}return!a}
function PD(){if(C(109)){var a=!Mj.ka;}var c=ND();if(C(109)){}try{var e=jg.ctid,f=x[Tj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Pw(a){if(AD<a.notBeforeEventId){var b=String(a.notBeforeEventId);BD[b]=BD[b]||[];BD[b].push(a)}else DD.push(OD(a)),DD.sort(GD),Lc(function(){FD||ND()})}function OD(a){return{message:a.message,messageContext:a.messageContext}}
function QD(){function a(f){var g={};if(uD(f)){var h=f;f=uD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=wc(Tj,[]),c=sp[Tj]=sp[Tj]||{};c.pruned===!0&&L(83);BD=Nw().get();Ow();dD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});zD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(sp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new vD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});CD.push.apply(CD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return ND()&&p};var e=b.slice(0).map(function(f){return a(f)});CD.push.apply(CD,e);if(!Mj.ka){if(C(109)){}Lc(PD)}}var ID=function(a){return x[Tj].push(a)};function RD(a){ID(a)};function SD(){var a,b=Rk(x.location.href);(a=b.hostname+b.pathname)&&Fn("dl",encodeURIComponent(a));var c;var d=jg.ctid;if(d){var e=vm.pe?1:0,f,g=Bm(qm());f=g&&g.context;c=d+";"+jg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Fn("tdp",h);var m=Gl(!0);m!==void 0&&Fn("frm",String(m))};function TD(){(Po()||gl)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=cm(a.effectiveDirective);if(b){var c;var d=am(b,a.blockedURI);c=d?Zl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Am){p.Am=!0;if(C(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Po()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Po()){var u=Vo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Oo(u)}}}Ln(p.endpoint)}}bm(b,a.blockedURI)}}}}})};function UD(){var a;var b=Am();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Fn("pcid",e)};var VD=/^(https?:)?\/\//;
function WD(){var a=Cm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=$c())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(VD,"")===d.replace(VD,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Fn("rtg",String(a.canonicalContainerId)),Fn("slo",String(p)),Fn("hlo",a.htmlLoadOrder||"-1"),
Fn("lst",String(a.loadScriptType||"0")))}else L(144)};function XD(){var a=[],b=Number('1')||0,c=function(){var f=!1;return f}();a.push({Hm:195,Gm:195,experimentId:104527906,controlId:104527907,percent:b,active:c,Wi:1});var d=Number('1')||0,e=function(){var f=!1;
return f}();a.push({Hm:196,Gm:196,experimentId:104528500,controlId:104528501,percent:d,active:e,Wi:0});return a};var YD={};function ZD(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Mj.R.H.add(Number(c.value))}function $D(){for(var a=l(XD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Hm;ki[d]=c;if(c.Wi===1){var e=d,f=tn(on.Z.po);ni(f,e);ZD(f)}else if(c.Wi===0){var g=YD;ni(g,d);ZD(g)}}};

function uE(){};var vE=function(){};vE.prototype.toString=function(){return"undefined"};var wE=new vE;function DE(a,b){function c(g){var h=Rk(g),m=Lk(h,"protocol"),n=Lk(h,"host",!0),p=Lk(h,"port"),q=Lk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function EE(a){return FE(a)?1:0}
function FE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ld(a,{});ld({arg1:c[d],any_of:void 0},e);if(EE(e))return!0}return!1}switch(a["function"]){case "_cn":return Pg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Kg.length;g++){var h=Kg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Lg(b,c);case "_eq":return Qg(b,c);case "_ge":return Rg(b,c);case "_gt":return Tg(b,c);case "_lc":return Mg(b,c);case "_le":return Sg(b,
c);case "_lt":return Ug(b,c);case "_re":return Og(b,c,a.ignore_case);case "_sw":return Vg(b,c);case "_um":return DE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var GE=function(a,b,c,d){Uq.call(this);this.gh=b;this.Kf=c;this.qb=d;this.Sa=new Map;this.hh=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};ra(GE,Uq);GE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.H;delete this.qb;Uq.prototype.N.call(this)};
var HE=function(a){if(a.C)return a.C;a.Kf&&a.Kf(a.H)?a.C=a.H:a.C=Fl(a.H,a.gh);var b;return(b=a.C)!=null?b:null},JE=function(a,b,c){if(HE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.qj){IE(a);var f=++a.hh;a.Ba.set(f,{Dh:e.Dh,Xo:e.gm(c),persistent:b==="addEventListener"});a.C.postMessage(e.qj(c,f),"*")}}},IE=function(a){a.R||(a.R=function(b){try{var c;c=a.qb?a.qb(b):void 0;if(c){var d=c.fq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Xo,c.payload)}}}catch(g){}},Pq(a.H,"message",a.R))};var KE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},LE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},ME={gm:function(a){return a.listener},qj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},NE={gm:function(a){return a.listener},qj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function OE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,fq:b.__gppReturn.callId}}
var PE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Uq.call(this);this.caller=new GE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},OE);this.caller.Sa.set("addEventListener",KE);this.caller.ka.set("addEventListener",ME);this.caller.Sa.set("removeEventListener",LE);this.caller.ka.set("removeEventListener",NE);this.timeoutMs=c!=null?c:500};ra(PE,Uq);PE.prototype.N=function(){this.caller.dispose();Uq.prototype.N.call(this)};
PE.prototype.addEventListener=function(a){var b=this,c=il(function(){a(QE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);JE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(RE,!0);return}a(SE,!0)}}})};
PE.prototype.removeEventListener=function(a){JE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var SE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},QE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},RE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function TE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){vv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");vv.C=d}}function UE(){try{var a=new PE(x,{timeoutMs:-1});HE(a.caller)&&a.addEventListener(TE)}catch(b){}};function VE(){var a=[["cv",Qi(1)],["rv",Rj],["tc",Gf.filter(function(b){return b}).length]];Sj&&a.push(["x",Sj]);jk()&&a.push(["tag_exp",jk()]);return a};var WE={};function Ti(a){WE[a]=(WE[a]||0)+1}function XE(){for(var a=[],b=l(Object.keys(WE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+WE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var YE={},ZE={};function $E(a){var b=a.eventId,c=a.Md,d=[],e=YE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=ZE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete YE[b],delete ZE[b]);return d};function aF(){return!1}function bF(){var a={};return function(b,c,d){}};function cF(){var a=dF;return function(b,c,d){var e=d&&d.event;eF(c);var f=Ah(b)?void 0:1,g=new Ta;qb(c,function(r,t){var u=Bd(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Nb(Zf());var h={Ql:ng(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Rf:e!==void 0?function(r){e.Pc.Rf(r)}:void 0,Jb:function(){return b},log:function(){},kp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},oq:!!RB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(aF()){var m=bF(),n,p;h.wb={Fj:[],Sf:{},ac:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Sh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=Xe(a,h,[b,g]);a.Nb();q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return Ad(q,void 0,f)}}function eF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ib(b)&&(a.gtmOnSuccess=function(){Lc(b)});ib(c)&&(a.gtmOnFailure=function(){Lc(c)})};function fF(a){}fF.M="internal.addAdsClickIds";function gF(a,b){var c=this;}gF.publicName="addConsentListener";var hF=!1;function iF(a){for(var b=0;b<a.length;++b)if(hF)try{a[b]()}catch(c){L(77)}else a[b]()}function jF(a,b,c){var d=this,e;return e}jF.M="internal.addDataLayerEventListener";function kF(a,b,c){}kF.publicName="addDocumentEventListener";function lF(a,b,c,d){}lF.publicName="addElementEventListener";function mF(a){return a.K.sb()};function nF(a){}nF.publicName="addEventCallback";
function CF(a){}CF.M="internal.addFormAbandonmentListener";function DF(a,b,c,d){}
DF.M="internal.addFormData";var EF={},FF=[],GF={},HF=0,IF=0;
function PF(a,b){}PF.M="internal.addFormInteractionListener";
function WF(a,b){}WF.M="internal.addFormSubmitListener";
function aG(a){}aG.M="internal.addGaSendListener";function bG(a){if(!a)return{};var b=a.kp;return QB(b.type,b.index,b.name)}function cG(a){return a?{originatingEntity:bG(a)}:{}};function kG(a){var b=sp.zones;return b?b.getIsAllowedFn(zm(),a):function(){return!0}}function lG(){var a=sp.zones;a&&a.unregisterChild(zm())}
function mG(){CC(ym(),function(a){var b=sp.zones;return b?b.isActive(zm(),a.originalEventData["gtm.uniqueEventId"]):!0});AC(ym(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return kG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var nG=function(a,b){this.tagId=a;this.ve=b};
function oG(a,b){var c=this;return a}oG.M="internal.loadGoogleTag";function pG(a){return new sd("",function(b){var c=this.evaluate(b);if(c instanceof sd)return new sd("",function(){var d=ya.apply(0,arguments),e=this,f=ld(mF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Kd(f);return c.Lb.apply(c,[h].concat(ua(g)))})})};function qG(a,b,c){var d=this;}qG.M="internal.addGoogleTagRestriction";var rG={},sG=[];
function zG(a,b){}
zG.M="internal.addHistoryChangeListener";function AG(a,b,c){}AG.publicName="addWindowEventListener";function BG(a,b){return!0}BG.publicName="aliasInWindow";function CG(a,b,c){if(!lh(a)||!lh(b))throw F(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!kd(e[d[f]]))throw Error("apendRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)e[d[f]]=[];else if(!Array.isArray(e[d[f]]))throw Error("appendRemoteConfigParameter failed, destination is not an array: "+
d[f]);e[d[f]].push(Ad(c,this.K));}CG.M="internal.appendRemoteConfigParameter";function DG(a){var b;return b}
DG.publicName="callInWindow";function EG(a){}EG.publicName="callLater";function FG(a){}FG.M="callOnDomReady";function GG(a){}GG.M="callOnWindowLoad";function HG(a,b){var c;return c}HG.M="internal.computeGtmParameter";function IG(a,b){var c=this;}IG.M="internal.consentScheduleFirstTry";function JG(a,b){var c=this;}JG.M="internal.consentScheduleRetry";function KG(a){var b;return b}KG.M="internal.copyFromCrossContainerData";function LG(a,b){var c;var d=Bd(c,this.K,Ah(mF(this).Jb())?2:1);d===void 0&&c!==void 0&&L(45);return d}LG.publicName="copyFromDataLayer";
function MG(a){var b=void 0;return b}MG.M="internal.copyFromDataLayerCache";function NG(a){var b;return b}NG.publicName="copyFromWindow";function OG(a){var b=void 0;return Bd(b,this.K,1)}OG.M="internal.copyKeyFromWindow";var PG=function(a){return a===Nm.X.Da&&en[a]===Mm.Ia.oe&&!O(J.m.U)};var QG=function(){return"0"},RG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];C(102)&&b.push("gbraid");return Sk(a,b,"0")};var SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH=(qH[J.m.Qa]=(SG[2]=[PG],SG),qH[J.m.rf]=(TG[2]=[PG],TG),qH[J.m.ef]=(UG[2]=[PG],UG),qH[J.m.li]=(VG[2]=[PG],VG),qH[J.m.mi]=(WG[2]=[PG],WG),qH[J.m.ni]=(XG[2]=[PG],XG),qH[J.m.oi]=(YG[2]=[PG],YG),qH[J.m.ri]=(ZG[2]=[PG],ZG),qH[J.m.wc]=($G[2]=[PG],$G),qH[J.m.uf]=(aH[2]=[PG],aH),qH[J.m.vf]=(bH[2]=[PG],bH),qH[J.m.wf]=(cH[2]=[PG],cH),qH[J.m.xf]=(dH[2]=
[PG],dH),qH[J.m.yf]=(eH[2]=[PG],eH),qH[J.m.zf]=(fH[2]=[PG],fH),qH[J.m.Af]=(gH[2]=[PG],gH),qH[J.m.Bf]=(hH[2]=[PG],hH),qH[J.m.lb]=(iH[1]=[PG],iH),qH[J.m.Zc]=(jH[1]=[PG],jH),qH[J.m.fd]=(kH[1]=[PG],kH),qH[J.m.Wd]=(lH[1]=[PG],lH),qH[J.m.Pe]=(mH[1]=[function(a){return C(102)&&PG(a)}],mH),qH[J.m.gd]=(nH[1]=[PG],nH),qH[J.m.Aa]=(oH[1]=[PG],oH),qH[J.m.Wa]=(pH[1]=[PG],pH),qH),sH={},tH=(sH[J.m.lb]=QG,sH[J.m.Zc]=QG,sH[J.m.fd]=QG,sH[J.m.Wd]=QG,sH[J.m.Pe]=QG,sH[J.m.gd]=function(a){if(!kd(a))return{};var b=ld(a,
null);delete b.match_id;return b},sH[J.m.Aa]=RG,sH[J.m.Wa]=RG,sH),uH={},vH={},wH=(vH[P.A.ib]=(uH[2]=[PG],uH),vH),xH={};var yH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};yH.prototype.getValue=function(a){a=a===void 0?Nm.X.Fb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};yH.prototype.H=function(){return id(this.C)==="array"||kd(this.C)?ld(this.C,null):this.C};
var zH=function(){},AH=function(a,b){this.conditions=a;this.C=b},BH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new yH(c,e,g,a.C[b]||zH)},CH,DH;var EH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;R(this,g,d[g])}},Lv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,Q(a,P.A.Pf))},T=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(CH!=null||(CH=new AH(rH,tH)),e=BH(CH,b,c));d[b]=e};
EH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return T(this,a,b),!0;if(!kd(c))return!1;T(this,a,Object.assign(c,b));return!0};var FH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
EH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&jb(d)&&C(92))try{d=c(d)}catch(e){}d!==void 0&&T(this,a,d)};
var Q=function(a,b){var c=a.metadata[b];if(b===P.A.Pf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,Q(a,P.A.Pf))},R=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(DH!=null||(DH=new AH(wH,xH)),e=BH(DH,b,c));d[b]=e},GH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},ew=function(a,b,c){var d=Tw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function HH(a,b){var c;if(!eh(a)||!fh(b))throw F(this.getName(),["Object","Object|undefined"],arguments);var d=Ad(b)||{},e=Ad(a,this.K,1).Ab(),f=e.D;d.omitEventContext&&(f=gq(new Wp(e.D.eventId,e.D.priorityId)));var g=new EH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=FH(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;T(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=GH(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;R(g,u,q[u])}g.isAborted=e.isAborted;c=Bd(Bw(g),this.K,1);return c}HH.M="internal.copyPreHit";function IH(a,b){var c=null;return Bd(c,this.K,2)}IH.publicName="createArgumentsQueue";function JH(a){return Bd(function(c){var d=$B();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
$B(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}JH.M="internal.createGaCommandQueue";function KH(a){return Bd(function(){if(!ib(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ah(mF(this).Jb())?2:1)}KH.publicName="createQueue";function LH(a,b){var c=null;if(!lh(a)||!mh(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new xd(new RegExp(a,d))}catch(e){}return c}LH.M="internal.createRegex";function MH(a){if(!eh(a))throw F(this.getName(),["Object"],arguments);for(var b=a.wa(),c=l(b),d=c.next();!d.done;d=c.next()){var e=d.value;e!==J.m.fc&&H(this,"access_consent",e,"write")}var f=mF(this),g=f.eventId,h=cG(f),m=Ad(a);Mw(Hw("consent","declare",m),g,h);}MH.M="internal.declareConsentState";function NH(a){var b="";return b}NH.M="internal.decodeUrlHtmlEntities";function OH(a,b,c){var d;return d}OH.M="internal.decorateUrlWithGaCookies";function PH(){}PH.M="internal.deferCustomEvents";function QH(a){var b;H(this,"detect_user_provided_data","auto");var c=Ad(a)||{},d=sx({Ae:!!c.includeSelector,Be:!!c.includeVisibility,Uf:c.excludeElementSelectors,Yb:c.fieldFilters,Eh:!!c.selectMultipleElements});b=new Ta;var e=new od;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(RH(f[g]));d.yj!==void 0&&b.set("preferredEmailElement",RH(d.yj));b.set("status",d.status);if(C(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(sc&&
sc.userAgent||"")){}return b}
var SH=function(a){switch(a){case qx.hc:return"email";case qx.xd:return"phone_number";case qx.pd:return"first_name";case qx.wd:return"last_name";case qx.Mi:return"street";case qx.Hh:return"city";case qx.Hi:return"region";case qx.Mf:return"postal_code";case qx.Je:return"country"}},RH=function(a){var b=new Ta;b.set("userData",a.la);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(C(33)){}else switch(a.type){case qx.hc:b.set("type","email")}return b};QH.M="internal.detectUserProvidedData";
function VH(a,b){return f}VH.M="internal.enableAutoEventOnClick";
function cI(a,b){return p}cI.M="internal.enableAutoEventOnElementVisibility";function dI(){}dI.M="internal.enableAutoEventOnError";var eI={},fI=[],gI={},hI=0,iI=0;
function oI(a,b){var c=this;return d}oI.M="internal.enableAutoEventOnFormInteraction";
function tI(a,b){var c=this;return f}tI.M="internal.enableAutoEventOnFormSubmit";
function yI(){var a=this;}yI.M="internal.enableAutoEventOnGaSend";var zI={},AI=[];
function HI(a,b){var c=this;return f}HI.M="internal.enableAutoEventOnHistoryChange";var II=["http://","https://","javascript:","file://"];
function MI(a,b){var c=this;return h}MI.M="internal.enableAutoEventOnLinkClick";var NI,OI;
function ZI(a,b){var c=this;return d}ZI.M="internal.enableAutoEventOnScroll";function $I(a){return function(){if(a.limit&&a.tj>=a.limit)a.yh&&x.clearInterval(a.yh);else{a.tj++;var b=yb();ID({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.tj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Fm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Fm,"gtm.triggers":a.Jq})}}}
function aJ(a,b){
return f}aJ.M="internal.enableAutoEventOnTimer";var mc=wa(["data-gtm-yt-inspected-"]),cJ=["www.youtube.com","www.youtube-nocookie.com"],dJ,eJ=!1;
function oJ(a,b){var c=this;return e}oJ.M="internal.enableAutoEventOnYouTubeActivity";eJ=!1;function pJ(a,b){if(!lh(a)||!fh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Ad(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Hh(f,c);return e}pJ.M="internal.evaluateBooleanExpression";var qJ;function rJ(a){var b=!1;return b}rJ.M="internal.evaluateMatchingRules";function aK(){return nr(7)&&nr(9)&&nr(10)};function WK(a,b,c,d){}WK.M="internal.executeEventProcessor";function XK(a){var b;return Bd(b,this.K,1)}XK.M="internal.executeJavascriptString";function YK(a){var b;return b};function ZK(a){var b="";return b}ZK.M="internal.generateClientId";function $K(a){var b={};return Bd(b)}$K.M="internal.getAdsCookieWritingOptions";function aL(a,b){var c=!1;return c}aL.M="internal.getAllowAdPersonalization";function bL(){var a;return a}bL.M="internal.getAndResetEventUsage";function cL(a,b){b=b===void 0?!0:b;var c;return c}cL.M="internal.getAuid";var dL=null;
function eL(){var a=new Ta;H(this,"read_container_data"),C(49)&&dL?a=dL:(a.set("containerId",'G-DP2X732JSX'),a.set("version",'8'),a.set("environmentName",''),a.set("debugMode",og),a.set("previewMode",pg.Jm),a.set("environmentMode",pg.fp),a.set("firstPartyServing",lk()||Mj.N),a.set("containerUrl",vc),a.Ua(),C(49)&&(dL=a));return a}
eL.publicName="getContainerVersion";function fL(a,b){b=b===void 0?!0:b;var c;return c}fL.publicName="getCookieValues";function gL(){var a="";return a}gL.M="internal.getCorePlatformServicesParam";function hL(){return ho()}hL.M="internal.getCountryCode";function iL(){var a=[];a=xm();return Bd(a)}iL.M="internal.getDestinationIds";function jL(a){var b=new Ta;return b}jL.M="internal.getDeveloperIds";function kL(a){var b;return b}kL.M="internal.getEcsidCookieValue";function lL(a,b){var c=null;return c}lL.M="internal.getElementAttribute";function mL(a){var b=null;return b}mL.M="internal.getElementById";function nL(a){var b="";return b}nL.M="internal.getElementInnerText";function oL(a,b){var c=null;return Bd(c)}oL.M="internal.getElementProperty";function pL(a){var b;return b}pL.M="internal.getElementValue";function qL(a){var b=0;return b}qL.M="internal.getElementVisibilityRatio";function rL(a){var b=null;return b}rL.M="internal.getElementsByCssSelector";
function sL(a){var b;if(!lh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=mF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),D=A.next();!D.done;D=
A.next()){var E=D.value;E===m?(w.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Bd(c,this.K,1);return b}sL.M="internal.getEventData";var tL={};tL.enableDecodeUri=C(92);tL.enableGaAdsConversions=C(122);tL.enableGaAdsConversionsClientId=C(121);tL.enableOverrideAdsCps=C(170);tL.enableUrlDecodeEventUsage=C(139);function uL(){return Bd(tL)}uL.M="internal.getFlags";function vL(){var a;return a}vL.M="internal.getGsaExperimentId";function wL(){return new xd(wE)}wL.M="internal.getHtmlId";function xL(a){var b;return b}xL.M="internal.getIframingState";function yL(a,b){var c={};return Bd(c)}yL.M="internal.getLinkerValueFromLocation";function zL(){var a=new Ta;return a}zL.M="internal.getPrivacyStrings";function AL(a,b){var c;if(!lh(a)||!lh(b))throw F(this.getName(),["string","string"],arguments);var d=Tw(a)||{};c=Bd(d[b],this.K);return c}AL.M="internal.getProductSettingsParameter";function BL(a,b){var c;return c}BL.publicName="getQueryParameters";function CL(a,b){var c;return c}CL.publicName="getReferrerQueryParameters";function DL(a){var b="";return b}DL.publicName="getReferrerUrl";function EL(){return io()}EL.M="internal.getRegionCode";function FL(a,b){var c;return c}FL.M="internal.getRemoteConfigParameter";function GL(){var a=new Ta;a.set("width",0);a.set("height",0);return a}GL.M="internal.getScreenDimensions";function HL(){var a="";return a}HL.M="internal.getTopSameDomainUrl";function IL(){var a="";return a}IL.M="internal.getTopWindowUrl";function JL(a){var b="";return b}JL.publicName="getUrl";function KL(){H(this,"get_user_agent");return sc.userAgent}KL.M="internal.getUserAgent";function LL(){var a;return a?Bd(Oy(a)):a}LL.M="internal.getUserAgentClientHints";var NL=function(a){var b=a.eventName===J.m.Yc&&Zm()&&fy(a),c=Q(a,P.A.pl),d=Q(a,P.A.Lj),e=Q(a,P.A.Hf),f=Q(a,P.A.me),g=Q(a,P.A.sg),h=Q(a,P.A.Nd),m=Q(a,P.A.tg),n=Q(a,P.A.ug),p=!!ey(a)||!!Q(a,P.A.Nh);return!(!Vc()&&sc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&ML)},ML=!1;
var OL=function(a){var b=0,c=0;return{start:function(){b=yb()},stop:function(){c=this.get()},get:function(){var d=0;a.kj()&&(d=yb()-b);return d+c}}},PL=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.R=this.P=void 0};k=PL.prototype;k.ho=function(a){var b=this;if(!this.C){this.N=z.hasFocus();this.isVisible=!z.hidden;this.isActive=!0;var c=function(e,f,g){Jc(e,f,function(h){b.C.stop();g(h);b.kj()&&b.C.start()})},d=x;c(d,"focus",function(){b.N=!0});c(d,"blur",function(){b.N=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&L(56);b.R&&b.R()});c(d,"pagehide",function(){b.isActive=!1;b.P&&b.P()});c(z,"visibilitychange",function(){b.isVisible=!z.hidden});fy(a)&&!yc()&&c(d,"beforeunload",function(){ML=!0});this.Bj(!0);this.H=0}};k.Bj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.wh(),this.C=OL(this),this.kj()&&this.C.start()};k.Iq=function(a){var b=this.wh();b>0&&T(a,J.m.Gg,b)};k.Ep=function(a){T(a,J.m.Gg);this.Bj();this.H=0};k.kj=function(){return this.N&&
this.isVisible&&this.isActive};k.up=function(){return this.H+this.wh()};k.wh=function(){return this.C&&this.C.get()||0};k.nq=function(a){this.P=a};k.zm=function(a){this.R=a};var QL=function(a){cb("GA4_EVENT",a)};var RL=function(a){var b=Q(a,P.A.Xk);if(Array.isArray(b))for(var c=0;c<b.length;c++)QL(b[c]);var d=fb("GA4_EVENT");d&&T(a,"_eu",d)},SL=function(){delete bb.GA4_EVENT};function TL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function UL(){var a=TL();a.hid=a.hid||nb();return a.hid}function VL(a,b){var c=TL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var WL=["GA1"];
var XL=function(a,b,c){var d=Q(a,P.A.Nj);if(d===void 0||c<=d)T(a,J.m.Qb,b),R(a,P.A.Nj,c)},ZL=function(a,b){var c=Lv(a,J.m.Qb);if(N(a.D,J.m.Lc)&&N(a.D,J.m.Kc)||b&&c===b)return c;if(c){c=""+c;if(!YL(c,a))return L(31),a.isAborted=!0,"";VL(c,O(J.m.ia));return c}L(32);a.isAborted=!0;return""},$L=function(a){var b=Q(a,P.A.ya),c=b.prefix+"_ga",d=As(b.prefix+"_ga",b.domain,b.path,WL,J.m.ia);if(!d){var e=String(N(a.D,J.m.ed,""));e&&e!==c&&(d=As(e,b.domain,b.path,WL,J.m.ia))}return d},YL=function(a,b){var c;
var d=Q(b,P.A.ya),e=d.prefix+"_ga",f=Mr(d,void 0,void 0,J.m.ia);if(N(b.D,J.m.Hc)===!1&&$L(b)===a)c=!0;else{var g;g=[WL[0],xs(d.domain,d.path),a].join(".");c=ss(e,g,f)!==1}return c};
var aM=function(a){if(a){var b;a:{var c=(Db(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Bt(c,2);break a}catch(d){}b=void 0}return b}},cM=function(a,b){var c;a:{var d=bM,e=At[2];if(e){var f,g=vs(b.domain),h=ws(b.path),m=Object.keys(e.Fh),n=Et.get(2),p;if(f=(p=ks(a,g,h,m,n))==null?void 0:p.Qo){var q=Bt(f,2,d);c=q?Gt(q):void 0;break a}}c=void 0}if(c){var r=Ft(a,2,bM);if(r&&r.length>1){QL(28);var t;if(r&&r.length!==0){for(var u,v=-Infinity,w=l(r),y=w.next();!y.done;y=w.next()){var A=y.value;
if(A.t!==void 0){var D=Number(A.t);!isNaN(D)&&D>v&&(v=D,u=A)}}t=u}else t=void 0;var E=t;E&&E.t!==c.t&&(QL(32),c=E)}return Dt(c,2)}},bM=function(a){a&&(a==="GS1"?QL(33):a==="GS2"&&QL(34))},dM=function(a){var b=aM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||QL(29);d||QL(30);isNaN(e)&&QL(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var fM=function(a,b,c){if(!b)return a;if(!a)return b;var d=dM(a);if(!d)return b;var e,f=tb((e=N(c.D,J.m.qf))!=null?e:30),g=Q(c,P.A.fb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=dM(b);if(!h)return a;h.o=d.o+1;var m;return(m=eM(h))!=null?m:b},hM=function(a,b){var c=Q(b,P.A.ya),d=gM(b,c),e=aM(a);if(!e)return!1;var f=Mr(c||{},void 0,void 0,Et.get(2));ss(d,void 0,f);return Ht(d,e,2,c)!==1},iM=function(a){var b=Q(a,P.A.ya);return cM(gM(a,b),b)},jM=function(a){var b=Q(a,P.A.fb),c={};c.s=Lv(a,J.m.uc);
c.o=Lv(a,J.m.Tg);var d;d=Lv(a,J.m.Sg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=Q(a,P.A.ne),c.j=Q(a,P.A.Jf)||0,c.l=!!Q(a,J.m.Zh),c.h=Lv(a,J.m.Hg),c);return eM(e)},eM=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=tb(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Dt(c,2)}},gM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Fp[6]]};
var kM=function(a){var b=N(a.D,J.m.Pa),c=a.D.H[J.m.Pa];if(c===b)return c;var d=ld(b,null);c&&c[J.m.ma]&&(d[J.m.ma]=(d[J.m.ma]||[]).concat(c[J.m.ma]));return d},lM=function(a,b){var c=Ts(!0);return c._up!=="1"?{}:{clientId:c[a],ub:c[b]}},mM=function(a,b,c){var d=Ts(!0),e=d[b];e&&(XL(a,e,2),YL(e,a));var f=d[c];f&&hM(f,a);return{clientId:e,ub:f}},nM=function(){var a=Nk(x.location,"host"),b=Nk(Rk(z.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},oM=function(a){if(!N(a.D,
J.m.Eb))return{};var b=Q(a,P.A.ya),c=b.prefix+"_ga",d=gM(a,b);at(function(){var e;if(O("analytics_storage"))e={};else{var f={_up:"1"},g;g=Lv(a,J.m.Qb);e=(f[c]=g,f[d]=jM(a),f)}return e},1);return!O("analytics_storage")&&nM()?lM(c,d):{}},qM=function(a){var b=kM(a)||{},c=Q(a,P.A.ya),d=c.prefix+"_ga",e=gM(a,c),f={};ct(b[J.m.ce],!!b[J.m.ma])&&(f=mM(a,d,e),f.clientId&&f.ub&&(pM=!0));b[J.m.ma]&&$s(function(){var g={},h=$L(a);h&&(g[d]=h);var m=iM(a);m&&(g[e]=m);var n=hs("FPLC",void 0,void 0,J.m.ia);n.length&&
(g._fplc=n[0]);return g},b[J.m.ma],b[J.m.Mc],!!b[J.m.sc]);return f},pM=!1;var rM=function(a){if(!Q(a,P.A.vd)&&Zk(a.D)){var b=kM(a)||{},c=(ct(b[J.m.ce],!!b[J.m.ma])?Ts(!0)._fplc:void 0)||(hs("FPLC",void 0,void 0,J.m.ia).length>0?void 0:"0");T(a,"_fplc",c)}};function sM(a){(fy(a)||lk())&&T(a,J.m.Sk,io()||ho());!fy(a)&&lk()&&T(a,J.m.il,"::")}function tM(a){if(lk()&&!fy(a)){var b=C(176);C(187)&&C(201)&&(b=b&&!lo());b&&T(a,J.m.Gk,!0);if(C(78)){Zv(a);$v(a,Ap.Df.Vm,Fo(N(a.D,J.m.cb)));var c=Ap.Df.Wm;var d=N(a.D,J.m.Hc);$v(a,c,d===!0?1:d===!1?0:void 0);$v(a,Ap.Df.Um,Fo(N(a.D,J.m.yb)));$v(a,Ap.Df.Sm,xs(Eo(N(a.D,J.m.nb)),Eo(N(a.D,J.m.Sb))))}}};var vM=function(a,b){tp("grl",function(){return uM()})(b)||(L(35),a.isAborted=!0)},uM=function(){var a=yb(),b=a+864E5,c=20,d=5E3;return function(e){var f=yb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Wo=d,e.Ho=c);return g}};
var wM=function(a){var b=Lv(a,J.m.Wa);return Lk(Rk(b),"host",!0)},xM=function(a){if(N(a.D,J.m.kf)!==void 0)a.copyToHitData(J.m.kf);else{var b=N(a.D,J.m.fi),c,d;a:{if(pM){var e=kM(a)||{};if(e&&e[J.m.ma])for(var f=wM(a),g=e[J.m.ma],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=wM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(T(a,J.m.kf,"1"),
QL(4))}};
var yM=function(a,b){ur()&&(a.gcs=vr(),Q(b,P.A.Gf)&&(a.gcu="1"));a.gcd=zr(b.D);C(97)?a.npa=Q(b,P.A.Gh)?"0":"1":tr(b.D)?a.npa="0":a.npa="1";Er()&&(a._ng="1")},zM=function(a){return O(J.m.U)&&O(J.m.ia)?lk()&&Q(a,P.A.Ci):!1},AM=function(a){if(Q(a,P.A.vd))return{url:$k("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=Wk(Zk(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=gy(a),d=N(a.D,J.m.Ob),e=c&&!jo()&&d!==!1&&aK()&&O(J.m.U)&&O(J.m.ia)?17:16;return{url:Kz(e),
endpoint:e}},BM={};BM[J.m.Qb]="cid";BM[J.m.Ph]="gcut";BM[J.m.dd]="are";BM[J.m.Eg]="pscdl";BM[J.m.ai]="_fid";BM[J.m.Ck]="_geo";BM[J.m.Ub]="gdid";BM[J.m.ae]="_ng";BM[J.m.Jc]="frm";BM[J.m.kf]="ir";BM[J.m.Gk]="fp";BM[J.m.zb]="ul";BM[J.m.Qg]="ni";BM[J.m.Qn]="pae";BM[J.m.Rg]="_rdi";BM[J.m.Nc]="sr";BM[J.m.Un]="tid";BM[J.m.ki]="tt";BM[J.m.wc]="ec_mode";BM[J.m.nl]="gtm_up";BM[J.m.uf]=
"uaa";BM[J.m.vf]="uab";BM[J.m.wf]="uafvl";BM[J.m.xf]="uamb";BM[J.m.yf]="uam";BM[J.m.zf]="uap";BM[J.m.Af]="uapv";BM[J.m.Bf]="uaw";BM[J.m.Sk]="ur";BM[J.m.il]="_uip";BM[J.m.Pn]="_prs";BM[J.m.kd]="lps";BM[J.m.Td]="gclgs";BM[J.m.Vd]="gclst";BM[J.m.Ud]="gcllp";var CM={};CM[J.m.Re]="cc";
CM[J.m.Se]="ci";CM[J.m.Te]="cm";CM[J.m.Ue]="cn";CM[J.m.We]="cs";CM[J.m.Xe]="ck";CM[J.m.Va]="cu";CM[J.m.jf]="_tu";CM[J.m.Aa]="dl";CM[J.m.Wa]="dr";CM[J.m.Db]="dt";CM[J.m.Sg]="seg";CM[J.m.uc]="sid";CM[J.m.Tg]="sct";CM[J.m.Qa]="uid";C(145)&&(CM[J.m.nf]="dp");var DM={};DM[J.m.Gg]="_et";DM[J.m.Tb]="edid";C(94)&&(DM._eu="_eu");var EM={};EM[J.m.Re]="cc";EM[J.m.Se]="ci";
EM[J.m.Te]="cm";EM[J.m.Ue]="cn";EM[J.m.We]="cs";EM[J.m.Xe]="ck";var FM={},GM=(FM[J.m.eb]=1,FM),HM=function(a,b,c){function d(W,da){if(da!==void 0&&!po.hasOwnProperty(W)){da===null&&(da="");var Y;var X=da;W!==J.m.Hg?Y=!1:Q(a,P.A.he)||fy(a)?(e.ecid=X,Y=!0):Y=void 0;if(!Y&&W!==J.m.Zh){var ma=da;da===!0&&(ma="1");da===!1&&(ma="0");ma=String(ma);var ja;if(BM[W])ja=BM[W],e[ja]=ma;else if(CM[W])ja=CM[W],g[ja]=ma;else if(DM[W])ja=DM[W],f[ja]=ma;else if(W.charAt(0)==="_")e[W]=ma;else{var la;EM[W]?la=!0:W!==
J.m.Ve?la=!1:(typeof da!=="object"&&D(W,da),la=!0);la||D(W,da)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=Kr({Ma:Q(a,P.A.hb)});e._p=C(159)?ek:UL();if(c&&(c.Za||c.ej)&&(C(125)||(e.em=c.Kb),c.Hb)){var h=c.Hb.xe;h&&!C(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}Q(a,P.A.Nd)&&(e._gaz=1);yM(e,a);Cr()&&(e.dma_cps=Ar());e.dma=Br();Yq(fr())&&(e.tcfd=Dr());Lz()&&(e.tag_exp=Lz());Mz()&&(e.ptag_exp=Mz());var m=Lv(a,J.m.Ub);m&&(e.gdid=m);f.en=String(a.eventName);if(Q(a,P.A.If)){var n=Q(a,P.A.kl);
f._fv=n?2:1}Q(a,P.A.bh)&&(f._nsi=1);if(Q(a,P.A.me)){var p=Q(a,P.A.ol);f._ss=p?2:1}Q(a,P.A.Hf)&&(f._c=1);Q(a,P.A.ud)&&(f._ee=1);if(Q(a,P.A.jl)){var q=Lv(a,J.m.sa)||N(a.D,J.m.sa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=tg(q[r])}var t=Lv(a,J.m.Tb);t&&(f.edid=t);var u=Lv(a,J.m.rc);if(u&&typeof u==="object")for(var v=l(Object.keys(u)),w=v.next();!w.done;w=v.next()){var y=w.value,A=u[y];A!==void 0&&(A===null&&(A=""),f["gap."+y]=String(A))}for(var D=function(W,da){if(typeof da!==
"object"||!GM[W]){var Y="ep."+W,X="epn."+W;W=kb(da)?X:Y;var ma=kb(da)?Y:X;f.hasOwnProperty(ma)&&delete f[ma];f[W]=String(da)}},E=l(Object.keys(a.C)),G=E.next();!G.done;G=E.next()){var I=G.value;d(I,Lv(a,I))}(function(W){fy(a)&&typeof W==="object"&&qb(W||{},function(da,Y){typeof Y!=="object"&&(e["sst."+da]=String(Y))})})(Lv(a,J.m.Ji));Nz(e,Lv(a,J.m.rd));var M=Lv(a,J.m.Wb)||{};N(a.D,J.m.Ob,void 0,4)===!1&&(e.ngs="1");qb(M,function(W,da){da!==void 0&&((da===null&&(da=""),W!==J.m.Qa||g.uid)?b[W]!==da&&
(f[(kb(da)?"upn.":"up.")+String(W)]=String(da),b[W]=da):g.uid=String(da))});if(C(176)){var S=C(187)&&lo();if(lk()&&!S){var ea=Q(a,P.A.ne);ea?e._gsid=ea:e.njid="1"}}else if(zM(a)){var U=Q(a,P.A.ne);U?e._gsid=U:e.njid="1"}var ta=AM(a);Gg.call(this,{ra:e,Ld:g,Zi:f},ta.url,ta.endpoint,fy(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};ra(HM,Gg);
var IM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},JM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(C(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},KM=function(a,b,c,d,e){var f=0,g=new x.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
FA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},MM=function(a,b,c){var d;return d=IA(HA(new GA(function(e,f){var g=IM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");fm(a,g,void 0,KA(d,f),h)}),function(e,f){var g=IM(e,b),h=f.dedupe_key;h&&km(a,g,h)}),function(e,
f){var g=IM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?LM(a,g,void 0,d,h,KA(d,f)):gm(a,g,void 0,h,void 0,KA(d,f))})},NM=function(a,b,c,d,e){$l(a,2,b);var f=MM(a,d,e);LM(a,b,c,f)},LM=function(a,b,c,d,e,f){Vc()?EA(a,b,c,d,e,void 0,f):KM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},OM=function(a,b,c){var d=Rk(b),e=JM(d),f=MA(d);!C(132)||xc("; wv")||
xc("FBAN")||xc("FBAV")||zc()?NM(a,f,c,e):Gy(f,c,e,function(g){NM(a,f,c,e,g)})};var PM={AW:on.Z.Om,G:on.Z.Yn,DC:on.Z.Wn};function QM(a){var b=aj(a);return""+$r(b.map(function(c){return c.value}).join("!"))}function RM(a){var b=Dp(a);return b&&PM[b.prefix]}function SM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};
var TM=function(a,b,c,d){var e=a+"?"+b;d?em(c,e,d):dm(c,e)},VM=function(a,b,c,d,e){var f=b,g=Yc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;UM&&(d=!Db(h,Jz())&&!Db(h,Iz()));if(d&&!ML)OM(e,h,c);else{var m=b;Vc()?gm(e,a+"?"+m,c,{Ch:!0})||TM(a,m,e,c):TM(a,m,e,c)}},WM=function(a,b){function c(w){q.push(w+"="+encodeURIComponent(""+a.ra[w]))}var d=b.wq,e=b.zq,f=b.yq,g=b.xq,h=b.wp,m=b.Pp,n=b.Op,p=b.mp;if(d||e||f||g){var q=[];a.ra._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Ld.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Ld.uid));c("dma");a.ra.dma_cps!=null&&c("dma_cps");a.ra.gcs!=null&&c("gcs");c("gcd");a.ra.npa!=null&&c("npa");a.ra.frm!=null&&c("frm");d&&(Lz()&&q.push("tag_exp="+Lz()),Mz()&&q.push("ptag_exp="+Mz()),TM("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),Wo({targetId:String(a.ra.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},Ya:b.Ya}));if(e&&(Lz()&&q.push("tag_exp="+Lz()),Mz()&&q.push("ptag_exp="+Mz()),q.push("z="+nb()),!m)){var r=h&&Db(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");fm({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);Wo({targetId:String(a.ra.tid),request:{url:t,parameterEncoding:2,endpoint:47},Ya:b.Ya})}}if(f){var u="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");C(176)&&a.ra._geo&&c("_geo");TM(u,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});Wo({targetId:String(a.ra.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Ya:b.Ya})}if(g){var v="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q=[];q.push("v=2");q.push("t=g");c("_gsid");c("gtm");a.ra._geo&&c("_geo");TM(v,q.join("&"),{destinationId:a.destinationId||
"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});Wo({targetId:String(a.ra.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:16},Ya:b.Ya})}}},UM=!1;var XM=function(){this.N=1;this.P={};this.H=-1;this.C=new zg};k=XM.prototype;k.Mb=function(a,b){var c=this,d=new HM(a,this.P,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=NL(a),g,
h;f&&this.C.R(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=x,p=n.setTimeout,q;fy(a)?YM?(YM=!1,q=ZM):q=$M:q=5E3;this.H=p.call(n,function(){c.flush()},q)}}else{var r=Cg(d,this.N++),t=r.params,u=r.body;g=t;h=u;VM(d.baseUrl,t,u,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=Q(a,P.A.sg),w=Q(a,P.A.Nd),y=Q(a,P.A.ug),A=Q(a,P.A.tg),D=N(a.D,J.m.mb)!==!1,E=tr(a.D),G={wq:v,zq:w,yq:y,xq:A,wp:no(),Ar:D,zr:E,Pp:jo(),Op:Q(a,P.A.he),
Ya:e,D:a.D,mp:lo()};WM(d,G)}tA(a.D.eventId);Xo(function(){if(m){var I=Cg(d),M=I.body;g=I.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},Ya:e,isBatched:!1}})};k.add=function(a){if(C(100)){var b=Q(a,P.A.Nh);if(b){T(a,J.m.wc,Q(a,P.A.Nl));T(a,J.m.Qg,"1");this.Mb(a,b);return}}var c=ey(a);if(C(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=RM(e);if(h){var m=QM(g);f=(sn(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:
void 0;if(d&&d+6E4>yb())c=void 0,T(a,J.m.wc);else{var p=c,q=a.target.destinationId,r=RM(q);if(r){var t=QM(p),u=sn(r)||{},v=u[t];if(v)v.timestamp=yb(),v.sentTo=v.sentTo||{},v.sentTo[q]=yb(),v.pending=!0;else{var w={};u[t]={pending:!0,timestamp:yb(),sentTo:(w[q]=yb(),w)}}SM(u,t);rn(r,u)}}}!c||ML||C(125)&&!C(93)?this.Mb(a):this.Aq(a)};k.flush=function(){if(this.C.events.length){var a=Eg(this.C,this.N++);VM(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,
eventId:this.C.ba,priorityId:this.C.ka});this.C=new zg;this.H>=0&&(x.clearTimeout(this.H),this.H=-1)}};k.Wl=function(a,b){var c=Lv(a,J.m.wc);T(a,J.m.wc);b.then(function(d){var e={},f=(e[P.A.Nh]=d,e[P.A.Nl]=c,e),g=Jw(a.target.destinationId,J.m.Sd,a.D.C);Mw(g,a.D.eventId,{eventMetadata:f})})};k.Aq=function(a){var b=this,c=ey(a);if(zj(c)){var d=oj(c,C(93));d?C(100)?(this.Wl(a,d),this.Mb(a)):d.then(function(g){b.Mb(a,g)},function(){b.Mb(a)}):this.Mb(a)}else{var e=yj(c);if(C(93)){var f=jj(e);f?C(100)?
(this.Wl(a,f),this.Mb(a)):f.then(function(g){b.Mb(a,g)},function(){b.Mb(a,e)}):this.Mb(a,e)}else this.Mb(a,e)}};var ZM=ug('',500),$M=ug('',5E3),YM=!0;
var aN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;aN(a+"."+f,b[f],c)}else c[a]=b;return c},bN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!O(e)}return b},dN=function(a,b){var c=cN.filter(function(e){return!O(e)});if(c.length){var d=bN(c);jp(c,function(){for(var e=bN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){R(b,P.A.Gf,!0);var n=f.map(function(p){return zo[p]}).join(".");n&&cy(b,"gcut",n);a(b)}})}},eN=function(a){fy(a)&&cy(a,"navt",Zc())},fN=function(a){fy(a)&&cy(a,"lpc",Nt())},gN=function(a){if(C(152)&&fy(a)){var b=N(a.D,J.m.Vb),c;b===!0&&(c="1");b===!1&&(c="0");c&&cy(a,"rdp",c)}},hN=function(a){C(147)&&fy(a)&&N(a.D,J.m.Qe,!0)===!1&&T(a,J.m.Qe,0)},iN=function(a,b){if(fy(b)){var c=Q(b,P.A.Hf);(b.eventName==="page_view"||c)&&dN(a,b)}},jN=function(a){if(fy(a)&&a.eventName===J.m.Sd&&
Q(a,P.A.Gf)){var b=Lv(a,J.m.Ph);b&&(cy(a,"gcut",b),cy(a,"syn",1))}},kN=function(a){fy(a)&&R(a,P.A.Ha,!1)},lN=function(a){fy(a)&&(Q(a,P.A.Ha)&&cy(a,"sp",1),Q(a,P.A.eo)&&cy(a,"syn",1),Q(a,P.A.Ke)&&(cy(a,"em_event",1),cy(a,"sp",1)))},mN=function(a){if(fy(a)){var b=ek;b&&cy(a,"tft",Number(b))}},nN=function(a){function b(e){var f=aN(J.m.eb,e);qb(f,function(g,h){T(a,g,h)})}if(fy(a)){var c=ew(a,"ccd_add_1p_data",!1)?1:0;cy(a,"ude",c);var d=N(a.D,J.m.eb);d!==void 0?(b(d),T(a,J.m.wc,"c")):b(Q(a,P.A.ib));R(a,
P.A.ib)}},oN=function(a){if(fy(a)){var b=Iv();b&&cy(a,"us_privacy",b);var c=mr();c&&cy(a,"gdpr",c);var d=lr();d&&cy(a,"gdpr_consent",d);var e=vv.gppString;e&&cy(a,"gpp",e);var f=vv.C;f&&cy(a,"gpp_sid",f)}},pN=function(a){fy(a)&&Zm()&&N(a.D,J.m.za)&&cy(a,"adr",1)},qN=function(a){if(fy(a)){var b=Hz();b&&cy(a,"gcsub",b)}},rN=function(a){if(fy(a)){N(a.D,J.m.Ob,void 0,4)===!1&&cy(a,"ngs",1);jo()&&cy(a,"ga_rd",1);aK()||cy(a,"ngst",1);var b=no();b&&cy(a,"etld",b)}},sN=function(a){},tN=function(a){fy(a)&&Zm()&&cy(a,"rnd",iv())},cN=[J.m.U,J.m.V];
var uN=function(a,b){var c;a:{var d=jM(a);if(d){if(hM(d,a)){c=d;break a}L(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:ZL(a,b),ub:e}},vN=function(a,b,c,d,e){var f=Eo(N(a.D,J.m.Qb));if(N(a.D,J.m.Lc)&&N(a.D,J.m.Kc))f?XL(a,f,1):(L(127),a.isAborted=!0);else{var g=f?1:8;R(a,P.A.bh,!1);f||(f=$L(a),g=3);f||(f=b,g=5);if(!f){var h=O(J.m.ia),m=TL();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=zs(),g=7,R(a,P.A.If,!0),R(a,P.A.bh,!0));XL(a,f,g)}var n=Q(a,P.A.fb),p=Math.floor(n/1E3),q=void 0;Q(a,P.A.bh)||
(q=iM(a)||c);var r=tb(N(a.D,J.m.qf,30));r=Math.min(475,r);r=Math.max(5,r);var t=tb(N(a.D,J.m.hi,1E4)),u=dM(q);R(a,P.A.If,!1);R(a,P.A.me,!1);R(a,P.A.Jf,0);u&&u.j&&R(a,P.A.Jf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){R(a,P.A.If,!0);v=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)R(a,P.A.me,!0),d.Ep(a);else if(d.up()>t||a.eventName===J.m.Yc)u.g=!0;Q(a,P.A.he)?N(a.D,J.m.Qa)?u.l=!0:(u.l&&!C(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var y=u.h;if(Q(a,P.A.he)||fy(a)){var A=N(a.D,J.m.Hg),D=A?1:8;A||(A=y,D=4);A||(A=ys(),D=7);var E=A.toString(),G=D,I=Q(a,P.A.Zj);if(I===void 0||G<=I)T(a,J.m.Hg,E),R(a,P.A.Zj,G)}e?(a.copyToHitData(J.m.uc,u.s),a.copyToHitData(J.m.Tg,u.o),a.copyToHitData(J.m.Sg,u.g?1:0)):(T(a,J.m.uc,u.s),T(a,J.m.Tg,u.o),T(a,J.m.Sg,u.g?1:0));R(a,J.m.Zh,u.l?1:0);lk()&&R(a,P.A.ne,u.d||Nb())};var wN=window,xN=document,yN=function(a){var b=wN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||xN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&wN["ga-disable-"+a]===!0)return!0;try{var c=wN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(xN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return xN.getElementById("__gaOptOutExtension")?!0:!1};
var AN=function(a){return!a||zN.test(a)||ro.hasOwnProperty(a)},BN=function(a){var b=J.m.Nc,c;c||(c=function(){});Lv(a,b)!==void 0&&T(a,b,c(Lv(a,b)))},CN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Kk(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},DN=function(a){N(a.D,J.m.Eb)&&(O(J.m.ia)||N(a.D,J.m.Qb)||T(a,J.m.nl,!0));var b;var c;c=c===void 0?3:c;var d=x.location.href;if(d){var e=Rk(d).search.replace("?",""),f=Ik(e,"_gl",!1,!0)||"";b=f?Us(f,c)!==void 0:!1}else b=!1;b&&fy(a)&&
cy(a,"glv",1);if(a.eventName!==J.m.qa)return{};N(a.D,J.m.Eb)&&Mu(["aw","dc"]);Ou(["aw","dc"]);var g=qM(a),h=oM(a);return Object.keys(g).length?g:h},EN=function(a){var b=Ib(a.D.getMergedValues(J.m.oa,1,Co(Dq.C[J.m.oa])),".");b&&T(a,J.m.Ub,b);var c=Ib(a.D.getMergedValues(J.m.oa,2),".");c&&T(a,J.m.Tb,c)},FN={jp:""},GN={},HN=(GN[J.m.Re]=1,GN[J.m.Se]=1,GN[J.m.Te]=1,GN[J.m.Ue]=1,GN[J.m.We]=1,GN[J.m.Xe]=1,GN),zN=/^(_|ga_|google_|gtag\.|firebase_).*$/,IN=[dw,
aw,Nv,fw,EN,Dw],JN=function(a){this.N=a;this.C=this.ub=this.clientId=void 0;this.Ba=this.R=!1;this.qb=0;this.P=!1;this.Sa=!0;this.ba={ij:!1};this.ka=new XM;this.H=new PL};k=JN.prototype;k.iq=function(a,b,c){var d=this,e=Dp(this.N);if(e)if(c.eventMetadata[P.A.ud]&&a.charAt(0)==="_")c.onFailure();else{a!==J.m.qa&&a!==J.m.Cb&&AN(a)&&L(58);KN(c.C);var f=new EH(e,a,c);R(f,P.A.fb,b);var g=[J.m.ia],h=fy(f);R(f,P.A.eh,h);if(ew(f,J.m.be,N(f.D,J.m.be))||h)g.push(J.m.U),g.push(J.m.V);Qy(function(){lp(function(){d.jq(f)},
g)});C(88)&&a===J.m.qa&&ew(f,"ga4_ads_linked",!1)&&ln(nn(Nm.X.Da),function(){d.gq(a,c,f)})}else c.onFailure()};k.gq=function(a,b,c){function d(){for(var h=l(IN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}Q(f,P.A.Ha)||f.isAborted||Tz(f)}var e=Dp(this.N),f=new EH(e,a,b);R(f,P.A.fa,K.J.Ga);R(f,P.A.Ha,!0);R(f,P.A.eh,Q(c,P.A.eh));var g=[J.m.U,J.m.V];lp(function(){d();O(g)||kp(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;R(f,P.A.ja,!0);R(f,P.A.He,m);R(f,P.A.Ie,
n);d()},g)},g)};k.jq=function(a){var b=this;try{dw(a);if(a.isAborted){SL();return}C(165)||(this.C=a);LN(a);MN(a);NN(a);ON(a);C(138)&&(a.isAborted=!0);Vv(a);var c={};vM(a,c);if(a.isAborted){a.D.onFailure();SL();return}C(165)&&(this.C=a);var d=c.Ho;c.Wo===0&&QL(25);d===0&&QL(26);fw(a);R(a,P.A.Pf,Nm.X.Fc);PN(a);QN(a);this.io(a);this.H.Iq(a);RN(a);SN(a);TN(a);UN(a);this.ym(DN(a));var e=a.eventName===J.m.qa;e&&(this.P=!0);VN(a);e&&!a.isAborted&&this.qb++>0&&QL(17);WN(a);XN(a);vN(a,this.clientId,this.ub,
this.H,!this.Ba);YN(a);ZN(a);$N(a);aO(a,this.ba);this.Sa=bO(a,this.Sa);cO(a);dO(a);eO(a);fO(a);gO(a);rM(a);xM(a);tN(a);sN(a);rN(a);qN(a);pN(a);oN(a);mN(a);lN(a);jN(a);hN(a);gN(a);fN(a);eN(a);sM(a);tM(a);hO(a);iO(a);jO(a);Xv(a);Wv(a);cw(a);kO(a);lO(a);Dw(a);mO(a);nN(a);kN(a);nO(a);!this.P&&Q(a,P.A.Ke)&&QL(18);RL(a);if(Q(a,P.A.Ha)||a.isAborted){a.D.onFailure();SL();return}this.ym(uN(a,this.clientId));this.Ba=!0;this.Fq(a);oO(a);iN(function(f){b.Ol(f)},a);this.H.Bj();pO(a);bw(a);if(a.isAborted){a.D.onFailure();
SL();return}this.Ol(a);a.D.onSuccess()}catch(f){a.D.onFailure()}SL()};k.Ol=function(a){this.ka.add(a)};k.ym=function(a){var b=a.clientId,c=a.ub;b&&c&&(this.clientId=b,this.ub=c)};k.flush=function(){this.ka.flush()};k.Fq=function(a){var b=this;if(!this.R){var c=O(J.m.V),d=O(J.m.ia),e=[J.m.V,J.m.ia];C(213)&&e.push(J.m.U);jp(e,function(){var f=O(J.m.V),g=O(J.m.ia),h=!1,m={},n={};if(d!==g&&b.C&&b.ub&&b.clientId){var p=b.clientId,q;var r=dM(b.ub);q=r?r.h:void 0;if(g){var t=$L(b.C);if(t){b.clientId=t;var u=
iM(b.C);u&&(b.ub=fM(u,b.ub,b.C))}else YL(b.clientId,b.C),VL(b.clientId,!0);hM(b.ub,b.C);h=!0;m[J.m.di]=p;C(69)&&q&&(m[J.m.Kn]=q)}else b.ub=void 0,b.clientId=void 0,x.gaGlobal={}}f&&!c&&(h=!0,n[P.A.Gf]=!0,m[J.m.Ph]=zo[J.m.V]);if(h){var v=Jw(b.N,J.m.Sd,m);Mw(v,a.D.eventId,{eventMetadata:n})}d=g;c=f;b.ba.ij=!0});this.R=!0}};k.io=function(a){a.eventName!==J.m.Cb&&this.H.ho(a)};var NN=function(a){var b=z.location.protocol;b!=="http:"&&b!=="https:"&&(L(29),a.isAborted=!0)},ON=function(a){sc&&sc.loadPurpose===
"preview"&&(L(30),a.isAborted=!0)},PN=function(a){var b={prefix:String(N(a.D,J.m.cb,"")),path:String(N(a.D,J.m.Sb,"/")),flags:String(N(a.D,J.m.yb,"")),domain:String(N(a.D,J.m.nb,"auto")),Bc:Number(N(a.D,J.m.ob,63072E3))};R(a,P.A.ya,b)},RN=function(a){Q(a,P.A.vd)?R(a,P.A.he,!1):ew(a,"ccd_add_ec_stitching",!1)&&R(a,P.A.he,!0)},SN=function(a){if(ew(a,"ccd_add_1p_data",!1)){var b=a.D.H[J.m.Ug];if(Dk(b)){var c=N(a.D,J.m.eb);if(c===null)R(a,P.A.ue,null);else if(b.enable_code&&kd(c)&&R(a,P.A.ue,c),kd(b.selectors)&&
!Q(a,P.A.mh)){var d={};R(a,P.A.mh,Bk(b.selectors,d));C(60)&&a.mergeHitDataForKey(J.m.rc,{ec_data_layer:yk(d)})}}}},TN=function(a){if(C(91)&&!C(88)&&ew(a,"ga4_ads_linked",!1)&&a.eventName===J.m.qa){var b=N(a.D,J.m.Oa)!==!1;if(b){var c=Jv(a);c.Bc&&(c.Bc=Math.min(c.Bc,7776E3));Kv({we:b,Ce:Co(N(a.D,J.m.Pa)),Ge:!!N(a.D,J.m.Eb),Rc:c})}}},UN=function(a){if(C(97)){var b=tr(a.D);N(a.D,J.m.Vb)===!0&&(b=!1);R(a,P.A.Gh,b)}},hO=function(a){if(!My(x))L(87);else if(Ry!==void 0){L(85);var b=Ky(x);b?N(a.D,J.m.Rg)&&
!fy(a)||Py(b,a):L(86)}},VN=function(a){a.eventName===J.m.qa&&(N(a.D,J.m.pb,!0)?(a.D.C[J.m.oa]&&(a.D.N[J.m.oa]=a.D.C[J.m.oa],a.D.C[J.m.oa]=void 0,T(a,J.m.oa)),a.eventName=J.m.Yc):a.isAborted=!0)},QN=function(a){function b(c,d){po[c]||d===void 0||T(a,c,d)}qb(a.D.N,b);qb(a.D.C,b)},YN=function(a){var b=Vp(a.D),c=function(d,e){HN[d]&&T(a,d,e)};kd(b[J.m.Ve])?qb(b[J.m.Ve],function(d,e){c((J.m.Ve+"_"+d).toLowerCase(),e)}):qb(b,c)},WN=EN,oO=function(a){if(C(132)&&fy(a)&&!(xc("; wv")||xc("FBAN")||xc("FBAV")||
zc())&&O(J.m.ia)){R(a,P.A.pl,!0);fy(a)&&cy(a,"sw_exp",1);a:{if(!C(132)||!fy(a))break a;var b=Wk(Zk(a.D),"/_/service_worker");Dy(b);}}},kO=function(a){if(a.eventName===J.m.Cb){var b=N(a.D,J.m.qc),c=N(a.D,J.m.Ic),d;d=Lv(a,b);c(d||N(a.D,b));a.isAborted=!0}},ZN=function(a){if(!N(a.D,J.m.Kc)||!N(a.D,J.m.Lc)){var b=a.copyToHitData,c=J.m.Aa,d="",e=z.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+
f);var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Kb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,CN);var p=a.copyToHitData,q=J.m.Wa,r;a:{var t=hs("_opt_expid",void 0,void 0,J.m.ia)[0];if(t){var u=Kk(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var w=sp.ga4_referrer_override;if(w!==void 0)r=w;else{var y=rk("gtm.gtagReferrer."+
a.target.destinationId),A=z.referrer;r=y?""+y:A}}p.call(a,q,r||void 0,CN);a.copyToHitData(J.m.Db,z.title);a.copyToHitData(J.m.zb,(sc.language||"").toLowerCase());var D=Uw();a.copyToHitData(J.m.Nc,D.width+"x"+D.height);C(145)&&a.copyToHitData(J.m.nf,void 0,CN);C(87)&&lv()&&a.copyToHitData(J.m.kd,"1")}},aO=function(a,b){C(213)&&lk()&&b.ij&&(R(a,P.A.ml,!0),R(a,P.A.ne,Nb()),b.ij=!1)},bO=function(a,b){var c=Q(a,P.A.Jf);c=c||0;var d=O(J.m.U),e=!b&&d,f;f=C(213)?!!Q(a,P.A.ml):e||!!Q(a,P.A.Gf)||!!Lv(a,J.m.di);
var g=c===0||f;R(a,P.A.Ei,g);g&&R(a,P.A.Jf,60);return d},cO=function(a){R(a,P.A.sg,!1);R(a,P.A.Nd,!1);if(!fy(a)&&!Q(a,P.A.vd)&&N(a.D,J.m.Ob)!==!1&&aK()&&O([J.m.U,J.m.ia])){var b=gy(a);(Q(a,P.A.me)||N(a.D,J.m.di))&&R(a,P.A.sg,!!b);b&&Q(a,P.A.Ei)&&Q(a,P.A.Ci)&&R(a,P.A.Nd,!0)}},dO=function(a){R(a,P.A.tg,!1);R(a,P.A.ug,!1);if(!(C(187)&&lo()||!lk()||fy(a)||Q(a,P.A.vd))&&Q(a,P.A.Ei)){var b=Q(a,P.A.Nd);Q(a,P.A.ne)&&(b?R(a,P.A.ug,!0):C(176)&&R(a,P.A.tg,!0))}},gO=function(a){a.copyToHitData(J.m.ki);for(var b=
N(a.D,J.m.ei)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(J.m.ki,d.traffic_type);QL(3);break}}},pO=function(a){a.copyToHitData(J.m.Ck);N(a.D,J.m.Rg)&&(T(a,J.m.Rg,!0),fy(a)||BN(a))},lO=function(a){a.copyToHitData(J.m.Qa);a.copyToHitData(J.m.Wb)},$N=function(a){ew(a,"google_ng")&&!jo()?a.copyToHitData(J.m.ae,1):Yv(a)},nO=function(a){var b=N(a.D,J.m.Lc);b&&QL(12);Q(a,P.A.Ke)&&QL(14);var c=Bm(qm());(b||Km(c)||c&&c.parent&&c.context&&c.context.source===5)&&QL(19)},LN=function(a){if(yN(a.target.destinationId))L(28),
a.isAborted=!0;else if(C(144)){var b=Am();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(yN(b.destinations[c])){L(125);a.isAborted=!0;break}}},iO=function(a){Jl("attribution-reporting")&&T(a,J.m.dd,"1")},MN=function(a){if(FN.jp.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=dy(a);b&&b.blacklisted&&(a.isAborted=!0)}},eO=function(a){var b=function(c){return!!c&&c.conversion};R(a,P.A.Hf,b(dy(a)));Q(a,P.A.If)&&R(a,P.A.kl,b(dy(a,"first_visit")));
Q(a,P.A.me)&&R(a,P.A.ol,b(dy(a,"session_start")))},fO=function(a){to.hasOwnProperty(a.eventName)&&(R(a,P.A.jl,!0),a.copyToHitData(J.m.sa),a.copyToHitData(J.m.Va))},mO=function(a){if(!fy(a)&&Q(a,P.A.Hf)&&O(J.m.U)&&ew(a,"ga4_ads_linked",!1)){var b=Jv(a),c=cu(b.prefix),d=Ev(c);T(a,J.m.Td,d.sh);T(a,J.m.Vd,d.uh);T(a,J.m.Ud,d.th)}},jO=function(a){if(C(122)){var b=lo();b&&R(a,P.A.Xn,b)}},XN=function(a){R(a,P.A.Ci,gy(a)&&N(a.D,J.m.Ob)!==!1&&aK()&&!jo())};
function KN(a){qb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Wb]||{};qb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var rO=function(a){if(!qO(a)){var b=!1,c=function(){!b&&qO(a)&&(b=!0,Kc(z,"visibilitychange",c),C(5)&&Kc(z,"prerenderingchange",c),L(55))};Jc(z,"visibilitychange",c);C(5)&&Jc(z,"prerenderingchange",c);L(54)}},qO=function(a){if(C(5)&&"prerendering"in z?z.prerendering:z.visibilityState==="prerender")return!1;a();return!0};function sO(a,b){rO(function(){var c=Dp(a);if(c){var d=tO(c,b);Cq(a,d,Nm.X.Fc)}});}function tO(a,b){var c=function(){};var d=new JN(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[P.A.vd]=!0);d.iq(g,h,m)};uO(a,d,b);return c}
function uO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[P.A.Lj]=!0,e),deferrable:!0};d.nq(function(){ML=!0;Dq.flush();d.wh()>=1E3&&sc.sendBeacon!==void 0&&Eq(J.m.Sd,{},a.id,f);b.flush();d.zm(function(){ML=!1;d.zm()})});};var vO=tO;function xO(a,b,c){var d=this;}xO.M="internal.gtagConfig";
function zO(a,b){}
zO.publicName="gtagSet";function AO(){var a={};return a};function BO(a){}BO.M="internal.initializeServiceWorker";function CO(a,b){}CO.publicName="injectHiddenIframe";var DO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function EO(a,b,c,d,e){}EO.M="internal.injectHtml";var IO={};
function KO(a,b,c,d){}var LO={dl:1,id:1},MO={};
function NO(a,b,c,d){}C(160)?NO.publicName="injectScript":KO.publicName="injectScript";NO.M="internal.injectScript";function OO(){return mo()}OO.M="internal.isAutoPiiEligible";function PO(a){var b=!0;return b}PO.publicName="isConsentGranted";function QO(a){var b=!1;return b}QO.M="internal.isDebugMode";function RO(){return ko()}RO.M="internal.isDmaRegion";function SO(a){var b=!1;return b}SO.M="internal.isEntityInfrastructure";function TO(a){var b=!1;if(!qh(a))throw F(this.getName(),["number"],[a]);b=C(a);return b}TO.M="internal.isFeatureEnabled";function UO(){var a=!1;return a}UO.M="internal.isFpfe";function VO(){var a=!1;return a}VO.M="internal.isGcpConversion";function WO(){var a=!1;return a}WO.M="internal.isLandingPage";function XO(){var a=!1;return a}XO.M="internal.isOgt";function YO(){var a;return a}YO.M="internal.isSafariPcmEligibleBrowser";function ZO(){var a=Nh(function(b){mF(this).log("error",b)});a.publicName="JSON";return a};function $O(a){var b=void 0;return Bd(b)}$O.M="internal.legacyParseUrl";function aP(){return!1}
var bP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function cP(){}cP.publicName="logToConsole";function dP(a,b){}dP.M="internal.mergeRemoteConfig";function eP(a,b,c){c=c===void 0?!0:c;var d=[];return Bd(d)}eP.M="internal.parseCookieValuesFromString";function fP(a){var b=void 0;return b}fP.publicName="parseUrl";function gP(a){}gP.M="internal.processAsNewEvent";function hP(a,b,c){var d;return d}hP.M="internal.pushToDataLayer";function iP(a){var b=ya.apply(1,arguments),c=!1;if(!lh(a))throw F(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(Ad(f.value,this.K,1));try{H.apply(null,d),c=!0}catch(g){return!1}return c}iP.publicName="queryPermission";function jP(a){var b=this;}jP.M="internal.queueAdsTransmission";function kP(a,b){var c=void 0;return c}kP.publicName="readAnalyticsStorage";function lP(){var a="";return a}lP.publicName="readCharacterSet";function mP(){return Tj}mP.M="internal.readDataLayerName";function nP(){var a="";return a}nP.publicName="readTitle";function oP(a,b){var c=this;if(!lh(a)||!hh(b))throw F(this.getName(),["string","function"],arguments);Ew(a,function(d){b.invoke(c.K,Bd(d,c.K,1))});}oP.M="internal.registerCcdCallback";function pP(a,b){return!0}pP.M="internal.registerDestination";var qP=["config","event","get","set"];function rP(a,b,c){}rP.M="internal.registerGtagCommandListener";function sP(a,b){var c=!1;return c}sP.M="internal.removeDataLayerEventListener";function tP(a,b){}
tP.M="internal.removeFormData";function uP(){}uP.publicName="resetDataLayer";function vP(a,b,c){var d=void 0;return d}vP.M="internal.scrubUrlParams";function wP(a){}wP.M="internal.sendAdsHit";function xP(a,b,c,d){}xP.M="internal.sendGtagEvent";function yP(a,b,c){}yP.publicName="sendPixel";function zP(a,b){}zP.M="internal.setAnchorHref";function AP(a){}AP.M="internal.setContainerConsentDefaults";function BP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}BP.publicName="setCookie";function CP(a){}CP.M="internal.setCorePlatformServices";function DP(a,b){}DP.M="internal.setDataLayerValue";function EP(a){}EP.publicName="setDefaultConsentState";function FP(a,b){if(!lh(a)||!lh(b))throw F(this.getName(),["string","string"],arguments);H(this,"access_consent",a,"write");H(this,"access_consent",b,"read");ko()&&(Vm.delegatedConsentTypes[a]=b);}FP.M="internal.setDelegatedConsentType";function GP(a,b){}GP.M="internal.setFormAction";function HP(a,b,c){c=c===void 0?!1:c;}HP.M="internal.setInCrossContainerData";function IP(a,b,c){return!1}IP.publicName="setInWindow";function JP(a,b,c){}JP.M="internal.setProductSettingsParameter";function KP(a,b,c){if(!lh(a)||!lh(b)||arguments.length!==3)throw F(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!kd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=Ad(c,this.K,1);}KP.M="internal.setRemoteConfigParameter";function LP(a,b){}LP.M="internal.setTransmissionMode";function MP(a,b,c,d){var e=this;}MP.publicName="sha256";function NP(a,b,c){if(!lh(a)||!lh(b)||!eh(c))throw F(this.getName(),["string","string","Object"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, path points to an undefined object: "+d[f]);if(!kd(e[d[f]]))throw Error("sortRemoteConfigParameters failed, path contains a non-object type: "+d[f]);e=e[d[f]]}if(e[d[f]]===void 0)throw Error("sortRemoteConfigParameters failed, destination is undefined "+
d[f]);if(!Array.isArray(e[d[f]]))throw Error("sortRemoteConfigParameters failed, destination is not an array: "+d[f]);var g=Ad(c)||{},h=g.sortKey;if(!h)throw Error("sortRemoteConfigParameters failed, option.sortKey is required");var m=g.ascending!==!1;e[d[f]].sort(function(n,p){if(n[h]===void 0||p[h]===void 0)throw Error("sortRemoteConfigParameters failed, object does not have required property: "+h);return m?n[h]-p[h]:p[h]-n[h]});}
NP.M="internal.sortRemoteConfigParameters";function OP(a){}OP.M="internal.storeAdsBraidLabels";function PP(a,b){var c=void 0;return c}PP.M="internal.subscribeToCrossContainerData";var QP={},RP={};QP.getItem=function(a){var b=null;return b};QP.setItem=function(a,b){};
QP.removeItem=function(a){};QP.clear=function(){};QP.publicName="templateStorage";function SP(a,b){var c=!1;return c}SP.M="internal.testRegex";function TP(a){var b;return b};function UP(a,b){var c;return c}UP.M="internal.unsubscribeFromCrossContainerData";function VP(a){}VP.publicName="updateConsentState";function WP(a){var b=!1;return b}WP.M="internal.userDataNeedsEncryption";var XP;function YP(a,b,c){XP=XP||new Yh;XP.add(a,b,c)}function ZP(a,b){var c=XP=XP||new Yh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=ib(b)?th(a,b):uh(a,b)}
function $P(){return function(a){var b;var c=XP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Jb();if(g){Ah(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function aQ(){var a=function(c){return void ZP(c.M,c)},b=function(c){return void YP(c.publicName,c)};b(gF);b(nF);b(BG);b(DG);b(EG);b(LG);b(NG);b(IH);b(ZO());b(KH);b(eL);b(fL);b(BL);b(CL);b(DL);b(JL);b(zO);b(CO);b(PO);b(cP);b(fP);b(iP);b(lP);b(nP);b(yP);b(BP);b(EP);b(IP);b(MP);b(QP);b(VP);YP("Math",yh());YP("Object",Wh);YP("TestHelper",$h());YP("assertApi",vh);YP("assertThat",wh);YP("decodeUri",Bh);YP("decodeUriComponent",Ch);YP("encodeUri",Dh);YP("encodeUriComponent",Eh);YP("fail",Jh);YP("generateRandom",
Kh);YP("getTimestamp",Lh);YP("getTimestampMillis",Lh);YP("getType",Mh);YP("makeInteger",Oh);YP("makeNumber",Ph);YP("makeString",Qh);YP("makeTableMap",Rh);YP("mock",Uh);YP("mockObject",Vh);YP("fromBase64",YK,!("atob"in x));YP("localStorage",bP,!aP());YP("toBase64",TP,!("btoa"in x));a(fF);a(jF);a(DF);a(PF);a(WF);a(aG);a(qG);a(zG);a(CG);a(FG);a(GG);a(HG);a(IG);a(JG);a(KG);a(MG);a(OG);a(HH);a(JH);a(LH);a(MH);a(NH);a(OH);a(PH);a(QH);a(VH);a(cI);a(dI);a(oI);a(tI);a(yI);a(HI);a(MI);a(ZI);a(aJ);a(oJ);a(pJ);
a(rJ);a(WK);a(XK);a(ZK);a($K);a(aL);a(bL);a(cL);a(hL);a(iL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(EL);a(FL);a(GL);a(HL);a(IL);a(LL);a(xO);a(BO);a(EO);a(NO);a(OO);a(QO);a(RO);a(SO);a(TO);a(UO);a(VO);a(WO);a(XO);a(YO);a($O);a(oG);a(dP);a(eP);a(gP);a(hP);a(jP);a(mP);a(oP);a(pP);a(rP);a(sP);a(tP);a(vP);a(wP);a(xP);a(zP);a(AP);a(CP);a(DP);a(FP);a(GP);a(HP);a(JP);a(KP);a(LP);a(NP);a(OP);a(PP);a(SP);a(UP);a(WP);ZP("internal.IframingStateSchema",
AO());
C(104)&&a(gL);C(160)?b(NO):b(KO);C(177)&&b(kP);return $P()};var dF;
function bQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;dF=new Ve;cQ();Cf=cF();var e=dF,f=aQ(),g=new td("require",f);g.Ua();e.C.C.set("require",g);Pa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Yf(n,d[m]);try{dF.execute(n),C(120)&&fl&&n[0]===50&&h.push(n[1])}catch(r){}}C(120)&&(Qf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");hk[q]=["sandboxedScripts"]}dQ(b)}function cQ(){dF.Vc(function(a,b,c){sp.SANDBOXED_JS_SEMAPHORE=sp.SANDBOXED_JS_SEMAPHORE||0;sp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{sp.SANDBOXED_JS_SEMAPHORE--}})}function dQ(a){a&&qb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");hk[e]=hk[e]||[];hk[e].push(b)}})};function eQ(a){Mw(Gw("developer_id."+a,!0),0,{})};var fQ=Array.isArray;function gQ(a,b){return ld(a,b||null)}function V(a){return window.encodeURIComponent(a)}function hQ(a,b,c){Ic(a,b,c)}
function iQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Lk(Rk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function jQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function kQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=jQ(b,"parameter","parameterValue");e&&(c=gQ(e,c))}return c}function lQ(a,b,c){if(Fr()){b&&Lc(b)}else return Ec(a,b,c,void 0)}function mQ(){return x.location.href}function nQ(a,b){return rk(a,b||2)}function oQ(a,b){x[a]=b}function pQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function qQ(a,b){if(Fr()){b&&Lc(b)}else Gc(a,b)}
var rQ={};var Z={securityGroups:{}};
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=nQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.F="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v["5"]=!0;
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!jb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Jg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();






Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data["5"]=!1;
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();




Z.securityGroups.access_consent=["google"],function(){function a(b,c,d){var e={consentType:c,read:!1,write:!1};switch(d){case "read":e.read=!0;break;case "write":e.write=!0;break;default:throw Error("Invalid "+b+" request "+d);}return e}(function(b){Z.__access_consent=b;Z.__access_consent.F="access_consent";Z.__access_consent.isVendorTemplate=!0;Z.__access_consent.priorityOverride=0;Z.__access_consent.isInfrastructure=!1;Z.__access_consent["5"]=!1})(function(b){for(var c=b.vtp_consentTypes||[],d=
b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],m=h.consentType;h.read&&e.push(m);h.write&&f.push(m)}return{assert:function(n,p,q){if(!jb(p))throw d(n,{},"Consent type must be a string.");if(q==="read"){if(e.indexOf(p)>-1)return}else if(q==="write"){if(f.indexOf(p)>-1)return}else throw d(n,{},"Access type must be either 'read', or 'write', was "+q);throw d(n,{},"Prohibited "+q+" on consent type: "+p+".");},T:a}})}();



Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.F="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct["5"]=!1})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[J.m.qf]=d);c[J.m.Jg]=b.vtp_eventSettings;c[J.m.lk]=b.vtp_dynamicEventSettings;c[J.m.be]=b.vtp_googleSignals===1;c[J.m.Dk]=b.vtp_foreignTld;c[J.m.Bk]=b.vtp_restrictDomain===
1;c[J.m.ei]=b.vtp_internalTrafficResults;var e=J.m.Pa,f=b.vtp_linker;f&&f[J.m.ma]&&(f[J.m.ma]=a(f[J.m.ma]));c[e]=f;var g=J.m.fi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Iq(b.vtp_trackingId,c);sO(b.vtp_trackingId,b.vtp_gtmEventId);Lc(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Jw(String(b.streamId),d,c);Mw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;



var vp={dataLayer:sk,callback:function(a){gk.hasOwnProperty(a)&&ib(gk[a])&&gk[a]();delete gk[a]},bootstrap:0};
function sQ(){up();Em();LB();Bb(hk,Z.securityGroups);var a=Bm(qm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Uo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Pf={Po:dg}}var tQ=!1;
function eo(){try{if(tQ||!Lm()){Pj();Mj.P=Pi(18,"");
Mj.qb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Mj.Sa="ad_storage|analytics_storage|ad_user_data";Mj.Ba="56n0";Mj.Ba="5770";Mj.ka=!0;if(C(109)){}Ha[7]=!0;var a=tp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});ap(a);rp();UE();gr();xp();if(Fm()){lG();BC().removeExternalRestrictions(ym());}else{Sy();Nf();Jf=Z;Kf=EE;fg=new mg;bQ();sQ();Gr();bo||(ao=go());
op();QD();cD();wD=!1;z.readyState==="complete"?yD():Jc(x,"load",yD);XC();fl&&(kq(yq),x.setInterval(xq,864E5),kq(VE),kq(oC),kq($z),kq(Bq),kq($E),kq(zC),C(120)&&(kq(tC),kq(uC),kq(vC)),WE={},kq(XE),Si());gl&&(Pn(),Rp(),SD(),WD(),UD(),Fn("bt",String(Mj.C?2:Mj.N?1:0)),Fn("ct",String(Mj.C?0:Mj.N?1:Fr()?2:3)),TD());uE();Zn(1);mG();$D();fk=yb();vp.bootstrap=fk;Mj.ka&&PD();C(109)&&vA();C(134)&&(typeof x.name==="string"&&Db(x.name,"web-pixel-sandbox-CUSTOM")&&ad()?eQ("dMDg0Yz"):x.Shopify&&(eQ("dN2ZkMj"),ad()&&eQ("dNTU0Yz")))}}}catch(b){Zn(4),uq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Ho(n)&&(m=h.Yk)}function c(){m&&vc?g(m):a()}if(!x[Pi(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Rk(z.referrer);d=Nk(e,"host")===Pi(38,"cct.google")}if(!d){var f=hs(Pi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Pi(37,"__TAGGY_INSTALLED")]=!0,Ec(Pi(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";Zj&&(v="OGT",w="GTAG");
var y=Pi(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Ec("https://"+Qj.vg+"/debug/bootstrap?id="+jg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Kr()));var D={messageType:"CONTAINER_STARTING",data:{scriptSource:vc,containerProduct:v,debug:!1,id:jg.ctid,targetRef:{ctid:jg.ctid,isDestination:wm()},aliases:zm(),destinations:xm()}};D.data.resume=function(){a()};Qj.Rm&&(D.data.initialPublish=!0);A.push(D)},h={bo:1,bl:2,xl:3,Xj:4,Yk:5};h[h.bo]="GTM_DEBUG_LEGACY_PARAM";h[h.bl]="GTM_DEBUG_PARAM";h[h.xl]="REFERRER";
h[h.Xj]="COOKIE";h[h.Yk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Lk(x.location,"query",!1,void 0,"gtm_debug");Ho(p)&&(m=h.bl);if(!m&&z.referrer){var q=Rk(z.referrer);Nk(q,"host")===Pi(24,"tagassistant.google.com")&&(m=h.xl)}if(!m){var r=hs("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Xj)}m||b();if(!m&&Go(n)){var t=!1;Jc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){C(83)&&tQ&&!go()["0"]?co():eo()});

})()

