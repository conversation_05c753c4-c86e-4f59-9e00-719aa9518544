(function(){function I(N,M,l){function h(a,d){if(!M[a]){if(!N[a]){var f=typeof require=="function"&&require;if(!d&&f)return f(a,!0);if(w)return w(a,!0);var v=new Error("Cannot find module '"+a+"'");throw v.code="MODULE_NOT_FOUND",v}var g=M[a]={exports:{}};N[a][0].call(g.exports,function(m){var u=N[a][1][m];return h(u||m)},g,g.exports,I,N,M,l)}return M[a].exports}for(var w=typeof require=="function"&&require,c=0;c<l.length;c++)h(l[c]);return h}return I})()({1:[function(I,N,M){"use strict";var l=I("underscore"),h=I("list.js"),w=I("aria-accordion").Accordion,c=13,a=27;function d(b,C){var x=Element.prototype,y=x.matches||x.webkitMatchesSelector||x.mozMatchesSelector||x.msMatchesSelector||function(E){return[].indexOf.call(document.querySelectorAll(E),this)!==-1};return y.call(b,C)}function f(b,C){return[].forEach.call(b,C)}var v=l.template('<li class="{{ glossaryItemClass }}"><button class="data-glossary-term {{ termClass }}">{{ term }}</button><div class="{{ definitionClass }}">{{ definition }}</div></li>',{interpolate:/\{\{(.+?)\}\}/g}),g={glossaryID:"#glossary",toggle:".js-glossary-toggle",close:".js-glossary-close",listClass:".js-glossary-list",searchClass:".js-glossary-search"},m={definitionClass:"glossary__definition",glossaryItemClass:"glossary__item",highlightedTerm:"term--highlight",termClass:"glossary__term"};function u(b){var C=o(b);f(C,function(x){x.setAttribute("tabIndex","-1")})}function A(b){var C=o(b);f(C,function(x){x.setAttribute("tabIndex","0")})}function o(b){return b.querySelectorAll("a, button, input, [tabindex]")}function S(b,C,x){this.terms=b,this.selectors=l.extend({},g,C),this.classes=l.extend({},m,x),this.body=document.querySelector(this.selectors.glossaryID),this.toggleBtn=document.querySelector(this.selectors.toggle),this.closeBtn=document.querySelector(this.selectors.close),this.search=this.body.querySelector(this.selectors.searchClass),this.listElm=this.body.querySelector(this.selectors.listClass),this.selectedTerm=this.toggleBtn,this.listElm&&(this.listElm.innerHTML=""),this.isOpen=!1,this.populate(),this.initList(),this.linkTerms(),u(this.body),this.accordion=new w(this.listElm,null,{contentPrefix:"glossary"}),this.listeners=[],this.addEventListener(this.toggleBtn,"click",this.toggle.bind(this)),this.addEventListener(this.closeBtn,"click",this.hide.bind(this)),this.addEventListener(this.search,"input",this.handleInput.bind(this)),this.addEventListener(document.body,"keyup",this.handleKeyup.bind(this))}S.prototype.populate=function(){this.terms.forEach(function(b){var C={term:b.term,definition:b.definition,definitionClass:this.classes.definitionClass,glossaryItemClass:this.classes.glossaryItemClass,termClass:this.classes.termClass};this.listElm.insertAdjacentHTML("beforeend",v(C))},this)},S.prototype.initList=function(){var b=this.selectors.glossaryID.slice(1),C=this.selectors.listClass.slice(1),x=this.selectors.searchClass.slice(1),y={valueNames:["data-glossary-term"],listClass:C,searchClass:x};this.list=new h(b,y),this.list.sort("data-glossary-term",{order:"asc"})},S.prototype.linkTerms=function(){var b=document.querySelectorAll("[data-term]");f(b,function(C){C.setAttribute("tabIndex",0),C.setAttribute("data-term",(C.getAttribute("data-term")||"").toLowerCase()),C.setAttribute("title","opens glossary for "+C.getAttribute("data-term"))}),document.body.addEventListener("click",this.handleTermTouch.bind(this)),document.body.addEventListener("keyup",this.handleTermTouch.bind(this))},S.prototype.handleTermTouch=function(b){(b.which===c||b.type==="click")&&(d(b.target,"[data-term]")?(this.show(b),this.selectedTerm=b.target,this.findTerm(b.target.getAttribute("data-term"))):(this.isOpen&&!this.clickedOnDrawer(b)&&this.hide(),this.selectedTerm=this.toggleBtn))},S.prototype.findTerm=function(b){this.search.value=b;var C=this.classes.highlightedTerm;f(this.body.querySelectorAll("."+C),function(y){y.classList.remove(C)}),f(this.body.querySelectorAll('span[data-term="'+b+'"]'),function(y){y.classList.add(C)}),this.list.filter(function(y){return y._values["data-glossary-term"].toLowerCase()===b}),this.list.search();var x=this.list.visibleItems[0].elm.querySelector("button");this.accordion.expand(x)},S.prototype.toggle=function(){var b=this.isOpen?this.hide:this.show;b.apply(this)},S.prototype.show=function(){this.body.setAttribute("aria-hidden","false"),this.toggleBtn.setAttribute("aria-expanded","true"),this.search.focus(),this.isOpen=!0,A(this.body)},S.prototype.hide=function(){this.body.setAttribute("aria-hidden","true"),this.toggleBtn.setAttribute("aria-expanded","false"),this.selectedTerm.focus(),this.isOpen=!1,u(this.body)},S.prototype.handleInput=function(){this.list.filtered&&this.list.filter()},S.prototype.handleKeyup=function(b){b.keyCode==a&&this.isOpen&&this.hide()},S.prototype.addEventListener=function(b,C,x){b&&(b.addEventListener(C,x),this.listeners.push({elm:b,event:C,callback:x}))},S.prototype.destroy=function(){this.accordion.destroy(),this.listeners.forEach(function(b){b.elm.removeEventListener(b.event,b.callback)})},S.prototype.getClassName=function(b){let C=".--";if(b){let x=b.split(" ");if(x.length>1){let y="";x.forEach(E=>{E.includes(":")||(y+="."+E)}),C=y}else C="."+x[0]}return C},S.prototype.clickedOnDrawer=function(b){let C=document.getElementById("glossary"),x=b.srcElement||b.target,y="",E="",F="",q="";x&&x.hasAttributes()&&(E=this.getClassName(x.className),y=x.getAttribute("id")?x.getAttribute("id"):"--"),x.parentNode&&x.parentNode.hasAttributes()&&(F=this.getClassName(x.parentNode.className),q=x.parentNode.getAttribute("id")?x.parentNode.getAttribute("id"):"--");let O=["glossary","glossary-results","glossary-search"];return!!(C&&O.includes(q)||O.includes(y)||E&&C.querySelector(E)||y&&C.querySelector("#"+y)||F&&C.querySelector(F)||q&&C.querySelector("#"+q))},N.exports=S},{"aria-accordion":3,"list.js":8,underscore:25}],2:[function(I,N,M){"use strict";var l=I("./terms"),h=I("./glossary");new h(l)},{"./glossary":1,"./terms":26}],3:[function(I,N,M){"use strict";var l=I("./util").extend,h={collapseOthers:!1,customHiding:!1,contentPrefix:"accordion",openFirst:!1,reflectStatic:!1},w={trigger:"button"},c=function(a,d,f){this.elm=a,this.selectors=l({},w,d),this.opts=l({},h,f),this.triggers=this.findTriggers(),this.listeners=[],this.addEventListener(this.elm,"click",this.handleClickElm.bind(this)),this.opts.openFirst&&this.expand(this.triggers[0])};c.prototype.handleClickElm=function(a){if(this.triggers.indexOf(a.target)>-1)this.toggle(a.target);else{var d=this;this.triggers.forEach(function(f){a.target.parentElement===f&&d.toggle(f)})}},c.prototype.findTriggers=function(){var a=this,d=[].slice.call(this.elm.querySelectorAll(this.selectors.trigger));return d.forEach(function(f,v){a.setAria(f,v)}),d},c.prototype.setAria=function(a,d){var f=a.nextElementSibling,v,g="false",m="true";f.hasAttribute("id")?v=f.getAttribute("id"):(v=this.opts.contentPrefix+"-content-"+d,f.setAttribute("id",v)),this.opts.reflectStatic&&(g=a.getAttribute("aria-expanded")||g,m=f.getAttribute("aria-hidden")||m),a.setAttribute("aria-controls",v),a.setAttribute("aria-expanded",g),f.setAttribute("aria-hidden",m),this.setStyles(f)},c.prototype.toggle=function(a){var d=a.getAttribute("aria-expanded")==="true"?this.collapse:this.expand;d.call(this,a)},c.prototype.expand=function(a){this.opts.collapseOthers&&this.collapseAll();var d=document.getElementById(a.getAttribute("aria-controls"));a.setAttribute("aria-expanded","true"),d.setAttribute("aria-hidden","false"),this.setStyles(d)},c.prototype.collapse=function(a){var d=document.getElementById(a.getAttribute("aria-controls"));a.setAttribute("aria-expanded","false"),d.setAttribute("aria-hidden","true"),this.setStyles(d)},c.prototype.collapseAll=function(){var a=this;this.triggers.forEach(function(d){a.collapse(d)})},c.prototype.expandAll=function(){var a=this;this.triggers.forEach(function(d){a.expand(d)})},c.prototype.setStyles=function(a){var d=a.getAttribute("aria-hidden")==="true"?"none":"block";this.opts.customHiding||(a.style.display=d)},c.prototype.addEventListener=function(a,d,f){a&&(a.addEventListener(d,f),this.listeners.push({elm:a,event:d,callback:f}))},c.prototype.destroy=function(){this.listeners.forEach(function(a){a.elm.removeEventListener(a.event,a.callback)})},N.exports={Accordion:c}},{"./util":4}],4:[function(I,N,M){var l=function(h){h=h||{};for(var w=1;w<arguments.length;w++)if(arguments[w])for(var c in arguments[w])arguments[w].hasOwnProperty(c)&&(h[c]=arguments[w][c]);return h};N.exports={extend:l}},{}],5:[function(I,N,M){N.exports=function(l){var h=function(w,c,a){var d=w.splice(0,50);a=a||[],a=a.concat(l.add(d)),w.length>0?setTimeout(function(){h(w,c,a)},1):(l.update(),c(a))};return h}},{}],6:[function(I,N,M){N.exports=function(l){return l.handlers.filterStart=l.handlers.filterStart||[],l.handlers.filterComplete=l.handlers.filterComplete||[],function(h){if(l.trigger("filterStart"),l.i=1,l.reset.filter(),h===void 0)l.filtered=!1;else{l.filtered=!0;for(var w=l.items,c=0,a=w.length;c<a;c++){var d=w[c];h(d)?d.filtered=!0:d.filtered=!1}}return l.update(),l.trigger("filterComplete"),l.visibleItems}}},{}],7:[function(I,N,M){var l=I("./utils/classes"),h=I("./utils/events"),w=I("./utils/extend"),c=I("./utils/to-string"),a=I("./utils/get-by-class"),d=I("./utils/fuzzy");N.exports=function(f,v){v=v||{},v=w({location:0,distance:100,threshold:.4,multiSearch:!0,searchClass:"fuzzy-search"},v);var g={search:function(m,u){for(var A=v.multiSearch?m.replace(/ +$/,"").split(/ +/):[m],o=0,S=f.items.length;o<S;o++)g.item(f.items[o],u,A)},item:function(m,u,A){for(var o=!0,S=0;S<A.length;S++){for(var b=!1,C=0,x=u.length;C<x;C++)g.values(m.values(),u[C],A[S])&&(b=!0);b||(o=!1)}m.found=o},values:function(m,u,A){if(m.hasOwnProperty(u)){var o=c(m[u]).toLowerCase();if(d(o,A,v))return!0}return!1}};return h.bind(a(f.listContainer,v.searchClass),"keyup",f.utils.events.debounce(function(m){var u=m.target||m.srcElement;f.search(u.value,g.search)},f.searchDelay)),function(m,u){f.search(m,u,g.search)}}},{"./utils/classes":15,"./utils/events":16,"./utils/extend":17,"./utils/fuzzy":18,"./utils/get-by-class":20,"./utils/to-string":23}],8:[function(I,N,M){var l=I("string-natural-compare"),h=I("./utils/get-by-class"),w=I("./utils/extend"),c=I("./utils/index-of"),a=I("./utils/events"),d=I("./utils/to-string"),f=I("./utils/classes"),v=I("./utils/get-attribute"),g=I("./utils/to-array");N.exports=function(m,u,A){var o=this,S,b=I("./item")(o),C=I("./add-async")(o),x=I("./pagination")(o);S={start:function(){o.listClass="list",o.searchClass="search",o.sortClass="sort",o.page=1e4,o.i=1,o.items=[],o.visibleItems=[],o.matchingItems=[],o.searched=!1,o.filtered=!1,o.searchColumns=void 0,o.searchDelay=0,o.handlers={updated:[]},o.valueNames=[],o.utils={getByClass:h,extend:w,indexOf:c,events:a,toString:d,naturalSort:l,classes:f,getAttribute:v,toArray:g},o.utils.extend(o,u),o.listContainer=typeof m=="string"?document.getElementById(m):m,o.listContainer&&(o.list=h(o.listContainer,o.listClass,!0),o.parse=I("./parse")(o),o.templater=I("./templater")(o),o.search=I("./search")(o),o.filter=I("./filter")(o),o.sort=I("./sort")(o),o.fuzzySearch=I("./fuzzy-search")(o,u.fuzzySearch),this.handlers(),this.items(),this.pagination(),o.update())},handlers:function(){for(var y in o.handlers)o[y]&&o.handlers.hasOwnProperty(y)&&o.on(y,o[y])},items:function(){o.parse(o.list),A!==void 0&&o.add(A)},pagination:function(){if(u.pagination!==void 0){u.pagination===!0&&(u.pagination=[{}]),u.pagination[0]===void 0&&(u.pagination=[u.pagination]);for(var y=0,E=u.pagination.length;y<E;y++)x(u.pagination[y])}}},this.reIndex=function(){o.items=[],o.visibleItems=[],o.matchingItems=[],o.searched=!1,o.filtered=!1,o.parse(o.list)},this.toJSON=function(){for(var y=[],E=0,F=o.items.length;E<F;E++)y.push(o.items[E].values());return y},this.add=function(y,E){if(y.length!==0){if(E){C(y.slice(0),E);return}var F=[],q=!1;y[0]===void 0&&(y=[y]);for(var O=0,_=y.length;O<_;O++){var $=null;q=o.items.length>o.page,$=new b(y[O],void 0,q),o.items.push($),F.push($)}return o.update(),F}},this.show=function(y,E){return this.i=y,this.page=E,o.update(),o},this.remove=function(y,E,F){for(var q=0,O=0,_=o.items.length;O<_;O++)o.items[O].values()[y]==E&&(o.templater.remove(o.items[O],F),o.items.splice(O,1),_--,O--,q++);return o.update(),q},this.get=function(y,E){for(var F=[],q=0,O=o.items.length;q<O;q++){var _=o.items[q];_.values()[y]==E&&F.push(_)}return F},this.size=function(){return o.items.length},this.clear=function(){return o.templater.clear(),o.items=[],o},this.on=function(y,E){return o.handlers[y].push(E),o},this.off=function(y,E){var F=o.handlers[y],q=c(F,E);return q>-1&&F.splice(q,1),o},this.trigger=function(y){for(var E=o.handlers[y].length;E--;)o.handlers[y][E](o);return o},this.reset={filter:function(){for(var y=o.items,E=y.length;E--;)y[E].filtered=!1;return o},search:function(){for(var y=o.items,E=y.length;E--;)y[E].found=!1;return o}},this.update=function(){var y=o.items,E=y.length;o.visibleItems=[],o.matchingItems=[],o.templater.clear();for(var F=0;F<E;F++)y[F].matching()&&o.matchingItems.length+1>=o.i&&o.visibleItems.length<o.page?(y[F].show(),o.visibleItems.push(y[F]),o.matchingItems.push(y[F])):(y[F].matching()&&o.matchingItems.push(y[F]),y[F].hide());return o.trigger("updated"),o},S.start()}},{"./add-async":5,"./filter":6,"./fuzzy-search":7,"./item":9,"./pagination":10,"./parse":11,"./search":12,"./sort":13,"./templater":14,"./utils/classes":15,"./utils/events":16,"./utils/extend":17,"./utils/get-attribute":19,"./utils/get-by-class":20,"./utils/index-of":21,"./utils/to-array":22,"./utils/to-string":23,"string-natural-compare":24}],9:[function(I,N,M){N.exports=function(l){return function(h,w,c){var a=this;this._values={},this.found=!1,this.filtered=!1;var d=function(f,v,g){if(v===void 0)g?a.values(f,g):a.values(f);else{a.elm=v;var m=l.templater.get(a,f);a.values(m)}};this.values=function(f,v){if(f!==void 0){for(var g in f)a._values[g]=f[g];v!==!0&&l.templater.set(a,a.values())}else return a._values},this.show=function(){l.templater.show(a)},this.hide=function(){l.templater.hide(a)},this.matching=function(){return l.filtered&&l.searched&&a.found&&a.filtered||l.filtered&&!l.searched&&a.filtered||!l.filtered&&l.searched&&a.found||!l.filtered&&!l.searched},this.visible=function(){return!!(a.elm&&a.elm.parentNode==l.list)},d(h,w,c)}}},{}],10:[function(I,N,M){var l=I("./utils/classes"),h=I("./utils/events"),w=I("./index");N.exports=function(c){var a=!1,d=function(v,g){if(c.page<1){c.listContainer.style.display="none",a=!0;return}else a&&(c.listContainer.style.display="block");var m,u=c.matchingItems.length,A=c.i,o=c.page,S=Math.ceil(u/o),b=Math.ceil(A/o),C=g.innerWindow||2,x=g.left||g.outerWindow||0,y=g.right||g.outerWindow||0;y=S-y,v.clear();for(var E=1;E<=S;E++){var F=b===E?"active":"";f.number(E,x,y,b,C)?(m=v.add({page:E,dotted:!1})[0],F&&l(m.elm).add(F),m.elm.firstChild.setAttribute("data-i",E),m.elm.firstChild.setAttribute("data-page",o)):f.dotted(v,E,x,y,b,C,v.size())&&(m=v.add({page:"...",dotted:!0})[0],l(m.elm).add("disabled"))}},f={number:function(v,g,m,u,A){return this.left(v,g)||this.right(v,m)||this.innerWindow(v,u,A)},left:function(v,g){return v<=g},right:function(v,g){return v>g},innerWindow:function(v,g,m){return v>=g-m&&v<=g+m},dotted:function(v,g,m,u,A,o,S){return this.dottedLeft(v,g,m,u,A,o)||this.dottedRight(v,g,m,u,A,o,S)},dottedLeft:function(v,g,m,u,A,o){return g==m+1&&!this.innerWindow(g,A,o)&&!this.right(g,u)},dottedRight:function(v,g,m,u,A,o,S){return v.items[S-1].values().dotted?!1:g==u&&!this.innerWindow(g,A,o)&&!this.right(g,u)}};return function(v){var g=new w(c.listContainer.id,{listClass:v.paginationClass||"pagination",item:v.item||"<li><a class='page' href='#'></a></li>",valueNames:["page","dotted"],searchClass:"pagination-search-that-is-not-supposed-to-exist",sortClass:"pagination-sort-that-is-not-supposed-to-exist"});h.bind(g.listContainer,"click",function(m){var u=m.target||m.srcElement,A=c.utils.getAttribute(u,"data-page"),o=c.utils.getAttribute(u,"data-i");o&&c.show((o-1)*A+1,A)}),c.on("updated",function(){d(g,v)}),d(g,v)}}},{"./index":8,"./utils/classes":15,"./utils/events":16}],11:[function(I,N,M){N.exports=function(l){var h=I("./item")(l),w=function(d){for(var f=d.childNodes,v=[],g=0,m=f.length;g<m;g++)f[g].data===void 0&&v.push(f[g]);return v},c=function(d,f){for(var v=0,g=d.length;v<g;v++)l.items.push(new h(f,d[v]))},a=function(d,f){var v=d.splice(0,50);c(v,f),d.length>0?setTimeout(function(){a(d,f)},1):(l.update(),l.trigger("parseComplete"))};return l.handlers.parseComplete=l.handlers.parseComplete||[],function(){var d=w(l.list),f=l.valueNames;l.indexAsync?a(d,f):c(d,f)}}},{"./item":9}],12:[function(I,N,M){N.exports=function(l){var h,w,c,a,d,f={resetList:function(){l.i=1,l.templater.clear(),d=void 0},setOptions:function(m){m.length==2&&m[1]instanceof Array?c=m[1]:m.length==2&&typeof m[1]=="function"?(c=void 0,d=m[1]):m.length==3?(c=m[1],d=m[2]):c=void 0},setColumns:function(){l.items.length!==0&&c===void 0&&(c=l.searchColumns===void 0?f.toArray(l.items[0].values()):l.searchColumns)},setSearchString:function(m){m=l.utils.toString(m).toLowerCase(),m=m.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&"),a=m},toArray:function(m){var u=[];for(var A in m)u.push(A);return u}},v={list:function(){for(var m=[],u,A=a;(u=A.match(/"([^"]+)"/))!==null;)m.push(u[1]),A=A.substring(0,u.index)+A.substring(u.index+u[0].length);A=A.trim(),A.length&&(m=m.concat(A.split(/\s+/)));for(var o=0,S=l.items.length;o<S;o++){var b=l.items[o];if(b.found=!1,!!m.length){for(var C=0,x=m.length;C<x;C++){for(var y=!1,E=0,F=c.length;E<F;E++){var q=b.values(),O=c[E];if(q.hasOwnProperty(O)&&q[O]!==void 0&&q[O]!==null){var _=typeof q[O]!="string"?q[O].toString():q[O];if(_.toLowerCase().indexOf(m[C])!==-1){y=!0;break}}}if(!y)break}b.found=y}}},reset:function(){l.reset.search(),l.searched=!1}},g=function(m){return l.trigger("searchStart"),f.resetList(),f.setSearchString(m),f.setOptions(arguments),f.setColumns(),a===""?v.reset():(l.searched=!0,d?d(a,c):v.list()),l.update(),l.trigger("searchComplete"),l.visibleItems};return l.handlers.searchStart=l.handlers.searchStart||[],l.handlers.searchComplete=l.handlers.searchComplete||[],l.utils.events.bind(l.utils.getByClass(l.listContainer,l.searchClass),"keyup",l.utils.events.debounce(function(m){var u=m.target||m.srcElement,A=u.value===""&&!l.searched;A||g(u.value)},l.searchDelay)),l.utils.events.bind(l.utils.getByClass(l.listContainer,l.searchClass),"input",function(m){var u=m.target||m.srcElement;u.value===""&&g("")}),g}},{}],13:[function(I,N,M){N.exports=function(l){var h={els:void 0,clear:function(){for(var c=0,a=h.els.length;c<a;c++)l.utils.classes(h.els[c]).remove("asc"),l.utils.classes(h.els[c]).remove("desc")},getOrder:function(c){var a=l.utils.getAttribute(c,"data-order");return a=="asc"||a=="desc"?a:l.utils.classes(c).has("desc")?"asc":l.utils.classes(c).has("asc")?"desc":"asc"},getInSensitive:function(c,a){var d=l.utils.getAttribute(c,"data-insensitive");d==="false"?a.insensitive=!1:a.insensitive=!0},setOrder:function(c){for(var a=0,d=h.els.length;a<d;a++){var f=h.els[a];if(l.utils.getAttribute(f,"data-sort")===c.valueName){var v=l.utils.getAttribute(f,"data-order");v=="asc"||v=="desc"?v==c.order&&l.utils.classes(f).add(c.order):l.utils.classes(f).add(c.order)}}}},w=function(){l.trigger("sortStart");var c={},a=arguments[0].currentTarget||arguments[0].srcElement||void 0;a?(c.valueName=l.utils.getAttribute(a,"data-sort"),h.getInSensitive(a,c),c.order=h.getOrder(a)):(c=arguments[1]||c,c.valueName=arguments[0],c.order=c.order||"asc",c.insensitive=typeof c.insensitive>"u"?!0:c.insensitive),h.clear(),h.setOrder(c);var d=c.sortFunction||l.sortFunction||null,f=c.order==="desc"?-1:1,v;d?v=function(g,m){return d(g,m,c)*f}:v=function(g,m){var u=l.utils.naturalSort;return u.alphabet=l.alphabet||c.alphabet||void 0,!u.alphabet&&c.insensitive&&(u=l.utils.naturalSort.caseInsensitive),u(g.values()[c.valueName],m.values()[c.valueName])*f},l.items.sort(v),l.update(),l.trigger("sortComplete")};return l.handlers.sortStart=l.handlers.sortStart||[],l.handlers.sortComplete=l.handlers.sortComplete||[],h.els=l.utils.getByClass(l.listContainer,l.sortClass),l.utils.events.bind(h.els,"click",w),l.on("searchStart",h.clear),l.on("filterStart",h.clear),w}},{}],14:[function(I,N,M){var l=function(h){var w,c=this,a=function(){var u;if(typeof h.item=="function"){w=function(A){var o=h.item(A);return v(o)};return}if(typeof h.item=="string"?h.item.indexOf("<")===-1?u=document.getElementById(h.item):u=v(h.item):u=f(),!u)throw new Error("The list needs to have at least one item on init otherwise you'll have to add a template.");u=d(u,h.valueNames),w=function(){return u.cloneNode(!0)}},d=function(u,A){var o=u.cloneNode(!0);o.removeAttribute("id");for(var S=0,b=A.length;S<b;S++){var C=void 0,x=A[S];if(x.data)for(var y=0,E=x.data.length;y<E;y++)o.setAttribute("data-"+x.data[y],"");else x.attr&&x.name?(C=h.utils.getByClass(o,x.name,!0),C&&C.setAttribute(x.attr,"")):(C=h.utils.getByClass(o,x,!0),C&&(C.innerHTML=""))}return o},f=function(){for(var u=h.list.childNodes,A=0,o=u.length;A<o;A++)if(u[A].data===void 0)return u[A].cloneNode(!0)},v=function(u){if(typeof u=="string"){if(/<tr[\s>]/g.exec(u)){var A=document.createElement("tbody");return A.innerHTML=u,A.firstElementChild}else if(u.indexOf("<")!==-1){var o=document.createElement("div");return o.innerHTML=u,o.firstElementChild}}},g=function(u){for(var A=0,o=h.valueNames.length;A<o;A++){var S=h.valueNames[A];if(S.data){for(var b=S.data,C=0,x=b.length;C<x;C++)if(b[C]===u)return{data:u}}else{if(S.attr&&S.name&&S.name==u)return S;if(S===u)return u}}},m=function(u,A,o){var S=void 0,b=g(A);b&&(b.data?u.elm.setAttribute("data-"+b.data,o):b.attr&&b.name?(S=h.utils.getByClass(u.elm,b.name,!0),S&&S.setAttribute(b.attr,o)):(S=h.utils.getByClass(u.elm,b,!0),S&&(S.innerHTML=o)))};this.get=function(u,A){c.create(u);for(var o={},S=0,b=A.length;S<b;S++){var C=void 0,x=A[S];if(x.data)for(var y=0,E=x.data.length;y<E;y++)o[x.data[y]]=h.utils.getAttribute(u.elm,"data-"+x.data[y]);else x.attr&&x.name?(C=h.utils.getByClass(u.elm,x.name,!0),o[x.name]=C?h.utils.getAttribute(C,x.attr):""):(C=h.utils.getByClass(u.elm,x,!0),o[x]=C?C.innerHTML:"")}return o},this.set=function(u,A){if(!c.create(u))for(var o in A)A.hasOwnProperty(o)&&m(u,o,A[o])},this.create=function(u){return u.elm!==void 0?!1:(u.elm=w(u.values()),c.set(u,u.values()),!0)},this.remove=function(u){u.elm.parentNode===h.list&&h.list.removeChild(u.elm)},this.show=function(u){c.create(u),h.list.appendChild(u.elm)},this.hide=function(u){u.elm!==void 0&&u.elm.parentNode===h.list&&h.list.removeChild(u.elm)},this.clear=function(){if(h.list.hasChildNodes())for(;h.list.childNodes.length>=1;)h.list.removeChild(h.list.firstChild)},a()};N.exports=function(h){return new l(h)}},{}],15:[function(I,N,M){var l=I("./index-of"),h=/\s+/,w=Object.prototype.toString;N.exports=function(a){return new c(a)};function c(a){if(!a||!a.nodeType)throw new Error("A DOM element reference is required");this.el=a,this.list=a.classList}c.prototype.add=function(a){if(this.list)return this.list.add(a),this;var d=this.array(),f=l(d,a);return~f||d.push(a),this.el.className=d.join(" "),this},c.prototype.remove=function(a){if(this.list)return this.list.remove(a),this;var d=this.array(),f=l(d,a);return~f&&d.splice(f,1),this.el.className=d.join(" "),this},c.prototype.toggle=function(a,d){return this.list?(typeof d<"u"?d!==this.list.toggle(a,d)&&this.list.toggle(a):this.list.toggle(a),this):(typeof d<"u"?d?this.add(a):this.remove(a):this.has(a)?this.remove(a):this.add(a),this)},c.prototype.array=function(){var a=this.el.getAttribute("class")||"",d=a.replace(/^\s+|\s+$/g,""),f=d.split(h);return f[0]===""&&f.shift(),f},c.prototype.has=c.prototype.contains=function(a){return this.list?this.list.contains(a):!!~l(this.array(),a)}},{"./index-of":21}],16:[function(I,N,M){var l=window.addEventListener?"addEventListener":"attachEvent",h=window.removeEventListener?"removeEventListener":"detachEvent",w=l!=="addEventListener"?"on":"",c=I("./to-array");M.bind=function(a,d,f,v){a=c(a);for(var g=0,m=a.length;g<m;g++)a[g][l](w+d,f,v||!1)},M.unbind=function(a,d,f,v){a=c(a);for(var g=0,m=a.length;g<m;g++)a[g][h](w+d,f,v||!1)},M.debounce=function(a,d,f){var v;return d?function(){var g=this,m=arguments,u=function(){v=null,f||a.apply(g,m)},A=f&&!v;clearTimeout(v),v=setTimeout(u,d),A&&a.apply(g,m)}:a}},{"./to-array":22}],17:[function(I,N,M){N.exports=function(h){for(var w=Array.prototype.slice.call(arguments,1),c=0,a;a=w[c];c++)if(a)for(var d in a)h[d]=a[d];return h}},{}],18:[function(I,N,M){N.exports=function(l,h,w){var c=w.location||0,a=w.distance||100,d=w.threshold||.4;if(h===l)return!0;if(h.length>32)return!1;var f=c,v=function(){var $={},U;for(U=0;U<h.length;U++)$[h.charAt(U)]=0;for(U=0;U<h.length;U++)$[h.charAt(U)]|=1<<h.length-U-1;return $}();function g($,U){var re=$/h.length,le=Math.abs(f-U);return a?re+le/a:le?1:re}var m=d,u=l.indexOf(h,f);u!=-1&&(m=Math.min(g(0,u),m),u=l.lastIndexOf(h,f+h.length),u!=-1&&(m=Math.min(g(0,u),m)));var A=1<<h.length-1;u=-1;for(var o,S,b=h.length+l.length,C,x=0;x<h.length;x++){for(o=0,S=b;o<S;)g(x,f+S)<=m?o=S:b=S,S=Math.floor((b-o)/2+o);b=S;var y=Math.max(1,f-S+1),E=Math.min(f+S,l.length)+h.length,F=Array(E+2);F[E+1]=(1<<x)-1;for(var q=E;q>=y;q--){var O=v[l.charAt(q-1)];if(x===0?F[q]=(F[q+1]<<1|1)&O:F[q]=(F[q+1]<<1|1)&O|((C[q+1]|C[q])<<1|1)|C[q+1],F[q]&A){var _=g(x,q-1);if(_<=m)if(m=_,u=q-1,u>f)y=Math.max(1,2*f-u);else break}}if(g(x+1,f)>m)break;C=F}return!(u<0)}},{}],19:[function(I,N,M){N.exports=function(l,h){var w=l.getAttribute&&l.getAttribute(h)||null;if(!w)for(var c=l.attributes,a=c.length,d=0;d<a;d++)c[d]!==void 0&&c[d].nodeName===h&&(w=c[d].nodeValue);return w}},{}],20:[function(I,N,M){var l=function(c,a,d){return d?c.getElementsByClassName(a)[0]:c.getElementsByClassName(a)},h=function(c,a,d){return a="."+a,d?c.querySelector(a):c.querySelectorAll(a)},w=function(c,a,d){for(var f=[],v="*",g=c.getElementsByTagName(v),m=g.length,u=new RegExp("(^|\\s)"+a+"(\\s|$)"),A=0,o=0;A<m;A++)if(u.test(g[A].className)){if(d)return g[A];f[o]=g[A],o++}return f};N.exports=function(){return function(c,a,d,f){return f=f||{},f.test&&f.getElementsByClassName||!f.test&&document.getElementsByClassName?l(c,a,d):f.test&&f.querySelector||!f.test&&document.querySelector?h(c,a,d):w(c,a,d)}}()},{}],21:[function(I,N,M){var l=[].indexOf;N.exports=function(h,w){if(l)return h.indexOf(w);for(var c=0,a=h.length;c<a;++c)if(h[c]===w)return c;return-1}},{}],22:[function(I,N,M){N.exports=function(w){if(typeof w>"u")return[];if(w===null)return[null];if(w===window)return[window];if(typeof w=="string")return[w];if(l(w))return w;if(typeof w.length!="number")return[w];if(typeof w=="function"&&w instanceof Function)return[w];for(var c=[],a=0,d=w.length;a<d;a++)(Object.prototype.hasOwnProperty.call(w,a)||a in w)&&c.push(w[a]);return c.length?c:[]};function l(h){return Object.prototype.toString.call(h)==="[object Array]"}},{}],23:[function(I,N,M){N.exports=function(l){return l=l===void 0?"":l,l=l===null?"":l,l=l.toString(),l}},{}],24:[function(I,N,M){"use strict";var l,h,w=0;function c(d){return d>=48&&d<=57}function a(d,f){for(var v=(d+="").length,g=(f+="").length,m=0,u=0;m<v&&u<g;){var A=d.charCodeAt(m),o=f.charCodeAt(u);if(c(A)){if(!c(o))return A-o;for(var S=m,b=u;A===48&&++S<v;)A=d.charCodeAt(S);for(;o===48&&++b<g;)o=f.charCodeAt(b);for(var C=S,x=b;C<v&&c(d.charCodeAt(C));)++C;for(;x<g&&c(f.charCodeAt(x));)++x;var y=C-S-x+b;if(y)return y;for(;S<C;)if(y=d.charCodeAt(S++)-f.charCodeAt(b++),y)return y;m=C,u=x;continue}if(A!==o)return A<w&&o<w&&h[A]!==-1&&h[o]!==-1?h[A]-h[o]:A-o;++m,++u}return m>=v&&u<g&&v>=g?-1:u>=g&&m<v&&g>=v?1:v-g}a.caseInsensitive=a.i=function(d,f){return a((""+d).toLowerCase(),(""+f).toLowerCase())},Object.defineProperties(a,{alphabet:{get:function(){return l},set:function(d){l=d,h=[];var f=0;if(l)for(;f<l.length;f++)h[l.charCodeAt(f)]=f;for(w=h.length,f=0;f<w;f++)h[f]===void 0&&(h[f]=-1)}}}),N.exports=a},{}],25:[function(I,N,M){(function(l){(function(){(function(h,w){typeof M=="object"&&typeof N<"u"?N.exports=w():typeof define=="function"&&define.amd?define("underscore",w):(h=typeof globalThis<"u"?globalThis:h||self,function(){var c=h._,a=h._=w();a.noConflict=function(){return h._=c,a}}())})(this,function(){var h="1.13.6",w=typeof self=="object"&&self.self===self&&self||typeof l=="object"&&l.global===l&&l||Function("return this")()||{},c=Array.prototype,a=Object.prototype,d=typeof Symbol<"u"?Symbol.prototype:null,f=c.push,v=c.slice,g=a.toString,m=a.hasOwnProperty,u=typeof ArrayBuffer<"u",A=typeof DataView<"u",o=Array.isArray,S=Object.keys,b=Object.create,C=u&&ArrayBuffer.isView,x=isNaN,y=isFinite,E=!{toString:null}.propertyIsEnumerable("toString"),F=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],q=Math.pow(2,53)-1;function O(e,t){return t=t==null?e.length-1:+t,function(){for(var r=Math.max(arguments.length-t,0),n=Array(r),s=0;s<r;s++)n[s]=arguments[s+t];switch(t){case 0:return e.call(this,n);case 1:return e.call(this,arguments[0],n);case 2:return e.call(this,arguments[0],arguments[1],n)}var i=Array(t+1);for(s=0;s<t;s++)i[s]=arguments[s];return i[t]=n,e.apply(this,i)}}function _(e){var t=typeof e;return t==="function"||t==="object"&&!!e}function $(e){return e===null}function U(e){return e===void 0}function re(e){return e===!0||e===!1||g.call(e)==="[object Boolean]"}function le(e){return!!(e&&e.nodeType===1)}function k(e){var t="[object "+e+"]";return function(r){return g.call(r)===t}}var pe=k("String"),ze=k("Number"),Bt=k("Date"),kt=k("RegExp"),zt=k("Error"),Qe=k("Symbol"),Ue=k("ArrayBuffer"),He=k("Function"),Qt=w.document&&w.document.childNodes;typeof/./!="function"&&typeof Int8Array!="object"&&typeof Qt!="function"&&(He=function(e){return typeof e=="function"||!1});var z=He,Ve=k("Object"),We=A&&Ve(new DataView(new ArrayBuffer(8))),me=typeof Map<"u"&&Ve(new Map),Ut=k("DataView");function Ht(e){return e!=null&&z(e.getInt8)&&Ue(e.buffer)}var ce=We?Ht:Ut,K=o||k("Array");function J(e,t){return e!=null&&m.call(e,t)}var ge=k("Arguments");(function(){ge(arguments)||(ge=function(e){return J(e,"callee")})})();var ve=ge;function Vt(e){return!Qe(e)&&y(e)&&!isNaN(parseFloat(e))}function $e(e){return ze(e)&&x(e)}function Ge(e){return function(){return e}}function Ye(e){return function(t){var r=e(t);return typeof r=="number"&&r>=0&&r<=q}}function Je(e){return function(t){return t?.[e]}}var ue=Je("byteLength"),Wt=Ye(ue),$t=/\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\]/;function Gt(e){return C?C(e)&&!ce(e):Wt(e)&&$t.test(g.call(e))}var Ke=u?Gt:Ge(!1),H=Je("length");function Yt(e){for(var t={},r=e.length,n=0;n<r;++n)t[e[n]]=!0;return{contains:function(s){return t[s]===!0},push:function(s){return t[s]=!0,e.push(s)}}}function Xe(e,t){t=Yt(t);var r=F.length,n=e.constructor,s=z(n)&&n.prototype||a,i="constructor";for(J(e,i)&&!t.contains(i)&&t.push(i);r--;)i=F[r],i in e&&e[i]!==s[i]&&!t.contains(i)&&t.push(i)}function R(e){if(!_(e))return[];if(S)return S(e);var t=[];for(var r in e)J(e,r)&&t.push(r);return E&&Xe(e,t),t}function Jt(e){if(e==null)return!0;var t=H(e);return typeof t=="number"&&(K(e)||pe(e)||ve(e))?t===0:H(R(e))===0}function Ze(e,t){var r=R(t),n=r.length;if(e==null)return!n;for(var s=Object(e),i=0;i<n;i++){var p=r[i];if(t[p]!==s[p]||!(p in s))return!1}return!0}function L(e){if(e instanceof L)return e;if(!(this instanceof L))return new L(e);this._wrapped=e}L.VERSION=h,L.prototype.value=function(){return this._wrapped},L.prototype.valueOf=L.prototype.toJSON=L.prototype.value,L.prototype.toString=function(){return String(this._wrapped)};function je(e){return new Uint8Array(e.buffer||e,e.byteOffset||0,ue(e))}var et="[object DataView]";function ye(e,t,r,n){if(e===t)return e!==0||1/e===1/t;if(e==null||t==null)return!1;if(e!==e)return t!==t;var s=typeof e;return s!=="function"&&s!=="object"&&typeof t!="object"?!1:tt(e,t,r,n)}function tt(e,t,r,n){e instanceof L&&(e=e._wrapped),t instanceof L&&(t=t._wrapped);var s=g.call(e);if(s!==g.call(t))return!1;if(We&&s=="[object Object]"&&ce(e)){if(!ce(t))return!1;s=et}switch(s){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:+e==0?1/+e===1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return d.valueOf.call(e)===d.valueOf.call(t);case"[object ArrayBuffer]":case et:return tt(je(e),je(t),r,n)}var i=s==="[object Array]";if(!i&&Ke(e)){var p=ue(e);if(p!==ue(t))return!1;if(e.buffer===t.buffer&&e.byteOffset===t.byteOffset)return!0;i=!0}if(!i){if(typeof e!="object"||typeof t!="object")return!1;var T=e.constructor,P=t.constructor;if(T!==P&&!(z(T)&&T instanceof T&&z(P)&&P instanceof P)&&"constructor"in e&&"constructor"in t)return!1}r=r||[],n=n||[];for(var D=r.length;D--;)if(r[D]===e)return n[D]===t;if(r.push(e),n.push(t),i){if(D=e.length,D!==t.length)return!1;for(;D--;)if(!ye(e[D],t[D],r,n))return!1}else{var B=R(e),Q;if(D=B.length,R(t).length!==D)return!1;for(;D--;)if(Q=B[D],!(J(t,Q)&&ye(e[Q],t[Q],r,n)))return!1}return r.pop(),n.pop(),!0}function Kt(e,t){return ye(e,t)}function ne(e){if(!_(e))return[];var t=[];for(var r in e)t.push(r);return E&&Xe(e,t),t}function be(e){var t=H(e);return function(r){if(r==null)return!1;var n=ne(r);if(H(n))return!1;for(var s=0;s<t;s++)if(!z(r[e[s]]))return!1;return e!==at||!z(r[we])}}var we="forEach",rt="has",Ae=["clear","delete"],nt=["get",rt,"set"],Xt=Ae.concat(we,nt),at=Ae.concat(nt),Zt=["add"].concat(Ae,we,rt),jt=me?be(Xt):k("Map"),er=me?be(at):k("WeakMap"),tr=me?be(Zt):k("Set"),rr=k("WeakSet");function j(e){for(var t=R(e),r=t.length,n=Array(r),s=0;s<r;s++)n[s]=e[t[s]];return n}function nr(e){for(var t=R(e),r=t.length,n=Array(r),s=0;s<r;s++)n[s]=[t[s],e[t[s]]];return n}function it(e){for(var t={},r=R(e),n=0,s=r.length;n<s;n++)t[e[r[n]]]=r[n];return t}function Ce(e){var t=[];for(var r in e)z(e[r])&&t.push(r);return t.sort()}function Te(e,t){return function(r){var n=arguments.length;if(t&&(r=Object(r)),n<2||r==null)return r;for(var s=1;s<n;s++)for(var i=arguments[s],p=e(i),T=p.length,P=0;P<T;P++){var D=p[P];(!t||r[D]===void 0)&&(r[D]=i[D])}return r}}var st=Te(ne),de=Te(R),ot=Te(ne,!0);function ar(){return function(){}}function lt(e){if(!_(e))return{};if(b)return b(e);var t=ar();t.prototype=e;var r=new t;return t.prototype=null,r}function ir(e,t){var r=lt(e);return t&&de(r,t),r}function sr(e){return _(e)?K(e)?e.slice():st({},e):e}function or(e,t){return t(e),e}function ct(e){return K(e)?e:[e]}L.toPath=ct;function ae(e){return L.toPath(e)}function xe(e,t){for(var r=t.length,n=0;n<r;n++){if(e==null)return;e=e[t[n]]}return r?e:void 0}function ut(e,t,r){var n=xe(e,ae(t));return U(n)?r:n}function lr(e,t){t=ae(t);for(var r=t.length,n=0;n<r;n++){var s=t[n];if(!J(e,s))return!1;e=e[s]}return!!r}function Se(e){return e}function ie(e){return e=de({},e),function(t){return Ze(t,e)}}function Ie(e){return e=ae(e),function(t){return xe(t,e)}}function se(e,t,r){if(t===void 0)return e;switch(r??3){case 1:return function(n){return e.call(t,n)};case 3:return function(n,s,i){return e.call(t,n,s,i)};case 4:return function(n,s,i,p){return e.call(t,n,s,i,p)}}return function(){return e.apply(t,arguments)}}function dt(e,t,r){return e==null?Se:z(e)?se(e,t,r):_(e)&&!K(e)?ie(e):Ie(e)}function Ee(e,t){return dt(e,t,1/0)}L.iteratee=Ee;function V(e,t,r){return L.iteratee!==Ee?L.iteratee(e,t):dt(e,t,r)}function cr(e,t,r){t=V(t,r);for(var n=R(e),s=n.length,i={},p=0;p<s;p++){var T=n[p];i[T]=t(e[T],T,e)}return i}function ft(){}function ur(e){return e==null?ft:function(t){return ut(e,t)}}function dr(e,t,r){var n=Array(Math.max(0,e));t=se(t,r,1);for(var s=0;s<e;s++)n[s]=t(s);return n}function Ne(e,t){return t==null&&(t=e,e=0),e+Math.floor(Math.random()*(t-e+1))}var oe=Date.now||function(){return new Date().getTime()};function ht(e){var t=function(i){return e[i]},r="(?:"+R(e).join("|")+")",n=RegExp(r),s=RegExp(r,"g");return function(i){return i=i==null?"":""+i,n.test(i)?i.replace(s,t):i}}var pt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},fr=ht(pt),hr=it(pt),pr=ht(hr),mr=L.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g},De=/(.)^/,gr={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},vr=/\\|'|\r|\n|\u2028|\u2029/g;function yr(e){return"\\"+gr[e]}var br=/^\s*(\w|\$)+\s*$/;function wr(e,t,r){!t&&r&&(t=r),t=ot({},t,L.templateSettings);var n=RegExp([(t.escape||De).source,(t.interpolate||De).source,(t.evaluate||De).source].join("|")+"|$","g"),s=0,i="__p+='";e.replace(n,function(D,B,Q,_t,Rt){return i+=e.slice(s,Rt).replace(vr,yr),s=Rt+D.length,B?i+=`'+
((__t=(`+B+`))==null?'':_.escape(__t))+
'`:Q?i+=`'+
((__t=(`+Q+`))==null?'':__t)+
'`:_t&&(i+=`';
`+_t+`
__p+='`),D}),i+=`';
`;var p=t.variable;if(p){if(!br.test(p))throw new Error("variable is not a bare identifier: "+p)}else i=`with(obj||{}){
`+i+`}
`,p="obj";i=`var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};
`+i+`return __p;
`;var T;try{T=new Function(p,"_",i)}catch(D){throw D.source=i,D}var P=function(D){return T.call(this,D,L)};return P.source="function("+p+`){
`+i+"}",P}function Ar(e,t,r){t=ae(t);var n=t.length;if(!n)return z(r)?r.call(e):r;for(var s=0;s<n;s++){var i=e?.[t[s]];i===void 0&&(i=r,s=n),e=z(i)?i.call(e):i}return e}var Cr=0;function Tr(e){var t=++Cr+"";return e?e+t:t}function xr(e){var t=L(e);return t._chain=!0,t}function mt(e,t,r,n,s){if(!(n instanceof t))return e.apply(r,s);var i=lt(e.prototype),p=e.apply(i,s);return _(p)?p:i}var ee=O(function(e,t){var r=ee.placeholder,n=function(){for(var s=0,i=t.length,p=Array(i),T=0;T<i;T++)p[T]=t[T]===r?arguments[s++]:t[T];for(;s<arguments.length;)p.push(arguments[s++]);return mt(e,n,this,this,p)};return n});ee.placeholder=L;var gt=O(function(e,t,r){if(!z(e))throw new TypeError("Bind must be called on a function");var n=O(function(s){return mt(e,n,t,this,r.concat(s))});return n}),W=Ye(H);function X(e,t,r,n){if(n=n||[],!t&&t!==0)t=1/0;else if(t<=0)return n.concat(e);for(var s=n.length,i=0,p=H(e);i<p;i++){var T=e[i];if(W(T)&&(K(T)||ve(T)))if(t>1)X(T,t-1,r,n),s=n.length;else for(var P=0,D=T.length;P<D;)n[s++]=T[P++];else r||(n[s++]=T)}return n}var Sr=O(function(e,t){t=X(t,!1,!1);var r=t.length;if(r<1)throw new Error("bindAll must be passed function names");for(;r--;){var n=t[r];e[n]=gt(e[n],e)}return e});function Ir(e,t){var r=function(n){var s=r.cache,i=""+(t?t.apply(this,arguments):n);return J(s,i)||(s[i]=e.apply(this,arguments)),s[i]};return r.cache={},r}var vt=O(function(e,t,r){return setTimeout(function(){return e.apply(null,r)},t)}),Er=ee(vt,L,1);function Nr(e,t,r){var n,s,i,p,T=0;r||(r={});var P=function(){T=r.leading===!1?0:oe(),n=null,p=e.apply(s,i),n||(s=i=null)},D=function(){var B=oe();!T&&r.leading===!1&&(T=B);var Q=t-(B-T);return s=this,i=arguments,Q<=0||Q>t?(n&&(clearTimeout(n),n=null),T=B,p=e.apply(s,i),n||(s=i=null)):!n&&r.trailing!==!1&&(n=setTimeout(P,Q)),p};return D.cancel=function(){clearTimeout(n),T=0,n=s=i=null},D}function Dr(e,t,r){var n,s,i,p,T,P=function(){var B=oe()-s;t>B?n=setTimeout(P,t-B):(n=null,r||(p=e.apply(T,i)),n||(i=T=null))},D=O(function(B){return T=this,i=B,s=oe(),n||(n=setTimeout(P,t),r&&(p=e.apply(T,i))),p});return D.cancel=function(){clearTimeout(n),n=i=T=null},D}function Fr(e,t){return ee(t,e)}function Fe(e){return function(){return!e.apply(this,arguments)}}function Pr(){var e=arguments,t=e.length-1;return function(){for(var r=t,n=e[t].apply(this,arguments);r--;)n=e[r].call(this,n);return n}}function qr(e,t){return function(){if(--e<1)return t.apply(this,arguments)}}function yt(e,t){var r;return function(){return--e>0&&(r=t.apply(this,arguments)),e<=1&&(t=null),r}}var Or=ee(yt,2);function bt(e,t,r){t=V(t,r);for(var n=R(e),s,i=0,p=n.length;i<p;i++)if(s=n[i],t(e[s],s,e))return s}function wt(e){return function(t,r,n){r=V(r,n);for(var s=H(t),i=e>0?0:s-1;i>=0&&i<s;i+=e)if(r(t[i],i,t))return i;return-1}}var Pe=wt(1),At=wt(-1);function Ct(e,t,r,n){r=V(r,n,1);for(var s=r(t),i=0,p=H(e);i<p;){var T=Math.floor((i+p)/2);r(e[T])<s?i=T+1:p=T}return i}function Tt(e,t,r){return function(n,s,i){var p=0,T=H(n);if(typeof i=="number")e>0?p=i>=0?i:Math.max(i+T,p):T=i>=0?Math.min(i+1,T):i+T+1;else if(r&&i&&T)return i=r(n,s),n[i]===s?i:-1;if(s!==s)return i=t(v.call(n,p,T),$e),i>=0?i+p:-1;for(i=e>0?p:T-1;i>=0&&i<T;i+=e)if(n[i]===s)return i;return-1}}var xt=Tt(1,Pe,Ct),Mr=Tt(-1,At);function qe(e,t,r){var n=W(e)?Pe:bt,s=n(e,t,r);if(s!==void 0&&s!==-1)return e[s]}function Lr(e,t){return qe(e,ie(t))}function G(e,t,r){t=se(t,r);var n,s;if(W(e))for(n=0,s=e.length;n<s;n++)t(e[n],n,e);else{var i=R(e);for(n=0,s=i.length;n<s;n++)t(e[i[n]],i[n],e)}return e}function Z(e,t,r){t=V(t,r);for(var n=!W(e)&&R(e),s=(n||e).length,i=Array(s),p=0;p<s;p++){var T=n?n[p]:p;i[p]=t(e[T],T,e)}return i}function St(e){var t=function(r,n,s,i){var p=!W(r)&&R(r),T=(p||r).length,P=e>0?0:T-1;for(i||(s=r[p?p[P]:P],P+=e);P>=0&&P<T;P+=e){var D=p?p[P]:P;s=n(s,r[D],D,r)}return s};return function(r,n,s,i){var p=arguments.length>=3;return t(r,se(n,i,4),s,p)}}var Oe=St(1),It=St(-1);function te(e,t,r){var n=[];return t=V(t,r),G(e,function(s,i,p){t(s,i,p)&&n.push(s)}),n}function _r(e,t,r){return te(e,Fe(V(t)),r)}function Et(e,t,r){t=V(t,r);for(var n=!W(e)&&R(e),s=(n||e).length,i=0;i<s;i++){var p=n?n[i]:i;if(!t(e[p],p,e))return!1}return!0}function Nt(e,t,r){t=V(t,r);for(var n=!W(e)&&R(e),s=(n||e).length,i=0;i<s;i++){var p=n?n[i]:i;if(t(e[p],p,e))return!0}return!1}function Y(e,t,r,n){return W(e)||(e=j(e)),(typeof r!="number"||n)&&(r=0),xt(e,t,r)>=0}var Rr=O(function(e,t,r){var n,s;return z(t)?s=t:(t=ae(t),n=t.slice(0,-1),t=t[t.length-1]),Z(e,function(i){var p=s;if(!p){if(n&&n.length&&(i=xe(i,n)),i==null)return;p=i[t]}return p==null?p:p.apply(i,r)})});function Me(e,t){return Z(e,Ie(t))}function Br(e,t){return te(e,ie(t))}function Dt(e,t,r){var n=-1/0,s=-1/0,i,p;if(t==null||typeof t=="number"&&typeof e[0]!="object"&&e!=null){e=W(e)?e:j(e);for(var T=0,P=e.length;T<P;T++)i=e[T],i!=null&&i>n&&(n=i)}else t=V(t,r),G(e,function(D,B,Q){p=t(D,B,Q),(p>s||p===-1/0&&n===-1/0)&&(n=D,s=p)});return n}function kr(e,t,r){var n=1/0,s=1/0,i,p;if(t==null||typeof t=="number"&&typeof e[0]!="object"&&e!=null){e=W(e)?e:j(e);for(var T=0,P=e.length;T<P;T++)i=e[T],i!=null&&i<n&&(n=i)}else t=V(t,r),G(e,function(D,B,Q){p=t(D,B,Q),(p<s||p===1/0&&n===1/0)&&(n=D,s=p)});return n}var zr=/[^\ud800-\udfff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff]/g;function Ft(e){return e?K(e)?v.call(e):pe(e)?e.match(zr):W(e)?Z(e,Se):j(e):[]}function Pt(e,t,r){if(t==null||r)return W(e)||(e=j(e)),e[Ne(e.length-1)];var n=Ft(e),s=H(n);t=Math.max(Math.min(t,s),0);for(var i=s-1,p=0;p<t;p++){var T=Ne(p,i),P=n[p];n[p]=n[T],n[T]=P}return n.slice(0,t)}function Qr(e){return Pt(e,1/0)}function Ur(e,t,r){var n=0;return t=V(t,r),Me(Z(e,function(s,i,p){return{value:s,index:n++,criteria:t(s,i,p)}}).sort(function(s,i){var p=s.criteria,T=i.criteria;if(p!==T){if(p>T||p===void 0)return 1;if(p<T||T===void 0)return-1}return s.index-i.index}),"value")}function fe(e,t){return function(r,n,s){var i=t?[[],[]]:{};return n=V(n,s),G(r,function(p,T){var P=n(p,T,r);e(i,p,P)}),i}}var Hr=fe(function(e,t,r){J(e,r)?e[r].push(t):e[r]=[t]}),Vr=fe(function(e,t,r){e[r]=t}),Wr=fe(function(e,t,r){J(e,r)?e[r]++:e[r]=1}),$r=fe(function(e,t,r){e[r?0:1].push(t)},!0);function Gr(e){return e==null?0:W(e)?e.length:R(e).length}function Yr(e,t,r){return t in r}var qt=O(function(e,t){var r={},n=t[0];if(e==null)return r;z(n)?(t.length>1&&(n=se(n,t[1])),t=ne(e)):(n=Yr,t=X(t,!1,!1),e=Object(e));for(var s=0,i=t.length;s<i;s++){var p=t[s],T=e[p];n(T,p,e)&&(r[p]=T)}return r}),Jr=O(function(e,t){var r=t[0],n;return z(r)?(r=Fe(r),t.length>1&&(n=t[1])):(t=Z(X(t,!1,!1),String),r=function(s,i){return!Y(t,i)}),qt(e,r,n)});function Ot(e,t,r){return v.call(e,0,Math.max(0,e.length-(t==null||r?1:t)))}function Le(e,t,r){return e==null||e.length<1?t==null||r?void 0:[]:t==null||r?e[0]:Ot(e,e.length-t)}function he(e,t,r){return v.call(e,t==null||r?1:t)}function Kr(e,t,r){return e==null||e.length<1?t==null||r?void 0:[]:t==null||r?e[e.length-1]:he(e,Math.max(0,e.length-t))}function Xr(e){return te(e,Boolean)}function Zr(e,t){return X(e,t,!1)}var Mt=O(function(e,t){return t=X(t,!0,!0),te(e,function(r){return!Y(t,r)})}),jr=O(function(e,t){return Mt(e,t)});function _e(e,t,r,n){re(t)||(n=r,r=t,t=!1),r!=null&&(r=V(r,n));for(var s=[],i=[],p=0,T=H(e);p<T;p++){var P=e[p],D=r?r(P,p,e):P;t&&!r?((!p||i!==D)&&s.push(P),i=D):r?Y(i,D)||(i.push(D),s.push(P)):Y(s,P)||s.push(P)}return s}var en=O(function(e){return _e(X(e,!0,!0))});function tn(e){for(var t=[],r=arguments.length,n=0,s=H(e);n<s;n++){var i=e[n];if(!Y(t,i)){var p;for(p=1;p<r&&Y(arguments[p],i);p++);p===r&&t.push(i)}}return t}function Re(e){for(var t=e&&Dt(e,H).length||0,r=Array(t),n=0;n<t;n++)r[n]=Me(e,n);return r}var rn=O(Re);function nn(e,t){for(var r={},n=0,s=H(e);n<s;n++)t?r[e[n]]=t[n]:r[e[n][0]]=e[n][1];return r}function an(e,t,r){t==null&&(t=e||0,e=0),r||(r=t<e?-1:1);for(var n=Math.max(Math.ceil((t-e)/r),0),s=Array(n),i=0;i<n;i++,e+=r)s[i]=e;return s}function sn(e,t){if(t==null||t<1)return[];for(var r=[],n=0,s=e.length;n<s;)r.push(v.call(e,n,n+=t));return r}function Be(e,t){return e._chain?L(t).chain():t}function Lt(e){return G(Ce(e),function(t){var r=L[t]=e[t];L.prototype[t]=function(){var n=[this._wrapped];return f.apply(n,arguments),Be(this,r.apply(L,n))}}),L}G(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var t=c[e];L.prototype[e]=function(){var r=this._wrapped;return r!=null&&(t.apply(r,arguments),(e==="shift"||e==="splice")&&r.length===0&&delete r[0]),Be(this,r)}}),G(["concat","join","slice"],function(e){var t=c[e];L.prototype[e]=function(){var r=this._wrapped;return r!=null&&(r=t.apply(r,arguments)),Be(this,r)}});var on={__proto__:null,VERSION:h,restArguments:O,isObject:_,isNull:$,isUndefined:U,isBoolean:re,isElement:le,isString:pe,isNumber:ze,isDate:Bt,isRegExp:kt,isError:zt,isSymbol:Qe,isArrayBuffer:Ue,isDataView:ce,isArray:K,isFunction:z,isArguments:ve,isFinite:Vt,isNaN:$e,isTypedArray:Ke,isEmpty:Jt,isMatch:Ze,isEqual:Kt,isMap:jt,isWeakMap:er,isSet:tr,isWeakSet:rr,keys:R,allKeys:ne,values:j,pairs:nr,invert:it,functions:Ce,methods:Ce,extend:st,extendOwn:de,assign:de,defaults:ot,create:ir,clone:sr,tap:or,get:ut,has:lr,mapObject:cr,identity:Se,constant:Ge,noop:ft,toPath:ct,property:Ie,propertyOf:ur,matcher:ie,matches:ie,times:dr,random:Ne,now:oe,escape:fr,unescape:pr,templateSettings:mr,template:wr,result:Ar,uniqueId:Tr,chain:xr,iteratee:Ee,partial:ee,bind:gt,bindAll:Sr,memoize:Ir,delay:vt,defer:Er,throttle:Nr,debounce:Dr,wrap:Fr,negate:Fe,compose:Pr,after:qr,before:yt,once:Or,findKey:bt,findIndex:Pe,findLastIndex:At,sortedIndex:Ct,indexOf:xt,lastIndexOf:Mr,find:qe,detect:qe,findWhere:Lr,each:G,forEach:G,map:Z,collect:Z,reduce:Oe,foldl:Oe,inject:Oe,reduceRight:It,foldr:It,filter:te,select:te,reject:_r,every:Et,all:Et,some:Nt,any:Nt,contains:Y,includes:Y,include:Y,invoke:Rr,pluck:Me,where:Br,max:Dt,min:kr,shuffle:Qr,sample:Pt,sortBy:Ur,groupBy:Hr,indexBy:Vr,countBy:Wr,partition:$r,toArray:Ft,size:Gr,pick:qt,omit:Jr,first:Le,head:Le,take:Le,initial:Ot,last:Kr,rest:he,tail:he,drop:he,compact:Xr,flatten:Zr,without:jr,uniq:_e,unique:_e,union:en,intersection:tn,difference:Mt,unzip:Re,transpose:Re,zip:rn,object:nn,range:an,chunk:sn,mixin:Lt,default:L},ke=Lt(on);return ke._=ke,ke})}).call(this)}).call(this,typeof global<"u"?global:typeof self<"u"?self:typeof window<"u"?window:{})},{}],26:[function(I,N,M){N.exports=[{term:"Accepts healthy volunteers",definition:'A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span> that indicates whether people who do not have the <span class="term" data-term="Condition/disease">condition/disease</span> being studied can participate in that clinical study.'},{term:"Active comparator arm",definition:'An <span class="term" data-term="Arm Type">arm type</span> in which a group of participants receives an <span class="term" data-term="Intervention/treatment">intervention/treatment</span> considered to be effective (or active) by health care providers.'},{term:"Adverse event",definition:'An unfavorable change in the health of a participant, including abnormal laboratory findings, that happens during a clinical study or within a certain amount of time after the study has ended. This change may or may not be caused by the <span class="term" data-term="Intervention/treatment">intervention/treatment</span> being studied.'},{term:"Age or age group",definition:'A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span> that indicates the age a person must be to participate in a clinical study. This may be indicated by a specific age or the following age groups:<p>The age groups are: <ul><li>Child (birth-17)</li><li>Adult (18-64)</li><li style="margin-bottom:-.4em;">Older Adult (65+)</li></ul>'},{term:"Allocation",definition:'A method used to assign participants to an arm of a clinical study. The types of allocation are <span class="term" data-term="Randomized allocation">randomized allocation</span> and nonrandomized.'},{term:"Arm",definition:`A group or subgroup of participants in a clinical trial that receives a specific <span class="term" data-term="Intervention/treatment">intervention/treatment</span>, or no intervention, according to the trial's <span class="term" data-term="Protocol">protocol</span>.`},{term:"Arm type",definition:'A general description of the clinical trial arm. It identifies the role of the intervention that participants receive. Types of arms include <span class="term" data-term="Experimental arm">experimental arm</span>, <span class="term" data-term="Active comparator arm">active comparator arm</span>, <span class="term" data-term="placebo comparator arm">placebo comparator arm</span>, <span class="term" data-term="sham comparator arm">sham comparator arm</span>, and <span class="term" data-term="no intervention arm">no intervention arm</span>.'},{term:"Baseline characteristics",definition:"Data collected at the beginning of a clinical study for all participants and for each arm or comparison group. These data include demographics, such as age, sex/gender, race and ethnicity, and study-specific measures (for example, systolic blood pressure, prior antidepressant treatment)."},{term:"Certain agreements",definition:'Information required by the <span class="term" data-term="Food and Drug Administration Amendments Act of 2007, Section 801 (FDAAA 801)">Food and Drug Administration Amendments Act of 2007</span>. In general, this is a description of any agreement between the sponsor of a clinical study and the <span class="term" data-term="Principal investigator (PI)">principal investigator</span> (PI) that does not allow the PI to discuss the results of the study or publish the study results in a scientific or academic journal after the study is completed.'},{term:"City and distance",definition:'In the search feature, the City field is used to find clinical studies with locations in a specific city. The Distance field is used to find studies with locations within the specified distance from a city in number of miles. For example, if you choose Illinois as the <span class="term" data-term="State">state</span>, identifying "Chicago" as the city and "100 miles" as the distance will find all studies listing a location within 100 miles of Chicago.'},{term:"Clinical study",definition:'A research study involving human volunteers (also called participants) that is intended to add to medical knowledge. There are two types of clinical studies: <span class="term" data-term="Interventional study (clinical trial)">interventional studies</span> (also called clinical trials) and <span class="term" data-term="Observational Study">observational studies</span>.'},{term:"Clinical trial",definition:'Another name for an <span class="term" data-term="Interventional Study (clinical trial)">interventional study</span>.'},{term:"ClinicalTrials.gov identifier (NCT number)",definition:'The unique identification code given to each clinical study upon <span class="term" data-term="Registration">registration</span> at ClinicalTrials.gov. The format is "NCT" followed by an 8-digit number (for example, NCT00000419).'},{term:"Collaborator",definition:'An organization other than the <span class="term" data-term="Sponsor">sponsor</span> that provides support for a clinical study. This support may include activities related to funding, design, implementation, data analysis, or reporting.'},{term:"Condition/disease",definition:"The disease, disorder, syndrome, illness, or injury that is being studied. On ClinicalTrials.gov, conditions may also include other health-related issues, such as lifespan, quality of life, and health risks."},{term:"Contact",definition:"The name and contact information for the person who can answer enrollment questions for a clinical study. Each location where the study is being conducted may also have a specific contact, who may be better able to answer those questions."},{term:"Country",definition:'In the search feature, the Country field is used to find clinical studies with locations in a specific country. For example,  if you choose the United States, you can then narrow your search by selecting a <span class="term" data-term="State">state</span> and identifying a <span class="term" data-term="City and distance">city and distance</span>.'},{term:"Cross-over assignment",definition:'A type of <span class="term" data-term="Intervention model">intervention model</span> describing a clinical trial in which groups of participants receive two or more interventions in a specific order. For example, two-by-two cross-over assignment involves two groups of participants. One group receives drug A during the initial phase of the trial, followed by drug B during a later phase. The other group receives drug B during the initial phase, followed by drug A. So during the trial, participants "cross over" to the other drug. All participants receive drug A and drug B at some point during the trial but in a different order, depending on the group to which they are assigned.'},{term:"Data Monitoring Committee (DMC)",definition:'A group of independent scientists who monitor the safety and scientific integrity of a <span class="term" data-term="Clinical trial">clinical trial</span>. The DMC can recommend to the sponsor that the trial be stopped if it is not effective, is harming participants, or is unlikely to serve its scientific purpose. Members are chosen based on the scientific skills and knowledge needed to monitor the particular trial. Also called a data safety and monitoring board, or DSMB.'},{term:"Eligibility criteria",definition:'The key requirements that people who want to participate in a clinical study must meet or the characteristics they must have. Eligibility criteria consist of both <span class="term" data-term="inclusion criteria">inclusion criteria</span> (which are required for a person to participate in the study) and <span class="term" data-term="exclusion criteria">exclusion criteria</span> (which prevent a person from participating). Types of eligibility criteria include whether a study <span class="term" data-term="Accepts healthy volunteers"> accepts healthy volunteers</span>, has <span class="term" data-term="Age or age group">age or age group</span> requirements, or is limited by <span class="term" data-term="Sex">sex</span>.'},{term:"Enrollment",definition:'The number of participants in a clinical study. The "estimated" enrollment is the target number of participants that the researchers need for the study.'},{term:"Exclusion criteria",definition:'A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span>. These are reasons that a person is not allowed to participate in a clinical study.'},{term:"Expanded access",definition:'A way for patients with serious diseases or conditions who cannot participate in a clinical trial to gain access to a medical product that has not been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">U.S. Food and Drug Administration (FDA)</span>. Also called compassionate use.  There are different <span class="term" data-term="Expanded access type">expanded access types</span>. <p style="margin-bottom:-.4em;">For more information, see FDA <a href="https://www.fda.gov/news-events/public-health-focus/expanded-access" class="usa-link usa-link--external" title="Expanded Access: Information for Patients (opens in a new tab)" aria-label="Expanded Access: Information for Patients (opens in a new tab)" target="_blank" rel="noopener noreferrer">Expanded Access: Information for Patients</a>.</p>'},{term:"Experimental arm",definition:'An <span class="term" data-term="Arm type">arm type</span> in which a group of participants receives the <span class="term" data-term="Intervention/treatment">intervention/treatment</span> that is the focus of the clinical trial.'},{term:"Factorial assignment",definition:'A type of <span class="term" data-term="Intervention model">intervention model</span> describing a clinical trial in which groups of participants receive one of several combinations of interventions. For example, two-by-two factorial assignment involves four groups of participants. Each group receives one of the following pairs of interventions: (1) drug A and drug B, (2) drug A and a placebo, (3) a placebo and drug B, or (4) a placebo and a placebo. So during the trial, all possible combinations of the two drugs (A and B) and the placebos are given to different groups of participants.'},{term:"First posted",definition:'The date on which the study record was first available on ClinicalTrials.gov after National Library of Medicine (NLM) <a href="/submit-studies/prs-help/protocol-registration-quality-control-review-criteria" class="usa-link">quality control (QC) review</a> has concluded. There is typically a delay of a few days between the date the study sponsor or investigator submitted the study record and the first posted date.'},{term:"First submitted",definition:"The date on which the study sponsor or investigator first submitted a study record to ClinicalTrials.gov. There is typically a delay of a few days between the first submitted date and the record's availability on ClinicalTrials.gov (the first posted date)."},{term:"First submitted that met QC criteria",definition:`The date on which the study sponsor or investigator first submits a <span class="term" data-term="Study record">study record</span> that is consistent with National Library of Medicine (NLM) <span class="term" data-term="quality control (QC) review">quality control (QC) review</span> criteria. The sponsor or investigator may need to revise and submit a study record one or more times before NLM's QC review criteria are met. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria.`},{term:"U.S. Food and Drug Administration (FDA)",definition:"An agency within the U.S. Department of Health and Human Services. The FDA is responsible for protecting the public health by making sure that human and veterinary drugs, vaccines and other biological products, medical devices, the Nation's food supply, cosmetics, dietary supplements, and products that give off radiation are safe, effective, and secure."},{term:"Food and Drug Administration Amendments Act of 2007, Section 801 (FDAAA 801)",definition:'U.S. Public Law 110-85, which was enacted on September 27, 2007. Section 801 of FDAAA amends Section 402 of the U.S. Public Health Service Act to expand ClinicalTrials.gov and create a clinical study <span class="term" data-term="Results database">results database</span>. For more information on FDAAA 801, see the <a href="/policy/reporting-requirements#final-rule" class="usa-link">Clinical Trial Reporting Requirements</a> page on this site.'},{term:"Funder type",definition:'Describes the organization that provides funding or support for a clinical study. This support may include activities related to funding, design, implementation, data analysis, or reporting. Organizations listed as <span class="term" data-term="Sponsor">sponsors</span> and <span class="term" data-term="Collaborator">collaborators</span> for a study are considered the funders of the study. ClinicalTrials.gov refers to four types of funders: <ul><li>U.S. National Institutes of Health</li><li>Other U.S. Federal agencies (for example, Food and Drug Administration, Centers for Disease Control and Prevention, or U.S. Department of Veterans Affairs)</li><li>Industry (for example: pharmaceutical and device companies)</li><li>All others (including individuals, universities, and community-based organizations)</li></ul>'},{term:"Gender-based eligibility",definition:`A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span> that indicates whether eligibility to participate in a clinical study is based on a person's self-representation of gender identity. Gender identity refers to a person's own sense of gender, which may or may not be the same as their biological <span class="term" data-term="sex">sex</span>.`},{term:"Group/cohort",definition:'A group or subgroup of participants in an <span class="term" data-term="observational study">observational study</span> that is assessed for biomedical or health outcomes.'},{term:"Human subjects protection review board",definition:`A group of people who review, approve, and monitor the clinical study's <span class="term" data-term="Protocol">protocol</span>. Their role is to protect the rights and welfare of people participating in a study (referred to as human research subjects), such as reviewing the <span class="term" data-term="Informed consent form (ICF)">informed consent form</span>. The group typically includes people with varying backgrounds, including a community member, to make sure that research activities conducted by an organization are completely and adequately reviewed. Also called an institutional review board, or IRB, or an ethics committee.<p>For more information, see <a href="/study-basics/learn-about-studies#q3" class="usa-link">Who can join clinical research?</a> on this site.</p>`},{term:"Inclusion criteria",definition:'A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span>. These are the reasons that a person is allowed to participate in a clinical study.'},{term:"Informed consent",definition:'A process used by researchers to communicate to potential and enrolled participants the risks and potential benefits of participating in a clinical study. <p>For more information, see <a href="/study-basics/learn-about-studies#q3" class="usa-link">Who can join clinical research?</a> on this site.</p>'},{term:"Intervention/treatment",definition:"A process or action that is the focus of a clinical study. Interventions include drugs, medical devices, procedures, vaccines, and other products that are either investigational or already available. Interventions can also include noninvasive approaches, such as education or modifying diet and exercise."},{term:"Intervention model",definition:'The general design of the strategy for assigning interventions to participants in a clinical study. Types of intervention models include: <span class="term" data-term="Single group assignment">single group assignment</span>, <span class="term" data-term="Parallel assignment">parallel assignment</span>, <span class="term" data-term="cross-over assignment">cross-over assignment</span>, and <span class="term" data-term="factorial assignment">factorial assignment</span>.'},{term:"Interventional study (clinical trial)",definition:`A type of <span class="term" data-term="Clinical study">clinical study</span> in which participants are assigned to groups that receive one or more <span class="term" data-term="Intervention/treatment">intervention/treatment</span> (or no intervention) so that researchers can evaluate the effects of the interventions on biomedical or health-related outcomes. The assignments are determined by the study's <span class="term" data-term="Protocol">protocol</span>. Participants may receive diagnostic, therapeutic, or other types of interventions.`},{term:"Investigator",definition:'A researcher involved in a clinical study. Related terms include site principal investigator, site sub-investigator, study chair, study director, and study <span class="term" data-term="Principal investigator (PI)">principal investigator</span>.'},{term:"Last update posted",definition:`The most recent date on which changes to a <span class="term" data-term="Study record">study record</span> were made available on ClinicalTrials.gov. There may be a delay between when the changes were submitted to ClinicalTrials.gov by the study's sponsor or investigator (the <span class="term" data-term="Last update submitted">last update submitted</span> date) and the last update posted date.`},{term:"Last update submitted",definition:'The most recent date on which the study sponsor or investigator submitted changes to a <span class="term" data-term="Study record">study record</span> to ClinicalTrials.gov. There is typically a delay of a few days between the last update submitted date and when the date changes are posted on ClinicalTrials.gov (the <span class="term" data-term="Last update posted">last update posted</span> date).'},{term:"Last update submitted that met QC criteria",definition:'The most recent date on which the study sponsor or investigator submitted changes to a <span class="term" data-term="study record">study record</span> that are consistent with National Library of Medicine (NLM) <span class="term" data-term="quality control (QC) review">quality control (QC) review</span> criteria. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria.'},{term:"Last verified",definition:`The most recent date on which the study sponsor or investigator confirmed the information about a clinical study on ClinicalTrials.gov as accurate and current. If a study with a <span class="term" data-term="Recruitment status">recruitment status</span> of recruiting; not yet recruiting; or active, not recruiting has not been confirmed within the past 2 years, the study's <span class="term" data-term="Recruitment status">recruitment status</span> is shown as <span class="term" data-term="Unknown">unknown</span>.`},{term:"Listed location countries",definition:'Countries in which research facilities for a study are located. A country is listed only once, even if there is more than one facility in the country. The list includes all countries as of the <span class="term" data-term="Last update submitted">last update submitted</span> date; any country for which all facilities were removed from the <span class="term" data-term="Study record">study record</span> are listed under <span class="term" data-term="removed location countries">removed location countries</span>.'},{term:"Location terms",definition:'In the search feature, the Location terms field is used to narrow a search by location-related terms other than Country, State, and City or distance. For example, you may enter a specific facility name (such as National Institutes of Health Clinical Center) or a part of a facility name (such as Veteran for studies listing Veterans Hospital or Veteran Affairs in the facility name). Note: Not all <span class="term" data-term="Study record">study records</span> include this level of detail about locations.'},{term:"Masking",definition:"A clinical trial design strategy in which one or more parties involved in the trial, such as the investigator or participants, do not know which participants have been assigned which interventions. Types of masking include: open label, single blind masking, and double-blind masking."},{term:"NCT number",definition:'A unique identification code given to each clinical study record registered on ClinicalTrials.gov. The format is "NCT" followed by an 8-digit number (for example, NCT00000419). Also called the <span class="term" data-term="ClinicalTrials.gov identifier (NCT number)">ClinicalTrials.gov identifier</span>.'},{term:"No intervention arm",definition:'An <span class="term" data-term="Arm type">arm type</span> in which a group of participants does not receive any <span class="term" data-term="Intervention/treatment">intervention/treatment</span> during the clinical trial.'},{term:"Observational study",definition:'A type of <span class="term" data-term="Clinical study">clinical study</span> in which participants are identified as belonging to study groups and are assessed for biomedical or health outcomes. Participants may receive diagnostic, therapeutic, or other types of interventions, but the investigator does not assign participants to a specific <span class="term" data-term="Intervention/treatment">interventions/treatment</span>.<p>A <span class="term" data-term="Patient registry">patient registry</span> is a type of observational study.</p>'},{term:"Observational study model",definition:'The general design of the strategy for identifying and following up with participants during an <span class="term" data-term="Observational study">observational study</span>. Types of observational study models include cohort, case-control, case-only, case-cross-over, ecologic or community studies, family-based, and other.'},{term:"Other adverse event",definition:'An <span class="term" data-term="Adverse event">adverse event</span> that is not a <span class="term" data-term="serious adverse event">serious adverse event</span>, meaning that it does not result in death, is not life-threatening, does not require inpatient hospitalization or extend a current hospital stay, does not result in an ongoing or significant incapacity or interfere substantially with normal life functions, and does not cause a congenital anomaly or birth defect; it also does not put the participant in danger and does not require medical or surgical intervention to prevent one of the results listed above.'},{term:"Other study IDs",definition:`Identifiers or ID numbers other than the <span class="term" data-term="NCT number">NCT number</span> that are assigned to a clinical study by the study's sponsor, funders, or others. These numbers may include unique identifiers from other trial registries and National Institutes of Health grant numbers.`},{term:"Other terms",definition:'In the search feature, the Other terms field is used to narrow a search. For example, you may enter the name of a drug or the NCT number of a clinical study to limit the search to <span class="term" data-term="Study record">study records</span> that contain these words.'},{term:"Outcome measure",definition:'For <span class="term" data-term="Clinical trial">clinical trials</span>, a planned measurement described in the protocol that is used to determine the effect of an <span class="term" data-term="Intervention/treatment">intervention/treatment</span> on participants. For <span class="term" data-term="Observational study">observational studies</span>, a measurement or observation that is used to describe patterns of diseases or traits, or associations with exposures, risk factors, or treatment. Types of outcome measures include <span class="term" data-term="Primary outcome measure">primary outcome measure</span> and <span class="term" data-term="Secondary outcome measure">secondary outcome measure</span>.'},{term:"Parallel assignment",definition:'A type of <span class="term" data-term="Intervention model">intervention model</span> describing a clinical trial in which two or more groups of participants receive different interventions. For example, a two-arm parallel assignment involves two groups of participants. One group receives drug A, and the other group receives drug B. So during the trial, participants in one group receive drug A "in parallel" to participants in the other group, who receive drug B.'},{term:"Participant flow",definition:'A summary of the progress of participants through each stage of a clinical study, by study <span class="term" data-term="Arm">arm</span> or <span class="term" data-term="group/cohort">group/cohort</span>. This includes the number of participants who started, completed, and dropped out of the study.'},{term:"Phase",definition:`The stage of a clinical trial studying a drug or biological product, based on definitions developed by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">U.S. Food and Drug Administration (FDA)</span>. The phase is based on the study's objective, the number of participants, and other characteristics. There are five phases: <span class="term" data-term="Early Phase 1 (formerly listed as Phase 0)">Early Phase 1 (formerly listed as Phase 0)</span>, <span class="term" data-term="Phase 1">Phase 1</span>, <span class="term" data-term="Phase 2">Phase 2</span>, <span class="term" data-term="Phase 3">Phase 3</span>, and <span class="term" data-term="Phase 4">Phase 4</span>. <span class="term" data-term="phase not applicable">Not Applicable</span> is used to describe trials without FDA-defined phases, including trials of devices or behavioral interventions.`},{term:"Early Phase 1 (formerly listed as Phase 0)",definition:'A <span class="term" data-term="phase">phase</span> of research used to describe exploratory trials conducted before traditional <span class="term" data-term="phase 1">phase 1</span> trials to investigate how or whether a drug affects the body. They involve very limited human exposure to the drug and have no therapeutic or diagnostic goals (for example, screening studies, microdose studies).'},{term:"Phase 1",definition:`A <span class="term" data-term="phase">phase</span> of research to describe clinical trials that focus on the safety of a drug. They are usually conducted with healthy volunteers, and the goal is to determine the drug's most frequent and <span class="term" data-term="Serious adverse event">serious adverse events</span> and, often, how the drug is broken down and excreted by the body. These trials usually involve a small number of participants.`},{term:"Phase 2",definition:`A <span class="term" data-term="phase">phase</span> of research to describe clinical trials that gather preliminary data on whether a drug works in people who have a certain <span class="term" data-term="Condition/disease">condition/disease</span> (that is, the drug's effectiveness). For example, participants receiving the drug may be compared to similar participants receiving a different treatment, usually an inactive substance (called a <span class="term" data-term="Placebo">placebo</span>) or a different drug. Safety continues to be evaluated, and short-term <span class="term" data-term="Adverse event">adverse events</span> are studied.`},{term:"Phase 3",definition:`A <span class="term" data-term="phase">phase</span> of research to describe clinical trials that gather more information about a drug's safety and effectiveness by studying different populations and different dosages and by using the drug in combination with other drugs. These studies typically involve more participants.`},{term:"Phase 4",definition:`A <span class="term" data-term="phase">phase</span> of research to describe clinical trials occurring after FDA has approved a drug for marketing. They include postmarket requirement and commitment studies that are required of or agreed to by the study sponsor. These trials gather additional information about a drug's safety, efficacy, or optimal use.`},{term:"Placebo",definition:'An inactive substance or treatment that looks the same as, and is given in the same way as, an active drug or <span class="term" data-term="Intervention/treatment">intervention/treatment</span> being studied.'},{term:"Placebo comparator arm",definition:'An <span class="term" data-term="Arm type">arm type</span> in which a group of participants receives a <span class="term" data-term="Placebo">placebo</span> during a clinical trial.'},{term:"Primary completion date",definition:'The date on which the last participant in a clinical study was examined or received an intervention to collect final data for the <span class="term" data-term="Primary outcome measure">primary outcome measure</span>. Whether the clinical study ended according to the <span class="term" data-term="Protocol">protocol</span> or was terminated does not affect this date. For clinical studies with more than one primary outcome measure with different completion dates, this term refers to the date on which data collection is completed for all the primary outcome measures. The "estimated" primary completion date is the date that the researchers think will be the primary completion date for the study. '},{term:"Primary outcome measure",definition:`In a clinical study's <span class="term" data-term="Protocol">protocol</span>, the planned outcome measure that is the most important for evaluating the effect of an <span class="term" data-term="Intervention/treatment">intervention/treatment</span>. Most clinical studies have one primary outcome measure, but some have more than one.`},{term:"Primary purpose",definition:'The main reason for the <span class="term" data-term="Clinical trial">clinical trial</span>. The types of primary purpose are: treatment, prevention, diagnostic, supportive care, screening, health services research, basic science, and other.'},{term:"Principal investigator (PI)",definition:"The person who is responsible for the scientific and technical direction of the entire clinical study."},{term:"Protocol",definition:"The written description of a clinical study. It includes the study's objectives, design, and methods. It may also include relevant scientific background and statistical information."},{term:"Quality control (QC) review",definition:'National Library of Medicine (NLM) staff perform a limited review of submitted <span class="term" data-term="study record">study records</span> for apparent errors, deficiencies, or inconsistencies. NLM staff identify potential major and advisory issues and provide comments directly to the study sponsor or investigator. Major issues identified in QC review must be addressed or corrected (see <span class="term" data-term="First submitted that met QC criteria">First submitted that met QC criteria</span> and <span class="term" data-term="Results first submitted that met QC criteria">Results first submitted that met QC criteria</span>). Advisory issues are suggestions to help improve the clarity of the record. NLM staff do not verify the scientific validity or relevance of the submitted information. The study sponsor or investigator is responsible for ensuring that the studies follow all applicable laws and regulations.'},{term:"Randomized allocation",definition:'A type of <span class="term" data-term="Allocation">allocation</span> strategy in which participants are assigned to the <span class="term" data-term="Arm">arms</span> of a clinical trial by chance.'},{term:"Status",definition:'Indicates the current <span class="term" data-term="Recruitment status">recruitment status</span> or the <span class="term" data-term="Expanded access Status">expanded access status</span>.'},{term:"Recruitment status",definition:`<ul><li><strong>Not yet recruiting:</strong> The study has not started recruiting participants.</li><li><strong>Recruiting:</strong> The study is currently recruiting participants.</li><li><strong>Enrolling by invitation:</strong> The study is selecting its participants from a population, or group of people, decided on by the researchers in advance. These studies are not open to everyone who meets the eligibility criteria but only to people in that particular population, who are specifically invited to participate.</li><li><strong>Active, not recruiting:</strong> The study is ongoing, and participants are receiving an intervention or being examined, but potential participants are not currently being recruited or enrolled.</li><li><strong>Suspended:</strong> The study has stopped early but may start again.</li><li><strong>Terminated:</strong> The study has stopped early and will not start again. Participants are no longer being examined or treated.</li><li><strong>Completed:</strong> The study has ended normally, and participants are no longer being examined or treated (that is, the last participant's last visit has occurred).</li><li><strong>Withdrawn:</strong> The study stopped early, before enrolling its first participant.</li><li><strong>Unknown:</strong> A study on ClinicalTrials.gov whose last known status was recruiting; not yet recruiting; or active, not recruiting but that has passed its completion date, and the status has not been <span class="term" data-term="Last verified">last verified</span> within the past 2 years.</li></ul>`},{term:"Expanded access status",definition:'<ul><li><strong>Available:</strong> <span class="term" data-term="Expanded access">Expanded access</span> is currently available for this investigational treatment, and patients who are not participants in the clinical study may be able to gain access to the drug, biologic, or medical device being studied.</li><li><strong>No longer available:</strong> <span class="term" data-term="Expanded access">Expanded access</span> was available for this intervention previously but is not currently available and will not be available in the future.</li><li><strong>Temporarily not available:</strong> <span class="term" data-term="Expanded access">Expanded access</span> is not currently available for this intervention but is expected to be available in the future.</li><li><strong>Approved for marketing:</strong> The intervention has been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)"> U.S. Food and Drug Administration</span> for use by the public.</li></ul>'},{term:"Registration",definition:'The process of submitting and updating summary information about a clinical study and its <span class="term" data-term="Protocol">protocol</span>, from its beginning to end, to a structured, public Web-based <span class="term" data-term="Study Registry">study registry</span> that is accessible to the public, such as ClinicalTrials.gov.'},{term:"Removed location countries",definition:'Countries that appeared under <span class="term" data-term="Listed location countries">listed location countries</span> but were removed from the <span class="term" data-term="study record">study record</span> by the sponsor or investigator.'},{term:"Reporting group",definition:"A grouping of participants in a clinical study that is used for summarizing the data collected during the study. This grouping may be the same as or different from a study arm or group."},{term:"Responsible party",definition:"The person responsible for submitting information about a clinical study to ClinicalTrials.gov and updating that information. Usually the study sponsor or investigator."},{term:"Results database",definition:'A structured online system, such as the ClinicalTrials.gov results database, that provides the public with access to registration and summary results information for completed or terminated clinical studies. A study with results available on ClinicalTrials.gov is described as having the results "posted." <p><strong>Note:</strong> The ClinicalTrials.gov results database became available in September 2008. Older studies are unlikely to have results available in the database.</p>'},{term:"Results first posted",definition:'The date on which summary results information was first available on ClinicalTrials.gov after National Library of Medicine (NLM) <a href="submit-studies/prs-help/results-quality-control-review-criteria" class="usa-link">quality control (QC) review</a> has concluded. There is typically a delay between the date the study sponsor or investigator first submits summary results information (the results first submitted date) and the results first posted date. Some results information may be available at an earlier date if <span class="term" data-term="Results first posted with QC comments">Results First Posted with QC Comments</span>.'},{term:"Results first submitted",definition:'The date on which the study sponsor or investigator first submits a <span class="term" data-term="Study record">study record</span> with summary results information. There is typically a delay between the results first submitted date and when summary results information becomes available on ClinicalTrials.gov (the <span class="term" data-term="Results first posted">results first posted</span> date).'},{term:"Results first submitted that met QC criteria",definition:`The date on which the study sponsor or investigator first submits a <span class="term" data-term="study record">study record</span> with summary results information that is consistent with National Library of Medicine (NLM) <span class="term" data-term="quality control (QC) review">quality control (QC) review</span> criteria. The sponsor or investigator may need to revise and submit results information one or more times before NLM's QC review criteria are met. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria.`},{term:"Results first posted with QC comments",definition:'The date on which summary results information was first available on ClinicalTrials.gov with quality control review comments from the National Library of Medicine (NLM) identifying major issues that must be addressed by the sponsor or investigator. As of January 1, 2020, initial results submissions for applicable clinical trials (ACTs) that do not meet <a href="/submit-studies/prs-help/results-quality-control-review-criteria" class="usa-link">quality control review criteria</a> will be publicly posted on ClinicalTrials.gov with brief standardized major comments. Accordingly, the Results First Posted with QC Comments date may be earlier than the Results First Posted date for an ACT with summary results information that is not consistent with NLM quality control review criteria.'},{term:"Results submitted to ClinicalTrials.gov",definition:'Indicates that the study sponsor or investigator has submitted summary results information for a clinical study to ClinicalTrials.gov but the <span class="term" data-term="quality control (QC) review">quality control (QC) review</span> process has not concluded.<p>The results submitted date indicates when the study sponsor or investigator first submitted summary results information or submitted changes to summary results information. Submissions with changes are typically in response to  QC review comments from the National Library of Medicine (NLM). If there is a date listed for results submitted to ClinicalTrials.gov, but there is not a subsequent date listed for <span class="term" data-term="Results returned after quality control review">results returned after quality control review</span>, this means that the submission is pending review by NLM.'},{term:"Results returned after quality control review",definition:'The date on which the National Library of Medicine provided <span class="term" data-term="Quality control (QC) review">quality control (QC) review</span> comments to the study sponsor or investigator. The sponsor or investigator must address major issues identified in the review comments. If there is a date listed for results returned after quality control review, but there is not a subsequent date listed for <span class="term" data-term="Results submitted to ClinicalTrials.gov">results submitted to ClinicalTrials.gov</span>, this means that the submission is pending changes by the sponsor or investigator.'},{term:"Secondary outcome measure",definition:`In a clinical study's <span class="term" data-term="Protocol">protocol</span>, a planned outcome measure that is not as important as the primary outcome measure for evaluating the effect of an intervention but is still of interest. Most clinical studies have more than one secondary outcome measure.`},{term:"Serious adverse event",definition:'An <span class="term" data-term="Adverse event">adverse event</span> that results in death, is life-threatening, requires inpatient hospitalization or extends a current hospital stay, results in an ongoing or significant incapacity or interferes substantially with normal life functions, or causes a congenital anomaly or birth defect. Medical events that do not result in death, are not life-threatening, or do not require hospitalization may be considered serious adverse events if they put the participant in danger or require medical or surgical intervention to prevent one of the results listed above.'},{term:"Sex",definition:`A type of <span class="term" data-term="Eligibility criteria">eligibility criteria</span> that indicates the sex of people who may participate in a clinical study (all, female, male). Sex is a person's classification as female or male based on biological distinctions. Sex is distinct from <span class="term" data-term="Gender-based eligibility">gender-based eligibility</span>.`},{term:"Sham comparator arm",definition:'An <span class="term" data-term="Arm type">arm type</span> in which a group of participants receives a procedure or device that appears to be the same as the actual procedure or device being studied but does not contain active processes or components.'},{term:"Single group assignment",definition:'A type of <span class="term" data-term="Intervention model">intervention model</span> describing a clinical trial in which all participants receive the same intervention/treatment.'},{term:"Sponsor",definition:"The organization or person who initiates the study and who has authority and control over the study."},{term:"State",definition:'In the search feature, the State field is used to find clinical studies with locations in a specific state within the United States. If you choose United States in the <span class="term" data-term="Country">Country</span> field, you can search for studies with locations in a specific state.'},{term:"Study completion date",definition:`The date on which the last participant in a clinical study was examined or received an <span class="term" data-term="Intervention/treatment">intervention/treatment</span> to collect final data for the <span class="term" data-term="Primary outcome measure">primary outcome measures</span>, <span class="term" data-term="Secondary outcome measure">secondary outcome measures</span>, and <span class="term" data-term="Adverse event">adverse events</span> (that is, the last participant's last visit). The "estimated" study completion date is the date that the researchers think will be the study completion date.`},{term:"Study design",definition:"The investigative methods and strategies used in the clinical study."},{term:"Study IDs",definition:`Identifiers that are assigned to a clinical study by the study's <span class="term" data-term="Sponsor">sponsor</span>, funders, or others. They include unique identifiers from other trial <span class="term" data-term="Study registry">study registries</span> and National Institutes of Health grant numbers. Note: ClinicalTrials.gov assigns a unique identification code to each clinical study registered on ClinicalTrials.gov. Also called the <span class="term" data-term="NCT number">NCT number</span>, the format is "NCT" followed by an 8-digit number (for example, NCT00000419).`},{term:"Study record",definition:`An entry on ClinicalTrials.gov that contains a summary of a clinical study's protocol information, including the <span class="term" data-term="Recruitment status">recruitment status</span>; eligibility criteria; contact information; and, in some cases, summary results. Each study record is assigned a ClinicalTrials.gov identifier, or <span class="term" data-term="NCT number">NCT number</span>.`},{term:"Study registry",definition:"A structured online system, such as ClinicalTrials.gov, that provides the public with access to summary information about ongoing and completed clinical studies."},{term:"Study results",definition:'A <span class="term" data-term="Study record">study record</span> that includes the summary results posted in the ClinicalTrials.gov <span class="term" data-term="Results database">results database</span>. Summary results information includes <span class="term" data-term="Participant flow">participant flow</span>, <span class="term" data-term="Baseline characteristics">baseline characteristics</span>, <span class="term" data-term="Outcome measure">outcome measures</span>, and <span class="term" data-term="Adverse event">adverse events</span> (including <span class="term" data-term="Serious adverse event">serious adverse events</span>).'},{term:"Study start date",definition:'The actual date on which the first participant was enrolled in a clinical study. The "estimated" study start date is the date that the researchers think will be the study start date.'},{term:"Study type",definition:'Describes the nature of a <span class="term" data-term="Clinical study">clinical study</span>. Study types include <span class="term" data-term="Interventional study (clinical trial)">interventional studies</span> (also called clinical trials), <span class="term" data-term="Observational study">observational studies</span> (including <span class="term" data-term="Patient registry">patient registries</span>), and <span class="term" data-term="Expanded access">expanded access</span>.'},{term:"Title",definition:'The official title of a <span class="term" data-term="Protocol">protocol</span> used to identify a clinical study or a short title written in language intended for the lay public.'},{term:"Title acronym",definition:`The acronym or initials used to identify a clinical study (not all studies have one). For example, the title acronym for the Women's Health Initiative is "WHI."`},{term:"Unknown",definition:'A type of <span class="term" data-term="Recruitment status">recruitment status</span>. It identifies a study on ClinicalTrials.gov whose last known status was recruiting; not yet recruiting; or active, not recruiting but that has passed its completion date, and the status has not been verified within the past 2 years. Studies with an unknown status are considered closed studies.'},{term:"Certification",definition:'A sponsor or investigator may submit a certification to delay submission of results information if they are applying for FDA approval of a new drug or device, or new use of an already approved drug or device. A sponsor or investigator who submits a certification can delay results submission up to 2 years after the <span class="term" data-term="Certification/extension first submitted">certification/extension first submitted</span> date, unless certain events occur sooner. See <a href="/policy/results-definitions#DelayResultsType" class="usa-link">Delay Results Type</a> in the Results Data Element definitions for more information about this certification.'},{term:"Certification/extension first posted",definition:'The date on which information about a <span class="term" data-term="Certification">certification</span> to delay submission of results or an <span class="term" data-term="Extension request">extension request</span> was first available on ClinicalTrials.gov. ClinicalTrials.gov does not indicate whether the submission was a certification or extension request. There is typically a delay between the date the study sponsor or investigator submitted the certification or extension request and the <span class="term" data-term="First Posted">first posted date</span>.'},{term:"Certification/extension first submitted",definition:'The date on which the study sponsor or investigator first submitted a <span class="term" data-term="Certification">certification</span> or an <span class="term" data-term="Extension request">extension request</span> to delay submission of results. A sponsor or investigator who submits a certification can delay results submission up to 2 years after this date, unless certain events occur sooner. There is typically a delay between the date the certification or extension request was submitted and the date the information is first available on ClinicalTrials.gov (<span class="term" data-term="Certification/extension first posted">certification/extension first posted</span>).'},{term:"Certification/extension first submitted that met QC criteria",definition:`The date on which the study sponsor or investigator first submitted a <span class="term" data-term="Certification">certification</span> or an <span class="term" data-term="Extension request">extension request</span> that is consistent with National Library of Medicine (NLM) <span class="term" data-term="Quality control (QC) review">quality control (QC) review</span> criteria. The sponsor or investigator may need to revise and submit a certification or extension request one or more times before NLM's QC review criteria are met. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria. Meeting QC criteria for an extension request does not mean that the National Institutes of Health (NIH) has determined that the request demonstrates good cause. The process for review and granting of extension requests by the NIH is being developed.`},{term:"Extension request",definition:'In certain circumstances, a sponsor or investigator may request an extension to delay the standard results submission deadline (generally one year after the <span class="term" data-term="Primary completion date">primary completion date</span>). The request for an extension must demonstrate good cause (for example, the need to preserve the scientific integrity of an ongoing <span class="term" data-term="Masking">masked</span> trial). All requests must be reviewed and granted by the National Institutes of Health. This process for review and granting of extension requests is being developed. See <a href="/policy/results-definitions#DelayResultsType" class="usa-link">Delay Results Type</a> in the Results Data Element definitions for more information.'},{term:"Results delayed",definition:'Indicates that the sponsor or investigator submitted a <span class="term" data-term="Certification">certification</span> or <span class="term" data-term="Extension request">extension request</span>.'},{term:"Canceled submission",definition:'Indicates that the study sponsor or investigator recalled a submission of <span class="term" data-term="study results">study results</span> before <span class="term" data-term="quality control (QC) review">quality control (QC) review</span> took place. If the submission was canceled on or after May 8, 2018, the date is shown. After submission of study results, a <span class="term" data-term="study record">study record</span> cannot be modified until QC review is completed, unless the submission is canceled.'},{term:"Informed consent form (ICF)",definition:'The document used in the <span class="term" data-term="Informed consent">informed consent</span> or process.'},{term:"Statistical analysis plan (SAP)",definition:'The written description of the statistical considerations and methods for analyzing the data collected in the <span class="term" data-term="Clinical study">clinical study</span>.'},{term:"Study documents",definition:'Refers to the type of documents that the study sponsor or principal investigator may add to their <span class="term" data-term="Study record">study record</span>. These include a study <span class="term" data-term="Protocol">protocol</span>, <span class="term" data-term="Statistical analysis plan (SAP)">statistical analysis plan</span>, and <span class="term" data-term="Informed consent form (ICF)">informed consent form</span>.'},{term:"Expanded access type",definition:'Describes the category of <span class="term" data-term="expanded access">expanded access</span> under <span class="term" data-term="U.S. Food and Drug Administration (FDA)">U.S. Food and Drug Administration (FDA)</span> regulations. There are three types of expanded access: <ul><li><strong>Individual Patients</strong>: Allows a single patient, with a serious disease or condition who cannot participate in a clinical trial, access to a drug or biological product that has not been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">FDA</span>. This category also includes access in an emergency situation.</li><li><strong>Intermediate-size Population</strong>: Allows more than one patient (but generally fewer patients than through a Treatment IND/Protocol) access to a drug or biological product that has not been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">FDA</span>.  This type of expanded access is used when multiple patients with the same disease or condition seek access to a specific drug or biological product that has not been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">FDA</span>.</li><li><strong><a id="treatment-ind"></a>Treatment IND/Protocol</strong>: Allows a large, widespread population access to a drug or biological product that has not been approved by the <span class="term" data-term="U.S. Food and Drug Administration (FDA)">FDA</span>. This type of expanded access can only be provided if the product is already being developed for marketing for the same use as the expanded access use.</li></ul>'},{term:"Phase Not Applicable",definition:'Describes trials without FDA-defined <span class="term" data-term="phase">phases</span>, including trials of devices or behavioral interventions.'},{term:"Sort studies by",definition:'The Sort studies by option is used to change the order of studies listed on the Search Results page. You can sort by Relevance or Newest First: <ul><li>Relevance: Studies that best match your search terms appear higher in the search results list. This is the default display for all searches.</li><li>Newest First: Studies with the most recent <span class="term" data-term="First Posted">First posted</span> dates appear higher in the search results list.</li></ul>'},{term:"All-cause mortality",definition:"A measure of all deaths, due to any cause, that occur during a clinical study."},{term:"Patient registry",definition:`A type of <span class="term" data-term="Observational Study">observational study</span> that collects information about patients' medical conditions and/or treatments to better understand how a condition or treatment affects patients in the real world.`},{term:"U.S. Agency for Healthcare Research and Quality (AHRQ)",definition:"An agency within the U.S. Department of Health and Human Services. AHRQ's mission is to produce evidence to make health care safer, higher quality, more accessible, equitable, and affordable, and to work within the U.S. Department of Health and Human Services and with other partners to make sure that the evidence is understood and used."},{term:"Submitted date",definition:"The date on which the study sponsor or investigator submitted a study record that is consistent with National Library of Medicine (NLM) quality control (QC) review criteria."},{term:"FDAAA 801 Violations",definition:"A FDAAA 801 Violation is shown on a study record when the U.S. Food and Drug Administration (FDA) has issued a Notice of Noncompliance to the responsible party of an applicable clinical trial. A Notice of Noncompliance indicates that the FDA has determined the responsible party was not in compliance with the registration or results reporting requirements for the clinical trial under the Food and Drug Administration Amendments Act of 2007, Section 801 (FDAAA 801).<p>The National Library of Medicine (NLM) is required by FDAAA 801 to add information to a study record about any FDAAA 801 Violation. This information is provided by the FDA. There are three categories of information that may be included:</p><ul><li>Violation: Shown when the FDA issues a Notice of Noncompliance and posts the Notice of Noncompliance on its designated webpage. There are three types of violations:<ul><li>Failure to submit required clinical trial information</li><li>Submission of false or misleading clinical trial information</li><li>Failure to submit primary and secondary outcomes</li></ul></li><li>Correction: Shown when the FDA confirms that the responsible party has updated the study record to correct the violation and posts the correction notice on its designated webpage.  Because of the time for FDA review and processing, there may be a delay between the date when the study record was updated and the addition of correction information to the FDAAA 801 Violation information. </li><li>Penalty: Shown when the FDA imposes a penalty for the violation and posts the penalty notice on its designated webpage.</li></ul>"},{term:"Type of intervention",definition:"A process or action that is the focus of a clinical study. Interventions include drugs, medical devices, procedures, vaccines, and other products that are either investigational or already available. Interventions can also include noninvasive approaches, such as education or modifying diet and exercise."}]},{}]},{},[2]);
