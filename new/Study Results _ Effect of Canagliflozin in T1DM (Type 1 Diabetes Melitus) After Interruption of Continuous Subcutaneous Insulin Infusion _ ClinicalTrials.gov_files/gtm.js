
// Copyright 2012 Google Inc. All rights reserved.
 
 (function(w,g){w[g]=w[g]||{};
 w[g].e=function(s){return eval(s);};})(window,'google_tag_manager');
 
(function(){

var data = {
"resource": {
  "version":"28",
  
  "macros":[{"function":"__e"},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__c","vtp_value":"button:not([data-pinger-ignore]), input[type=button]:not([data-pinger-ignore]), input[type=submit]:not([data-pinger-ignore]), input[type=reset]:not([data-pinger-ignore]), a:not([data-pinger-ignore]), area:not([data-pinger-ignore])"},{"function":"__jsm","vtp_javascript":["template","(function(){for(var a=",["escape",["macro",1],8,16],";a;){if(a.matches(",["escape",["macro",2],8,16],"))return a;a=a.parentElement||a.parentNode}return null})();"]},{"function":"__v","vtp_name":"gtm.triggers","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":""},{"function":"__c","vtp_value":"G-DP2X732JSX"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":true,"vtp_defaultValue":"(not set)","vtp_name":"ncbi_app"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",6],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",6],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","^$","value","(not set)"]]},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":true,"vtp_defaultValue":"(not set)","vtp_name":"ncbi_db"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",8],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",8],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","^$","value","(not set)"]]},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",6],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",9],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","blast","value","blast"]]},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":true,"vtp_defaultValue":"(not set)","vtp_name":"ncbi_pdid"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",11],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",11],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","^$","value","(not set)"]]},{"function":"__v","convert_true_to":"YES","convert_false_to":"NO","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"myncbi_signed_in"},{"function":"__c","vtp_value":["template",["macro",7],":",["macro",10],":",["macro",12]]},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_program"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_algorithm"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"ncbi_nwds"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_pcid"},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",18],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_defaultValue":["macro",18],"vtp_ignoreCase":true,"vtp_map":["list",["map","key","^$","value","(not set)"]]},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_featured_srcdb"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_pagename"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_feature"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_domain"},{"function":"__v","vtp_dataLayerVersion":1,"vtp_setDefaultValue":false,"vtp_name":"ncbi_resultcount"},{"function":"__u","vtp_stripWww":false,"vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",25],"vtp_fullMatch":false,"vtp_replaceAfterMatch":false,"vtp_defaultValue":"","vtp_ignoreCase":true,"vtp_map":["list",["map","key","dev\\.ncbi\\.nlm\\.nih\\.gov$","value","host"],["map","key","qa\\.ncbi\\.nlm\\.nih\\.gov$","value","host"],["map","key","test\\.ncbi\\.nlm\\.nih\\.gov$","value","host"],["map","key","prstest\\.clinicaltrials\\.gov$","value","host"]]},{"function":"__u","vtp_component":"QUERY","vtp_queryKey":"ga_debug","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__remm","vtp_setDefaultValue":true,"vtp_input":["macro",27],"vtp_fullMatch":false,"vtp_replaceAfterMatch":false,"vtp_defaultValue":"","vtp_ignoreCase":true,"vtp_map":["list",["map","key","true","value","query"]]},{"function":"__jsm","vtp_javascript":["template","(function(){var a=",["escape",["macro",3],8,16],";if(a\u0026\u0026a.getAttribute\u0026\u0026a.getAttribute(\"data-ga-category\"))return a.getAttribute(\"data-ga-category\");for(;a\u0026\u0026a.getAttribute\u0026\u0026!a.getAttribute(\"data-section\");)a=a.parentNode;return a\u0026\u0026a.getAttribute\u0026\u0026a.getAttribute(\"data-section\")?a.getAttribute(\"data-section\"):\"Link Click\"})();"]},{"function":"__jsm","vtp_javascript":["template","(function(){var a=",["escape",["macro",3],8,16],";return a\u0026\u0026a.getAttribute\u0026\u0026a.getAttribute(\"data-ga-action\")?a.getAttribute(\"data-ga-action\"):a.innerText||a.textContent||a.value||\"\"})();"]},{"function":"__jsm","vtp_javascript":["template","(function(){var a=",["escape",["macro",3],8,16],";return a\u0026\u0026a.getAttribute?a.getAttribute(\"data-ga-label\")||a.getAttribute(\"ref\")||a.getAttribute(\"href\")||\"None\":\"None\"})();"]},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"SELF_URL"},{"function":"__u","vtp_component":"QUERY","vtp_queryKey":"term","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":["macro",29],"vtp_name":"ga_category"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":["macro",30],"vtp_name":"ga_action"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":["macro",31],"vtp_name":"ga_label"},{"function":"__gas","vtp_cookieDomain":"auto","vtp_doubleClick":false,"vtp_setTrackerName":false,"vtp_useDebugVersion":false,"vtp_useHashAutoLink":false,"vtp_decorateFormsAutoLink":false,"vtp_enableLinkId":false,"vtp_enableEcommerce":false,"vtp_trackingId":"UA-68648221-2","vtp_enableRecaptchaOption":false,"vtp_enableUaRlsa":false,"vtp_enableUseInternalVersion":false,"vtp_enableGA4Schema":true},{"function":"__c","vtp_value":"G-HF818T9R4Y"},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"ATTRIBUTE","vtp_attribute":"data-ga-category"},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"ATTRIBUTE","vtp_attribute":"data-ga-action"},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"ATTRIBUTE","vtp_attribute":"data-ga-label"},{"function":"__jsm","vtp_javascript":["template","(function(){return function(a){var c=[{rx:\/.{4}@.{4}\/g,replacement:\"[REDACTED EMAIL]\"},{rx:\/\\d{5,}\/g,replacement:\"[REDACTED NUMBER]\"}];c.forEach(function(b){a=a.replace(b.rx,b.replacement)});return a}})();"]},{"function":"__aev","vtp_setDefaultValue":false,"vtp_varType":"ATTRIBUTE","vtp_attribute":"value"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"},{"function":"__v","vtp_name":"gtm.elementClasses","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementId","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementTarget","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__aev","vtp_varType":"TEXT"}],
  "tags":[{"function":"__paused","vtp_originalTagType":"ua","tag_id":20},{"function":"__paused","vtp_originalTagType":"ua","tag_id":26},{"function":"__paused","vtp_originalTagType":"ua","tag_id":28},{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":["macro",5],"vtp_configSettingsTable":["list",["map","parameter","ncbi_app","parameterValue",["macro",7]],["map","parameter","ncbi_db","parameterValue",["macro",10]],["map","parameter","ncbi_pdid","parameterValue",["macro",12]],["map","parameter","ncbi_signed_in","parameterValue",["macro",13]],["map","parameter","ncbi_app_db_pdid","parameterValue",["macro",14]],["map","parameter","ncbi_program","parameterValue",["macro",15]],["map","parameter","ncbi_algorithm","parameterValue",["macro",16]],["map","parameter","ncbi_nwds","parameterValue",["macro",17]],["map","parameter","ncbi_pcid","parameterValue",["macro",19]],["map","parameter","ncbi_featured_srcdb","parameterValue",["macro",20]],["map","parameter","ncbi_pagename","parameterValue",["macro",21]],["map","parameter","ncbi_feature","parameterValue",["macro",22]],["map","parameter","ncbi_domain","parameterValue",["macro",23]],["map","parameter","ncbi_resultcount","parameterValue",["macro",24]],["map","parameter","debug_event","parameterValue",["template",["macro",26],["macro",28]]],["map","parameter","content_group","parameterValue",["macro",14]],["map","parameter","content_group2","parameterValue",["macro",7]],["map","parameter","content_group3","parameterValue",["macro",10]],["map","parameter","content_group4","parameterValue",["macro",12]],["map","parameter","send_page_view","parameterValue","false"]],"tag_id":41},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","click_ga_category","parameterValue",["macro",29]],["map","parameter","click_ga_action","parameterValue",["macro",30]],["map","parameter","click_ga_label","parameterValue",["macro",31]],["map","parameter","ncbi_pdid","parameterValue",["macro",12]],["map","parameter","ncbi_pcid","parameterValue",["macro",19]],["map","parameter","ncbi_app_db_pdid","parameterValue",["macro",14]],["map","parameter","ncbi_app","parameterValue",["macro",7]],["map","parameter","ncbi_db","parameterValue",["macro",10]],["map","parameter","content_group","parameterValue",["macro",14]],["map","parameter","content_group2","parameterValue",["macro",7]],["map","parameter","content_group3","parameterValue",["macro",10]],["map","parameter","content_group4","parameterValue",["macro",12]],["map","parameter","page_location","parameterValue",["macro",32]]],"vtp_eventName":"GA4 Click","vtp_measurementIdOverride":["macro",5],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":42},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":43},{"function":"__paused","vtp_originalTagType":"gaawe","tag_id":56},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","click_ga_category","parameterValue",["macro",34]],["map","parameter","click_ga_action","parameterValue",["macro",35]],["map","parameter","click_ga_label","parameterValue",["macro",36]],["map","parameter","ncbi_pdid","parameterValue",["macro",12]],["map","parameter","ncbi_pcid","parameterValue",["macro",19]],["map","parameter","ncbi_app_db_pdid","parameterValue",["macro",14]],["map","parameter","ncbi_app","parameterValue",["macro",7]],["map","parameter","ncbi_db","parameterValue",["macro",10]],["map","parameter","content_group","parameterValue",["macro",14]],["map","parameter","content_group2","parameterValue",["macro",7]],["map","parameter","content_group3","parameterValue",["macro",10]],["map","parameter","content_group4","parameterValue",["macro",12]],["map","parameter","page_location","parameterValue",["macro",32]]],"vtp_eventName":"GA4 Click","vtp_measurementIdOverride":["macro",5],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":63},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","ncbi_pdid","parameterValue",["macro",12]],["map","parameter","ncbi_pcid","parameterValue",["macro",19]],["map","parameter","ncbi_app_db_pdid","parameterValue",["macro",14]],["map","parameter","ncbi_app","parameterValue",["macro",7]],["map","parameter","ncbi_db","parameterValue",["macro",10]],["map","parameter","content_group","parameterValue",["macro",14]],["map","parameter","content_group2","parameterValue",["macro",7]],["map","parameter","content_group3","parameterValue",["macro",10]],["map","parameter","content_group4","parameterValue",["macro",12]],["map","parameter","page_location","parameterValue",["macro",32]]],"vtp_eventName":"page_view","vtp_measurementIdOverride":["macro",5],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":72},{"function":"__cl","tag_id":86},{"function":"__sdl","vtp_verticalThresholdUnits":"PIXELS","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_verticalThresholdsPixels":"400","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"47342615_27","vtp_enableTriggerStartOption":true,"tag_id":87},{"function":"__html","metadata":["map"],"once_per_event":true,"vtp_html":"\u003Cscript async type=\"text\/gtmscript\" id=\"_fed_an_ua_tag\" data-gtmsrc=\"https:\/\/dap.digitalgov.gov\/Universal-Federated-Analytics-Min.js?agency=HHS\u0026amp;subagency=NCBI%20-%20ncbi.nlm.nih.gov\u0026amp;sitetopic=NCBI%20Pinger%200.39.3\u0026amp;siteplatform=NCBI%20Pinger%200.39.3\"\u003E\u003C\/script\u003E","vtp_supportDocumentWrite":false,"vtp_enableIframeMode":false,"vtp_enableEditJsMacroBehavior":false,"tag_id":85}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",3],"arg1":"null"},{"function":"_eq","arg0":["macro",3],"arg1":"undefined"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.click"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.scrollDepth"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)47342615_27($|,)))"},{"function":"_re","arg0":["macro",33],"arg1":"^(undefined|null|0|false|NaN|)$"},{"function":"_eq","arg0":["macro",0],"arg1":"custom_click"},{"function":"_eq","arg0":["macro",0],"arg1":"render"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.load"}],
  "rules":[[["if",0],["add",0,3,11,9]],[["if",3],["unless",1,2],["add",1,4]],[["if",4,5],["add",2,5]],[["if",0],["unless",6],["add",6]],[["if",7],["add",7]],[["if",8],["add",8]],[["if",9],["add",10]]]
},
"runtime":[ [50,"__aev",[46,"a"],[50,"aC",[46,"aJ"],[22,[2,[15,"v"],"hasOwnProperty",[7,[15,"aJ"]]],[46,[53,[36,[16,[15,"v"],[15,"aJ"]]]]]],[52,"aK",[16,[15,"z"],"element"]],[22,[28,[15,"aK"]],[46,[36,[44]]]],[52,"aL",["g",[15,"aK"]]],["aD",[15,"aJ"],[15,"aL"]],[36,[15,"aL"]]],[50,"aD",[46,"aJ","aK"],[43,[15,"v"],[15,"aJ"],[15,"aK"]],[2,[15,"w"],"push",[7,[15,"aJ"]]],[22,[18,[17,[15,"w"],"length"],[15,"s"]],[46,[53,[52,"aL",[2,[15,"w"],"shift",[7]]],[2,[15,"b"],"delete",[7,[15,"v"],[15,"aL"]]]]]]],[50,"aE",[46,"aJ","aK"],[52,"aL",["n",[30,[30,[16,[15,"z"],"elementUrl"],[15,"aJ"]],""]]],[52,"aM",["n",[30,[17,[15,"aK"],"component"],"URL"]]],[38,[15,"aM"],[46,"URL","IS_OUTBOUND","PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT"],[46,[5,[46,[36,[15,"aL"]]]],[5,[46,[36,["aG",[15,"aL"],[17,[15,"aK"],"affiliatedDomains"]]]]],[5,[46,[36,[2,[15,"l"],"B",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"C",[7,[15,"aL"],[17,[15,"aK"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"l"],"D",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"E",[7,[15,"aL"],[17,[15,"aK"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"l"],"F",[7,[15,"aL"]]]]]],[5,[46,[22,[17,[15,"aK"],"queryKey"],[46,[53,[36,[2,[15,"l"],"H",[7,[15,"aL"],[17,[15,"aK"],"queryKey"]]]]]],[46,[53,[36,[2,[17,["m",[15,"aL"]],"search"],"replace",[7,"?",""]]]]]]]],[5,[46,[36,[2,[15,"l"],"G",[7,[15,"aL"]]]]]],[9,[46,[36,[17,["m",[15,"aL"]],"href"]]]]]]],[50,"aF",[46,"aJ","aK"],[52,"aL",[8,"ATTRIBUTE","elementAttribute","CLASSES","elementClasses","ELEMENT","element","ID","elementId","HISTORY_CHANGE_SOURCE","historyChangeSource","HISTORY_NEW_STATE","newHistoryState","HISTORY_NEW_URL_FRAGMENT","newUrlFragment","HISTORY_OLD_STATE","oldHistoryState","HISTORY_OLD_URL_FRAGMENT","oldUrlFragment","TARGET","elementTarget"]],[52,"aM",[16,[15,"z"],[16,[15,"aL"],[15,"aJ"]]]],[36,[39,[21,[15,"aM"],[44]],[15,"aM"],[15,"aK"]]]],[50,"aG",[46,"aJ","aK"],[22,[28,[15,"aJ"]],[46,[53,[36,false]]]],[52,"aL",["aI",[15,"aJ"]]],[22,["aH",[15,"aL"],["k"]],[46,[53,[36,false]]]],[22,[28,["q",[15,"aK"]]],[46,[53,[3,"aK",[2,[2,["n",[30,[15,"aK"],""]],"replace",[7,["c","\\s+","g"],""]],"split",[7,","]]]]]],[65,"aM",[15,"aK"],[46,[53,[22,[20,["j",[15,"aM"]],"object"],[46,[53,[22,[16,[15,"aM"],"is_regex"],[46,[53,[52,"aN",["c",[16,[15,"aM"],"domain"]]],[22,[20,[15,"aN"],[45]],[46,[6]]],[22,["p",[15,"aN"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[16,[15,"aM"],"domain"]],[46,[53,[36,false]]]]]]]]],[46,[22,[20,["j",[15,"aM"]],"RegExp"],[46,[53,[22,["p",[15,"aM"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[15,"aM"]],[46,[53,[36,false]]]]]]]]]]]],[36,true]],[50,"aH",[46,"aJ","aK"],[22,[28,[15,"aK"]],[46,[36,false]]],[22,[19,[2,[15,"aJ"],"indexOf",[7,[15,"aK"]]],0],[46,[36,true]]],[3,"aK",["aI",[15,"aK"]]],[22,[28,[15,"aK"]],[46,[36,false]]],[3,"aK",[2,[15,"aK"],"toLowerCase",[7]]],[41,"aL"],[3,"aL",[37,[17,[15,"aJ"],"length"],[17,[15,"aK"],"length"]]],[22,[1,[18,[15,"aL"],0],[29,[2,[15,"aK"],"charAt",[7,0]],"."]],[46,[53,[34,[3,"aL",[37,[15,"aL"],1]]],[3,"aK",[0,".",[15,"aK"]]]]]],[36,[1,[19,[15,"aL"],0],[12,[2,[15,"aJ"],"indexOf",[7,[15,"aK"],[15,"aL"]]],[15,"aL"]]]]],[50,"aI",[46,"aJ"],[22,[28,["p",[15,"r"],[15,"aJ"]]],[46,[53,[3,"aJ",[0,"http://",[15,"aJ"]]]]]],[36,[2,[15,"l"],"C",[7,[15,"aJ"],true]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","internal.getElementAttribute"]],[52,"e",["require","internal.getElementValue"]],[52,"f",["require","internal.getEventData"]],[52,"g",["require","internal.getElementInnerText"]],[52,"h",["require","internal.getElementProperty"]],[52,"i",["require","internal.copyFromDataLayerCache"]],[52,"j",["require","getType"]],[52,"k",["require","getUrl"]],[52,"l",[15,"__module_legacyUrls"]],[52,"m",["require","internal.legacyParseUrl"]],[52,"n",["require","makeString"]],[52,"o",["require","templateStorage"]],[52,"p",["require","internal.testRegex"]],[52,"q",[51,"",[7,"aJ"],[36,[20,["j",[15,"aJ"]],"array"]]]],[52,"r",["c","^https?:\\/\\/","i"]],[52,"s",35],[52,"t","eq"],[52,"u","evc"],[52,"v",[30,[2,[15,"o"],"getItem",[7,[15,"u"]]],[8]]],[2,[15,"o"],"setItem",[7,[15,"u"],[15,"v"]]],[52,"w",[30,[2,[15,"o"],"getItem",[7,[15,"t"]]],[7]]],[2,[15,"o"],"setItem",[7,[15,"t"],[15,"w"]]],[52,"x",[17,[15,"a"],"defaultValue"]],[52,"y",[17,[15,"a"],"varType"]],[52,"z",["i","gtm"]],[38,[15,"y"],[46,"TAG_NAME","TEXT","URL","ATTRIBUTE"],[46,[5,[46,[52,"aA",[16,[15,"z"],"element"]],[52,"aB",[1,[15,"aA"],["h",[15,"aA"],"tagName"]]],[36,[30,[15,"aB"],[15,"x"]]]]],[5,[46,[36,[30,["aC",["f","gtm\\.uniqueEventId"]],[15,"x"]]]]],[5,[46,[36,["aE",[15,"x"],[15,"a"]]]]],[5,[46,[22,[20,[17,[15,"a"],"attribute"],[44]],[46,[53,[36,["aF",[15,"y"],[15,"x"]]]]],[46,[53,[52,"aJ",[16,[15,"z"],"element"]],[52,"aK",[1,[15,"aJ"],[39,[20,[17,[15,"a"],"attribute"],"value"],["e",[15,"aJ"]],["d",[15,"aJ"],[17,[15,"a"],"attribute"]]]]],[36,[30,[30,[15,"aK"],[15,"x"]],""]]]]]]],[9,[46,[36,["aF",[15,"y"],[15,"x"]]]]]]]]
 ,[50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__cl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnClick"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"m",[46,"v","w"],[66,"x",[2,[15,"b"],"keys",[7,[15,"w"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],[15,"x"]]]]]]],[50,"n",[46],[36,[7,[17,[15,"f"],"CB"],[17,[15,"f"],"CI"]]]],[50,"o",[46,"v"],[52,"w",["n"]],[65,"x",[15,"w"],[46,[53,[52,"y",[16,[15,"v"],[15,"x"]]],[22,[15,"y"],[46,[36,[15,"y"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g",["require","getType"]],[52,"h",["require","internal.loadGoogleTag"]],[52,"i",["require","logToConsole"]],[52,"j",["require","makeNumber"]],[52,"k",["require","makeString"]],[52,"l",["require","makeTableMap"]],[52,"p",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["g",[15,"p"]],"string"],[24,[2,[15,"p"],"indexOf",[7,"-"]],0]],[46,[53,["i",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"p"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"q",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"r",[30,["l",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"q"],[15,"r"]],[52,"s",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"t",[30,["l",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"s"],[15,"t"]],[52,"u",[15,"q"]],["m",[15,"u"],[15,"s"]],[22,[30,[2,[15,"u"],"hasOwnProperty",[7,[17,[15,"f"],"CZ"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"v",[30,[16,[15,"u"],[17,[15,"f"],"CZ"]],[8]]],["m",[15,"v"],[30,["l",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"u"],[17,[15,"f"],"CZ"],[15,"v"]]]]],[2,[15,"d"],"C",[7,[15,"u"],[17,[15,"d"],"A"],[51,"",[7,"v"],[36,[39,[20,"false",[2,["k",[15,"v"]],"toLowerCase",[7]]],false,[28,[28,[15,"v"]]]]]]]],[2,[15,"d"],"C",[7,[15,"u"],[17,[15,"d"],"B"],[51,"",[7,"v"],[36,["j",[15,"v"]]]]]],["h",[15,"p"],[8,"firstPartyUrl",["o",[15,"u"]]]],["e",[15,"p"],[15,"u"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__html",[46,"a"],[52,"b",["require","internal.injectHtml"]],["b",[17,[15,"a"],"html"],[17,[15,"a"],"gtmOnSuccess"],[17,[15,"a"],"gtmOnFailure"],[17,[15,"a"],"useIframe"],[17,[15,"a"],"supportDocumentWrite"]]]
 ,[50,"__jsm",[46,"a"],[52,"b",["require","internal.executeJavascriptString"]],[22,[20,[17,[15,"a"],"javascript"],[44]],[46,[36]]],[36,["b",[17,[15,"a"],"javascript"]]]]
 ,[50,"__paused",[46,"a"],[2,[15,"a"],"gtmOnFailure",[7]]]
 ,[50,"__sdl",[46,"a"],[50,"f",[46,"h"],[2,[15,"h"],"gtmOnSuccess",[7]],[52,"i",[17,[15,"h"],"horizontalThresholdUnits"]],[52,"j",[17,[15,"h"],"verticalThresholdUnits"]],[52,"k",[8]],[43,[15,"k"],"horizontalThresholdUnits",[15,"i"]],[38,[15,"i"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],[43,[15,"k"],"verticalThresholdUnits",[15,"j"]],[38,[15,"j"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],["c",[15,"k"],[17,[15,"h"],"uniqueTriggerId"]]],[50,"g",[46,"h"],[52,"i",[7]],[52,"j",[2,["e",[15,"h"]],"split",[7,","]]],[53,[41,"k"],[3,"k",0],[63,[7,"k"],[23,[15,"k"],[17,[15,"j"],"length"]],[33,[15,"k"],[3,"k",[0,[15,"k"],1]]],[46,[53,[52,"l",["d",[16,[15,"j"],[15,"k"]]]],[22,[29,[15,"l"],[15,"l"]],[46,[53,[36,[7]]]],[46,[22,[29,[17,[2,[16,[15,"j"],[15,"k"]],"trim",[7]],"length"],0],[46,[53,[2,[15,"i"],"push",[7,[15,"l"]]]]]]]]]]]],[36,[15,"i"]]],[52,"b",["require","callOnWindowLoad"]],[52,"c",["require","internal.enableAutoEventOnScroll"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[22,[17,[15,"a"],"triggerStartOption"],[46,[53,["f",[15,"a"]]]],[46,[53,["b",[51,"",[7],[36,["f",[15,"a"]]]]]]]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[50,"__v",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getType"]],[52,"e",[17,[15,"a"],"name"]],[22,[30,[28,[15,"e"]],[21,["d",[15,"e"]],"string"]],[46,[36,false]]],[52,"f",[2,[15,"e"],"replace",[7,["c","\\\\.","g"],"."]]],[52,"g",["b",[15,"f"],[30,[17,[15,"a"],"dataLayerVersion"],1]]],[36,[39,[21,[15,"g"],[44]],[15,"g"],[17,[15,"a"],"defaultValue"]]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_direct_google_requests"],[52,"v","allow_google_signals"],[52,"w","auid"],[52,"x","discount"],[52,"y","aw_feed_country"],[52,"z","aw_feed_language"],[52,"aA","items"],[52,"aB","aw_merchant_id"],[52,"aC","aw_basket_type"],[52,"aD","client_id"],[52,"aE","conversion_id"],[52,"aF","conversion_linker"],[52,"aG","conversion_api"],[52,"aH","cookie_deprecation"],[52,"aI","cookie_expires"],[52,"aJ","cookie_update"],[52,"aK","country"],[52,"aL","currency"],[52,"aM","customer_buyer_stage"],[52,"aN","customer_lifetime_value"],[52,"aO","customer_loyalty"],[52,"aP","customer_ltv_bucket"],[52,"aQ","debug_mode"],[52,"aR","shipping"],[52,"aS","engagement_time_msec"],[52,"aT","estimated_delivery_date"],[52,"aU","event_developer_id_string"],[52,"aV","event"],[52,"aW","event_timeout"],[52,"aX","first_party_collection"],[52,"aY","gdpr_applies"],[52,"aZ","google_analysis_params"],[52,"bA","_google_ng"],[52,"bB","gpp_sid"],[52,"bC","gpp_string"],[52,"bD","gsa_experiment_id"],[52,"bE","gtag_event_feature_usage"],[52,"bF","iframe_state"],[52,"bG","ignore_referrer"],[52,"bH","is_passthrough"],[52,"bI","_lps"],[52,"bJ","language"],[52,"bK","merchant_feed_label"],[52,"bL","merchant_feed_language"],[52,"bM","merchant_id"],[52,"bN","new_customer"],[52,"bO","page_hostname"],[52,"bP","page_path"],[52,"bQ","page_referrer"],[52,"bR","page_title"],[52,"bS","_platinum_request_status"],[52,"bT","restricted_data_processing"],[52,"bU","screen_resolution"],[52,"bV","send_page_view"],[52,"bW","server_container_url"],[52,"bX","session_duration"],[52,"bY","session_engaged_time"],[52,"bZ","session_id"],[52,"cA","_shared_user_id"],[52,"cB","topmost_url"],[52,"cC","transaction_id"],[52,"cD","transport_url"],[52,"cE","update"],[52,"cF","_user_agent_architecture"],[52,"cG","_user_agent_bitness"],[52,"cH","_user_agent_full_version_list"],[52,"cI","_user_agent_mobile"],[52,"cJ","_user_agent_model"],[52,"cK","_user_agent_platform"],[52,"cL","_user_agent_platform_version"],[52,"cM","_user_agent_wow64"],[52,"cN","user_data_auto_latency"],[52,"cO","user_data_auto_meta"],[52,"cP","user_data_auto_multi"],[52,"cQ","user_data_auto_selectors"],[52,"cR","user_data_auto_status"],[52,"cS","user_data_mode"],[52,"cT","user_id"],[52,"cU","user_properties"],[52,"cV","us_privacy_string"],[52,"cW","value"],[52,"cX","_fpm_parameters"],[52,"cY","_host_name"],[52,"cZ","_in_page_command"],[52,"dA","non_personalized_ads"],[52,"dB","conversion_label"],[52,"dC","page_location"],[52,"dD","global_developer_id_string"],[52,"dE","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"W",[15,"s"],"X",[15,"t"],"Y",[15,"u"],"Z",[15,"v"],"AA",[15,"w"],"AB",[15,"x"],"AC",[15,"y"],"AD",[15,"z"],"AE",[15,"aA"],"AF",[15,"aB"],"AG",[15,"aC"],"AH",[15,"aD"],"AI",[15,"aE"],"DG",[15,"dB"],"AJ",[15,"aF"],"AK",[15,"aG"],"AL",[15,"aH"],"AM",[15,"aI"],"AN",[15,"aJ"],"AO",[15,"aK"],"AP",[15,"aL"],"AQ",[15,"aM"],"AR",[15,"aN"],"AS",[15,"aO"],"AT",[15,"aP"],"AU",[15,"aQ"],"AV",[15,"aR"],"AW",[15,"aS"],"AX",[15,"aT"],"AY",[15,"aU"],"AZ",[15,"aV"],"BA",[15,"aW"],"BB",[15,"aX"],"BC",[15,"aY"],"DI",[15,"dD"],"BD",[15,"aZ"],"BE",[15,"bA"],"BF",[15,"bB"],"BG",[15,"bC"],"BH",[15,"bD"],"BI",[15,"bE"],"BJ",[15,"bF"],"BK",[15,"bG"],"BL",[15,"bH"],"BM",[15,"bI"],"BN",[15,"bJ"],"BO",[15,"bK"],"BP",[15,"bL"],"BQ",[15,"bM"],"BR",[15,"bN"],"BS",[15,"bO"],"DH",[15,"dC"],"BT",[15,"bP"],"BU",[15,"bQ"],"BV",[15,"bR"],"BW",[15,"bS"],"BX",[15,"bT"],"BY",[15,"bU"],"CA",[15,"bV"],"CB",[15,"bW"],"CC",[15,"bX"],"CD",[15,"bY"],"CE",[15,"bZ"],"CF",[15,"cA"],"DJ",[15,"dE"],"CG",[15,"cB"],"CH",[15,"cC"],"CI",[15,"cD"],"CJ",[15,"cE"],"CK",[15,"cF"],"CL",[15,"cG"],"CM",[15,"cH"],"CN",[15,"cI"],"CO",[15,"cJ"],"CP",[15,"cK"],"CQ",[15,"cL"],"CR",[15,"cM"],"CS",[15,"cN"],"CT",[15,"cO"],"CU",[15,"cP"],"CV",[15,"cQ"],"CW",[15,"cR"],"CX",[15,"cS"],"CY",[15,"cT"],"CZ",[15,"cU"],"DA",[15,"cV"],"DB",[15,"cW"],"DC",[15,"cX"],"DD",[15,"cY"],"DE",[15,"cZ"],"DF",[15,"dA"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46,"g","h","i"],[65,"j",[15,"h"],[46,[53,[22,[2,[15,"g"],"hasOwnProperty",[7,[15,"j"]]],[46,[53,[43,[15,"g"],[15,"j"],["i",[16,[15,"g"],[15,"j"]]]]]]]]]]],[52,"b",["require","Object"]],[52,"c",[15,"__module_gtagSchema"]],[52,"d",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"X"],[17,[15,"c"],"Y"],[17,[15,"c"],"Z"],[17,[15,"c"],"AN"],[17,[15,"c"],"BK"],[17,[15,"c"],"CJ"],[17,[15,"c"],"BB"],[17,[15,"c"],"CA"]]]]],[52,"e",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"AM"],[17,[15,"c"],"BA"],[17,[15,"c"],"CC"],[17,[15,"c"],"CD"],[17,[15,"c"],"AW"]]]]],[36,[8,"A",[15,"d"],"B",[15,"e"],"C",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__aev":{"2":true,"5":true}
,
"__c":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__f":{"2":true,"5":true}
,
"__googtag":{"1":10,"5":true}
,
"__paused":{"5":true}
,
"__sdl":{"5":true}
,
"__u":{"2":true,"5":true}
,
"__v":{"2":true,"5":true}


}
,"blob":{"1":"28"}
,"permissions":{
"__aev":{"read_data_layer":{"allowedKeys":"specific","keyPatterns":["gtm"]},"read_event_data":{"eventDataAccess":"any"},"read_dom_element_text":{},"get_element_attributes":{"allowedAttributes":"any"},"get_url":{"urlParts":"any"},"access_dom_element_properties":{"properties":[{"property":"tagName","read":true}]},"access_template_storage":{},"access_element_values":{"allowRead":[true],"allowWrite":[false]}}
,
"__c":{}
,
"__cl":{"detect_click_events":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__html":{"unsafe_inject_arbitrary_html":{}}
,
"__jsm":{"unsafe_run_arbitrary_javascript":{}}
,
"__paused":{}
,
"__sdl":{"process_dom_events":{"targets":[{"targetType":"window","eventName":"load"}]},"detect_scroll_events":{}}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}
,
"__v":{"read_data_layer":{"allowedKeys":"any"}}


}



,"security_groups":{
"customScripts":[
"__html"
,
"__jsm"

]
,
"google":[
"__aev"
,
"__c"
,
"__cl"
,
"__e"
,
"__f"
,
"__googtag"
,
"__sdl"
,
"__u"
,
"__v"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ca(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if(typeof Object.setPrototypeOf=="function")ka=Object.setPrototypeOf;else{var na;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;na=pa.a;break a}catch(a){}na=!1}ka=na?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=ka,ra=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Eq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:sa(l(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.Eq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Cr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map=new Map;this.C=new Set};k=Ca.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.rl=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Da=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ca.prototype.wa=function(){return Da(this,1)};Ca.prototype.Zb=function(){return Da(this,2)};Ca.prototype.Ib=function(){return Da(this,3)};var Ea=function(){this.map={};this.C={}};k=Ea.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.rl=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Fa=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ea.prototype.wa=function(){return Fa(this,1)};Ea.prototype.Zb=function(){return Fa(this,2)};Ea.prototype.Ib=function(){return Fa(this,3)};var Ga=function(){};Ga.prototype.reset=function(){};var Ha=[],Ia={};function Ja(a){return Ha[a]===void 0?!1:Ha[a]};var Ka=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.tb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ja(15)?new Ca:new Ea};k=Ka.prototype;k.add=function(a,b){this.tb||this.values.set(a,b)};k.oh=function(a,b){this.tb||this.values.rl(a,b)};k.set=function(a,b){this.tb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ka(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Ld(this.N);return a};k.Cd=function(){return this.P};k.Nb=function(a){this.C=a};k.bm=function(){return this.C};k.Vc=function(a){this.H=a};k.cj=function(){return this.H};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.N=a};k.sb=function(){return this.N};var La=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.tb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.oh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.tb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.tb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new La(this.ba,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Ld(this.P);return a};k.Cd=function(){return this.ba};k.Nb=function(a){this.H=a};k.bm=function(){return this.H};k.Vc=function(a){this.N=a};k.cj=function(){return this.N};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.P=a};k.sb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.qm=a;this.Tl=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=new Map;function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ra(a,e.value),c instanceof Ba);e=d.next());return c}function Ra(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f,g=String(d);f=Ja(17)?Pa.has(g)?Pa.get(g):a.get(g):a.get(g);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return Ja(18)?f.apply(a,e):f.invoke.apply(f,[a].concat(ua(e)))}catch(m){var h=a.bm();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Sa=function(){this.H=new Ga;this.C=Ja(16)?new La(this.H):new Ka(this.H)};k=Sa.prototype;k.Cd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Dj([a].concat(ua(ya.apply(1,arguments))))};k.Dj=function(){for(var a,b=l(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ra(this.C,c.value);return a};
k.ho=function(a){var b=ya.apply(1,arguments),c=this.C.rb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ra(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ta=function(){this.Ca=!1;this.aa=new Ea};k=Ta.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};function Ua(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Xa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,Za;function $a(a){Wa=Wa||Xa();Za=Za||Ua();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function ab(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Za[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Xa();Za=Za||Ua();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var bb={};function cb(a,b){bb[a]=bb[a]||[];bb[a][b]=!0}function db(){bb.GTAG_EVENT_FEATURE_CHANNEL=eb}function fb(a){var b=bb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return $a(c.join("")).replace(/\.+$/,"")}function gb(){for(var a=[],b=bb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function hb(){}function ib(a){return typeof a==="function"}function jb(a){return typeof a==="string"}function kb(a){return typeof a==="number"&&!isNaN(a)}function lb(a){return Array.isArray(a)?a:[a]}function mb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function nb(a,b){if(!kb(a)||!kb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function ob(a,b){for(var c=new pb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function qb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function rb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function tb(a){return Math.round(Number(a))||0}function ub(a){return"false"===String(a).toLowerCase()?!1:!!a}
function vb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function wb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function xb(){return new Date(Date.now())}function yb(){return xb().getTime()}var pb=function(){this.prefix="gtm.";this.values={}};pb.prototype.set=function(a,b){this.values[this.prefix+a]=b};pb.prototype.get=function(a){return this.values[this.prefix+a]};pb.prototype.contains=function(a){return this.get(a)!==void 0};
function zb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Ab(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Bb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Cb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Db(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Eb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Fb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Gb=/^\w{1,9}$/;function Hb(a,b){a=a||{};b=b||",";var c=[];qb(a,function(d,e){Gb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Nb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ua(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ob=globalThis.trustedTypes,Pb;function Qb(){var a=null;if(!Ob)return a;try{var b=function(c){return c};a=Ob.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Rb(){Pb===void 0&&(Pb=Qb());return Pb};var Sb=function(a){this.C=a};Sb.prototype.toString=function(){return this.C+""};function Tb(a){var b=a,c=Rb(),d=c?c.createScriptURL(b):b;return new Sb(d)}function Ub(a){if(a instanceof Sb)return a.C;throw Error("");};var Vb=wa([""]),Wb=va(["\x00"],["\\0"]),Xb=va(["\n"],["\\n"]),Yb=va(["\x00"],["\\u0000"]);function Zb(a){return a.toString().indexOf("`")===-1}Zb(function(a){return a(Vb)})||Zb(function(a){return a(Wb)})||Zb(function(a){return a(Xb)})||Zb(function(a){return a(Yb)});var $b=function(a){this.C=a};$b.prototype.toString=function(){return this.C};var bc=function(a){this.Up=a};function cc(a){return new bc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var dc=[cc("data"),cc("http"),cc("https"),cc("mailto"),cc("ftp"),new bc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ec(a){var b;b=b===void 0?dc:b;if(a instanceof $b)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof bc&&d.Up(a))return new $b(a)}}var hc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ic(a){var b;if(a instanceof $b)if(a instanceof $b)b=a.C;else throw Error("");else b=hc.test(a)?a:void 0;return b};function jc(a,b){var c=ic(b);c!==void 0&&(a.action=c)};function kc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var lc=function(a){this.C=a};lc.prototype.toString=function(){return this.C+""};var nc=function(){this.C=mc[0].toLowerCase()};nc.prototype.toString=function(){return this.C};function oc(a,b){var c=[new nc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof nc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var pc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function qc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,rc=window.history,z=document,sc=navigator;function tc(){var a;try{a=sc.serviceWorker}catch(b){return}return a}var uc=z.currentScript,vc=uc&&uc.src;function wc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function xc(a){return(sc.userAgent||"").indexOf(a)!==-1}function yc(){return xc("Firefox")||xc("FxiOS")}function zc(){return(xc("GSA")||xc("GoogleApp"))&&(xc("iPhone")||xc("iPad"))}function Ac(){return xc("Edg/")||xc("EdgA/")||xc("EdgiOS/")}
var Bc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Cc={onload:1,src:1,width:1,height:1,style:1};function Dc(a,b,c){b&&qb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Ec(a,b,c,d,e){var f=z.createElement("script");Dc(f,d,Bc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Tb(qc(a));f.src=Ub(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Fc(){if(vc){var a=vc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Gc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Dc(g,c,Cc);d&&qb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Hc(a,b,c,d){return Ic(a,b,c,d)}function Jc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Kc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Lc(a){x.setTimeout(a,0)}function Mc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Nc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Oc(a){var b=z.createElement("div"),c=b,d,e=qc("A<div>"+a+"</div>"),f=Rb(),g=f?f.createHTML(e):e;d=new lc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof lc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Pc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Qc(a,b,c){var d;try{d=sc.sendBeacon&&sc.sendBeacon(a)}catch(e){cb("TAGGING",15)}d?b==null||b():Ic(a,b,c)}function Rc(a,b){try{return sc.sendBeacon(a,b)}catch(c){cb("TAGGING",15)}return!1}var Sc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Uc(a,b,c,d,e){if(Vc()){var f=Object.assign({},Sc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Dh)return e==null||e(),!1;if(b){var h=
Rc(a,b);h?d==null||d():e==null||e();return h}Wc(a,d,e);return!0}function Vc(){return typeof x.fetch==="function"}function Xc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Yc(){var a=x.performance;if(a&&ib(a.now))return a.now()}
function Zc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function $c(){return x.performance||void 0}function ad(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Ic=function(a,b,c,d){var e=new Image(1,1);Dc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Wc=Qc;function bd(a,b){return this.evaluate(a)&&this.evaluate(b)}function cd(a,b){return this.evaluate(a)===this.evaluate(b)}function dd(a,b){return this.evaluate(a)||this.evaluate(b)}function ed(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function fd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function gd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ta&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var hd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,id=function(a){if(a==null)return String(a);var b=hd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},jd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},kd=function(a){if(!a||id(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!jd(a,"constructor")&&!jd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
jd(a,b)},ld=function(a,b){var c=b||(id(a)=="array"?[]:{}),d;for(d in a)if(jd(a,d)){var e=a[d];id(e)=="array"?(id(c[d])!="array"&&(c[d]=[]),c[d]=ld(e,c[d])):kd(e)?(kd(c[d])||(c[d]={}),c[d]=ld(e,c[d])):c[d]=e}return c};function md(a){if(a==void 0||Array.isArray(a)||kd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function nd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var od=function(a){a=a===void 0?[]:a;this.aa=new Ea;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(nd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=od.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof od?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!nd(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else nd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():nd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Zb=function(){for(var a=this.aa.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Ib=function(){for(var a=this.aa.Ib(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){nd(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new od(this.values.splice(a)):new od(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};k.has=function(a){return nd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.tb=function(){return this.Ca};
function pd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var qd=function(a,b){this.functionName=a;this.zd=b;this.aa=new Ea;this.Ca=!1};k=qd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new od(this.wa())};k.invoke=function(a){return this.zd.call.apply(this.zd,[new rd(this,a)].concat(ua(ya.apply(1,arguments))))};k.apply=function(a,b){return this.zd.apply(new rd(this,a),b)};k.Lb=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};
k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};var sd=function(a,b){qd.call(this,a,b)};ra(sd,qd);var td=function(a,b){qd.call(this,a,b)};ra(td,qd);var rd=function(a,b){this.zd=a;this.K=b};
rd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ra(b,a):a};rd.prototype.getName=function(){return this.zd.getName()};rd.prototype.Cd=function(){return this.K.Cd()};var ud=function(){this.map=new Map};ud.prototype.set=function(a,b){this.map.set(a,b)};ud.prototype.get=function(a){return this.map.get(a)};var vd=function(){this.keys=[];this.values=[]};vd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};vd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function wd(){try{return Map?new ud:new vd}catch(a){return new vd}};var xd=function(a){if(a instanceof xd)return a;if(md(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};xd.prototype.getValue=function(){return this.value};xd.prototype.toString=function(){return String(this.value)};var zd=function(a){this.promise=a;this.Ca=!1;this.aa=new Ea;this.aa.set("then",yd(this));this.aa.set("catch",yd(this,!0));this.aa.set("finally",yd(this,!1,!0))};k=zd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};
var yd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new sd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof sd||(d=void 0);e instanceof sd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new xd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new zd(h)})};zd.prototype.Ua=function(){this.Ca=!0};zd.prototype.tb=function(){return this.Ca};function Ad(a,b,c){var d=wd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof od){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof zd)return g.promise.then(function(u){return Ad(u,b,1)},function(u){return Promise.reject(Ad(u,b,1))});if(g instanceof Ta){var q={};d.set(g,q);e(g,q);return q}if(g instanceof sd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(u[w],b,c);var y=new Ka(b?b.Cd():new Ga);b&&y.Ld(b.sb());return f(g.invoke.apply(g,[y].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof xd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Bd(a,b,c){var d=wd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||rb(g)){var m=new od;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(kd(g)){var p=new Ta;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new sd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Ad(this.evaluate(u[w]),b,c);return f(this.K.cj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new xd(g)};return f(a)};var Cd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof od)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new od(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new od(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new od(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=pd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new od(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=pd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var Dd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ed=new Ba("break"),Fd=new Ba("continue");function Gd(a,b){return this.evaluate(a)+this.evaluate(b)}function Hd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof od))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Ad(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Dd.hasOwnProperty(e)){var m=2;m=1;var n=Ad(f,void 0,m);return Bd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof od){if(d.has(e)){var p=d.get(String(e));if(p instanceof sd){var q=pd(f);return p.invoke.apply(p,[this.K].concat(ua(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(Cd.supportedMethods.indexOf(e)>=
0){var r=pd(f);return Cd[e].call.apply(Cd[e],[d,this.K].concat(ua(r)))}}if(d instanceof sd||d instanceof Ta||d instanceof zd){if(d.has(e)){var t=d.get(e);if(t instanceof sd){var u=pd(f);return t.invoke.apply(t,[this.K].concat(ua(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof sd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof xd&&e==="toString")return d.toString();throw Oa(Error("TypeError: Object has no '"+
e+"' property."));}function Jd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Kd(){var a=ya.apply(0,arguments),b=this.K.rb(),c=Qa(b,a);if(c instanceof Ba)return c}function Ld(){return Ed}function Md(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Nd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.oh(c,d)}}}function Od(){return Fd}function Pd(a,b){return new Ba(a,this.evaluate(b))}function Qd(a,b){for(var c=ya.apply(2,arguments),d=new od,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.K.add(a,this.evaluate(g))}function Rd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Sd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof xd,f=d instanceof xd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Td(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ud(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Vd(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(f){return f},c);if(b instanceof Ta||b instanceof zd||b instanceof od||b instanceof sd){var d=b.wa(),e=d.length;return Ud(a,function(){return e},function(f){return d[f]},c)}}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){g.set(d,h);return g},e,f)}
function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){g.set(d,h);return g},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function $d(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof od)return Ud(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ce(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof od))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Ra(m,b);){var n=Qa(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Ra(p,c);m=p}}
function de(a,b){var c=ya.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof od))throw Error("Error: non-List value given for Fn argument names.");return new sd(a,function(){return function(){var f=ya.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Ld(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new od(h));var r=Qa(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function ee(a){var b=this.evaluate(a),c=this.K;if(fe&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ge(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ta||d instanceof zd||d instanceof od||d instanceof sd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:nd(e)&&(c=d[e]);else if(d instanceof xd)return;return c}function he(a,b){return this.evaluate(a)>this.evaluate(b)}function ie(a,b){return this.evaluate(a)>=this.evaluate(b)}
function je(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof xd&&(c=c.getValue());d instanceof xd&&(d=d.getValue());return c===d}function ke(a,b){return!je.call(this,a,b)}function le(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.K,d);if(e instanceof Ba)return e}var fe=!1;
function me(a,b){return this.evaluate(a)<this.evaluate(b)}function ne(a,b){return this.evaluate(a)<=this.evaluate(b)}function oe(){for(var a=new od,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function pe(){for(var a=new Ta,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function qe(a,b){return this.evaluate(a)%this.evaluate(b)}
function re(a,b){return this.evaluate(a)*this.evaluate(b)}function se(a){return-this.evaluate(a)}function te(a){return!this.evaluate(a)}function ue(a,b){return!Sd.call(this,a,b)}function ve(){return null}function we(a,b){return this.evaluate(a)||this.evaluate(b)}function xe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ye(a){return this.evaluate(a)}function ze(){return ya.apply(0,arguments)}function Ae(a){return new Ba("return",this.evaluate(a))}
function Be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof sd||d instanceof od||d instanceof Ta)&&d.set(String(e),f);return f}function Ce(a,b){return this.evaluate(a)-this.evaluate(b)}
function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function Ee(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Fe(a){var b=this.evaluate(a);return b instanceof sd?"function":typeof b}function Ge(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function He(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.K,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.K,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ie(a){return~Number(this.evaluate(a))}function Je(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Le(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Pe(){}
function Qe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Na&&h.Tl))throw h;var e=this.K.rb();a!==""&&(h instanceof Na&&(h=h.qm),e.add(a,new xd(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Ba)return g}}function Re(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Tl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Te=function(){this.C=new Sa;Se(this)};Te.prototype.execute=function(a){return this.C.Dj(a)};var Se=function(a){var b=function(c,d){var e=new td(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Pa.set(f,e)};b("map",pe);b("and",bd);b("contains",ed);b("equals",cd);b("or",dd);b("startsWith",fd);b("variable",gd)};Te.prototype.Nb=function(a){this.C.Nb(a)};var Ve=function(){this.H=!1;this.C=new Sa;Ue(this);this.H=!0};Ve.prototype.execute=function(a){return We(this.C.Dj(a))};var Xe=function(a,b,c){return We(a.C.ho(b,c))};Ve.prototype.Ua=function(){this.C.Ua()};
var Ue=function(a){var b=function(c,d){var e=String(c),f=new td(e,d);f.Ua();a.C.C.set(e,f);Pa.set(e,f)};b(0,Gd);b(1,Hd);b(2,Id);b(3,Jd);b(56,Me);b(57,Je);b(58,Ie);b(59,Oe);b(60,Ke);b(61,Le);b(62,Ne);b(53,Kd);b(4,Ld);b(5,Md);b(68,Qe);b(52,Nd);b(6,Od);b(49,Pd);b(7,oe);b(8,pe);b(9,Md);b(50,Qd);b(10,Rd);b(12,Sd);b(13,Td);b(67,Re);b(51,de);b(47,Wd);b(54,Xd);b(55,Yd);b(63,ce);b(64,Zd);b(65,ae);b(66,be);b(15,ee);b(16,ge);b(17,ge);b(18,he);b(19,ie);b(20,je);b(21,ke);b(22,le);b(23,me);b(24,ne);b(25,qe);b(26,
re);b(27,se);b(28,te);b(29,ue);b(45,ve);b(30,we);b(32,xe);b(33,xe);b(34,ye);b(35,ye);b(46,ze);b(36,Ae);b(43,Be);b(37,Ce);b(38,De);b(39,Ee);b(40,Fe);b(44,Pe);b(41,Ge);b(42,He)};Ve.prototype.Cd=function(){return this.C.Cd()};Ve.prototype.Nb=function(a){this.C.Nb(a)};Ve.prototype.Vc=function(a){this.C.Vc(a)};
function We(a){if(a instanceof Ba||a instanceof sd||a instanceof od||a instanceof Ta||a instanceof zd||a instanceof xd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ye=function(a){this.message=a};function Ze(a){a.Jr=!0;return a};var $e=Ze(function(a){return typeof a==="string"});function af(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ye("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function bf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var cf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function df(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+af(e)+c}a<<=2;d||(a|=32);return c=""+af(a|b)+c}
function ef(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+df(1,1)+af(d<<2|e));var f=a.Sl,g=a.No,h="4"+c+(f?""+df(2,1)+af(f):"")+(g?""+df(12,1)+af(g):""),m,n=a.Ej;m=n&&cf.test(n)?""+df(3,2)+n:"";var p,q=a.Aj;p=q?""+df(4,1)+af(q):"";var r;var t=a.ctid;if(t&&b){var u=df(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+af(1+y.length)+(a.gm||0)+y}}else r="";var A=a.Dq,C=a.we,E=a.Ma,G=a.Nr,I=h+m+p+r+(A?""+df(6,1)+af(A):"")+(C?""+df(7,3)+af(C.length)+
C:"")+(E?""+df(8,3)+af(E.length)+E:"")+(G?""+df(9,3)+af(G.length)+G:""),M;var S=a.Ul;S=S===void 0?{}:S;for(var ea=[],U=l(Object.keys(S)),ta=U.next();!ta.done;ta=U.next()){var W=ta.value;ea[Number(W)]=S[W]}if(ea.length){var da=df(10,3),Y;if(ea.length===0)Y=af(0);else{for(var X=[],ma=0,ja=!1,la=0;la<ea.length;la++){ja=!0;var Va=la%6;ea[la]&&(ma|=1<<Va);Va===5&&(X.push(af(ma)),ma=0,ja=!1)}ja&&X.push(af(ma));Y=X.join("")}var Ya=Y;M=""+da+af(Ya.length)+Ya}else M="";var sb=a.rm,ac=a.tq;return I+M+(sb?""+
df(11,3)+af(sb.length)+sb:"")+(ac?""+df(13,3)+af(ac.length)+ac:"")};var ff=function(){function a(b){return{toString:function(){return b}}}return{Rm:a("consent"),Tj:a("convert_case_to"),Uj:a("convert_false_to"),Vj:a("convert_null_to"),Wj:a("convert_true_to"),Xj:a("convert_undefined_to"),Pq:a("debug_mode_metadata"),Ra:a("function"),Ai:a("instance_name"),ko:a("live_only"),lo:a("malware_disabled"),METADATA:a("metadata"),oo:a("original_activity_id"),kr:a("original_vendor_template_id"),jr:a("once_on_load"),no:a("once_per_event"),tl:a("once_per_load"),mr:a("priority_override"),
rr:a("respected_consent_types"),Dl:a("setup_tags"),mh:a("tag_id"),Ll:a("teardown_tags")}}();
var hf=function(a){return gf[a]},kf=function(a){return jf[a]},mf=function(a){return lf[a]},nf=[],lf={"\x00":"&#0;",'"':"&quot;","&":"&amp;","'":"&#39;","<":"&lt;",">":"&gt;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;","-":"&#45;","/":"&#47;","=":"&#61;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"},of=/[\x00\x22\x26\x27\x3c\x3e]/g;
var sf=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\\\x85\u2028\u2029]/g,jf={"\x00":"\\x00",
"\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22","&":"\\x26","'":"\\x27","/":"\\/","<":"\\x3c","=":"\\x3d",">":"\\x3e","\\":"\\\\","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029",$:"\\x24","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e",":":"\\x3a","?":"\\x3f","[":"\\x5b","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d"};
nf[8]=function(a){if(a==null)return" null ";switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+String(String(a)).replace(sf,kf)+"'"}};var Af=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g,gf={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10",
"\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28",")":"%29","<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86",
"\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB","\uff3d":"%EF%BC%BD"};nf[16]=function(a){return a};var Cf;var Df=[],Ef=[],Ff=[],Gf=[],Hf=[],Jf,Kf,Lf;function Mf(a){Lf=Lf||a}
function Nf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Df.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Gf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ff.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Of(p[r])}Ef.push(p)}}
function Of(a){}var Pf,Qf=[],Rf=[];function Sf(a,b){var c={};c[ff.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Tf(a,b,c){try{return Kf(Uf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Uf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Vf(a[e],b,c));return d},Vf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Vf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Df[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ff.Ai]);try{var m=Uf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Wf(m,{event:b,index:f,type:2,
name:h});Pf&&(d=Pf.Qo(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Vf(a[n],b,c)]=Vf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Vf(a[q],b,c);Lf&&(p=p||Lf.Rp(r));d.push(r)}return Lf&&p?Lf.Vo(d):d.join("");case "escape":d=Vf(a[1],b,c);if(Lf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Lf.Sp(a))return Lf.iq(d);d=String(d);for(var t=2;t<a.length;t++)nf[a[t]]&&(d=nf[a[t]](d));return d;
case "tag":var u=a[1];if(!Gf[u])throw Error("Unable to resolve tag reference "+u+".");return{Yl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[ff.Ra]=a[1];var w=Tf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Wf=function(a,b){var c=a[ff.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Jf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Qf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Db(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Df[q];break;case 1:r=Gf[q];break;default:n="";break a}var t=r&&r[ff.Ai];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Rf.indexOf(c)===-1){Rf.push(c);
var y=yb();u=e(g);var A=yb()-y,C=yb();v=Cf(c,h,b);w=A-(yb()-C)}else if(e&&(u=e(g)),!e||f)v=Cf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),md(u)?(Array.isArray(u)?Array.isArray(v):kd(u)?kd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Xf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Xf,Error);Xf.prototype.getMessage=function(){return this.message};function Yf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Yf(a[c],b[c])}};function Zf(){return function(a,b){var c;var d=$f;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function $f(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)kb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function ag(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=bg(a),f=0;f<Ef.length;f++){var g=Ef[f],h=cg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Gf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function cg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function bg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Tf(Ff[c],a));return b[c]}};function dg(a,b){b[ff.Tj]&&typeof a==="string"&&(a=b[ff.Tj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ff.Vj)&&a===null&&(a=b[ff.Vj]);b.hasOwnProperty(ff.Xj)&&a===void 0&&(a=b[ff.Xj]);b.hasOwnProperty(ff.Wj)&&a===!0&&(a=b[ff.Wj]);b.hasOwnProperty(ff.Uj)&&a===!1&&(a=b[ff.Uj]);return a};var eg=function(){this.C={}},gg=function(a,b){var c=fg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function hg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Xf(c,d,g);}}
function ig(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));hg(e,b,d,g);hg(f,b,d,g)}}}};var mg=function(){var a=data.permissions||{},b=jg.ctid,c=this;this.H={};this.C=new eg;var d={},e={},f=ig(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});qb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw kg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};qb(h,function(p,q){var r=lg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Ql&&!e[p]&&(e[p]=r.Ql)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw kg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},ng=function(a){return fg.H[a]||function(){}};
function lg(a,b){var c=Sf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=kg;try{return Wf(c)}catch(d){return{assert:function(e){throw new Xf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Xf(a,{},"Permission "+a+" is unknown.");}}}}function kg(a,b,c){return new Xf(a,b,c)};var og=!1;var pg={};pg.Km=ub('');pg.hp=ub('');function ug(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var vg=[];function wg(a){switch(a){case 1:return 0;case 38:return 12;case 53:return 1;case 54:return 2;case 52:return 6;case 215:return 18;case 211:return 17;case 75:return 3;case 103:return 13;case 197:return 14;case 203:return 15;case 114:return 11;case 116:return 4;case 209:return 16;case 135:return 8;case 136:return 5}}function xg(a,b){vg[a]=b;var c=wg(a);c!==void 0&&(Ha[c]=b)}function B(a){xg(a,!0)}
B(39);B(34);B(35);B(36);
B(56);B(145);B(153);B(144);B(120);
B(5);B(111);B(139);B(87);
B(92);B(159);B(132);
B(20);B(72);B(113);
B(154);B(116);B(143);
xg(23,!1),B(24);
Ia[1]=ug('1',6E4);Ia[3]=ug('10',1);Ia[2]=ug('',50);B(29);
yg(26,25);
B(37);B(9);
B(91);B(123);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(95);B(38);B(103);B(112);
B(63);
B(152);
B(101);B(122);B(121);
B(108);
B(134);B(31);B(22);

B(19);B(90);B(59);
B(208);
B(175);B(176);
B(185);B(186);B(187);B(192);
B(200);B(201);


function D(a){return!!vg[a]}function yg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};var Ag={},Bg=(Ag.uaa=!0,Ag.uab=!0,Ag.uafvl=!0,Ag.uamb=!0,Ag.uam=!0,Ag.uap=!0,Ag.uapv=!0,Ag.uaw=!0,Ag);
var Jg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Hg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Ig.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Db(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Ig=/^[a-z$_][\w-$]*$/i,Hg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Kg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Lg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Mg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Ng=new pb;function Og(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Ng.get(e);f||(f=new RegExp(b,d),Ng.set(e,f));return f.test(a)}catch(g){return!1}}function Pg(a,b){return String(a).indexOf(String(b))>=0}
function Qg(a,b){return String(a)===String(b)}function Rg(a,b){return Number(a)>=Number(b)}function Sg(a,b){return Number(a)<=Number(b)}function Tg(a,b){return Number(a)>Number(b)}function Ug(a,b){return Number(a)<Number(b)}function Vg(a,b){return Db(String(a),String(b))};var bh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ch={Fn:"function",PixieMap:"Object",List:"Array"};
function dh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=bh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof sd?n="Fn":m instanceof od?n="List":m instanceof Ta?n="PixieMap":m instanceof zd?n="PixiePromise":m instanceof xd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ch[n]||n)+", which does not match required type ")+
((ch[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof sd?d.push("function"):g instanceof od?d.push("Array"):g instanceof Ta?d.push("Object"):g instanceof zd?d.push("Promise"):g instanceof xd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function eh(a){return a instanceof Ta}function fh(a){return eh(a)||a===null||gh(a)}
function hh(a){return a instanceof sd}function ih(a){return hh(a)||a===null||gh(a)}function jh(a){return a instanceof od}function kh(a){return a instanceof xd}function lh(a){return typeof a==="string"}function mh(a){return lh(a)||a===null||gh(a)}function nh(a){return typeof a==="boolean"}function oh(a){return nh(a)||gh(a)}function ph(a){return nh(a)||a===null||gh(a)}function qh(a){return typeof a==="number"}function gh(a){return a===void 0};function rh(a){return""+a}
function sh(a,b){var c=[];return c};function th(a,b){var c=new sd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Ua();return c}
function uh(a,b){var c=new Ta,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ib(e)?c.set(d,th(a+"_"+d,e)):kd(e)?c.set(d,uh(a+"_"+d,e)):(kb(e)||jb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function vh(a,b){if(!lh(a))throw F(this.getName(),["string"],arguments);if(!mh(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Ta;return d=uh("AssertApiSubject",
c)};function wh(a,b){if(!mh(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof zd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ta;return d=uh("AssertThatSubject",c)};function xh(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Ad(b[e],d));return Bd(a.apply(null,c))}}function yh(){for(var a=Math,b=zh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=xh(a[e].bind(a)))}return c};function Ah(a){return a!=null&&Db(a,"__cvt_")};function Bh(a){var b;return b};function Ch(a){var b;if(!lh(a))throw F(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Dh(a){try{return encodeURI(a)}catch(b){}};function Eh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Jh(a){if(!mh(a))throw F(this.getName(),["string|undefined"],arguments);};function Kh(a,b){if(!qh(a)||!qh(b))throw F(this.getName(),["number","number"],arguments);return nb(a,b)};function Lh(){return(new Date).getTime()};function Mh(a){if(a===null)return"null";if(a instanceof od)return"array";if(a instanceof sd)return"function";if(a instanceof xd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Nh(a){function b(c){return function(d){try{return c(d)}catch(e){(og||pg.Km)&&a.call(this,e.message)}}}return{parse:b(function(c){return Bd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Ad(c))}),publicName:"JSON"}};function Oh(a){return tb(Ad(a,this.K))};function Ph(a){return Number(Ad(a,this.K))};function Qh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Rh(a,b,c){var d=null,e=!1;if(!jh(a)||!lh(b)||!lh(c))throw F(this.getName(),["Array","string","string"],arguments);d=new Ta;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof Ta&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var zh="floor ceil round max min abs pow sqrt".split(" ");function Sh(){var a={};return{up:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Fm:function(b,c){a[b]=c},reset:function(){a={}}}}function Th(a,b){return function(){return sd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Uh(a,b){if(!lh(a))throw F(this.getName(),["string","any"],arguments);}
function Vh(a,b){if(!lh(a)||!eh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Wh={};var Xh=function(a){var b=new Ta;if(a instanceof od)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof sd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Wh.keys=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.wa());return new od};
Wh.values=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.Zb());return new od};
Wh.entries=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ta||a instanceof zd)return new od(a.Ib().map(function(b){return new od(b)}));return new od};
Wh.freeze=function(a){(a instanceof Ta||a instanceof zd||a instanceof od||a instanceof sd)&&a.Ua();return a};Wh.delete=function(a,b){if(a instanceof Ta&&!a.tb())return a.remove(b),!0;return!1};function H(a,b){var c=ya.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.qq){try{d.Rl.apply(null,[b].concat(ua(c)))}catch(e){throw cb("TAGGING",21),e;}return}d.Rl.apply(null,[b].concat(ua(c)))};var Yh=function(){this.H={};this.C={};this.N=!0;};Yh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Yh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Yh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:ib(b)?th(a,b):uh(a,b)};function Zh(a,b){var c=void 0;return c};function $h(){var a={};return a};var J={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ia:"analytics_storage",fc:"region",ja:"consent_updated",rg:"wait_for_update",dn:"app_remove",fn:"app_store_refund",gn:"app_store_subscription_cancel",hn:"app_store_subscription_convert",jn:"app_store_subscription_renew",kn:"consent_update",dk:"add_payment_info",ek:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",fk:"view_cart",Xc:"begin_checkout",Rd:"select_item",jc:"view_item_list",Gc:"select_promotion",kc:"view_promotion",
kb:"purchase",Sd:"refund",xb:"view_item",gk:"add_to_wishlist",ln:"exception",mn:"first_open",nn:"first_visit",qa:"gtag.config",Cb:"gtag.get",on:"in_app_purchase",Yc:"page_view",pn:"screen_view",qn:"session_start",rn:"source_update",sn:"timing_complete",tn:"track_social",Td:"user_engagement",un:"user_id_update",Me:"gclid_link_decoration_source",Ne:"gclid_storage_source",mc:"gclgb",lb:"gclid",hk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Oe:"gad_source",Pe:"gad_source_src",
Zc:"gclid_url",ik:"gclsrc",Qe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Re:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Ob:"allow_google_signals",mb:"allow_interest_groups",vn:"app_id",wn:"app_installer_id",xn:"app_name",yn:"app_version",Pb:"auid",zn:"auto_detection_enabled",bd:"aw_remarketing",Ph:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",sa:"items",Eg:"aw_merchant_id",jk:"aw_basket_type",
Se:"campaign_content",Te:"campaign_id",Ue:"campaign_medium",Ve:"campaign_name",We:"campaign",Xe:"campaign_source",Ye:"campaign_term",Qb:"client_id",kk:"rnd",Qh:"consent_update_type",An:"content_group",Bn:"content_type",Rb:"conversion_cookie_prefix",Ze:"conversion_id",Oa:"conversion_linker",Rh:"conversion_linker_disabled",dd:"conversion_api",Fg:"cookie_deprecation",nb:"cookie_domain",ob:"cookie_expires",yb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",Yd:"country",
Va:"currency",Sh:"customer_buyer_stage",af:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",bf:"custom_map",Vh:"gcldc",fd:"dclid",lk:"debug_mode",oa:"developer_id",Cn:"disable_merchant_reported_purchases",gd:"dc_custom_params",Dn:"dc_natural_search",mk:"dynamic_event_settings",nk:"affiliation",Gg:"checkout_option",Wh:"checkout_step",pk:"coupon",cf:"item_list_name",Xh:"list_name",En:"promotions",df:"shipping",Yh:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Zh:"enhanced_conversions",
qk:"enhanced_conversions_automatic_settings",Jg:"estimated_delivery_date",ai:"euid_logged_in_state",ef:"event_callback",Gn:"event_category",Tb:"event_developer_id_string",Hn:"event_label",hd:"event",Kg:"event_settings",Lg:"event_timeout",In:"description",Jn:"fatal",Kn:"experiments",bi:"firebase_id",Zd:"first_party_collection",Mg:"_x_20",oc:"_x_19",rk:"fledge_drop_reason",sk:"fledge",tk:"flight_error_code",uk:"flight_error_message",vk:"fl_activity_category",wk:"fl_activity_group",di:"fl_advertiser_id",
xk:"fl_ar_dedupe",ff:"match_id",yk:"fl_random_number",zk:"tran",Ak:"u",Ng:"gac_gclid",ae:"gac_wbraid",Bk:"gac_wbraid_multiple_conversions",Ck:"ga_restrict_domain",ei:"ga_temp_client_id",Ln:"ga_temp_ecid",jd:"gdpr_applies",Dk:"geo_granularity",Ic:"value_callback",qc:"value_key",rc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Ek:"google_tld",hf:"gpp_sid",jf:"gpp_string",Og:"groups",Fk:"gsa_experiment_id",kf:"gtag_event_feature_usage",Gk:"gtm_up",Jc:"iframe_state",lf:"ignore_referrer",
fi:"internal_traffic_results",Hk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Pg:"is_passthrough",kd:"_lps",zb:"language",Qg:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",sc:"decorate_forms",ma:"domains",Mc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Ik:"method",Mn:"name",Jk:"navigation_type",nf:"new_customer",Rg:"non_interaction",Nn:"optimize_id",Kk:"page_hostname",pf:"page_path",Wa:"page_referrer",Db:"page_title",Lk:"passengers",
Mk:"phone_conversion_callback",On:"phone_conversion_country_code",Nk:"phone_conversion_css_class",Pn:"phone_conversion_ids",Ok:"phone_conversion_number",Pk:"phone_conversion_options",Qn:"_platinum_request_status",Rn:"_protected_audience_enabled",qf:"quantity",Sg:"redact_device_info",gi:"referral_exclusion_definition",Sq:"_request_start_time",Vb:"restricted_data_processing",Sn:"retoken",Tn:"sample_rate",hi:"screen_name",Nc:"screen_resolution",Qk:"_script_source",Un:"search_term",pb:"send_page_view",
ld:"send_to",md:"server_container_url",rf:"session_duration",Tg:"session_engaged",ii:"session_engaged_time",uc:"session_id",Ug:"session_number",tf:"_shared_user_id",uf:"delivery_postal_code",Tq:"_tag_firing_delay",Uq:"_tag_firing_time",Vq:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Vn:"tracking_id",li:"traffic_type",Xa:"transaction_id",vc:"transport_url",Rk:"trip_type",od:"update",Eb:"url_passthrough",Sk:"uptgs",vf:"_user_agent_architecture",wf:"_user_agent_bitness",xf:"_user_agent_full_version_list",
yf:"_user_agent_mobile",zf:"_user_agent_model",Af:"_user_agent_platform",Bf:"_user_agent_platform_version",Cf:"_user_agent_wow64",eb:"user_data",mi:"user_data_auto_latency",ni:"user_data_auto_meta",oi:"user_data_auto_multi",ri:"user_data_auto_selectors",si:"user_data_auto_status",wc:"user_data_mode",Vg:"user_data_settings",Qa:"user_id",Wb:"user_properties",Tk:"_user_region",Df:"us_privacy_string",Fa:"value",Uk:"wbraid_multiple_conversions",rd:"_fpm_parameters",yi:"_host_name",il:"_in_page_command",
jl:"_ip_override",ol:"_is_passthrough_cid",xc:"non_personalized_ads",Ki:"_sst_parameters",nc:"conversion_label",Aa:"page_location",Ub:"global_developer_id_string",nd:"tc_privacy_string"}};var ai={},bi=(ai[J.m.ja]="gcu",ai[J.m.mc]="gclgb",ai[J.m.lb]="gclaw",ai[J.m.hk]="gclid_len",ai[J.m.Ud]="gclgs",ai[J.m.Vd]="gcllp",ai[J.m.Wd]="gclst",ai[J.m.Pb]="auid",ai[J.m.Bg]="dscnt",ai[J.m.Cg]="fcntr",ai[J.m.Dg]="flng",ai[J.m.Eg]="mid",ai[J.m.jk]="bttype",ai[J.m.Qb]="gacid",ai[J.m.nc]="label",ai[J.m.dd]="capi",ai[J.m.Fg]="pscdl",ai[J.m.Va]="currency_code",ai[J.m.Sh]="clobs",ai[J.m.af]="vdltv",ai[J.m.Th]="clolo",ai[J.m.Uh]="clolb",ai[J.m.lk]="_dbg",ai[J.m.Jg]="oedeld",ai[J.m.Tb]="edid",ai[J.m.rk]=
"fdr",ai[J.m.sk]="fledge",ai[J.m.Ng]="gac",ai[J.m.ae]="gacgb",ai[J.m.Bk]="gacmcov",ai[J.m.jd]="gdpr",ai[J.m.Ub]="gdid",ai[J.m.be]="_ng",ai[J.m.hf]="gpp_sid",ai[J.m.jf]="gpp",ai[J.m.Fk]="gsaexp",ai[J.m.kf]="_tu",ai[J.m.Jc]="frm",ai[J.m.Pg]="gtm_up",ai[J.m.kd]="lps",ai[J.m.Qg]="did",ai[J.m.ee]="fcntr",ai[J.m.fe]="flng",ai[J.m.he]="mid",ai[J.m.nf]=void 0,ai[J.m.Db]="tiba",ai[J.m.Vb]="rdp",ai[J.m.uc]="ecsid",ai[J.m.tf]="ga_uid",ai[J.m.uf]="delopc",ai[J.m.nd]="gdpr_consent",ai[J.m.Xa]="oid",ai[J.m.Sk]=
"uptgs",ai[J.m.vf]="uaa",ai[J.m.wf]="uab",ai[J.m.xf]="uafvl",ai[J.m.yf]="uamb",ai[J.m.zf]="uam",ai[J.m.Af]="uap",ai[J.m.Bf]="uapv",ai[J.m.Cf]="uaw",ai[J.m.mi]="ec_lat",ai[J.m.ni]="ec_meta",ai[J.m.oi]="ec_m",ai[J.m.ri]="ec_sel",ai[J.m.si]="ec_s",ai[J.m.wc]="ec_mode",ai[J.m.Qa]="userId",ai[J.m.Df]="us_privacy",ai[J.m.Fa]="value",ai[J.m.Uk]="mcov",ai[J.m.yi]="hn",ai[J.m.il]="gtm_ee",ai[J.m.xc]="npa",ai[J.m.Ze]=null,ai[J.m.Nc]=null,ai[J.m.zb]=null,ai[J.m.sa]=null,ai[J.m.Aa]=null,ai[J.m.Wa]=null,ai[J.m.ki]=
null,ai[J.m.rd]=null,ai[J.m.Me]=null,ai[J.m.Ne]=null,ai[J.m.rc]=null,ai);function ci(a,b){if(a){var c=a.split("x");c.length===2&&(di(b,"u_w",c[0]),di(b,"u_h",c[1]))}}
function ei(a){var b=fi;b=b===void 0?gi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(hi(q.value)),r.push(hi(q.quantity)),r.push(hi(q.item_id)),r.push(hi(q.start_date)),r.push(hi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function gi(a){return ii(a.item_id,a.id,a.item_name)}function ii(){for(var a=l(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ji(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function di(a,b,c){c===void 0||c===null||c===""&&!Bg[b]||(a[b]=c)}function hi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ki={},li=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=nb(0,1)===0,b=nb(0,1)===0,c++,c>30)return;return a},ni={wq:mi};function oi(a,b){var c=ki[b],d=c.Hm,e=c.experimentId,f=c.De;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;ki[b].active||ki[b].De>.5?pi(a,e):f<=0||f>1||ni.wq(a,b)}}
function mi(a,b){var c=ki[b],d=c.controlId2;if(!(nb(0,9999)<c.De*(c.controlId2&&c.De<=.25?4:2)*1E4))return a;qi(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.De<=.25?d:void 0,experimentCallback:function(){}});return a}function pi(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function qi(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=li()?0:1;e&&(g|=(li()?0:1)<<1);g===0?(pi(a,c),f()):g===1?pi(a,d):g===2&&pi(a,e)}};var K={J:{Nj:"call_conversion",W:"conversion",Wn:"floodlight",Ff:"ga_conversion",Gi:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};function ti(a){return ui?z.querySelectorAll(a):null}
function vi(a,b){if(!ui)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var wi=!1;
if(z.querySelectorAll)try{var xi=z.querySelectorAll(":root");xi&&xi.length==1&&xi[0]==z.documentElement&&(wi=!0)}catch(a){}var ui=wi;function yi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function zi(){this.blockSize=-1};function Ai(a,b){this.blockSize=-1;this.blockSize=64;this.N=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=za.Int32Array?new Int32Array(64):Array(64);Bi===void 0&&(za.Int32Array?Bi=new Int32Array(Ci):Bi=Ci);this.reset()}Aa(Ai,zi);for(var Di=[],Ei=0;Ei<63;Ei++)Di[Ei]=0;var Fi=[].concat(128,Di);
Ai.prototype.reset=function(){this.P=this.H=0;var a;if(za.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Gi=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Bi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Ai.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Gi(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Gi(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Ai.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Fi,56-this.H):this.update(Fi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Gi(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ci=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Bi;function Hi(){Ai.call(this,8,Ii)}Aa(Hi,Ai);var Ii=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ji=/^[0-9A-Fa-f]{64}$/;function Ki(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Li(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Ji.test(a))return Promise.resolve(a);try{var d=Ki(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Mi(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Mi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ni=[],Oi;function Pi(a){Oi?Oi(a):Ni.push(a)}function Qi(a,b){if(!D(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Pi(a),b):c}function Ri(a,b){if(!D(190))return b;var c=Si(a,"");return c!==b?(Pi(a),b):c}function Si(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ti(a,b){if(!D(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Pi(a),b)}function Ui(){Oi=Vi;for(var a=l(Ni),b=a.next();!b.done;b=a.next())Oi(b.value);Ni.length=0};var Wi={Ym:'512',Zm:'0',bn:'1000',ao:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',bo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',yo:Ri(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104839054~104839056~104885889~104885891~104908321~104908323~104909299~104909301')},Xi={bp:Number(Wi.Ym)||0,cp:Number(Wi.Zm)||0,fp:Number(Wi.bn)||0,yp:Wi.ao.split("~"),zp:Wi.bo.split("~"),Mq:Wi.yo};Object.assign({},Xi);function L(a){cb("GTM",a)};var Ij={},Jj=(Ij[J.m.mb]=1,Ij[J.m.md]=2,Ij[J.m.vc]=2,Ij[J.m.za]=3,Ij[J.m.af]=4,Ij[J.m.yg]=5,Ij[J.m.Hc]=6,Ij[J.m.cb]=6,Ij[J.m.nb]=6,Ij[J.m.ed]=6,Ij[J.m.Sb]=6,Ij[J.m.yb]=6,Ij[J.m.ob]=7,Ij[J.m.Vb]=9,Ij[J.m.zg]=10,Ij[J.m.Ob]=11,Ij),Kj={},Lj=(Kj.unknown=13,Kj.standard=14,Kj.unique=15,Kj.per_session=16,Kj.transactions=17,Kj.items_sold=18,Kj);var eb=[];function Mj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Jj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Jj[f],h=b;h=h===void 0?!1:h;cb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(eb[g]=!0)}}};var Nj=function(){this.C=new Set;this.H=new Set},Pj=function(a){var b=Oj.R;a=a===void 0?[]:a;var c=[].concat(ua(b.C)).concat([].concat(ua(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Qj=function(){var a=[].concat(ua(Oj.R.C));a.sort(function(b,c){return b-c});return a},Rj=function(){var a=Oj.R,b=Xi.Mq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Sj={},Tj=Ri(14,"5770"),Uj=Ti(15,Number("2")),Vj=Ri(19,"pingerDataLayer");Ri(20,"");Ri(16,"ChAI8IOzwwYQnJyErMiZz5oyEiQAx0A2Qq29517iIHDJHSxl46jiQK2YK40Wtnyeq2/6BAvsOe8aAkq3");var Wj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Xj={__paused:1,__tg:1},Yj;for(Yj in Wj)Wj.hasOwnProperty(Yj)&&(Xj[Yj]=1);var Zj=Qi(11,ub("")),ak=!1,bk,ck=!1;
bk=ck;var dk,ek=!1;dk=ek;Sj.wg=Ri(3,"www.googletagmanager.com");var fk=""+Sj.wg+(bk?"/gtag/js":"/gtm.js"),gk=null,hk=null,ik={},jk={};Sj.Sm=Qi(2,ub(""));var kk="";Sj.Li=kk;
var Oj=new function(){this.R=new Nj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.qb=this.P="";this.ba=this.ka=!1};function lk(){var a;a=a===void 0?[]:a;return Pj(a).join("~")}function mk(){var a=Oj.P.length;return Oj.P[a-1]==="/"?Oj.P.substring(0,a-1):Oj.P}function nk(){return Oj.C?D(84)?Oj.H===0:Oj.H!==1:!1}function ok(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var pk=new pb,qk={},rk={},uk={name:Vj,set:function(a,b){ld(Fb(a,b),qk);sk()},get:function(a){return tk(a,2)},reset:function(){pk=new pb;qk={};sk()}};function tk(a,b){return b!=2?pk.get(a):vk(a)}function vk(a,b){var c=a.split(".");b=b||[];for(var d=qk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function wk(a,b){rk.hasOwnProperty(a)||(pk.set(a,b),ld(Fb(a,b),qk),sk())}
function xk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=tk(c,1);if(Array.isArray(d)||kd(d))d=ld(d,null);rk[c]=d}}function sk(a){qb(rk,function(b,c){pk.set(b,c);ld(Fb(b),qk);ld(Fb(b,c),qk);a&&delete rk[b]})}function yk(a,b){var c,d=(b===void 0?2:b)!==1?vk(a):pk.get(a);id(d)==="array"||id(d)==="object"?c=ld(d,null):c=d;return c};var Ik=/:[0-9]+$/,Jk=/^\d+\.fls\.doubleclick\.net$/;function Kk(a,b,c,d){var e=Lk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Lk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=sa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Mk(a){try{return decodeURIComponent(a)}catch(b){}}function Nk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Ok(a.protocol)||Ok(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Ik,"").toLowerCase());return Pk(a,b,c,d,e)}
function Pk(a,b,c,d,e){var f,g=Ok(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Qk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Ik,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||cb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Kk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Ok(a){return a?a.replace(":","").toLowerCase():""}function Qk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Rk={},Sk=0;
function Tk(a){var b=Rk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||cb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Ik,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Sk<5&&(Rk[a]=b,Sk++)}return b}function Uk(a,b,c){var d=Tk(a);return Lb(b,d,c)}
function Vk(a){var b=Tk(x.location.href),c=Nk(b,"host",!1);if(c&&c.match(Jk)){var d=Nk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Wk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Xk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Yk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Tk(""+c+b).href}}function Zk(a,b){if(nk()||Oj.N)return Yk(a,b)}
function $k(){return!!Sj.Li&&Sj.Li.split("@@").join("")!=="SGTM_TOKEN"}function al(a){for(var b=l([J.m.md,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function bl(a,b,c){c=c===void 0?"":c;if(!nk())return a;var d=b?Wk[a]||"":"";d==="/gs"&&(c="");return""+mk()+d+c}function cl(a){if(!nk())return a;for(var b=l(Xk),c=b.next();!c.done;c=b.next())if(Db(a,""+mk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function dl(a){var b=String(a[ff.Ra]||"").replace(/_/g,"");return Db(b,"cvt")?"cvt":b}var el=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var fl={rq:Ti(27,Number("0.005000")),Zo:Ti(42,Number("0.010000"))},gl=Math.random(),hl=el||gl<Number(fl.rq),il=el||gl>=1-Number(fl.Zo);var jl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},kl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var ll,ml;a:{for(var nl=["CLOSURE_FLAGS"],ol=za,pl=0;pl<nl.length;pl++)if(ol=ol[nl[pl]],ol==null){ml=null;break a}ml=ol}var ql=ml&&ml[610401301];ll=ql!=null?ql:!1;function rl(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var sl,tl=za.navigator;sl=tl?tl.userAgentData||null:null;function ul(a){if(!ll||!sl)return!1;for(var b=0;b<sl.brands.length;b++){var c=sl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function vl(a){return rl().indexOf(a)!=-1};function wl(){return ll?!!sl&&sl.brands.length>0:!1}function xl(){return wl()?!1:vl("Opera")}function yl(){return vl("Firefox")||vl("FxiOS")}function zl(){return wl()?ul("Chromium"):(vl("Chrome")||vl("CriOS"))&&!(wl()?0:vl("Edge"))||vl("Silk")};var Al=function(a){Al[" "](a);return a};Al[" "]=function(){};var Bl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Cl(){return ll?!!sl&&!!sl.platform:!1}function Dl(){return vl("iPhone")&&!vl("iPod")&&!vl("iPad")}function El(){Dl()||vl("iPad")||vl("iPod")};xl();wl()||vl("Trident")||vl("MSIE");vl("Edge");!vl("Gecko")||rl().toLowerCase().indexOf("webkit")!=-1&&!vl("Edge")||vl("Trident")||vl("MSIE")||vl("Edge");rl().toLowerCase().indexOf("webkit")!=-1&&!vl("Edge")&&vl("Mobile");Cl()||vl("Macintosh");Cl()||vl("Windows");(Cl()?sl.platform==="Linux":vl("Linux"))||Cl()||vl("CrOS");Cl()||vl("Android");Dl();vl("iPad");vl("iPod");El();rl().toLowerCase().indexOf("kaios");var Fl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Al(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Gl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Hl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Il=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Fl(b.top)?1:2},Jl=function(a){a=a===void 0?document:a;return a.createElement("img")},Kl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Fl(a)&&(b=a);return b};function Ll(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Ml(){return Ll("join-ad-interest-group")&&ib(sc.joinAdInterestGroup)}
function Nl(a,b,c){var d=Ia[3]===void 0?1:Ia[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ia[2]===void 0?50:Ia[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&yb()-q<(Ia[1]===void 0?6E4:Ia[1])?(cb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ol(f[0]);else{if(n)return cb("TAGGING",10),!1}else f.length>=d?Ol(f[0]):n&&Ol(m[0]);Gc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:yb()});return!0}function Ol(a){try{a.parentNode.removeChild(a)}catch(b){}};function Pl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};yl();Dl()||vl("iPod");vl("iPad");!vl("Android")||zl()||yl()||xl()||vl("Silk");zl();!vl("Safari")||zl()||(wl()?0:vl("Coast"))||xl()||(wl()?0:vl("Edge"))||(wl()?ul("Microsoft Edge"):vl("Edg/"))||(wl()?ul("Opera"):vl("OPR"))||yl()||vl("Silk")||vl("Android")||El();var Rl={},Sl=null,Tl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Sl){Sl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Rl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Sl[q]===void 0&&(Sl[q]=p)}}}for(var r=Rl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],C=b[v+2],E=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|C>>6],M=r[C&63];t[w++]=""+E+G+I+M}var S=0,ea=u;switch(b.length-v){case 2:S=b[v+1],ea=r[(S&15)<<2]||u;case 1:var U=b[v];t[w]=""+r[U>>2]+r[(U&3)<<4|S>>4]+ea+u}return t.join("")};var Ul=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Vl=/#|$/,Wl=function(a,b){var c=a.search(Vl),d=Ul(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Bl(a.slice(d,e!==-1?e:0))},Xl=/[?&]($|#)/,Yl=function(a,b,c){for(var d,e=a.search(Vl),f=0,g,h=[];(g=Ul(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Xl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Zl(a,b,c,d,e,f,g){var h=Wl(c,"fmt");if(d){var m=Wl(c,"random"),n=Wl(c,"label")||"";if(!m)return!1;var p=Tl(Bl(n)+":"+Bl(m));if(!Pl(a,p,d))return!1}h&&Number(h)!==4&&(c=Yl(c,"rfmt",h));var q=Yl(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||g.H();Ec(q,function(){g==null||g.C();a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||g.C();e==null||e()},f,r||void 0);return!0};var $l={},am=($l[1]={},$l[2]={},$l[3]={},$l[4]={},$l);function bm(a,b,c){var d=cm(b,c);if(d){var e=am[b][d];e||(e=am[b][d]=[]);e.push(Object.assign({},a))}}function dm(a,b){var c=cm(a,b);if(c){var d=am[a][c];d&&(am[a][c]=d.filter(function(e){return!e.Bm}))}}function em(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function cm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function fm(a){var b=ya.apply(1,arguments);il&&(bm(a,2,b[0]),bm(a,3,b[0]));Qc.apply(null,ua(b))}function gm(a){var b=ya.apply(1,arguments);il&&bm(a,2,b[0]);return Rc.apply(null,ua(b))}function hm(a){var b=ya.apply(1,arguments);il&&bm(a,3,b[0]);Hc.apply(null,ua(b))}
function im(a){var b=ya.apply(1,arguments),c=b[0];il&&(bm(a,2,c),bm(a,3,c));return Uc.apply(null,ua(b))}function jm(a){var b=ya.apply(1,arguments);il&&bm(a,1,b[0]);Ec.apply(null,ua(b))}function km(a){var b=ya.apply(1,arguments);b[0]&&il&&bm(a,4,b[0]);Gc.apply(null,ua(b))}function lm(a){var b=ya.apply(1,arguments);il&&bm(a,1,b[2]);return Zl.apply(null,ua(b))}function mm(a){var b=ya.apply(1,arguments);il&&bm(a,4,b[0]);Nl.apply(null,ua(b))};var nm=/gtag[.\/]js/,om=/gtm[.\/]js/,pm=!1;function qm(a){if(pm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(nm.test(c))return"3";if(om.test(c))return"2"}return"0"};function rm(a,b,c){var d=sm(),e=tm().container[a];e&&e.state!==3||(tm().container[a]={state:1,context:b,parent:d},um({ctid:a,isDestination:!1},c))}function um(a,b){var c=tm();c.pending||(c.pending=[]);mb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function vm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var wm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=vm()};function tm(){var a=wc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new wm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=vm());return c};var xm={},jg={ctid:Ri(5,"GTM-PC9B6M3"),canonicalContainerId:Ri(6,"47342615"),tm:Ri(10,"GTM-PC9B6M3"),vm:Ri(9,"GTM-PC9B6M3")};xm.qe=Qi(7,ub(""));function ym(){return xm.qe&&zm().some(function(a){return a===jg.ctid})}function Am(){return jg.canonicalContainerId||"_"+jg.ctid}function Bm(){return jg.tm?jg.tm.split("|"):[jg.ctid]}
function zm(){return jg.vm?jg.vm.split("|").filter(function(a){return D(108)?a.indexOf("GTM-")!==0:!0}):[]}function Cm(){var a=Dm(sm()),b=a&&a.parent;if(b)return Dm(b)}function Em(){var a=Dm(sm());if(a){for(;a.parent;){var b=Dm(a.parent);if(!b)break;a=b}return a}}function Dm(a){var b=tm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Fm(){var a=tm();if(a.pending){for(var b,c=[],d=!1,e=Bm(),f=zm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],mb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Am())}catch(m){}}}
function Gm(){for(var a=jg.ctid,b=Bm(),c=zm(),d=function(n,p){var q={canonicalContainerId:jg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};uc&&(q.scriptElement=uc);vc&&(q.scriptSource=vc);if(Cm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Oj.C,y=Tk(v),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,E="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}E=String(G)}}if(E){t=E;break b}}t=void 0}var M=t;if(M){pm=!0;r=M;break a}}var S=[].slice.call(z.scripts);r=q.scriptElement?String(S.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=qm(q)}var ea=p?e.destination:e.container,U=ea[n];U?(p&&U.state===0&&L(93),Object.assign(U,q)):ea[n]=q},e=tm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Am()]={};Fm()}function Hm(){var a=Am();return!!tm().canonical[a]}function Im(a){return!!tm().container[a]}function Jm(a){var b=tm().destination[a];return!!b&&!!b.state}function sm(){return{ctid:jg.ctid,isDestination:xm.qe}}function Km(){var a=tm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Lm(){var a={};qb(tm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Mm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Nm(){for(var a=tm(),b=l(Bm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Om={Ia:{je:0,pe:1,Hi:2}};Om.Ia[Om.Ia.je]="FULL_TRANSMISSION";Om.Ia[Om.Ia.pe]="LIMITED_TRANSMISSION";Om.Ia[Om.Ia.Hi]="NO_TRANSMISSION";var Pm={X:{Fb:0,Da:1,Fc:2,Oc:3}};Pm.X[Pm.X.Fb]="NO_QUEUE";Pm.X[Pm.X.Da]="ADS";Pm.X[Pm.X.Fc]="ANALYTICS";Pm.X[Pm.X.Oc]="MONITORING";function Qm(){var a=wc("google_tag_data",{});return a.ics=a.ics||new Rm}var Rm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Rm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;cb("TAGGING",19);b==null?cb("TAGGING",18):Sm(this,a,b==="granted",c,d,e,f,g)};Rm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Sm(this,a[d],void 0,void 0,"","",b,c)};
var Sm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&jb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(cb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Rm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Tm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Tm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&jb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,zd:b})};var Tm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.wm=!0)}};Rm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.wm){d.wm=!1;try{d.zd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Um=!1,Vm=!1,Wm={},Xm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Wm.ad_storage=1,Wm.analytics_storage=1,Wm.ad_user_data=1,Wm.ad_personalization=1,Wm),usedContainerScopedDefaults:!1};function Ym(a){var b=Qm();b.accessedAny=!0;return(jb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Xm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Zm(a){var b=Qm();b.accessedAny=!0;return b.getConsentState(a,Xm)}function $m(a){var b=Qm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function an(){if(!Ja(7))return!1;var a=Qm();a.accessedAny=!0;if(a.active)return!0;if(!Xm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Xm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Xm.containerScopedDefaults[c.value]!==1)return!0;return!1}function bn(a,b){Qm().addListener(a,b)}
function cn(a,b){Qm().notifyListeners(a,b)}function dn(a,b){function c(){for(var e=0;e<b.length;e++)if(!$m(b[e]))return!0;return!1}if(c()){var d=!1;bn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function en(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Ym(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=jb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),bn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var fn={},gn=(fn[Pm.X.Fb]=Om.Ia.je,fn[Pm.X.Da]=Om.Ia.je,fn[Pm.X.Fc]=Om.Ia.je,fn[Pm.X.Oc]=Om.Ia.je,fn),hn=function(a,b){this.C=a;this.consentTypes=b};hn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Ym(a)});case 1:return this.consentTypes.some(function(a){return Ym(a)});default:kc(this.C,"consentsRequired had an unknown type")}};
var jn={},kn=(jn[Pm.X.Fb]=new hn(0,[]),jn[Pm.X.Da]=new hn(0,["ad_storage"]),jn[Pm.X.Fc]=new hn(0,["analytics_storage"]),jn[Pm.X.Oc]=new hn(1,["ad_storage","analytics_storage"]),jn);var mn=function(a){var b=this;this.type=a;this.C=[];bn(kn[a].consentTypes,function(){ln(b)||b.flush()})};mn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var ln=function(a){return gn[a.type]===Om.Ia.Hi&&!kn[a.type].isConsentGranted()},nn=function(a,b){ln(a)?a.C.push(b):b()},on=new Map;function pn(a){on.has(a)||on.set(a,new mn(a));return on.get(a)};var qn={Z:{Pm:"aw_user_data_cache",Lh:"cookie_deprecation_label",xg:"diagnostics_page_id",Xn:"fl_user_data_cache",Zn:"ga4_user_data_cache",Gf:"ip_geo_data_cache",Bi:"ip_geo_fetch_in_progress",sl:"nb_data",qo:"page_experiment_ids",Of:"pt_data",vl:"pt_listener_set",Cl:"service_worker_endpoint",El:"shared_user_id",Fl:"shared_user_id_requested",kh:"shared_user_id_source"}};var rn=function(a){return Ze(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(qn.Z);
function sn(a,b){b=b===void 0?!1:b;if(rn(a)){var c,d,e=(d=(c=wc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function tn(a,b){var c=sn(a,!0);c&&c.set(b)}function un(a){var b;return(b=sn(a))==null?void 0:b.get()}function vn(a){var b={},c=sn(a);if(!c){c=sn(a,!0);if(!c)return;c.set(b)}return c.get()}function wn(a,b){if(typeof b==="function"){var c;return(c=sn(a,!0))==null?void 0:c.subscribe(b)}}function xn(a,b){var c=sn(a);return c?c.unsubscribe(b):!1};var yn="https://"+Ri(21,"www.googletagmanager.com"),zn="/td?id="+jg.ctid,An={},Bn=(An.tdp=1,An.exp=1,An.pid=1,An.dl=1,An.seq=1,An.t=1,An.v=1,An),Cn=["mcc"],Dn={},En={},Fn=!1,Gn=void 0;function Hn(a,b,c){En[a]=b;(c===void 0||c)&&In(a)}function In(a,b){Dn[a]!==void 0&&(b===void 0||!b)||Db(jg.ctid,"GTM-")&&a==="mcc"||(Dn[a]=!0)}
function Jn(a){a=a===void 0?!1:a;var b=Object.keys(Dn).filter(function(c){return Dn[c]===!0&&En[c]!==void 0&&(a||!Cn.includes(c))}).map(function(c){var d=En[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+bl(yn)+zn+(""+b+"&z=0")}function Kn(){Object.keys(Dn).forEach(function(a){Bn[a]||(Dn[a]=!1)})}
function Ln(a){a=a===void 0?!1:a;if(Oj.ba&&il&&jg.ctid){var b=pn(Pm.X.Oc);if(ln(b))Fn||(Fn=!0,nn(b,Ln));else{var c=Jn(a),d={destinationId:jg.ctid,endpoint:61};a?im(d,c,void 0,{Dh:!0},void 0,function(){hm(d,c+"&img=1")}):hm(d,c);Kn();Fn=!1}}}var Mn={};
function Nn(a){var b=String(a);Mn.hasOwnProperty(b)||(Mn[b]=!0,Hn("csp",Object.keys(Mn).join("~")),In("csp",!0),Gn===void 0&&D(171)&&(Gn=x.setTimeout(function(){var c=Dn.csp;Dn.csp=!0;Dn.seq=!1;var d=Jn(!1);Dn.csp=c;Dn.seq=!0;Ec(d+"&script=1");Gn=void 0},500)))}function On(){Object.keys(Dn).filter(function(a){return Dn[a]&&!Bn[a]}).length>0&&Ln(!0)}var Pn;
function Qn(){if(un(qn.Z.xg)===void 0){var a=function(){tn(qn.Z.xg,nb());Pn=0};a();x.setInterval(a,864E5)}else wn(qn.Z.xg,function(){Pn=0});Pn=0}function Rn(){Qn();Hn("v","3");Hn("t","t");Hn("pid",function(){return String(un(qn.Z.xg))});Hn("seq",function(){return String(++Pn)});Hn("exp",lk());Jc(x,"pagehide",On)};var Sn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Tn=[J.m.md,J.m.vc,J.m.Zd,J.m.Qb,J.m.uc,J.m.Qa,J.m.Pa,J.m.cb,J.m.nb,J.m.Sb],Un=!1,Vn=!1,Wn={},Xn={};function Yn(){!Vn&&Un&&(Sn.some(function(a){return Xm.containerScopedDefaults[a]!==1})||Zn("mbc"));Vn=!0}function Zn(a){il&&(Hn(a,"1"),Ln())}function $n(a,b){if(!Wn[b]&&(Wn[b]=!0,Xn[b]))for(var c=l(Tn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){Zn("erc");break}};function ao(a){cb("HEALTH",a)};var bo={tp:Ri(22,"eyIwIjoiVFciLCIxIjoiVFctVEFPIiwiMiI6ZmFsc2UsIjMiOiJnb29nbGUuY29tLnR3IiwiNCI6IiIsIjUiOnRydWUsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0")},co={},eo=!1;function fo(){function a(){c!==void 0&&xn(qn.Z.Gf,c);try{var e=un(qn.Z.Gf);co=JSON.parse(e)}catch(f){L(123),ao(2),co={}}eo=!0;b()}var b=go,c=void 0,d=un(qn.Z.Gf);d?a(d):(c=wn(qn.Z.Gf,a),ho())}
function ho(){function a(c){tn(qn.Z.Gf,c||"{}");tn(qn.Z.Bi,!1)}if(!un(qn.Z.Bi)){tn(qn.Z.Bi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function io(){var a=bo.tp;try{return JSON.parse(ab(a))}catch(b){return L(123),ao(2),{}}}function jo(){return co["0"]||""}function ko(){return co["1"]||""}function lo(){var a=!1;return a}function mo(){return co["6"]!==!1}function no(){var a="";return a}
function oo(){var a=!1;return a}function po(){var a="";return a};var qo={},ro=Object.freeze((qo[J.m.Ea]=1,qo[J.m.zg]=1,qo[J.m.Ag]=1,qo[J.m.Ob]=1,qo[J.m.sa]=1,qo[J.m.nb]=1,qo[J.m.ob]=1,qo[J.m.yb]=1,qo[J.m.ed]=1,qo[J.m.Sb]=1,qo[J.m.cb]=1,qo[J.m.Hc]=1,qo[J.m.bf]=1,qo[J.m.oa]=1,qo[J.m.mk]=1,qo[J.m.ef]=1,qo[J.m.Kg]=1,qo[J.m.Lg]=1,qo[J.m.Zd]=1,qo[J.m.Ck]=1,qo[J.m.rc]=1,qo[J.m.ce]=1,qo[J.m.Ek]=1,qo[J.m.Og]=1,qo[J.m.fi]=1,qo[J.m.Kc]=1,qo[J.m.Lc]=1,qo[J.m.Pa]=1,qo[J.m.gi]=1,qo[J.m.Vb]=1,qo[J.m.pb]=1,qo[J.m.ld]=1,qo[J.m.md]=1,qo[J.m.rf]=1,qo[J.m.ii]=1,qo[J.m.uf]=1,qo[J.m.vc]=
1,qo[J.m.od]=1,qo[J.m.Vg]=1,qo[J.m.Wb]=1,qo[J.m.rd]=1,qo[J.m.Ki]=1,qo));Object.freeze([J.m.Aa,J.m.Wa,J.m.Db,J.m.zb,J.m.hi,J.m.Qa,J.m.bi,J.m.An]);
var so={},to=Object.freeze((so[J.m.dn]=1,so[J.m.fn]=1,so[J.m.gn]=1,so[J.m.hn]=1,so[J.m.jn]=1,so[J.m.mn]=1,so[J.m.nn]=1,so[J.m.on]=1,so[J.m.qn]=1,so[J.m.Td]=1,so)),uo={},vo=Object.freeze((uo[J.m.dk]=1,uo[J.m.ek]=1,uo[J.m.Pd]=1,uo[J.m.Qd]=1,uo[J.m.fk]=1,uo[J.m.Xc]=1,uo[J.m.Rd]=1,uo[J.m.jc]=1,uo[J.m.Gc]=1,uo[J.m.kc]=1,uo[J.m.kb]=1,uo[J.m.Sd]=1,uo[J.m.xb]=1,uo[J.m.gk]=1,uo)),wo=Object.freeze([J.m.Ea,J.m.Re,J.m.Ob,J.m.Hc,J.m.Zd,J.m.lf,J.m.pb,J.m.od]),xo=Object.freeze([].concat(ua(wo))),yo=Object.freeze([J.m.ob,
J.m.Lg,J.m.rf,J.m.ii,J.m.Hg]),zo=Object.freeze([].concat(ua(yo))),Ao={},Bo=(Ao[J.m.U]="1",Ao[J.m.ia]="2",Ao[J.m.V]="3",Ao[J.m.La]="4",Ao),Co={},Do=Object.freeze((Co.search="s",Co.youtube="y",Co.playstore="p",Co.shopping="h",Co.ads="a",Co.maps="m",Co));function Eo(a){return typeof a!=="object"||a===null?{}:a}function Fo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Go(a){if(a!==void 0&&a!==null)return Fo(a)}function Ho(a){return typeof a==="number"?a:Go(a)};function Io(a){return a&&a.indexOf("pending:")===0?Jo(a.substr(8)):!1}function Jo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=yb();return b<c+3E5&&b>c-9E5};var Ko=!1,Lo=!1,Mo=!1,No=0,Oo=!1,Po=[];function Qo(a){if(No===0)Oo&&Po&&(Po.length>=100&&Po.shift(),Po.push(a));else if(Ro()){var b=Ri(41,'google.tagmanager.ta.prodqueue'),c=wc(b,[]);c.length>=50&&c.shift();c.push(a)}}function So(){To();Kc(z,"TAProdDebugSignal",So)}function To(){if(!Lo){Lo=!0;Uo();var a=Po;Po=void 0;a==null||a.forEach(function(b){Qo(b)})}}
function Uo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Jo(a)?No=1:!Io(a)||Ko||Mo?No=2:(Mo=!0,Jc(z,"TAProdDebugSignal",So,!1),x.setTimeout(function(){To();Ko=!0},200))}function Ro(){if(!Oo)return!1;switch(No){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Vo=!1;function Wo(a,b){var c=Bm(),d=zm();if(Ro()){var e=Xo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Qo(e)}}
function Yo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=Ro()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Xo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Qo(h)}}function Zo(a){Ro()&&Yo(a())}
function Xo(a,b){b=b===void 0?{}:b;b.groupId=$o;var c,d=b,e={publicId:ap};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'28',messageType:a};c.containerProduct=Vo?"OGT":"GTM";c.key.targetRef=bp;return c}var ap="",bp={ctid:"",isDestination:!1},$o;
function cp(a){var b=jg.ctid,c=ym();No=0;Oo=!0;Uo();$o=a;ap=b;Vo=bk;bp={ctid:b,isDestination:c}};var dp=[J.m.U,J.m.ia,J.m.V,J.m.La],ep,fp;function gp(a){var b=a[J.m.fc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)qb(a,function(d){return function(e,f){if(e!==J.m.fc){var g=Fo(f),h=b[d.cg],m=jo(),n=ko();Vm=!0;Um&&cb("TAGGING",20);Qm().declare(e,g,h,m,n)}}}(c))}
function hp(a){Yn();!fp&&ep&&Zn("crc");fp=!0;var b=a[J.m.rg];b&&L(41);var c=a[J.m.fc];c?L(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)qb(a,function(e){return function(f,g){if(f!==J.m.fc&&f!==J.m.rg){var h=Go(g),m=c[e.dg],n=Number(b),p=jo(),q=ko();n=n===void 0?0:n;Um=!0;Vm&&cb("TAGGING",20);Qm().default(f,h,m,p,q,n,Xm)}}}(d))}
function ip(a){Xm.usedContainerScopedDefaults=!0;var b=a[J.m.fc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ko())&&!c.includes(jo()))return}qb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Xm.usedContainerScopedDefaults=!0;Xm.containerScopedDefaults[d]=e==="granted"?3:2})}
function jp(a,b){Yn();ep=!0;qb(a,function(c,d){var e=Fo(d);Um=!0;Vm&&cb("TAGGING",20);Qm().update(c,e,Xm)});cn(b.eventId,b.priorityId)}function kp(a){a.hasOwnProperty("all")&&(Xm.selectedAllCorePlatformServices=!0,qb(Do,function(b){Xm.corePlatformServices[b]=a.all==="granted";Xm.usedCorePlatformServices=!0}));qb(a,function(b,c){b!=="all"&&(Xm.corePlatformServices[b]=c==="granted",Xm.usedCorePlatformServices=!0)})}function O(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Ym(b)})}
function lp(a,b){bn(a,b)}function mp(a,b){en(a,b)}function np(a,b){dn(a,b)}function op(){var a=[J.m.U,J.m.La,J.m.V];Qm().waitForUpdate(a,500,Xm)}function pp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Qm().clearTimeout(d,void 0,Xm)}cn()}function qp(){if(!dk)for(var a=mo()?ok(Oj.Sa):ok(Oj.qb),b=0;b<dp.length;b++){var c=dp[b],d=c,e=a[c]?"granted":"denied";Qm().implicit(d,e)}};var rp=!1,sp=[];function tp(){if(!rp){rp=!0;for(var a=sp.length-1;a>=0;a--)sp[a]();sp=[]}};var up=x.google_tag_manager=x.google_tag_manager||{};function vp(a,b){return up[a]=up[a]||b()}function wp(){var a=jg.ctid,b=xp;up[a]=up[a]||b}function yp(){var a=up.sequence||1;up.sequence=a+1;return a};function zp(){if(up.pscdl!==void 0)un(qn.Z.Lh)===void 0&&tn(qn.Z.Lh,up.pscdl);else{var a=function(c){up.pscdl=c;tn(qn.Z.Lh,c)},b=function(){a("error")};try{sc.cookieDeprecationLabel?(a("pending"),sc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Ap=0;function Bp(a){il&&a===void 0&&Ap===0&&(Hn("mcc","1"),Ap=1)};var Cp={Ef:{Tm:"cd",Um:"ce",Vm:"cf",Wm:"cpf",Xm:"cu"}};var Dp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Ep=/\s/;
function Fp(a,b){if(jb(a)){a=wb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Dp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Ep.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Gp(a,b){for(var c={},d=0;d<a.length;++d){var e=Fp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Hp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Ip={},Hp=(Ip[0]=0,Ip[1]=1,Ip[2]=2,Ip[3]=0,Ip[4]=1,Ip[5]=0,Ip[6]=0,Ip[7]=0,Ip);var Jp=Number('')||500,Kp={},Lp={},Mp={initialized:11,complete:12,interactive:13},Np={},Op=Object.freeze((Np[J.m.pb]=!0,Np)),Pp=void 0;function Qp(a,b){if(b.length&&il){var c;(c=Kp)[a]!=null||(c[a]=[]);Lp[a]!=null||(Lp[a]=[]);var d=b.filter(function(e){return!Lp[a].includes(e)});Kp[a].push.apply(Kp[a],ua(d));Lp[a].push.apply(Lp[a],ua(d));!Pp&&d.length>0&&(In("tdc",!0),Pp=x.setTimeout(function(){Ln();Kp={};Pp=void 0},Jp))}}
function Rp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Sp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;id(t)==="object"?u=t[r]:id(t)==="array"&&(u=t[r]);return u===void 0?Op[r]:u},f=Rp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=id(m)==="object"||id(m)==="array",q=id(n)==="object"||id(n)==="array";if(p&&q)Sp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Tp(){Hn("tdc",function(){Pp&&(x.clearTimeout(Pp),Pp=void 0);var a=[],b;for(b in Kp)Kp.hasOwnProperty(b)&&a.push(b+"*"+Kp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Up=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Vp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l(Vp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Wp=function(a){for(var b={},c=Vp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Up.prototype.getMergedValues=function(a,b,c){function d(n){kd(n)&&qb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Vp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Xp=function(a){for(var b=[J.m.We,J.m.Se,J.m.Te,J.m.Ue,J.m.Ve,J.m.Xe,J.m.Ye],c=Vp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Yp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Zp=function(a,
b){a.H=b;return a},$p=function(a,b){a.R=b;return a},aq=function(a,b){a.C=b;return a},bq=function(a,b){a.N=b;return a},cq=function(a,b){a.ba=b;return a},dq=function(a,b){a.P=b;return a},eq=function(a,b){a.eventMetadata=b||{};return a},fq=function(a,b){a.onSuccess=b;return a},gq=function(a,b){a.onFailure=b;return a},hq=function(a,b){a.isGtmEvent=b;return a},iq=function(a){return new Up(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={A:{Kj:"accept_by_default",qg:"add_tag_timing",Hh:"allow_ad_personalization",Mj:"batch_on_navigation",Oj:"client_id_source",Ie:"consent_event_id",Je:"consent_priority_id",Oq:"consent_state",ja:"consent_updated",Wc:"conversion_linker_enabled",ya:"cookie_options",tg:"create_dc_join",ug:"create_fpm_geo_join",vg:"create_fpm_signals_join",Od:"create_google_join",Le:"em_event",Rq:"endpoint_for_debug",bk:"enhanced_client_id_source",Oh:"enhanced_match_result",ie:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Yk:"event_usage",Xg:"extra_tag_experiment_ids",Yq:"add_parameter",wi:"attribution_reporting_experiment",xi:"counting_method",Yg:"send_as_iframe",Zq:"parameter_order",Zg:"parsed_target",Yn:"ga4_collection_subdomain",bl:"gbraid_cookie_marked",fa:"hit_type",sd:"hit_type_override",eo:"is_config_command",Hf:"is_consent_update",If:"is_conversion",kl:"is_ecommerce",ud:"is_external_event",Ci:"is_fallback_aw_conversion_ping_allowed",Jf:"is_first_visit",ml:"is_first_visit_conversion",ah:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",bh:"is_fpm_split",me:"is_gcp_conversion",Di:"is_google_signals_allowed",vd:"is_merchant_center",nl:"is_new_join_id_required",eh:"is_new_to_site",fh:"is_server_side_destination",ne:"is_session_start",pl:"is_session_start_conversion",gr:"is_sgtm_ga_ads_conversion_study_control_group",hr:"is_sgtm_prehit",ql:"is_sgtm_service_worker",Ei:"is_split_conversion",fo:"is_syn",oe:"join_id",Fi:"join_elapsed",Kf:"join_timer_sec",se:"tunnel_updated",lr:"prehit_for_retry",nr:"promises",qr:"record_aw_latency",
yc:"redact_ads_data",te:"redact_click_ids",ro:"remarketing_only",Al:"send_ccm_parallel_ping",jh:"send_fledge_experiment",ur:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ji:"send_to_targets",Bl:"send_user_data_hit",hb:"source_canonical_id",Ha:"speculative",Gl:"speculative_in_message",Hl:"suppress_script_load",Il:"syn_or_mod",Ml:"transient_ecsid",Qf:"transmission_type",ib:"user_data",xr:"user_data_from_automatic",yr:"user_data_from_automatic_getter",ve:"user_data_from_code",nh:"user_data_from_manual",
Ol:"user_data_mode",Rf:"user_id_updated"}};var jq={Om:Number("5"),Pr:Number("")},kq=[],lq=!1;function mq(a){kq.push(a)}var nq="?id="+jg.ctid,oq=void 0,pq={},qq=void 0,rq=new function(){var a=5;jq.Om>0&&(a=jq.Om);this.H=a;this.C=0;this.N=[]},sq=1E3;
function tq(a,b){var c=oq;if(c===void 0)if(b)c=yp();else return"";for(var d=[bl("https://www.googletagmanager.com"),"/a",nq],e=l(kq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function uq(){if(Oj.ba&&(qq&&(x.clearTimeout(qq),qq=void 0),oq!==void 0&&vq)){var a=pn(Pm.X.Oc);if(ln(a))lq||(lq=!0,nn(a,uq));else{var b;if(!(b=pq[oq])){var c=rq;b=c.C<c.H?!1:yb()-c.N[c.C%c.H]<1E3}if(b||sq--<=0)L(1),pq[oq]=!0;else{var d=rq,e=d.C++%d.H;d.N[e]=yb();var f=tq(!0);hm({destinationId:jg.ctid,endpoint:56,eventId:oq},f);lq=vq=!1}}}}function wq(){if(hl&&Oj.ba){var a=tq(!0,!0);hm({destinationId:jg.ctid,endpoint:56,eventId:oq},a)}}var vq=!1;
function xq(a){pq[a]||(a!==oq&&(uq(),oq=a),vq=!0,qq||(qq=x.setTimeout(uq,500)),tq().length>=2022&&uq())}var yq=nb();function zq(){yq=nb()}function Aq(){return[["v","3"],["t","t"],["pid",String(yq)]]};var Bq={};function Cq(a,b,c){hl&&a!==void 0&&(Bq[a]=Bq[a]||[],Bq[a].push(c+b),xq(a))}function Dq(a){var b=a.eventId,c=a.Nd,d=[],e=Bq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Bq[b];return d};function Eq(a,b,c,d){var e=Fp(a,!0);e&&Fq.register(e,b,c,d)}function Gq(a,b,c,d){var e=Fp(c,d.isGtmEvent);e&&(ak&&(d.deferrable=!0),Fq.push("event",[b,a],e,d))}function Hq(a,b,c,d){var e=Fp(c,d.isGtmEvent);e&&Fq.push("get",[a,b],e,d)}function Iq(a){var b=Fp(a,!0),c;b?c=Jq(Fq,b).C:c={};return c}function Kq(a,b){var c=Fp(a,!0);c&&Lq(Fq,c,b)}
var Mq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Nq=function(a,b,c,d){this.H=yb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Oq=function(){this.destinations={};this.C={};this.commands=[]},Jq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Mq},Pq=function(a,b,c,d){if(d.C){var e=Jq(a,d.C),f=e.ba;if(f){var g=ld(c,null),h=ld(e.R[d.C.id],null),m=ld(e.P,null),n=ld(e.C,null),p=ld(a.C,null),q={};if(hl)try{q=
ld(qk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Cq(d.messageContext.eventId,r,w)},u=iq(hq(gq(fq(eq(cq(bq(dq(aq($p(Zp(new Yp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Cq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(il&&w==="config"){var A,C=(A=Fp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var E,G=wc("google_tag_data",{});G.td||(G.td={});E=G.td;var I=ld(u.P);ld(u.C,I);var M=[],S;for(S in E)E.hasOwnProperty(S)&&Sp(E[S],I).length&&M.push(S);M.length&&(Qp(y,M),cb("TAGGING",Mp[z.readyState]||14));E[y]=I}}f(d.C.id,b,d.H,u)}catch(ea){Cq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():nn(e.ka,v)}}};
Oq.prototype.register=function(a,b,c,d){var e=Jq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=pn(c),Lq(this,a,d||{}),this.flush())};
Oq.prototype.push=function(a,b,c,d){c!==void 0&&(Jq(this,c).status===1&&(Jq(this,c).status=2,this.push("require",[{}],c,{})),Jq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.A.Pf]||(d.eventMetadata[P.A.Pf]=[c.destinationId]),d.eventMetadata[P.A.Ji]||(d.eventMetadata[P.A.Ji]=[c.id]));this.commands.push(new Nq(a,c,b,d));d.deferrable||this.flush()};
Oq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,sh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Jq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Jq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];qb(h,function(t,u){ld(Fb(t,u),b.C)});Mj(h,!0);break;case "config":var m=Jq(this,g);
e.Qc={};qb(f.args[0],function(t){return function(u,v){ld(Fb(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.od];delete e.Qc[J.m.od];var p=g.destinationId===g.id;Mj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Pq(this,J.m.qa,e.Qc,f);m.N=!0;p?ld(e.Qc,m.P):(ld(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.sh={};qb(f.args[0],function(t){return function(u,v){ld(Fb(u,v),t.sh)}}(e));Mj(e.sh);Pq(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[J.m.qc]=f.args[0],q[J.m.Ic]=f.args[1],q);Pq(this,J.m.Cb,r,f)}this.commands.shift();
Qq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Qq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Jq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Lq=function(a,b,c){var d=ld(c,null);ld(Jq(a,b).C,d);Jq(a,b).C=d},Fq=new Oq;function Rq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Sq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Tq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Jl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=pc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Sq(e,"load",f);Sq(e,"error",f)};Rq(e,"load",f);Rq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Uq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Gl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Vq(c,b)}
function Vq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Tq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Wq=function(){this.ba=this.ba;this.P=this.P};Wq.prototype.ba=!1;Wq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Wq.prototype[Symbol.dispose]=function(){this.dispose()};Wq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Wq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Xq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Yq=function(a,b){b=b===void 0?{}:b;Wq.call(this);this.C=null;this.ka={};this.qb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Er)!=null?d:!1};ra(Yq,Wq);Yq.prototype.N=function(){this.ka={};this.R&&(Sq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Wq.prototype.N.call(this)};var $q=function(a){return typeof a.H.__tcfapi==="function"||Zq(a)!=null};
Yq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=kl(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Xq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{ar(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Yq.prototype.removeEventListener=function(a){a&&a.listenerId&&ar(this,"removeEventListener",null,a.listenerId)};
var cr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=br(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&br(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?br(a.purpose.legitimateInterests,
b)&&br(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},br=function(a,b){return!(!a||!a[b])},ar=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Zq(a)){dr(a);var g=++a.qb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Zq=function(a){if(a.C)return a.C;a.C=Hl(a.H,"__tcfapiLocator");return a.C},dr=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Rq(a.H,"message",b)}},er=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Xq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Uq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var fr={1:0,3:0,4:0,7:3,9:3,10:3};function gr(){return vp("tcf",function(){return{}})}var hr=function(){return new Yq(x,{timeoutMs:-1})};
function ir(){var a=gr(),b=hr();$q(b)&&!jr()&&!kr()&&L(124);if(!a.active&&$q(b)){jr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Qm().active=!0,a.tcString="tcunavailable");op();try{b.addEventListener(function(c){if(c.internalErrorState!==0)lr(a),pp([J.m.U,J.m.La,J.m.V]),Qm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,kr()&&(a.active=!0),!mr(c)||jr()||kr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in fr)fr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(mr(c)){var g={},h;for(h in fr)if(fr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={rp:!0};p=p===void 0?{}:p;m=er(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.rp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?cr(n,"1",0):!0:!1;g["1"]=m}else g[h]=cr(c,h,fr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(pp([J.m.U,J.m.La,J.m.V]),Qm().active=!0):(r[J.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":pp([J.m.V]),jp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:nr()||""}))}}else pp([J.m.U,J.m.La,J.m.V])})}catch(c){lr(a),pp([J.m.U,J.m.La,J.m.V]),Qm().active=!0}}}
function lr(a){a.type="e";a.tcString="tcunavailable"}function mr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function jr(){return x.gtag_enable_tcf_support===!0}function kr(){return gr().enableAdvertiserConsentMode===!0}function nr(){var a=gr();if(a.active)return a.tcString}function or(){var a=gr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function pr(a){if(!fr.hasOwnProperty(String(a)))return!0;var b=gr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var qr=[J.m.U,J.m.ia,J.m.V,J.m.La],rr={},sr=(rr[J.m.U]=1,rr[J.m.ia]=2,rr);function tr(a){if(a===void 0)return 0;switch(N(a,J.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function ur(){return(D(183)?Xi.yp:Xi.zp).indexOf(ko())!==-1&&sc.globalPrivacyControl===!0}function vr(a){if(ur())return!1;var b=tr(a);if(b===3)return!1;switch(Zm(J.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function wr(){return an()||!Ym(J.m.U)||!Ym(J.m.ia)}function xr(){var a={},b;for(b in sr)sr.hasOwnProperty(b)&&(a[sr[b]]=Zm(b));return"G1"+bf(a[1]||0)+bf(a[2]||0)}var yr={},zr=(yr[J.m.U]=0,yr[J.m.ia]=1,yr[J.m.V]=2,yr[J.m.La]=3,yr);function Ar(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Br(a){for(var b="1",c=0;c<qr.length;c++){var d=b,e,f=qr[c],g=Xm.delegatedConsentTypes[f];e=g===void 0?0:zr.hasOwnProperty(g)?12|zr[g]:8;var h=Qm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Ar(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Ar(m.declare)<<4|Ar(m.default)<<2|Ar(m.update)])}var n=b,p=(ur()?1:0)<<3,q=(an()?1:0)<<2,r=tr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Xm.containerScopedDefaults.ad_storage<<4|Xm.containerScopedDefaults.analytics_storage<<2|Xm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Xm.usedContainerScopedDefaults?1:0)<<2|Xm.containerScopedDefaults.ad_personalization]}
function Cr(){if(!Ym(J.m.V))return"-";for(var a=Object.keys(Do),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Xm.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Do[m])}(Xm.usedCorePlatformServices?Xm.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Dr(){return mo()||(jr()||kr())&&or()==="1"?"1":"0"}function Er(){return(mo()?!0:!(!jr()&&!kr())&&or()==="1")||!Ym(J.m.V)}
function Fr(){var a="0",b="0",c;var d=gr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=gr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;mo()&&(h|=1);or()==="1"&&(h|=2);jr()&&(h|=4);var m;var n=gr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Qm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Gr(){return ko()==="US-CO"};var fg;function Hr(){var a=!1;return a}function Ir(){D(212)&&bk&&gg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Jr;function Kr(){if(vc===null)return 0;var a=$c();if(!a)return 0;var b=a.getEntriesByName(vc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Lr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Mr(a){a=a===void 0?{}:a;var b=jg.ctid.split("-")[0].toUpperCase(),c={ctid:jg.ctid,Aj:Uj,Ej:Tj,gm:xm.qe?2:1,Dq:a.Em,we:jg.canonicalContainerId};if(D(210)){var d;c.tq=(d=Em())==null?void 0:d.canonicalContainerId}if(D(204)){var e;c.No=(e=Jr)!=null?e:Jr=Kr()}c.we!==a.Ma&&(c.Ma=a.Ma);var f=Cm();c.rm=f?f.canonicalContainerId:void 0;bk?(c.Uc=Lr[b],c.Uc||(c.Uc=0)):c.Uc=dk?13:10;Oj.C?(c.Sc=0,c.Sl=2):Oj.N?c.Sc=1:Hr()?c.Sc=2:c.Sc=3;var g={6:!1};Oj.H===2?g[7]=!0:Oj.H===1&&(g[2]=!0);if(vc){var h=Nk(Tk(vc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Ul=g;return ef(c,a.ph)}
function Nr(){if(!D(192))return Mr();if(D(193))return ef({Aj:Uj,Ej:Tj});var a=jg.ctid.split("-")[0].toUpperCase(),b={ctid:jg.ctid,Aj:Uj,Ej:Tj,gm:xm.qe?2:1,we:jg.canonicalContainerId},c=Cm();b.rm=c?c.canonicalContainerId:void 0;bk?(b.Uc=Lr[a],b.Uc||(b.Uc=0)):b.Uc=dk?13:10;Oj.C?(b.Sc=0,b.Sl=2):Oj.N?b.Sc=1:Hr()?b.Sc=2:b.Sc=3;var d={6:!1};Oj.H===2?d[7]=!0:Oj.H===1&&(d[2]=!0);if(vc){var e=Nk(Tk(vc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Ul=d;return ef(b)};function Or(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||yb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var Pr=["ad_storage","ad_user_data"];function Qr(a,b){if(!a)return cb("TAGGING",32),10;if(b===null||b===void 0||b==="")return cb("TAGGING",33),11;var c=Rr(!1);if(c.error!==0)return cb("TAGGING",34),c.error;if(!c.value)return cb("TAGGING",35),2;c.value[a]=b;var d=Sr(c);d!==0&&cb("TAGGING",36);return d}
function Tr(a){if(!a)return cb("TAGGING",27),{error:10};var b=Rr();if(b.error!==0)return cb("TAGGING",29),b;if(!b.value)return cb("TAGGING",30),{error:2};if(!(a in b.value))return cb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(cb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Rr(a){a=a===void 0?!0:a;if(!Ym(Pr))return cb("TAGGING",43),{error:3};try{if(!x.localStorage)return cb("TAGGING",44),{error:1}}catch(f){return cb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return cb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return cb("TAGGING",47),{error:12}}}catch(f){return cb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return cb("TAGGING",49),{error:4};
if(b.version!==1)return cb("TAGGING",50),{error:5};try{var e=Ur(b);a&&e&&Sr({value:b,error:0})}catch(f){return cb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Ur(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,cb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Ur(a[e.value])||c;return c}return!1}
function Sr(a){if(a.error)return a.error;if(!a.value)return cb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return cb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return cb("TAGGING",53),7}return 0};var Vr={qj:"value",Gb:"conversionCount"},Wr=[Vr,{fm:9,ym:10,qj:"timeouts",Gb:"timeouts"}];function Xr(){var a=Vr;if(!Yr(a))return{};var b=Zr(Wr),c=b[a.Gb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Gb]=c+1,d));return $r(e)?e:b}
function Zr(a){var b;a:{var c=Tr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Yr(m)){var n=e[m.qj];n===void 0||Number.isNaN(n)?f[m.Gb]=-1:f[m.Gb]=Number(n)}else f[m.Gb]=-1}return f}
function $r(a,b){b=b||{};for(var c=yb(),d=Or(b,c,!0),e={},f=l(Wr),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Gb];m!==void 0&&m!==-1&&(e[h.qj]=m)}e.creationTimeMs=c;return Qr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Yr(a){return Ym(["ad_storage","ad_user_data"])?!a.ym||Ja(a.ym):!1}function as(a){return Ym(["ad_storage","ad_user_data"])?!a.fm||Ja(a.fm):!1};function bs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var cs={O:{so:0,Lj:1,sg:2,Rj:3,Jh:4,Pj:5,Qj:6,Sj:7,Kh:8,Wk:9,Vk:10,ui:11,Xk:12,Wg:13,al:14,Mf:15,po:16,ue:17,Oi:18,Pi:19,Qi:20,Kl:21,Ri:22,Mh:23,Zj:24}};cs.O[cs.O.so]="RESERVED_ZERO";cs.O[cs.O.Lj]="ADS_CONVERSION_HIT";cs.O[cs.O.sg]="CONTAINER_EXECUTE_START";cs.O[cs.O.Rj]="CONTAINER_SETUP_END";cs.O[cs.O.Jh]="CONTAINER_SETUP_START";cs.O[cs.O.Pj]="CONTAINER_BLOCKING_END";cs.O[cs.O.Qj]="CONTAINER_EXECUTE_END";cs.O[cs.O.Sj]="CONTAINER_YIELD_END";cs.O[cs.O.Kh]="CONTAINER_YIELD_START";cs.O[cs.O.Wk]="EVENT_EXECUTE_END";
cs.O[cs.O.Vk]="EVENT_EVALUATION_END";cs.O[cs.O.ui]="EVENT_EVALUATION_START";cs.O[cs.O.Xk]="EVENT_SETUP_END";cs.O[cs.O.Wg]="EVENT_SETUP_START";cs.O[cs.O.al]="GA4_CONVERSION_HIT";cs.O[cs.O.Mf]="PAGE_LOAD";cs.O[cs.O.po]="PAGEVIEW";cs.O[cs.O.ue]="SNIPPET_LOAD";cs.O[cs.O.Oi]="TAG_CALLBACK_ERROR";cs.O[cs.O.Pi]="TAG_CALLBACK_FAILURE";cs.O[cs.O.Qi]="TAG_CALLBACK_SUCCESS";cs.O[cs.O.Kl]="TAG_EXECUTE_END";cs.O[cs.O.Ri]="TAG_EXECUTE_START";cs.O[cs.O.Mh]="CUSTOM_PERFORMANCE_START";cs.O[cs.O.Zj]="CUSTOM_PERFORMANCE_END";var ds=[],es={},fs={};var gs=["1"];function hs(a){return a.origin!=="null"};function is(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ja(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function js(a,b,c,d){if(!ks(d))return[];if(ds.includes("1")){var e;(e=$c())==null||e.mark("1-"+cs.O.Mh+"-"+(fs["1"]||0))}var f=is(a,String(b||ls()),c);if(ds.includes("1")){var g="1-"+cs.O.Zj+"-"+(fs["1"]||0),h={start:"1-"+cs.O.Mh+"-"+(fs["1"]||0),end:g},m;(m=$c())==null||m.mark(g);var n,p,q=(p=(n=$c())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(fs["1"]=(fs["1"]||0)+1,es["1"]=q+(es["1"]||0))}return f}
function ms(a,b,c,d,e){if(ks(e)){var f=ns(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=os(f,function(g){return g.ap},b);if(f.length===1)return f[0];f=os(f,function(g){return g.cq},c);return f[0]}}}function ps(a,b,c,d){var e=ls(),f=window;hs(f)&&(f.document.cookie=a);var g=ls();return e!==g||c!==void 0&&js(b,g,!1,d).indexOf(c)>=0}
function qs(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!ks(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=rs(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Yp);g=e(g,"samesite",c.uq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ss(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!ts(u,c.path)&&ps(v,a,b,c.Dc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return ts(n,c.path)?1:ps(g,a,b,c.Dc)?0:1}function us(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return qs(a,b,c)}
function os(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ns(a,b,c){for(var d=[],e=js(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Ro:e[f],So:g.join("."),ap:Number(n[0])||1,cq:Number(n[1])||1})}}}return d}function rs(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var vs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,ws=/(^|\.)doubleclick\.net$/i;function ts(a,b){return a!==void 0&&(ws.test(window.document.location.hostname)||b==="/"&&vs.test(a))}function xs(a){if(!a)return 1;var b=a;Ja(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ys(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function zs(a,b){var c=""+xs(a),d=ys(b);d>1&&(c+="-"+d);return c}
var ls=function(){return hs(window)?window.document.cookie:""},ks=function(a){return a&&Ja(7)?(Array.isArray(a)?a:[a]).every(function(b){return $m(b)&&Ym(b)}):!0},ss=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;ws.test(e)||vs.test(e)||a.push("none");return a};function As(a){var b=Math.round(Math.random()*2147483647);return a?String(b^bs(a)&2147483647):String(b)}function Bs(a){return[As(a),Math.round(yb()/1E3)].join(".")}function Cs(a,b,c,d,e){var f=xs(b),g;return(g=ms(a,f,ys(c),d,e))==null?void 0:g.So};var Ds;function Es(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Fs,d=Gs,e=Hs();if(!e.init){Jc(z,"mousedown",a);Jc(z,"keyup",a);Jc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Is(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Hs().decorators.push(f)}
function Js(a,b,c){for(var d=Hs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Bb(e,g.callback())}}return e}
function Hs(){var a=wc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ks=/(.*?)\*(.*?)\*(.*)/,Ls=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ms=/^(?:www\.|m\.|amp\.)+/,Ns=/([^?#]+)(\?[^#]*)?(#.*)?/;function Os(a){var b=Ns.exec(a);if(b)return{wj:b[1],query:b[2],fragment:b[3]}}function Ps(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Qs(a,b){var c=[sc.userAgent,(new Date).getTimezoneOffset(),sc.userLanguage||sc.language,Math.floor(yb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Ds)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Ds=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Ds[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Rs(a){return function(b){var c=Tk(x.location.href),d=c.search.replace("?",""),e=Kk(d,"_gl",!1,!0)||"";b.query=Ss(e)||{};var f=Nk(c,"fragment"),g;var h=-1;if(Db(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Ss(g||"")||{};a&&Ts(c,d,f)}}function Us(a,b){var c=Ps(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ts(a,b,c){function d(g,h){var m=Us("_gl",g);m.length&&(m=h+m);return m}if(rc&&rc.replaceState){var e=Ps("_gl");if(e.test(b)||e.test(c)){var f=Nk(a,"path");b=d(b,"?");c=d(c,"#");rc.replaceState({},"",""+f+b+c)}}}function Vs(a,b){var c=Rs(!!b),d=Hs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Bb(e,f.query),a&&Bb(e,f.fragment));return e}
var Ss=function(a){try{var b=Ws(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ab(d[e+1]);c[f]=g}cb("TAGGING",6);return c}}catch(h){cb("TAGGING",8)}};function Ws(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ks.exec(d);if(f){c=f;break a}d=Mk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Qs(h,p)){m=!0;break a}m=!1}if(m)return h;cb("TAGGING",7)}}}
function Xs(a,b,c,d,e){function f(p){p=Us(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Os(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.wj+h+m}
function Ys(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push($a(String(y))))}var A=v.join("*");u=["1",Qs(A),A].join("*");d?(Ja(3)||Ja(1)||!p)&&Zs("_gl",u,a,p,q):$s("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Js(b,1,d),f=Js(b,2,d),g=Js(b,4,d),h=Js(b,3,d);c(e,!1,!1);c(f,!0,!1);Ja(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
at(m,h[m],a)}function at(a,b,c){c.tagName.toLowerCase()==="a"?$s(a,b,c):c.tagName.toLowerCase()==="form"&&Zs(a,b,c)}function $s(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ja(4)||d)){var h=x.location.href,m=Os(c.href),n=Os(h);g=!(m&&n&&m.wj===n.wj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Xs(a,b,c.href,d,e);hc.test(p)&&(c.href=p)}}
function Zs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Xs(a,b,f,d,e);hc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Fs(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Ys(e,e.hostname)}}catch(g){}}function Gs(a){try{var b=a.getAttribute("action");if(b){var c=Nk(Tk(b),"host");Ys(a,c)}}catch(d){}}function bt(a,b,c,d){Es();var e=c==="fragment"?2:1;d=!!d;Is(a,b,e,d,!1);e===2&&cb("TAGGING",23);d&&cb("TAGGING",24)}
function ct(a,b){Es();Is(a,[Pk(x.location,"host",!0)],b,!0,!0)}function dt(){var a=z.location.hostname,b=Ls.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Mk(f[2])||"":Mk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ms,""),m=e.replace(Ms,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function et(a,b){return a===!1?!1:a||b||dt()};var ft=["1"],gt={},ht={};function it(a,b){b=b===void 0?!0:b;var c=jt(a.prefix);if(gt[c])kt(a);else if(lt(c,a.path,a.domain)){var d=ht[jt(a.prefix)]||{id:void 0,Bh:void 0};b&&mt(a,d.id,d.Bh);kt(a)}else{var e=Vk("auiddc");if(e)cb("TAGGING",17),gt[c]=e;else if(b){var f=jt(a.prefix),g=Bs();nt(f,g,a);lt(c,a.path,a.domain);kt(a,!0)}}}
function kt(a,b){if((b===void 0?0:b)&&Yr(Vr)){var c=Rr(!1);c.error!==0?cb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Sr(c)!==0&&cb("TAGGING",41)):cb("TAGGING",40):cb("TAGGING",39)}if(as(Vr)&&Zr([Vr])[Vr.Gb]===-1){for(var d={},e=(d[Vr.Gb]=0,d),f=l(Wr),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Vr&&as(h)&&(e[h.Gb]=0)}$r(e,a)}}
function mt(a,b,c){var d=jt(a.prefix),e=gt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(yb()/1E3)));nt(d,h,a,g*1E3)}}}}function nt(a,b,c,d){var e;e=["1",zs(c.domain,c.path),b].join(".");var f=Or(c,d);f.Dc=ot();us(a,e,f)}function lt(a,b,c){var d=Cs(a,b,c,ft,ot());if(!d)return!1;pt(a,d);return!0}
function pt(a,b){var c=b.split(".");c.length===5?(gt[a]=c.slice(0,2).join("."),ht[a]={id:c.slice(2,4).join("."),Bh:Number(c[4])||0}):c.length===3?ht[a]={id:c.slice(0,2).join("."),Bh:Number(c[2])||0}:gt[a]=b}function jt(a){return(a||"_gcl")+"_au"}function qt(a){function b(){Ym(c)&&a()}var c=ot();dn(function(){b();Ym(c)||en(b,c)},c)}
function rt(a){var b=Vs(!0),c=jt(a.prefix);qt(function(){var d=b[c];if(d){pt(c,d);var e=Number(gt[c].split(".")[1])*1E3;if(e){cb("TAGGING",16);var f=Or(a,e);f.Dc=ot();var g=["1",zs(a.domain,a.path),d].join(".");us(c,g,f)}}})}function st(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Cs(a,e.path,e.domain,ft,ot());h&&(g[a]=h);return g};qt(function(){bt(f,b,c,d)})}function ot(){return["ad_storage","ad_user_data"]};function tt(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Hj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function ut(a,b){var c=tt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Hj]||(d[c[e].Hj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Hj].push(g)}}return d};var vt={},wt=(vt.k={da:/^[\w-]+$/},vt.b={da:/^[\w-]+$/,Bj:!0},vt.i={da:/^[1-9]\d*$/},vt.h={da:/^\d+$/},vt.t={da:/^[1-9]\d*$/},vt.d={da:/^[A-Za-z0-9_-]+$/},vt.j={da:/^\d+$/},vt.u={da:/^[1-9]\d*$/},vt.l={da:/^[01]$/},vt.o={da:/^[1-9]\d*$/},vt.g={da:/^[01]$/},vt.s={da:/^.+$/},vt);var xt={},Ct=(xt[5]={Gh:{2:zt},pj:"2",qh:["k","i","b","u"]},xt[4]={Gh:{2:zt,GCL:At},pj:"2",qh:["k","i","b"]},xt[2]={Gh:{GS2:zt,GS1:Bt},pj:"GS2",qh:"sogtjlhd".split("")},xt);function Dt(a,b,c){var d=Ct[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function zt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ct[b];if(f){for(var g=f.qh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=wt[p];r&&(r.Bj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Et(a,b,c){var d=Ct[b];if(d)return[d.pj,c||"1",Ft(a,b)].join(".")}
function Ft(a,b){var c=Ct[b];if(c){for(var d=[],e=l(c.qh),f=e.next();!f.done;f=e.next()){var g=f.value,h=wt[g];if(h){var m=a[g];if(m!==void 0)if(h.Bj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function At(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Bt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Gt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ht(a,b,c){if(Ct[b]){for(var d=[],e=js(a,void 0,void 0,Gt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Dt(g.value,b,c);h&&d.push(It(h))}return d}}function Jt(a,b,c,d,e){d=d||{};var f=zs(d.domain,d.path),g=Et(b,c,f);if(!g)return 1;var h=Or(d,e,void 0,Gt.get(c));return us(a,g,h)}function Kt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function It(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=wt[e];d.Uf?d.Uf.Bj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Kt(h,g.Uf)}}(d)):void 0:typeof f==="string"&&Kt(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Lt=function(){this.value=0};Lt.prototype.set=function(a){return this.value|=1<<a};var Mt=function(a,b){b<=0||(a.value|=1<<b-1)};Lt.prototype.get=function(){return this.value};Lt.prototype.clear=function(a){this.value&=~(1<<a)};Lt.prototype.clearAll=function(){this.value=0};Lt.prototype.equals=function(a){return this.value===a.value};function Nt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Ot(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Pt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(bs((""+b+e).toLowerCase()))};var Qt={},Rt=(Qt.gclid=!0,Qt.dclid=!0,Qt.gbraid=!0,Qt.wbraid=!0,Qt),St=/^\w+$/,Tt=/^[\w-]+$/,Ut={},Vt=(Ut.aw="_aw",Ut.dc="_dc",Ut.gf="_gf",Ut.gp="_gp",Ut.gs="_gs",Ut.ha="_ha",Ut.ag="_ag",Ut.gb="_gb",Ut),Wt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Xt=/^www\.googleadservices\.com$/;function Yt(){return["ad_storage","ad_user_data"]}function Zt(a){return!Ja(7)||Ym(a)}function $t(a,b){function c(){var d=Zt(b);d&&a();return d}dn(function(){c()||en(c,b)},b)}
function au(a){return bu(a).map(function(b){return b.gclid})}function cu(a){return du(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function du(a){var b=eu(a.prefix),c=fu("gb",b),d=fu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=bu(c).map(e("gb")),g=gu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function hu(a,b,c,d,e,f){var g=mb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Fd=f),g.labels=iu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Fd:f})}function gu(a){for(var b=Ht(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=ju(f);h&&hu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function bu(a){for(var b=[],c=js(a,z.cookie,void 0,Yt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ku(e.value);if(f!=null){var g=f;hu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return lu(b)}function mu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function nu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Lt,q=(n=b.Ka)!=null?n:new Lt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Fd=b.Fd);d.labels=mu(d.labels||[],b.labels||[]);d.Bb=mu(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function ou(a){if(!a)return new Lt;var b=new Lt;if(a===1)return Mt(b,2),Mt(b,3),b;Mt(b,a);return b}
function pu(){var a=Tr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Tt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Lt;typeof e==="number"?g=ou(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Bb:[2]}}catch(h){return null}}
function qu(){var a=Tr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Tt))return b;var f=new Lt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Bb:[2]});return b},[])}catch(b){return null}}
function ru(a){for(var b=[],c=js(a,z.cookie,void 0,Yt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=ku(e.value);f!=null&&(f.Fd=void 0,f.Ka=new Lt,f.Bb=[1],nu(b,f))}var g=pu();g&&(g.Fd=void 0,g.Bb=g.Bb||[2],nu(b,g));if(Ja(13)){var h=qu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Fd=void 0;p.Bb=p.Bb||[2];nu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return lu(b)}
function iu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function eu(a){return a&&typeof a==="string"&&a.match(St)?a:"_gcl"}function su(a,b){if(a){var c={value:a,Ka:new Lt};Mt(c.Ka,b);return c}}
function tu(a,b,c){var d=Tk(a),e=Nk(d,"query",!1,void 0,"gclsrc"),f=su(Nk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=su(Kk(g,"gclid",!1),3));e||(e=Kk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function uu(a,b){var c=Tk(a),d=Nk(c,"query",!1,void 0,"gclid"),e=Nk(c,"query",!1,void 0,"gclsrc"),f=Nk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Nk(c,"query",!1,void 0,"gbraid"),h=Nk(c,"query",!1,void 0,"gad_source"),m=Nk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Kk(n,"gclid",!1);e=e||Kk(n,"gclsrc",!1);f=f||Kk(n,"wbraid",!1);g=g||Kk(n,"gbraid",!1);h=h||Kk(n,"gad_source",!1)}return vu(d,e,m,f,g,h)}function wu(){return uu(x.location.href,!0)}
function vu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Tt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Tt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Tt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Tt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function xu(a){for(var b=wu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=uu(x.document.referrer,!1),b.gad_source=void 0);yu(b,!1,a)}
function zu(a){xu(a);var b=tu(x.location.href,!0,!1);b.length||(b=tu(x.document.referrer,!1,!0));a=a||{};Au(a);if(b.length){var c=b[0],d=yb(),e=Or(a,d,!0),f=Yt(),g=function(){Zt(f)&&e.expires!==void 0&&Qr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};dn(function(){g();Zt(f)||en(g,f)},f)}}
function Au(a){var b;if(b=Ja(14)){var c=Bu();b=Wt.test(c)||Xt.test(c)||Cu()}if(b){var d;a:{for(var e=Tk(x.location.href),f=Lk(Nk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Rt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Nt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Ot(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,C=y,E=A,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Ot(t,E);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var S=void 0,ea=t,U=E;switch(G){case 0:M=(S=Ot(ea,U))==null?void 0:S[1];break d;case 1:M=U+8;break d;case 2:var ta=Ot(ea,U);if(ta===void 0)break;var W=l(ta),da=W.next().value;M=W.next().value+da;break d;case 5:M=U+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Du(Y,7,a)}}
function Du(a,b,c){c=c||{};var d=yb(),e=Or(c,d,!0),f=Yt(),g=function(){if(Zt(f)&&e.expires!==void 0){var h=qu()||[];nu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:ou(b)},!0);Qr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};dn(function(){Zt(f)?g():en(g,f)},f)}
function yu(a,b,c,d,e){c=c||{};e=e||[];var f=eu(c.prefix),g=d||yb(),h=Math.round(g/1E3),m=Yt(),n=!1,p=!1,q=function(){if(Zt(m)){var r=Or(c,g,!0);r.Dc=m;for(var t=function(S,ea){var U=fu(S,f);U&&(us(U,ea,r),S!=="gb"&&(n=!0))},u=function(S){var ea=["GCL",h,S];e.length>0&&ea.push(e.join("."));return ea.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=fu("gb",f);!b&&bu(C).some(function(S){return S.gclid===A&&S.labels&&
S.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&Zt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,G=fu("ag",f);if(b||!gu(G).some(function(S){return S.gclid===E&&S.labels&&S.labels.length>0})){var I={},M=(I.k=E,I.i=""+h,I.b=e,I);Jt(G,M,5,c,g)}}Eu(a,f,g,c)};dn(function(){q();Zt(m)||en(q,m)},m)}
function Eu(a,b,c,d){if(a.gad_source!==void 0&&Zt("ad_storage")){var e=Zc();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=fu("gs",b);if(g){var h=Math.floor((yb()-(Yc()||0))/1E3),m,n=Pt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Jt(g,m,5,d,c)}}}}
function Fu(a,b){var c=Vs(!0);$t(function(){for(var d=eu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Vt[f]!==void 0){var g=fu(f,d),h=c[g];if(h){var m=Math.min(Gu(h),yb()),n;b:{for(var p=m,q=js(g,z.cookie,void 0,Yt()),r=0;r<q.length;++r)if(Gu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Or(b,m,!0);t.Dc=Yt();us(g,h,t)}}}}yu(vu(c.gclid,c.gclsrc),!1,b)},Yt())}
function Hu(a){var b=["ag"],c=Vs(!0),d=eu(a.prefix);$t(function(){for(var e=0;e<b.length;++e){var f=fu(b[e],d);if(f){var g=c[f];if(g){var h=Dt(g,5);if(h){var m=ju(h);m||(m=yb());var n;a:{for(var p=m,q=Ht(f,5),r=0;r<q.length;++r)if(ju(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Jt(f,h,5,a,m)}}}}},["ad_storage"])}function fu(a,b){var c=Vt[a];if(c!==void 0)return b+c}function Gu(a){return Iu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function ju(a){return a?(Number(a.i)||0)*1E3:0}function ku(a){var b=Iu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Iu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Tt.test(a[2])?[]:a}
function Ju(a,b,c,d,e){if(Array.isArray(b)&&hs(x)){var f=eu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=fu(a[m],f);if(n){var p=js(n,z.cookie,void 0,Yt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};$t(function(){bt(g,b,c,d)},Yt())}}
function Ku(a,b,c,d){if(Array.isArray(a)&&hs(x)){var e=["ag"],f=eu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=fu(e[m],f);if(!n)return{};var p=Ht(n,5);if(p.length){var q=p.sort(function(r,t){return ju(t)-ju(r)})[0];h[n]=Et(q,5)}}return h};$t(function(){bt(g,a,b,c)},["ad_storage"])}}function lu(a){return a.filter(function(b){return Tt.test(b.gclid)})}
function Lu(a,b){if(hs(x)){for(var c=eu(b.prefix),d={},e=0;e<a.length;e++)Vt[a[e]]&&(d[a[e]]=Vt[a[e]]);$t(function(){qb(d,function(f,g){var h=js(c+g,z.cookie,void 0,Yt());h.sort(function(t,u){return Gu(u)-Gu(t)});if(h.length){var m=h[0],n=Gu(m),p=Iu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Iu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];yu(q,!0,b,n,p)}})},Yt())}}
function Mu(a){var b=["ag"],c=["gbraid"];$t(function(){for(var d=eu(a.prefix),e=0;e<b.length;++e){var f=fu(b[e],d);if(!f)break;var g=Ht(f,5);if(g.length){var h=g.sort(function(q,r){return ju(r)-ju(q)})[0],m=ju(h),n=h.b,p={};p[c[e]]=h.k;yu(p,!0,a,m,n)}}},["ad_storage"])}function Nu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Ou(a){function b(h,m,n){n&&(h[m]=n)}if(an()){var c=wu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Vs(!1)._gs);if(Nu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ct(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ct(function(){return g},1)}}}function Cu(){var a=Tk(x.location.href);return Nk(a,"query",!1,void 0,"gad_source")}
function Pu(a){if(!Ja(1))return null;var b=Vs(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ja(2)){b=Cu();if(b!=null)return b;var c=wu();if(Nu(c,a))return"0"}return null}function Qu(a){var b=Pu(a);b!=null&&ct(function(){var c={};return c.gad_source=b,c},4)}function Ru(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Su(a,b,c,d){var e=[];c=c||{};if(!Zt(Yt()))return e;var f=bu(a),g=Ru(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Or(c,p,!0);r.Dc=Yt();us(a,q,r)}return e}
function Tu(a,b){var c=[];b=b||{};var d=du(b),e=Ru(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=eu(b.prefix),n=fu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Jt(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),C=Or(b,u,!0);C.Dc=Yt();us(n,A,C)}}return c}
function Uu(a,b){var c=eu(b),d=fu(a,c);if(!d)return 0;var e;e=a==="ag"?gu(d):bu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Vu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Wu(a){var b=Math.max(Uu("aw",a),Vu(Zt(Yt())?ut():{})),c=Math.max(Uu("gb",a),Vu(Zt(Yt())?ut("_gac_gb",!0):{}));c=Math.max(c,Uu("ag",a));return c>b}
function Bu(){return z.referrer?Nk(Tk(z.referrer),"host"):""};function kv(){return vp("dedupe_gclid",function(){return Bs()})};var lv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,mv=/^www.googleadservices.com$/;function nv(a){a||(a=ov());return a.Lq?!1:a.Gp||a.Hp||a.Kp||a.Ip||a.Yf||a.qp||a.Jp||a.wp?!0:!1}function ov(){var a={},b=Vs(!0);a.Lq=!!b._up;var c=wu();a.Gp=c.aw!==void 0;a.Hp=c.dc!==void 0;a.Kp=c.wbraid!==void 0;a.Ip=c.gbraid!==void 0;a.Jp=c.gclsrc==="aw.ds";a.Yf=Zu().Yf;var d=z.referrer?Nk(Tk(z.referrer),"host"):"";a.wp=lv.test(d);a.qp=mv.test(d);return a};function pv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function qv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function rv(){return["ad_storage","ad_user_data"]}function sv(a){if(D(38)&&!un(qn.Z.sl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{pv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(tn(qn.Z.sl,function(d){d.gclid&&Du(d.gclid,5,a)}),qv(c)||L(178))})}catch(c){L(177)}};dn(function(){Zt(rv())?b():en(b,rv())},rv())}};var tv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function uv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?tn(qn.Z.Of,{gadSource:a.data.gadSource}):L(173)}
function vv(a,b){if(D(a)){if(un(qn.Z.Of))return L(176),qn.Z.Of;if(un(qn.Z.vl))return L(170),qn.Z.Of;var c=Kl();if(!c)L(171);else if(c.opener){var d=function(g){if(tv.includes(g.origin)){a===119?uv(g):a===200&&(uv(g),g.data.gclid&&Du(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Sq(c,"message",d)}else L(172)};if(Rq(c,"message",d)){tn(qn.Z.vl,!0);for(var e=l(tv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return qn.Z.Of}L(175)}}}
;var wv=function(){this.C=this.gppString=void 0};wv.prototype.reset=function(){this.C=this.gppString=void 0};var xv=new wv;var yv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),zv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Av=/^\d+\.fls\.doubleclick\.net$/,Bv=/;gac=([^;?]+)/,Cv=/;gacgb=([^;?]+)/;
function Dv(a,b){if(Av.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(yv)?Mk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Ev(a,b,c){for(var d=Zt(Yt())?ut("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Su("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{pp:f?e.join(";"):"",op:Dv(d,Cv)}}function Fv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(zv)?b[1]:void 0}
function Gv(a){var b={},c,d,e;Av.test(z.location.host)&&(c=Fv("gclgs"),d=Fv("gclst"),e=Fv("gcllp"));if(c&&d&&e)b.th=c,b.wh=d,b.uh=e;else{var f=yb(),g=gu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Fd});h.length>0&&m.length>0&&n.length>0&&(b.th=h.join("."),b.wh=m.join("."),b.uh=n.join("."))}return b}
function Hv(a,b,c,d){d=d===void 0?!1:d;if(Av.test(z.location.host)){var e=Fv(c);if(e){if(d){var f=new Lt;Mt(f,2);Mt(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?ru(g):bu(g)}if(b==="wbraid")return bu((a||"_gcl")+"_gb");if(b==="braids")return du({prefix:a})}return[]}function Iv(a){return Av.test(z.location.host)?!(Fv("gclaw")||Fv("gac")):Wu(a)}
function Jv(a,b,c){var d;d=c?Tu(a,b):Su((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Kv(){var a=x.__uspapi;if(ib(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function Xv(a){var b=N(a.D,J.m.Lc),c=N(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Td&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function Yv(a){var b=O(J.m.U)?up.pscdl:"denied";b!=null&&T(a,J.m.Fg,b)}function Zv(a){var b=Il(!0);T(a,J.m.Jc,b)}function $v(a){Gr()&&T(a,J.m.be,1)}
function Ov(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Mk(a.substring(0,b))===void 0;)b--;return Mk(a.substring(0,b))||""}function aw(a){bw(a,Cp.Ef.Um,N(a.D,J.m.ob))}function bw(a,b,c){Nv(a,J.m.rd)||T(a,J.m.rd,{});Nv(a,J.m.rd)[b]=c}function cw(a){R(a,P.A.Qf,Pm.X.Da)}function dw(a){var b=fb("GTAG_EVENT_FEATURE_CHANNEL");b&&(T(a,J.m.kf,b),db())}function ew(a){var b=a.D.getMergedValues(J.m.rc);b&&a.mergeHitDataForKey(J.m.rc,b)}
function fw(a,b){b=b===void 0?!1:b;if(D(108)){var c=Q(a,P.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(R(a,P.A.Kj,!1),b||!gw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else R(a,P.A.Kj,!0)}}function hw(a){il&&(Un=!0,a.eventName===J.m.qa?$n(a.D,a.target.id):(Q(a,P.A.Le)||(Xn[a.target.id]=!0),Bp(Q(a,P.A.hb))))};function rw(a,b,c,d){var e=Fc(),f;if(e===1)a:{var g=fk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Dw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Nv(a,b)},setHitData:function(b,c){T(a,b,c)},setHitDataIfNotDefined:function(b,c){Nv(a,b)===void 0&&T(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return Q(a,b)},setMetadata:function(b,c){R(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Ab:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return kd(c)?a.mergeHitDataForKey(b,c):!1}}};function Iw(a,b){return arguments.length===1?Jw("set",a):Jw("set",a,b)}function Kw(a,b){return arguments.length===1?Jw("config",a):Jw("config",a,b)}function Lw(a,b,c){c=c||{};c[J.m.ld]=a;return Jw("event",b,c)}function Jw(){return arguments};var Nw=function(){this.messages=[];this.C=[]};Nw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Nw.prototype.listen=function(a){this.C.push(a)};
Nw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Nw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Ow(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.A.hb]=jg.canonicalContainerId;Pw().enqueue(a,b,c)}
function Qw(){var a=Rw;Pw().listen(a)}function Pw(){return vp("mb",function(){return new Nw})};var Sw,Tw=!1;function Uw(){Tw=!0;Sw=Sw||{}}function Vw(a){Tw||Uw();return Sw[a]};function Ww(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Xw(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var Zw=function(a){var b=Yw(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},Yw=function(){var a=z.body,b=z.documentElement||a&&a.parentElement,c,d;if(z.compatMode&&z.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var jy=Number('')||5,ky=Number('')||50,ly=nb();
var ny=function(a,b){a&&(my("sid",a.targetId,b),my("cc",a.clientCount,b),my("tl",a.totalLifeMs,b),my("hc",a.heartbeatCount,b),my("cl",a.clientLifeMs,b))},my=function(a,b,c){b!=null&&c.push(a+"="+b)},oy=function(){var a=z.referrer;if(a){var b;return Nk(Tk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},py="https://"+Ri(21,"www.googletagmanager.com")+"/a?",ry=function(){this.R=qy;this.N=0};ry.prototype.H=function(a,b,c,d){var e=oy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&my("si",a.gg,g);my("m",0,g);my("iss",f,g);my("if",c,g);ny(b,g);d&&my("fm",encodeURIComponent(d.substring(0,ky)),g);this.P(g);};ry.prototype.C=function(a,b,c,d,e){var f=[];my("m",1,f);my("s",a,f);my("po",oy(),f);b&&(my("st",b.state,f),my("si",b.gg,f),my("sm",b.mg,f));ny(c,f);my("c",d,f);e&&my("fm",encodeURIComponent(e.substring(0,
ky)),f);this.P(f);};ry.prototype.P=function(a){a=a===void 0?[]:a;!hl||this.N>=jy||(my("pid",ly,a),my("bc",++this.N,a),a.unshift("ctid="+jg.ctid+"&t=s"),this.R(""+py+a.join("&")))};var sy=Number('')||500,ty=Number('')||5E3,uy=Number('20')||10,vy=Number('')||5E3;function wy(a){return a.performance&&a.performance.now()||Date.now()}
var xy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{lm:function(){},om:function(){},km:function(){},onFailure:function(){}}:h;this.zo=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.xo=0;this.hh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=wy(this.C);this.mg=wy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(wy(this.C)-this.gg),mg:Math.round(wy(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=wy(this.C))};e.prototype.Jl=function(){return String(this.xo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Jl(),maxDelay:this.ih()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>uy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.wo();var n,p;(p=(n=f.N).km)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Nl();else{if(f.heartbeatCount>g.stats.heartbeatCount+uy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.hh){var u,v;(v=(u=f.N).om)==null||v.call(u)}else{f.hh=!0;var w,y;(y=(w=f.N).lm)==null||y.call(w)}f.ba=0;f.Ao();f.Nl()}}})};e.prototype.ih=function(){return this.state===2?
ty:sy};e.prototype.Nl=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.ih()-(wy(this.C)-this.ka)))};e.prototype.Do=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Jl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Lf(t,7)},(p=f.maxDelay)!=null?p:vy),r={request:f,Cm:g,xm:m,Xp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=wy(this.C);f.xm=!1;this.zo(f.request)};e.prototype.Ao=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.xm&&this.sendRequest(h)}};e.prototype.wo=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Lf(this.H[g.value],this.R)};e.prototype.Lf=function(f,g){this.qb(f);var h=f.request;h.failure={failureType:g};f.Cm(h)};e.prototype.qb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Xp)};e.prototype.Ep=function(f){this.ka=wy(this.C);var g=this.H[f.requestId];if(g)this.qb(g),g.Cm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var yy;
var zy=function(){yy||(yy=new ry);return yy},qy=function(a){nn(pn(Pm.X.Oc),function(){Ic(a)})},Ay=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},By=function(a){var b=a,c=Oj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Cy=function(a){var b=un(qn.Z.Cl);return b&&b[a]},Dy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Uo(a);x.setTimeout(function(){f.initialize()},1E3);Lc(function(){f.Op(a,b,e)})};k=Dy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(yb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Do(a,b,c)};k.getState=function(){return this.N.getState().state};k.Op=function(a,b,c){var d=x.location.origin,e=this,
f=Gc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Ay(h):"",p;D(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Gc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Ep(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Uo=function(a){var b=this,c=xy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{lm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},om:function(){},km:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Ey(){var a=ig(fg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Fy(a,b){var c=Math.round(yb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Ey()||D(168))return;nk()&&(a=""+d+mk()+"/_/service_worker");var e=By(a);if(e===null||Cy(e.origin))return;if(!tc()){zy().H(void 0,void 0,6);return}var f=new Dy(e,!!a,c||Math.round(yb()),zy(),b);vn(qn.Z.Cl)[e.origin]=f;}
var Gy=function(a,b,c,d){var e;if((e=Cy(a))==null||!e.delegate){var f=tc()?16:6;zy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Cy(a).delegate(b,c,d);};
function Hy(a,b,c,d,e){var f=By();if(f===null){d(tc()?16:6);return}var g,h=(g=Cy(f.origin))==null?void 0:g.initTime,m=Math.round(yb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Gy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Iy(a,b,c,d){var e=By(a);if(e===null){d("_is_sw=f"+(tc()?16:6)+"te");return}var f=b?1:0,g=Math.round(yb()),h,m=(h=Cy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;D(169)&&(p=!0);Gy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Cy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Jy(a){if(D(10)||nk()||Oj.N||al(a.D)||D(168))return;Fy(void 0,D(131));};var Ky="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ly(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function My(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Ny(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Oy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Py(a){if(!Oy(a))return null;var b=Ly(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Ky).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Ry=function(a,b){if(a)for(var c=Qy(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;T(b,f,c[f])}},Qy=function(a){var b={};b[J.m.vf]=a.architecture;b[J.m.wf]=a.bitness;a.fullVersionList&&(b[J.m.xf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.yf]=a.mobile?"1":"0";b[J.m.zf]=a.model;b[J.m.Af]=a.platform;b[J.m.Bf]=a.platformVersion;b[J.m.Cf]=a.wow64?"1":"0";return b},Sy=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=My(d);if(e)c(e);else{var f=Ny(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.hg||(c.hg=!0,L(106),c(null,Error("Timeout")))},b);f.then(function(h){c.hg||(c.hg=!0,L(104),d.clearTimeout(g),c(h))}).catch(function(h){c.hg||(c.hg=!0,L(105),d.clearTimeout(g),c(null,h))})}else c(null)}},Uy=function(){var a=x;if(Oy(a)&&(Ty=yb(),!Ny(a))){var b=Py(a);b&&(b.then(function(){L(95)}),b.catch(function(){L(96)}))}},Ty;function Vy(a){var b=a.location.href;if(a===a.top)return{url:b,Tp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Tp:c}};function Mz(a,b){var c=!!nk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?mk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?D(187)?Jz()?Kz():""+mk()+"/ag/g/c":Jz().toLowerCase()==="region1"?""+mk()+"/r1ag/g/c":""+mk()+"/ag/g/c":Kz();case 16:if(c){if(D(187))return Jz()?Lz():
""+mk()+"/ga/g/c";var d=Jz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+mk()+d}return Lz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?mk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?mk()+"/d/pagead/form-data":D(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Eo+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?mk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(D(207)?c:c&&b.Ah)?mk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?mk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(D(207)?c:c&&b.Ah)?mk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";
case 55:return c?mk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return D(205)?"https://www.google.com/measurement/conversion/":c?mk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(D(207)?c:c&&b.Ah)?mk()+"/d/ccm/form-data":D(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:kc(a,"Unknown endpoint")}};function Nz(a){a=a===void 0?[]:a;return Pj(a).join("~")}function Oz(){if(!D(118))return"";var a,b;return(((a=Dm(sm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Pz(a,b){b&&qb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var Xz={};Xz.O=cs.O;var Yz={ir:"L",uo:"S",zr:"Y",Nq:"B",Xq:"E",er:"I",wr:"TC",ar:"HTC"},Zz={uo:"S",Wq:"V",Qq:"E",vr:"tag"},$z={},aA=($z[Xz.O.Pi]="6",$z[Xz.O.Qi]="5",$z[Xz.O.Oi]="7",$z);function bA(){function a(c,d){var e=fb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var cA=!1;
function vA(a){}function wA(a){}
function xA(){}function yA(a){}
function zA(a){}function AA(a){}
function BA(){}function CA(a,b){}
function DA(a,b,c){}
function EA(){};var FA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function GA(a,b,c,d,e,f,g){var h=Object.assign({},FA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});HA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():D(128)&&(b+="&_z=retryFetch",c?gm(a,b,c):fm(a,b))})};var IA=function(a){this.P=a;this.C=""},JA=function(a,b){a.H=b;return a},KA=function(a,b){a.N=b;return a},HA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}LA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},MA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};LA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},LA=function(a,b){b&&(NA(b.send_pixel,b.options,a.P),NA(b.create_iframe,b.options,a.H),NA(b.fetch,b.options,a.N))};function OA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function NA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=kd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var DB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),EB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},FB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},GB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function HB(){var a=tk("gtm.allowlist")||tk("gtm.whitelist");a&&L(9);bk&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);DB.test(x.location&&x.location.hostname)&&(bk?L(116):(L(117),IB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Cb(vb(a),EB),c=tk("gtm.blocklist")||tk("gtm.blacklist");c||(c=tk("tagTypeBlacklist"))&&L(3);c?L(8):c=[];DB.test(x.location&&x.location.hostname)&&(c=vb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
vb(c).indexOf("google")>=0&&L(2);var d=c&&Cb(vb(c),FB),e={};return function(f){var g=f&&f[ff.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=jk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(bk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=ob(d,h||[]);t&&L(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:bk&&h.indexOf("cmpPartners")>=0?!JB():b&&b.indexOf("sandboxedScripts")!==-1?0:ob(d,GB))&&(u=!0);return e[g]=u}}function JB(){var a=ig(fg.C,jg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var IB=!1;IB=!0;function KB(a,b,c,d,e){if(!LB()&&!Im(a)){d.loadExperiments=Qj();rm(a,d,e);var f=MB(a),g=function(){tm().container[a]&&(tm().container[a].state=3);NB()},h={destinationId:a,endpoint:0};if(nk())jm(h,mk()+"/"+f,void 0,g);else{var m=Db(a,"GTM-"),n=$k(),p=c?"/gtag/js":"/gtm.js",q=Zk(b,p+f);if(!q){var r=Sj.wg+p;n&&vc&&m&&(r=vc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=rw("https://","http://",r+f)}jm(h,q,void 0,g)}}}
function NB(){Km()||qb(Lm(),function(a,b){OB(a,b.transportUrl,b.context);L(92)})}
function OB(a,b,c,d){if(!LB()&&!Jm(a))if(c.loadExperiments||(c.loadExperiments=Qj()),Km()){var e;(e=tm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:sm()});tm().destination[a].state=0;um({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=tm().destination)[a]!=null||(f[a]={context:c,state:1,parent:sm()});tm().destination[a].state=1;um({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(nk())jm(g,mk()+("/gtd"+MB(a,!0)));else{var h="/gtag/destination"+MB(a,!0),
m=Zk(b,h);m||(m=rw("https://","http://",Sj.wg+h));jm(g,m)}}}function MB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Vj!=="dataLayer"&&(c+="&l="+Vj);if(!Db(a,"GTM-")||b)c=D(130)?c+(nk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Nr();$k()&&(c+="&sign="+Sj.Li);var d=Oj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!D(191)&&Qj().join("~")&&(c+="&tag_exp="+Qj().join("~"));return c}
function LB(){if(Hr()){return!0}return!1};var PB=function(){this.H=0;this.C={}};PB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ge:c};return d};PB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var RB=function(a,b){var c=[];qb(QB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ge===void 0||b.indexOf(e.Ge)>=0)&&c.push(e.listener)});return c};function SB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:jg.ctid}};function TB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var VB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;UB(this,a,b)},WB=function(a,b,c,d){if(Xj.hasOwnProperty(b)||b==="__zone")return-1;var e={};kd(d)&&(e=ld(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},XB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},YB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},UB=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){YB(a)},
Number(c))};VB.prototype.Sf=function(a){var b=this,c=Ab(function(){Lc(function(){a(jg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var ZB=function(a){a.N++;return Ab(function(){a.H++;a.R&&a.H>=a.N&&YB(a)})},$B=function(a){a.R=!0;a.H>=a.N&&YB(a)};var aC={};function bC(){return x[cC()]}
function cC(){return x.GoogleAnalyticsObject||"ga"}function fC(){var a=jg.ctid;}
function gC(a,b){return function(){var c=bC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var mC=["es","1"],nC={},oC={};function pC(a,b){if(hl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";nC[a]=[["e",c],["eid",a]];xq(a)}}function qC(a){var b=a.eventId,c=a.Nd;if(!nC[b])return[];var d=[];oC[b]||d.push(mC);d.push.apply(d,ua(nC[b]));c&&(oC[b]=!0);return d};var rC={},sC={},tC={};function uC(a,b,c,d){hl&&D(120)&&((d===void 0?0:d)?(tC[b]=tC[b]||0,++tC[b]):c!==void 0?(sC[a]=sC[a]||{},sC[a][b]=Math.round(c)):(rC[a]=rC[a]||{},rC[a][b]=(rC[a][b]||0)+1))}function vC(a){var b=a.eventId,c=a.Nd,d=rC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete rC[b];return e.length?[["md",e.join(".")]]:[]}
function wC(a){var b=a.eventId,c=a.Nd,d=sC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete sC[b];return e.length?[["mtd",e.join(".")]]:[]}function xC(){for(var a=[],b=l(Object.keys(tC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+tC[d])}return a.length?[["mec",a.join(".")]]:[]};var yC={},zC={};function AC(a,b,c){if(hl&&b){var d=dl(b);yC[a]=yC[a]||[];yC[a].push(c+d);var e=b[ff.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Jf[e]?"1":"2")+d;zC[a]=zC[a]||[];zC[a].push(f);xq(a)}}function BC(a){var b=a.eventId,c=a.Nd,d=[],e=yC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=zC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete yC[b],delete zC[b]);return d};function CC(a,b,c){c=c===void 0?!1:c;DC().addRestriction(0,a,b,c)}function EC(a,b,c){c=c===void 0?!1:c;DC().addRestriction(1,a,b,c)}function FC(){var a=Am();return DC().getRestrictions(1,a)}var GC=function(){this.container={};this.C={}},HC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
GC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=HC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
GC.prototype.getRestrictions=function(a,b){var c=HC(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
GC.prototype.getExternalRestrictions=function(a,b){var c=HC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};GC.prototype.removeExternalRestrictions=function(a){var b=HC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function DC(){return vp("r",function(){return new GC})};function IC(a,b,c,d){var e=Gf[a],f=JC(a,b,c,d);if(!f)return null;var g=Vf(e[ff.Dl],c,[]);if(g&&g.length){var h=g[0];f=IC(h.index,{onSuccess:f,onFailure:h.Yl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function JC(a,b,c,d){function e(){function w(){ao(3);var M=yb()-I;AC(c.id,f,"7");XB(c.Pc,E,"exception",M);D(109)&&DA(c,f,Xz.O.Oi);G||(G=!0,h())}if(f[ff.lo])h();else{var y=Uf(f,c,[]),A=y[ff.Rm];if(A!=null)for(var C=0;C<A.length;C++)if(!O(A[C])){h();return}var E=WB(c.Pc,String(f[ff.Ra]),Number(f[ff.mh]),y[ff.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=yb()-I;AC(c.id,Gf[a],"5");XB(c.Pc,E,"success",M);D(109)&&DA(c,f,Xz.O.Qi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=yb()-
I;AC(c.id,Gf[a],"6");XB(c.Pc,E,"failure",M);D(109)&&DA(c,f,Xz.O.Pi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);AC(c.id,f,"1");D(109)&&CA(c,f);var I=yb();try{Wf(y,{event:c,index:a,type:1})}catch(M){w(M)}D(109)&&DA(c,f,Xz.O.Kl)}}var f=Gf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Vf(f[ff.Ll],c,[]);if(n&&n.length){var p=n[0],q=IC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Yl===
2?m:q}if(f[ff.tl]||f[ff.no]){var r=f[ff.tl]?Hf:c.Fq,t=g,u=h;if(!r[a]){var v=KC(a,r,Ab(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function KC(a,b,c){var d=[],e=[];b[a]=LC(d,e,c);return{onSuccess:function(){b[a]=MC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=NC;for(var f=0;f<e.length;f++)e[f]()}}}function LC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function MC(a){a()}function NC(a,b){b()};var QC=function(a,b){for(var c=[],d=0;d<Gf.length;d++)if(a[d]){var e=Gf[d];var f=ZB(b.Pc);try{var g=IC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ff.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Jf[h];c.push({Jm:d,priorityOverride:(m?m.priorityOverride||0:0)||TB(e[ff.Ra],1)||0,execute:g})}else OC(d,b),f()}catch(p){f()}}c.sort(PC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function RC(a,b){if(!QB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=RB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=ZB(b);try{d[e](a,f)}catch(g){f()}}return!0}function PC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Jm,h=b.Jm;f=g>h?1:g<h?-1:0}return f}
function OC(a,b){if(hl){var c=function(d){var e=b.isBlocked(Gf[d])?"3":"4",f=Vf(Gf[d][ff.Dl],b,[]);f&&f.length&&c(f[0].index);AC(b.id,Gf[d],e);var g=Vf(Gf[d][ff.Ll],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var SC=!1,QB;function TC(){QB||(QB=new PB);return QB}
function UC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(D(109)){}if(d==="gtm.js"){if(SC)return!1;SC=!0}var e=!1,f=FC(),g=ld(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}pC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:VC(g,e),Fq:[],logMacroError:function(){L(6);ao(0)},cachedModelValues:WC(),Pc:new VB(function(){if(D(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};D(120)&&hl&&(n.reportMacroDiscrepancy=uC);D(109)&&zA(n.id);var p=ag(n);D(109)&&AA(n.id);e&&(p=XC(p));D(109)&&yA(b);var q=QC(p,n),r=RC(a,n.Pc);$B(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||fC();return YC(p,q)||r}function WC(){var a={};a.event=yk("event",1);a.ecommerce=yk("ecommerce",1);a.gtm=yk("gtm");a.eventModel=yk("eventModel");return a}
function VC(a,b){var c=HB();return function(d){if(c(d))return!0;var e=d&&d[ff.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Am();f=DC().getRestrictions(0,g);var h=a;b&&(h=ld(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=jk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function XC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Gf[c][ff.Ra]);if(Wj[d]||Gf[c][ff.oo]!==void 0||TB(d,2))b[c]=!0}return b}function YC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Gf[c]&&!Xj[String(Gf[c][ff.Ra])])return!0;return!1};function ZC(){TC().addListener("gtm.init",function(a,b){Oj.ba=!0;Ln();b()})};var $C=!1,aD=0,bD=[];function cD(a){if(!$C){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){$C=!0;for(var e=0;e<bD.length;e++)Lc(bD[e])}bD.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)Lc(f[g]);return 0}}}function dD(){if(!$C&&aD<140){aD++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");cD()}catch(c){x.setTimeout(dD,50)}}}
function eD(){var a=x;$C=!1;aD=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")cD();else{Jc(z,"DOMContentLoaded",cD);Jc(z,"readystatechange",cD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&dD()}Jc(a,"load",cD)}}function fD(a){$C?a():bD.push(a)};var gD={},hD={};function iD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={yj:void 0,ej:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.yj=Fp(g,b),e.yj){var h=zm();mb(h,function(r){return function(t){return r.yj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=gD[g]||[];e.ej={};m.forEach(function(r){return function(t){r.ej[t]=!0}}(e));for(var n=Bm(),p=0;p<n.length;p++)if(e.ej[n[p]]){c=c.concat(zm());break}var q=hD[g]||[];q.length&&(c=c.concat(q))}}return{sj:c,Zp:d}}
function jD(a){qb(gD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function kD(a){qb(hD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var lD=!1,mD=!1;function nD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ld(b,null),b[J.m.ef]&&(d.eventCallback=b[J.m.ef]),b[J.m.Lg]&&(d.eventTimeout=b[J.m.Lg]));return d}function oD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:yp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function pD(a,b){var c=a&&a[J.m.ld];c===void 0&&(c=tk(J.m.ld,2),c===void 0&&(c="default"));if(jb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?jb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=iD(d,b.isGtmEvent),f=e.sj,g=e.Zp;if(g.length)for(var h=qD(a),m=0;m<g.length;m++){var n=Fp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=tm().destination[q];r&&r.state===0||OB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{sj:Gp(f,b.isGtmEvent),
Fo:Gp(t,b.isGtmEvent)}}}var rD=void 0,sD=void 0;function tD(a,b,c){var d=ld(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=ld(b,null);ld(c,e);Ow(Kw(Bm()[0],e),a.eventId,d)}function qD(a){for(var b=l([J.m.md,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Fq.C[d];if(e)return e}}
var uD={config:function(a,b){var c=oD(a,b);if(!(a.length<2)&&jb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!kd(a[2])||a.length>3)return;d=a[2]}var e=Fp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!xm.qe){var m=Dm(sm());if(Mm(m)){var n=m.parent,p=n.isDestination;h={bq:Dm(n),Vp:p};break a}}h=void 0}var q=h;q&&(f=q.bq,g=q.Vp);pC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?zm().indexOf(r)===-1:Bm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=qD(d);if(t)OB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;rD?tD(b,v,rD):sD||(sD=ld(v,null))}else KB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;sD?(tD(b,sD,y),w=!1):(!y[J.m.od]&&Zj&&rD||(rD=ld(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}il&&(Ap===1&&(Dn.mcc=!1),Ap=2);if(Zj&&!t&&!d[J.m.od]){var A=mD;mD=!0;if(A)return}lD||L(43);if(!b.noTargetGroup)if(t){kD(e.id);
var C=e.id,E=d[J.m.Og]||"default";E=String(E).split(",");for(var G=0;G<E.length;G++){var I=hD[E[G]]||[];hD[E[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{jD(e.id);var M=e.id,S=d[J.m.Og]||"default";S=S.toString().split(",");for(var ea=0;ea<S.length;ea++){var U=gD[S[ea]]||[];gD[S[ea]]=U;U.indexOf(M)<0&&U.push(M)}}delete d[J.m.Og];var ta=b.eventMetadata||{};ta.hasOwnProperty(P.A.ud)||(ta[P.A.ud]=!b.fromContainerExecution);b.eventMetadata=ta;delete d[J.m.ef];for(var W=t?[e.id]:zm(),da=0;da<W.length;da++){var Y=
d,X=W[da],ma=ld(b,null),ja=Fp(X,ma.isGtmEvent);ja&&Fq.push("config",[Y],ja,ma)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=oD(a,b),d=a[1],e={},f=Eo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.rg?Array.isArray(h)?NaN:Number(h):g===J.m.fc?(Array.isArray(h)?h:[h]).map(Fo):Go(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.La]&&L(140));d==="default"?hp(e):d==="update"?jp(e,c):d==="declare"&&b.fromContainerExecution&&gp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&jb(c)){var d=void 0;if(a.length>2){if(!kd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=nD(c,d),f=oD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=pD(d,b);if(m){var n=m.sj,p=m.Fo,q,r,t;if(D(108)){q=p.map(function(M){return M.id});r=p.map(function(M){return M.destinationId});t=n.map(function(M){return M.id});for(var u=l(zm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(M){return M.id}),r=n.map(function(M){return M.destinationId}),t=q;pC(g,c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,E=ld(b,null),G=ld(d,null);delete G[J.m.ef];var I=E.eventMetadata||{};I.hasOwnProperty(P.A.ud)||(I[P.A.ud]=!E.fromContainerExecution);I[P.A.Ji]=q.slice();I[P.A.Pf]=r.slice();E.eventMetadata=I;Gq(c,G,C,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.ld]=q.join(","):delete e.eventModel[J.m.ld];lD||L(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[P.A.Il]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&jb(a[1])&&jb(a[2])&&ib(a[3])){var c=Fp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){lD||L(43);var f=qD();if(mb(zm(),function(h){return c.destinationId===h})){oD(a,b);var g={};ld((g[J.m.qc]=d,g[J.m.Ic]=e,g),null);Hq(d,function(h){Lc(function(){e(h)})},c.id,b)}else OB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){lD=!0;var c=oD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&jb(a[1])&&ib(a[2])){if(gg(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](jg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&kd(a[1])?c=ld(a[1],null):a.length===3&&jb(a[1])&&(c={},kd(a[2])||Array.isArray(a[2])?
c[a[1]]=ld(a[2],null):c[a[1]]=a[2]);if(c){var d=oD(a,b),e=d.eventId,f=d.priorityId;ld(c,null);var g=ld(c,null);Fq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},vD={policy:!0};var xD=function(a){if(wD(a))return a;this.value=a};xD.prototype.getUntrustedMessageValue=function(){return this.value};var wD=function(a){return!a||id(a)!=="object"||kd(a)?!1:"getUntrustedMessageValue"in a};xD.prototype.getUntrustedMessageValue=xD.prototype.getUntrustedMessageValue;var yD=!1,zD=[];function AD(){if(!yD){yD=!0;for(var a=0;a<zD.length;a++)Lc(zD[a])}}function BD(a){yD?Lc(a):zD.push(a)};var CD=0,DD={},ED=[],FD=[],GD=!1,HD=!1;function ID(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function JD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return KD(a)}function LD(a,b){if(!kb(b)||b<0)b=0;var c=up[Vj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function MD(a,b){var c=a._clear||b.overwriteModelFields;qb(a,function(e,f){e!=="_clear"&&(c&&wk(e),wk(e,f))});gk||(gk=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=yp(),a["gtm.uniqueEventId"]=d,wk("gtm.uniqueEventId",d));return UC(a)}function ND(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(rb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function OD(){var a;if(FD.length)a=FD.shift();else if(ED.length)a=ED.shift();else return;var b;var c=a;if(GD||!ND(c.message))b=c;else{GD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=yp(),f=yp(),c.message["gtm.uniqueEventId"]=yp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};ED.unshift(n,c);b=h}return b}
function PD(){for(var a=!1,b;!HD&&(b=OD());){HD=!0;delete qk.eventModel;sk();var c=b,d=c.message,e=c.messageContext;if(d==null)HD=!1;else{e.fromContainerExecution&&xk();try{if(ib(d))try{d.call(uk)}catch(u){}else if(Array.isArray(d)){if(jb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=tk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(rb(d))a:{if(d.length&&jb(d[0])){var p=uD[d[0]];if(p&&(!e.fromContainerExecution||!vD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=MD(n,e)||a)}}finally{e.fromContainerExecution&&sk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=DD[String(q)]||[],t=0;t<r.length;t++)FD.push(QD(r[t]));r.length&&FD.sort(ID);delete DD[String(q)];q>CD&&(CD=q)}HD=!1}}}return!a}
function RD(){if(D(109)){var a=!Oj.ka;}var c=PD();if(D(109)){}try{var e=jg.ctid,f=x[Vj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Rw(a){if(CD<a.notBeforeEventId){var b=String(a.notBeforeEventId);DD[b]=DD[b]||[];DD[b].push(a)}else FD.push(QD(a)),FD.sort(ID),Lc(function(){HD||PD()})}function QD(a){return{message:a.message,messageContext:a.messageContext}}
function SD(){function a(f){var g={};if(wD(f)){var h=f;f=wD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=wc(Vj,[]),c=up[Vj]=up[Vj]||{};c.pruned===!0&&L(83);DD=Pw().get();Qw();fD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});BD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(up.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new xD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});ED.push.apply(ED,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return PD()&&p};var e=b.slice(0).map(function(f){return a(f)});ED.push.apply(ED,e);if(!Oj.ka){if(D(109)){}Lc(RD)}}var KD=function(a){return x[Vj].push(a)};function TD(a){KD(a)};function UD(){var a,b=Tk(x.location.href);(a=b.hostname+b.pathname)&&Hn("dl",encodeURIComponent(a));var c;var d=jg.ctid;if(d){var e=xm.qe?1:0,f,g=Dm(sm());f=g&&g.context;c=d+";"+jg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Hn("tdp",h);var m=Il(!0);m!==void 0&&Hn("frm",String(m))};function VD(){(Ro()||il)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=em(a.effectiveDirective);if(b){var c;var d=cm(b,a.blockedURI);c=d?am[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Bm){p.Bm=!0;if(D(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Ro()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Ro()){var u=Xo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Qo(u)}}}Nn(p.endpoint)}}dm(b,a.blockedURI)}}}}})};function WD(){var a;var b=Cm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Hn("pcid",e)};var XD=/^(https?:)?\/\//;
function YD(){var a=Em();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=$c())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(XD,"")===d.replace(XD,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Hn("rtg",String(a.canonicalContainerId)),Hn("slo",String(p)),Hn("hlo",a.htmlLoadOrder||"-1"),
Hn("lst",String(a.loadScriptType||"0")))}else L(144)};function ZD(){var a=[],b=Number('1')||0,c=Number('')||0;c||(c=b/100);var d=function(){var h=!1;return h}();a.push({Im:195,Hm:195,experimentId:104527906,controlId:104527907,controlId2:104898015,De:c,active:d,Xi:1});var e=Number('1')||
0,f=Number('')||0;f||(f=e/100);var g=function(){var h=!1;return h}();a.push({Im:196,Hm:196,experimentId:104528500,controlId:104528501,controlId2:104898016,De:f,active:g,Xi:0});return a};var $D={};function aE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Oj.R.H.add(Number(c.value))}function bE(){for(var a=l(ZD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Im;ki[d]=c;if(c.Xi===1){var e=d,f=vn(qn.Z.qo);oi(f,e);aE(f)}else if(c.Xi===0){var g=$D;oi(g,d);aE(g)}}};
function wE(){};var xE=function(){};xE.prototype.toString=function(){return"undefined"};var yE=new xE;
var AE=function(){vp("rm",function(){return{}})[Am()]=function(a){if(zE.hasOwnProperty(a))return zE[a]}},DE=function(a,b,c){if(a instanceof BE){var d=a,e=d.resolve,f=b,g=String(yp());CE[g]=[f,c];a=e.call(d,g);b=hb}return{Mp:a,onSuccess:b}},EE=function(a){var b=a?0:1;return function(c){L(a?134:135);var d=CE[c];if(d&&typeof d[b]==="function")d[b]();CE[c]=void 0}},BE=function(a){this.valueOf=this.toString;this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===yE?b:a[d]);return c.join("")}};
BE.prototype.toString=function(){return this.resolve("undefined")};var zE={},CE={};function FE(a,b){function c(g){var h=Tk(g),m=Nk(h,"protocol"),n=Nk(h,"host",!0),p=Nk(h,"port"),q=Nk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function GE(a){return HE(a)?1:0}
function HE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ld(a,{});ld({arg1:c[d],any_of:void 0},e);if(GE(e))return!0}return!1}switch(a["function"]){case "_cn":return Pg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Kg.length;g++){var h=Kg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Lg(b,c);case "_eq":return Qg(b,c);case "_ge":return Rg(b,c);case "_gt":return Tg(b,c);case "_lc":return Mg(b,c);case "_le":return Sg(b,
c);case "_lt":return Ug(b,c);case "_re":return Og(b,c,a.ignore_case);case "_sw":return Vg(b,c);case "_um":return FE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var IE=function(a,b,c,d){Wq.call(this);this.hh=b;this.Lf=c;this.qb=d;this.Sa=new Map;this.ih=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};ra(IE,Wq);IE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Sq(this.H,"message",this.R),delete this.R);delete this.H;delete this.qb;Wq.prototype.N.call(this)};
var JE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=Hl(a.H,a.hh);var b;return(b=a.C)!=null?b:null},LE=function(a,b,c){if(JE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.rj){KE(a);var f=++a.ih;a.Ba.set(f,{Eh:e.Eh,Yo:e.hm(c),persistent:b==="addEventListener"});a.C.postMessage(e.rj(c,f),"*")}}},KE=function(a){a.R||(a.R=function(b){try{var c;c=a.qb?a.qb(b):void 0;if(c){var d=c.gq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Eh)==null||f.call(e,
e.Yo,c.payload)}}}catch(g){}},Rq(a.H,"message",a.R))};var ME=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},NE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},OE={hm:function(a){return a.listener},rj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},PE={hm:function(a){return a.listener},rj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function QE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,gq:b.__gppReturn.callId}}
var RE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Wq.call(this);this.caller=new IE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},QE);this.caller.Sa.set("addEventListener",ME);this.caller.ka.set("addEventListener",OE);this.caller.Sa.set("removeEventListener",NE);this.caller.ka.set("removeEventListener",PE);this.timeoutMs=c!=null?c:500};ra(RE,Wq);RE.prototype.N=function(){this.caller.dispose();Wq.prototype.N.call(this)};
RE.prototype.addEventListener=function(a){var b=this,c=kl(function(){a(SE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);LE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(TE,!0);return}a(UE,!0)}}})};
RE.prototype.removeEventListener=function(a){LE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var UE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},SE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},TE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function VE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){xv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");xv.C=d}}function WE(){try{var a=new RE(x,{timeoutMs:-1});JE(a.caller)&&a.addEventListener(VE)}catch(b){}};function XE(){var a=[["cv",Si(1)],["rv",Tj],["tc",Gf.filter(function(b){return b}).length]];Uj&&a.push(["x",Uj]);lk()&&a.push(["tag_exp",lk()]);return a};var YE={};function Vi(a){YE[a]=(YE[a]||0)+1}function ZE(){for(var a=[],b=l(Object.keys(YE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+YE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var $E={},aF={};function bF(a){var b=a.eventId,c=a.Nd,d=[],e=$E[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=aF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete $E[b],delete aF[b]);return d};function cF(){return!1}function dF(){var a={};return function(b,c,d){}};function eF(){var a=fF;return function(b,c,d){var e=d&&d.event;gF(c);var f=Ah(b)?void 0:1,g=new Ta;qb(c,function(r,t){var u=Bd(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Nb(Zf());var h={Rl:ng(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Pc.Sf(r)}:void 0,Jb:function(){return b},log:function(){},lp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},qq:!!TB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(cF()){var m=dF(),n,p;h.wb={Gj:[],Tf:{},ac:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Ch:Sh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=Xe(a,h,[b,g]);a.Nb();q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return Ad(q,void 0,f)}}function gF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ib(b)&&(a.gtmOnSuccess=function(){Lc(b)});ib(c)&&(a.gtmOnFailure=function(){Lc(c)})};function hF(a){}hF.M="internal.addAdsClickIds";function iF(a,b){var c=this;}iF.publicName="addConsentListener";var jF=!1;function kF(a){for(var b=0;b<a.length;++b)if(jF)try{a[b]()}catch(c){L(77)}else a[b]()}function lF(a,b,c){var d=this,e;return e}lF.M="internal.addDataLayerEventListener";function mF(a,b,c){}mF.publicName="addDocumentEventListener";function nF(a,b,c,d){}nF.publicName="addElementEventListener";function oF(a){return a.K.sb()};function pF(a){}pF.publicName="addEventCallback";
var qF=function(a){return typeof a==="string"?a:String(yp())},tF=function(a,b){rF(a,"init",!1)||(sF(a,"init",!0),b())},rF=function(a,b,c){var d=uF(a);return zb(d,b,c)},vF=function(a,b,c,d){var e=uF(a),f=zb(e,b,d);e[b]=c(f)},sF=function(a,b,c){uF(a)[b]=c},uF=function(a){var b=vp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},wF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Xc(a,"className"),"gtm.elementId":a.for||Mc(a,"id")||"","gtm.elementTarget":a.formTarget||
Xc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Xc(a,"href")||a.src||a.code||a.codebase||"";return d};
function EF(a){}EF.M="internal.addFormAbandonmentListener";function FF(a,b,c,d){}
FF.M="internal.addFormData";var GF={},HF=[],IF={},JF=0,KF=0;
function RF(a,b){}RF.M="internal.addFormInteractionListener";
function YF(a,b){}YF.M="internal.addFormSubmitListener";
function cG(a){}cG.M="internal.addGaSendListener";function dG(a){if(!a)return{};var b=a.lp;return SB(b.type,b.index,b.name)}function eG(a){return a?{originatingEntity:dG(a)}:{}};
var gG=function(a,b,c){fG().updateZone(a,b,c)},iG=function(a,b,c,d,e,f){var g=fG();c=c&&Cb(c,hG);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,jg.ctid,h)){var p=n,q=a,r=d,t=e,u=f;if(Db(p,"GTM-"))KB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Jw("js",xb());KB(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};Ow(v,q,w);Ow(Kw(p,r),q,w)}}}return h},fG=function(){return vp("zones",function(){return new jG})},
kG={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},hG={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},jG=function(){this.C={};this.H={};this.N=0};k=jG.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.xj],b))return!1;for(var e=0;e<c.pg.length;e++)if(this.H[c.pg[e]].ze(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.pg.length;f++){var g=this.H[c.pg[f]];g.ze(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.xj],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].N(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.N);this.H[c]=new lG(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.P(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&up[a]||!d&&Im(a)||d&&d.xj!==b)return!1;if(d)return d.pg.push(c),!1;this.C[a]={xj:b,pg:[c]};return!0};var lG=function(a,b){this.H=null;this.C=[{eventId:a,ze:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};lG.prototype.P=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.ze!==b&&this.C.push({eventId:a,ze:b})};lG.prototype.ze=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].ze;return!1};lG.prototype.N=function(a,b){b=b||[];if(!this.H||kG[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function mG(a){var b=up.zones;return b?b.getIsAllowedFn(Bm(),a):function(){return!0}}function nG(){var a=up.zones;a&&a.unregisterChild(Bm())}
function oG(){EC(Am(),function(a){var b=up.zones;return b?b.isActive(Bm(),a.originalEventData["gtm.uniqueEventId"]):!0});CC(Am(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return mG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var pG=function(a,b){this.tagId=a;this.we=b};
function qG(a,b){var c=this;if(!lh(a)||!eh(b)&&!gh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var d=Ad(b,this.K,1)||{},e=d.firstPartyUrl,f=d.onLoad,g=d.loadByDestination===!0,h=d.isGtmEvent===!0;kF([function(){H(c,"load_google_tags",a,e)}]);if(g){if(Jm(a))return a}else if(Im(a))return a;var m=6,n=oF(this);h&&(m=7);n.Jb()==="__zone"&&(m=1);var p={source:m,fromContainerExecution:!0},q=function(r){CC(r,function(t){for(var u=
DC().getExternalRestrictions(0,Am()),v=l(u),w=v.next();!w.done;w=v.next()){var y=w.value;if(!y(t))return!1}return!0},!0);EC(r,function(t){for(var u=DC().getExternalRestrictions(1,Am()),v=l(u),w=v.next();!w.done;w=v.next()){var y=w.value;if(!y(t))return!1}return!0},!0);f&&f(new pG(a,r))};g?OB(a,e,p,q):KB(a,e,!Db(a,"GTM-"),p,q);f&&n.Jb()==="__zone"&&iG(Number.MIN_SAFE_INTEGER,[a],null,{},dG(oF(this)));return a}qG.M="internal.loadGoogleTag";function rG(a){return new sd("",function(b){var c=this.evaluate(b);if(c instanceof sd)return new sd("",function(){var d=ya.apply(0,arguments),e=this,f=ld(oF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Ld(f);return c.Lb.apply(c,[h].concat(ua(g)))})})};function sG(a,b,c){var d=this;}sG.M="internal.addGoogleTagRestriction";var tG={},uG=[];
function BG(a,b){}
BG.M="internal.addHistoryChangeListener";function CG(a,b,c){}CG.publicName="addWindowEventListener";function DG(a,b){return!0}DG.publicName="aliasInWindow";function EG(a,b,c){}EG.M="internal.appendRemoteConfigParameter";function FG(a){var b;return b}
FG.publicName="callInWindow";function GG(a){}GG.publicName="callLater";function HG(a){}HG.M="callOnDomReady";function IG(a){if(!hh(a))throw F(this.getName(),["function"],arguments);H(this,"process_dom_events","window","load");BD(Ad(a));}IG.M="callOnWindowLoad";function JG(a,b){var c;return c}JG.M="internal.computeGtmParameter";function KG(a,b){var c=this;}KG.M="internal.consentScheduleFirstTry";function LG(a,b){var c=this;}LG.M="internal.consentScheduleRetry";function MG(a){var b;return b}MG.M="internal.copyFromCrossContainerData";function NG(a,b){var c;if(!lh(a)||!qh(b)&&b!==null&&!gh(b))throw F(this.getName(),["string","number|undefined"],arguments);H(this,"read_data_layer",a);c=(b||2)!==2?tk(a,1):vk(a,[x,z]);var d=Bd(c,this.K,Ah(oF(this).Jb())?2:1);d===void 0&&c!==void 0&&L(45);return d}NG.publicName="copyFromDataLayer";
function OG(a){var b=void 0;H(this,"read_data_layer",a);a=String(a);var c;a:{for(var d=oF(this).cachedModelValues,e=l(a.split(".")),f=e.next();!f.done;f=e.next()){if(d==null){c=void 0;break a}d=d[f.value]}c=d}b=Bd(c,this.K,1);return b}OG.M="internal.copyFromDataLayerCache";function PG(a){var b;return b}PG.publicName="copyFromWindow";function QG(a){var b=void 0;return Bd(b,this.K,1)}QG.M="internal.copyKeyFromWindow";var RG=function(a){return a===Pm.X.Da&&gn[a]===Om.Ia.pe&&!O(J.m.U)};var SG=function(){return"0"},TG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];D(102)&&b.push("gbraid");return Uk(a,b,"0")};var UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH=(sH[J.m.Qa]=(UG[2]=[RG],UG),sH[J.m.tf]=(VG[2]=[RG],VG),sH[J.m.ff]=(WG[2]=[RG],WG),sH[J.m.mi]=(XG[2]=[RG],XG),sH[J.m.ni]=(YG[2]=[RG],YG),sH[J.m.oi]=(ZG[2]=[RG],ZG),sH[J.m.ri]=($G[2]=[RG],$G),sH[J.m.si]=(aH[2]=[RG],aH),sH[J.m.wc]=(bH[2]=[RG],bH),sH[J.m.vf]=(cH[2]=[RG],cH),sH[J.m.wf]=(dH[2]=[RG],dH),sH[J.m.xf]=(eH[2]=[RG],eH),sH[J.m.yf]=(fH[2]=
[RG],fH),sH[J.m.zf]=(gH[2]=[RG],gH),sH[J.m.Af]=(hH[2]=[RG],hH),sH[J.m.Bf]=(iH[2]=[RG],iH),sH[J.m.Cf]=(jH[2]=[RG],jH),sH[J.m.lb]=(kH[1]=[RG],kH),sH[J.m.Zc]=(lH[1]=[RG],lH),sH[J.m.fd]=(mH[1]=[RG],mH),sH[J.m.Xd]=(nH[1]=[RG],nH),sH[J.m.Qe]=(oH[1]=[function(a){return D(102)&&RG(a)}],oH),sH[J.m.gd]=(pH[1]=[RG],pH),sH[J.m.Aa]=(qH[1]=[RG],qH),sH[J.m.Wa]=(rH[1]=[RG],rH),sH),uH={},vH=(uH[J.m.lb]=SG,uH[J.m.Zc]=SG,uH[J.m.fd]=SG,uH[J.m.Xd]=SG,uH[J.m.Qe]=SG,uH[J.m.gd]=function(a){if(!kd(a))return{};var b=ld(a,
null);delete b.match_id;return b},uH[J.m.Aa]=TG,uH[J.m.Wa]=TG,uH),wH={},xH={},yH=(xH[P.A.ib]=(wH[2]=[RG],wH),xH),zH={};var AH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};AH.prototype.getValue=function(a){a=a===void 0?Pm.X.Fb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};AH.prototype.H=function(){return id(this.C)==="array"||kd(this.C)?ld(this.C,null):this.C};
var BH=function(){},CH=function(a,b){this.conditions=a;this.C=b},DH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new AH(c,e,g,a.C[b]||BH)},EH,FH;var GH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;R(this,g,d[g])}},Nv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,Q(a,P.A.Qf))},T=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(EH!=null||(EH=new CH(tH,vH)),e=DH(EH,b,c));d[b]=e};
GH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return T(this,a,b),!0;if(!kd(c))return!1;T(this,a,Object.assign(c,b));return!0};var HH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
GH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&jb(d)&&D(92))try{d=c(d)}catch(e){}d!==void 0&&T(this,a,d)};
var Q=function(a,b){var c=a.metadata[b];if(b===P.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,Q(a,P.A.Qf))},R=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(FH!=null||(FH=new CH(yH,zH)),e=DH(FH,b,c));d[b]=e},IH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},gw=function(a,b,c){var d=Vw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function JH(a,b){var c;return c}JH.M="internal.copyPreHit";function KH(a,b){var c=null;if(!lh(a)||!lh(b))throw F(this.getName(),["string","string"],arguments);H(this,"access_globals","readwrite",a);H(this,"access_globals","readwrite",b);var d=[x,z],e=a.split("."),f=Eb(x,e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return ib(h)?Bd(h,this.K,2):null;var m;h=function(){if(!ib(m.push))throw Error("Object at "+b+" in window is not an array.");m.push.call(m,
arguments)};f[g]=h;var n=b.split("."),p=Eb(x,n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");m=p[q];m===void 0&&(m=[],p[q]=m);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return Bd(c,this.K,2)}KH.publicName="createArgumentsQueue";function LH(a){return Bd(function(c){var d=bC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
bC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}LH.M="internal.createGaCommandQueue";function MH(a){return Bd(function(){if(!ib(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ah(oF(this).Jb())?2:1)}MH.publicName="createQueue";function NH(a,b){var c=null;if(!lh(a)||!mh(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new xd(new RegExp(a,d))}catch(e){}return c}NH.M="internal.createRegex";function OH(a){}OH.M="internal.declareConsentState";function PH(a){var b="";return b}PH.M="internal.decodeUrlHtmlEntities";function QH(a,b,c){var d;return d}QH.M="internal.decorateUrlWithGaCookies";function RH(){}RH.M="internal.deferCustomEvents";function SH(a){var b;return b}SH.M="internal.detectUserProvidedData";
var VH=function(a){var b=Pc(a,["button","input"],50);if(!b)return null;var c=String(b.tagName).toLowerCase();if(c==="button")return b;if(c==="input"){var d=Mc(b,"type");if(d==="button"||d==="submit"||d==="image"||d==="file"||d==="reset")return b}return null},WH=function(a,b,c){var d=c.target;if(d){var e=rF(a,"individualElementIds",[]);if(e.length>0){var f=wF(d,b,e);KD(f)}var g=!1,h=rF(a,"commonButtonIds",[]);if(h.length>0){var m=VH(d);if(m){var n=wF(m,b,h);KD(n);g=!0}}var p=rF(a,"selectorToTriggerIds",
{}),q;for(q in p)if(p.hasOwnProperty(q)){var r=g?p[q].filter(function(v){return h.indexOf(v)===-1}):p[q];if(r.length!==0){var t=vi(d,q);if(t){var u=wF(t,b,r);KD(u)}}}}};
function XH(a,b){if(!fh(a))throw F(this.getName(),["Object|undefined","any"],arguments);var c=a?Ad(a):{},d=ub(c.matchCommonButtons),e=!!c.cssSelector,f=qF(b);H(this,"detect_click_events",c.matchCommonButtons,c.cssSelector);var g=c.useV2EventName?"gtm.click-v2":"gtm.click",h=c.useV2EventName?"ecl":"cl",m=function(p){p.push(f);return p};if(e||d){if(d&&vF(h,"commonButtonIds",m,[]),e){var n=wb(String(c.cssSelector));vF(h,"selectorToTriggerIds",
function(p){p.hasOwnProperty(n)||(p[n]=[]);m(p[n]);return p},{})}}else vF(h,"individualElementIds",m,[]);tF(h,function(){Jc(z,"click",function(p){WH(h,g,p)},!0)});return f}XH.M="internal.enableAutoEventOnClick";
function eI(a,b){return p}eI.M="internal.enableAutoEventOnElementVisibility";function fI(){}fI.M="internal.enableAutoEventOnError";var gI={},hI=[],iI={},jI=0,kI=0;
function qI(a,b){var c=this;return d}qI.M="internal.enableAutoEventOnFormInteraction";
function vI(a,b){var c=this;return f}vI.M="internal.enableAutoEventOnFormSubmit";
function AI(){var a=this;}AI.M="internal.enableAutoEventOnGaSend";var BI={},CI=[];
function JI(a,b){var c=this;return f}JI.M="internal.enableAutoEventOnHistoryChange";var KI=["http://","https://","javascript:","file://"];
function OI(a,b){var c=this;return h}OI.M="internal.enableAutoEventOnLinkClick";var PI,QI;
var RI=function(a){return rF("sdl",a,{})},SI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];vF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},VI=function(){function a(){TI();UI(a,!0)}return a},WI=function(){function a(){f?e=x.setTimeout(a,c):(e=0,TI(),UI(b));f=!1}function b(){d&&PI();e?f=!0:(e=x.setTimeout(a,c),sF("sdl","pending",!0))}var c=250,d=!1;z.scrollingElement&&z.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
UI=function(a,b){rF("sdl","init",!1)&&!XI()&&(b?Kc(x,"scrollend",a):Kc(x,"scroll",a),Kc(x,"resize",a),sF("sdl","init",!1))},TI=function(){var a=PI(),b=a.depthX,c=a.depthY,d=b/QI.scrollWidth*100,e=c/QI.scrollHeight*100;YI(b,"horiz.pix","PIXELS","horizontal");YI(d,"horiz.pct","PERCENT","horizontal");YI(c,"vert.pix","PIXELS","vertical");YI(e,"vert.pct","PERCENT","vertical");sF("sdl","pending",!1)},YI=function(a,b,c,d){var e=RI(b),f={},g;for(g in e)if(f={Fe:f.Fe},f.Fe=g,e.hasOwnProperty(f.Fe)){var h=
Number(f.Fe);if(!(a<h)){var m={};TD((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Fe].join(","),m));vF("sdl",b,function(n){return function(p){delete p[n.Fe];return p}}(f),{})}}},$I=function(){vF("sdl","scr",function(a){a||(a=z.scrollingElement||z.body&&z.body.parentNode);return QI=a},!1);vF("sdl","depth",function(a){a||(a=ZI());return PI=a},!1)},ZI=function(){var a=0,b=0;return function(){var c=Yw(),d=c.height;
a=Math.max(QI.scrollLeft+c.width,a);b=Math.max(QI.scrollTop+d,b);return{depthX:a,depthY:b}}},XI=function(){return!!(Object.keys(RI("horiz.pix")).length||Object.keys(RI("horiz.pct")).length||Object.keys(RI("vert.pix")).length||Object.keys(RI("vert.pct")).length)};
function aJ(a,b){var c=this;if(!eh(a))throw F(this.getName(),["Object","any"],arguments);kF([function(){H(c,"detect_scroll_events")}]);$I();if(!QI)return;var d=qF(b),e=Ad(a);switch(e.horizontalThresholdUnits){case "PIXELS":SI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":SI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":SI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":SI(e.verticalThresholds,
d,"vert.pct")}rF("sdl","init",!1)?rF("sdl","pending",!1)||Lc(function(){TI()}):(sF("sdl","init",!0),sF("sdl","pending",!0),Lc(function(){TI();if(XI()){var f=WI();"onscrollend"in x?(f=VI(),Jc(x,"scrollend",f)):Jc(x,"scroll",f);Jc(x,"resize",f)}else sF("sdl","init",!1)}));return d}aJ.M="internal.enableAutoEventOnScroll";function bJ(a){return function(){if(a.limit&&a.uj>=a.limit)a.zh&&x.clearInterval(a.zh);else{a.uj++;var b=yb();KD({event:a.eventName,"gtm.timerId":a.zh,"gtm.timerEventNumber":a.uj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Gm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Gm,"gtm.triggers":a.Kq})}}}
function cJ(a,b){
return f}cJ.M="internal.enableAutoEventOnTimer";var mc=wa(["data-gtm-yt-inspected-"]),eJ=["www.youtube.com","www.youtube-nocookie.com"],fJ,gJ=!1;
function qJ(a,b){var c=this;return e}qJ.M="internal.enableAutoEventOnYouTubeActivity";gJ=!1;function rJ(a,b){if(!lh(a)||!fh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Ad(b):{},d=a,e=!1;return e}rJ.M="internal.evaluateBooleanExpression";var sJ;function tJ(a){var b=!1;return b}tJ.M="internal.evaluateMatchingRules";function cK(){return pr(7)&&pr(9)&&pr(10)};function YK(a,b,c,d){}YK.M="internal.executeEventProcessor";function ZK(a){var b;if(!lh(a))throw F(this.getName(),["string"],arguments);H(this,"unsafe_run_arbitrary_javascript");try{var c=x.google_tag_manager;c&&typeof c.e==="function"&&(b=c.e(a))}catch(d){}return Bd(b,this.K,1)}ZK.M="internal.executeJavascriptString";function $K(a){var b;return b};function aL(a){var b="";return b}aL.M="internal.generateClientId";function bL(a){var b={};return Bd(b)}bL.M="internal.getAdsCookieWritingOptions";function cL(a,b){var c=!1;return c}cL.M="internal.getAllowAdPersonalization";function dL(){var a;return a}dL.M="internal.getAndResetEventUsage";function eL(a,b){b=b===void 0?!0:b;var c;return c}eL.M="internal.getAuid";var fL=null;
function gL(){var a=new Ta;return a}
gL.publicName="getContainerVersion";function hL(a,b){b=b===void 0?!0:b;var c;return c}hL.publicName="getCookieValues";function iL(){var a="";return a}iL.M="internal.getCorePlatformServicesParam";function jL(){return jo()}jL.M="internal.getCountryCode";function kL(){var a=[];return Bd(a)}kL.M="internal.getDestinationIds";function lL(a){var b=new Ta;return b}lL.M="internal.getDeveloperIds";function mL(a){var b;return b}mL.M="internal.getEcsidCookieValue";function nL(a,b){var c=null;if(!kh(a)||!lh(b))throw F(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementAttribute requires an HTML Element.");H(this,"get_element_attributes",d,b);c=Mc(d,b);return c}nL.M="internal.getElementAttribute";function oL(a){var b=null;return b}oL.M="internal.getElementById";function pL(a){var b="";if(!kh(a))throw F(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementInnerText requires an HTML Element.");H(this,"read_dom_element_text",c);b=Nc(c);return b}pL.M="internal.getElementInnerText";function qL(a,b){var c=null;if(!kh(a)||!lh(b))throw F(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementProperty requires an HTML element.");H(this,"access_dom_element_properties",d,"read",b);c=d[b];return Bd(c)}qL.M="internal.getElementProperty";function rL(a){var b;if(!kh(a))throw F(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementValue requires an HTML Element.");H(this,"access_element_values",c,"read");b=c instanceof HTMLInputElement?c.value:Mc(c,"value")||"";return b}rL.M="internal.getElementValue";function sL(a){var b=0;return b}sL.M="internal.getElementVisibilityRatio";function tL(a){var b=null;return b}tL.M="internal.getElementsByCssSelector";
function uL(a){var b;if(!lh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=oF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var E=C.value;E===m?(w.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Bd(c,this.K,1);return b}uL.M="internal.getEventData";var vL={};vL.enableDecodeUri=D(92);vL.enableGaAdsConversions=D(122);vL.enableGaAdsConversionsClientId=D(121);vL.enableOverrideAdsCps=D(170);vL.enableUrlDecodeEventUsage=D(139);function wL(){return Bd(vL)}wL.M="internal.getFlags";function xL(){var a;return a}xL.M="internal.getGsaExperimentId";function yL(){return new xd(yE)}yL.M="internal.getHtmlId";function zL(a){var b;return b}zL.M="internal.getIframingState";function AL(a,b){var c={};return Bd(c)}AL.M="internal.getLinkerValueFromLocation";function BL(){var a=new Ta;return a}BL.M="internal.getPrivacyStrings";function CL(a,b){var c;return c}CL.M="internal.getProductSettingsParameter";function DL(a,b){var c;return c}DL.publicName="getQueryParameters";function EL(a,b){var c;return c}EL.publicName="getReferrerQueryParameters";function FL(a){var b="";if(!mh(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_referrer",a);b=Pk(Tk(z.referrer),a);return b}FL.publicName="getReferrerUrl";function GL(){return ko()}GL.M="internal.getRegionCode";function HL(a,b){var c;return c}HL.M="internal.getRemoteConfigParameter";function IL(){var a=new Ta;a.set("width",0);a.set("height",0);return a}IL.M="internal.getScreenDimensions";function JL(){var a="";return a}JL.M="internal.getTopSameDomainUrl";function KL(){var a="";return a}KL.M="internal.getTopWindowUrl";function LL(a){var b="";if(!mh(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_url",a);b=Nk(Tk(x.location.href),a);return b}LL.publicName="getUrl";function ML(){H(this,"get_user_agent");return sc.userAgent}ML.M="internal.getUserAgent";function NL(){var a;return a?Bd(Qy(a)):a}NL.M="internal.getUserAgentClientHints";function VL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function WL(){var a=VL();a.hid=a.hid||nb();return a.hid}function XL(a,b){var c=VL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function uM(a){(hy(a)||nk())&&T(a,J.m.Tk,ko()||jo());!hy(a)&&nk()&&T(a,J.m.jl,"::")}function vM(a){if(nk()&&!hy(a)){var b=D(176);D(187)&&D(201)&&(b=b&&!no());b&&T(a,J.m.Hk,!0);if(D(78)){aw(a);bw(a,Cp.Ef.Wm,Ho(N(a.D,J.m.cb)));var c=Cp.Ef.Xm;var d=N(a.D,J.m.Hc);bw(a,c,d===!0?1:d===!1?0:void 0);bw(a,Cp.Ef.Vm,Ho(N(a.D,J.m.yb)));bw(a,Cp.Ef.Tm,zs(Go(N(a.D,J.m.nb)),Go(N(a.D,J.m.Sb))))}}};var RM={AW:qn.Z.Pm,G:qn.Z.Zn,DC:qn.Z.Xn};function SM(a){var b=cj(a);return""+bs(b.map(function(c){return c.value}).join("!"))}function TM(a){var b=Fp(a);return b&&RM[b.prefix]}function UM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var yN=window,zN=document,AN=function(a){var b=yN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||zN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&yN["ga-disable-"+a]===!0)return!0;try{var c=yN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(zN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return zN.getElementById("__gaOptOutExtension")?!0:!1};
function MN(a){qb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Wb]||{};qb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function uO(a,b){}function vO(a,b){var c=function(){};return c}
function wO(a,b,c){};var xO=vO;var yO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function zO(a,b,c){var d=this;if(!lh(a)||!fh(b)||!fh(c))throw F(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?Ad(b):{};kF([function(){return H(d,"configure_google_tags",a,e)}]);var f=c?Ad(c):{},g=oF(this);f.originatingEntity=dG(g);Ow(Kw(a,e),g.eventId,f);}zO.M="internal.gtagConfig";
function BO(a,b){}
BO.publicName="gtagSet";function CO(){var a={};return a};function DO(a){}DO.M="internal.initializeServiceWorker";function EO(a,b){}EO.publicName="injectHiddenIframe";var FO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function GO(a,b,c,d,e){if(!((lh(a)||kh(a))&&hh(b)&&hh(c)&&ph(d)&&ph(e)))throw F(this.getName(),["string|OpaqueValue","function","function","boolean|undefined","boolean|undefined"],arguments);var f=oF(this);d&&FO(3);e&&(FO(1),FO(2));var g=f.eventId,h=f.Jb(),m=FO(void 0);if(hl){var n=String(m)+h;$E[g]=$E[g]||[];$E[g].push(n);aF[g]=aF[g]||[];aF[g].push("p"+h)}
if(d&&e)throw Error("useIframe and supportDocumentWrite cannot both be true.");H(this,"unsafe_inject_arbitrary_html",d,e);var p=Ad(b,this.K),q=Ad(c,this.K),r=Ad(a,this.K,1);HO(r,p,q,!!d,!!e,f);}
var IO=function(a,b,c,d){return function(){try{if(b.length>0){var e=b.shift(),f=IO(a,b,c,d),g=e;if(String(g.nodeName).toUpperCase()==="SCRIPT"&&g.type==="text/gtmscript"){var h=g.text||g.textContent||g.innerHTML||"",m=g.getAttribute("data-gtmsrc"),n=g.charset||"";m?Ec(m,f,d,{async:!1,id:e.id,text:h,charset:n},a):(g=z.createElement("script"),g.async=!1,g.type="text/javascript",g.id=e.id,g.text=h,g.charset=n,f&&(g.onload=f),a.insertBefore(g,null));m||f()}else if(e.innerHTML&&e.innerHTML.toLowerCase().indexOf("<script")>=
0){for(var p=[];e.firstChild;)p.push(e.removeChild(e.firstChild));a.insertBefore(e,null);IO(e,p,f,d)()}else a.insertBefore(e,null),f()}else c()}catch(q){d()}}},HO=function(a,b,c,d,e,f){if(z.body){var g=DE(a,b,c);a=g.Mp;b=g.onSuccess;if(d){}else e?
JO(a,b,c):IO(z.body,Oc(a),b,c)()}else x.setTimeout(function(){HO(a,b,c,d,e,f)})};GO.M="internal.injectHtml";var KO={};
function MO(a,b,c,d){}var NO={dl:1,id:1},OO={};
function PO(a,b,c,d){}D(160)?PO.publicName="injectScript":MO.publicName="injectScript";PO.M="internal.injectScript";function QO(){return oo()}QO.M="internal.isAutoPiiEligible";function RO(a){var b=!0;return b}RO.publicName="isConsentGranted";function SO(a){var b=!1;return b}SO.M="internal.isDebugMode";function TO(){return mo()}TO.M="internal.isDmaRegion";function UO(a){var b=!1;return b}UO.M="internal.isEntityInfrastructure";function VO(a){var b=!1;return b}VO.M="internal.isFeatureEnabled";function WO(){var a=!1;return a}WO.M="internal.isFpfe";function XO(){var a=!1;return a}XO.M="internal.isGcpConversion";function YO(){var a=!1;return a}YO.M="internal.isLandingPage";function ZO(){var a=!1;return a}ZO.M="internal.isOgt";function $O(){var a;return a}$O.M="internal.isSafariPcmEligibleBrowser";function aP(){var a=Nh(function(b){oF(this).log("error",b)});a.publicName="JSON";return a};function bP(a){var b=void 0;if(!lh(a))throw F(this.getName(),["string"],arguments);b=Tk(a);return Bd(b)}bP.M="internal.legacyParseUrl";function cP(){return!1}
var dP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function eP(){try{H(this,"logging")}catch(c){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=Ad(a[b],this.K);console.log.apply(console,a);}eP.publicName="logToConsole";function fP(a,b){}fP.M="internal.mergeRemoteConfig";function gP(a,b,c){c=c===void 0?!0:c;var d=[];return Bd(d)}gP.M="internal.parseCookieValuesFromString";function hP(a){var b=void 0;if(typeof a!=="string")return;a&&Db(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Bd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Tk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Mk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Bd(n);
return b}hP.publicName="parseUrl";function iP(a){}iP.M="internal.processAsNewEvent";function jP(a,b,c){var d;return d}jP.M="internal.pushToDataLayer";function kP(a){var b=ya.apply(1,arguments),c=!1;return c}kP.publicName="queryPermission";function lP(a){var b=this;}lP.M="internal.queueAdsTransmission";function mP(a,b){var c=void 0;return c}mP.publicName="readAnalyticsStorage";function nP(){var a="";return a}nP.publicName="readCharacterSet";function oP(){return Vj}oP.M="internal.readDataLayerName";function pP(){var a="";return a}pP.publicName="readTitle";function qP(a,b){var c=this;}qP.M="internal.registerCcdCallback";function rP(a,b){return!0}rP.M="internal.registerDestination";var sP=["config","event","get","set"];function tP(a,b,c){}tP.M="internal.registerGtagCommandListener";function uP(a,b){var c=!1;return c}uP.M="internal.removeDataLayerEventListener";function vP(a,b){}
vP.M="internal.removeFormData";function wP(){}wP.publicName="resetDataLayer";function xP(a,b,c){var d=void 0;return d}xP.M="internal.scrubUrlParams";function yP(a){}yP.M="internal.sendAdsHit";function zP(a,b,c,d){}zP.M="internal.sendGtagEvent";function AP(a,b,c){}AP.publicName="sendPixel";function BP(a,b){}BP.M="internal.setAnchorHref";function CP(a){}CP.M="internal.setContainerConsentDefaults";function DP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}DP.publicName="setCookie";function EP(a){}EP.M="internal.setCorePlatformServices";function FP(a,b){}FP.M="internal.setDataLayerValue";function GP(a){}GP.publicName="setDefaultConsentState";function HP(a,b){}HP.M="internal.setDelegatedConsentType";function IP(a,b){}IP.M="internal.setFormAction";function JP(a,b,c){c=c===void 0?!1:c;}JP.M="internal.setInCrossContainerData";function KP(a,b,c){return!1}KP.publicName="setInWindow";function LP(a,b,c){}LP.M="internal.setProductSettingsParameter";function MP(a,b,c){}MP.M="internal.setRemoteConfigParameter";function NP(a,b){}NP.M="internal.setTransmissionMode";function OP(a,b,c,d){var e=this;}OP.publicName="sha256";function PP(a,b,c){}
PP.M="internal.sortRemoteConfigParameters";function QP(a){}QP.M="internal.storeAdsBraidLabels";function RP(a,b){var c=void 0;return c}RP.M="internal.subscribeToCrossContainerData";var SP={},TP={};SP.getItem=function(a){var b=null;H(this,"access_template_storage");var c=oF(this).Jb();TP[c]&&(b=TP[c].hasOwnProperty("gtm."+a)?TP[c]["gtm."+a]:null);return b};SP.setItem=function(a,b){H(this,"access_template_storage");var c=oF(this).Jb();TP[c]=TP[c]||{};TP[c]["gtm."+a]=b;};
SP.removeItem=function(a){H(this,"access_template_storage");var b=oF(this).Jb();if(!TP[b]||!TP[b].hasOwnProperty("gtm."+a))return;delete TP[b]["gtm."+a];};SP.clear=function(){H(this,"access_template_storage"),delete TP[oF(this).Jb()];};SP.publicName="templateStorage";function UP(a,b){var c=!1;if(!kh(a)||!lh(b))throw F(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}UP.M="internal.testRegex";function VP(a){var b;return b};function WP(a,b){var c;return c}WP.M="internal.unsubscribeFromCrossContainerData";function XP(a){}XP.publicName="updateConsentState";function YP(a){var b=!1;return b}YP.M="internal.userDataNeedsEncryption";var ZP;function $P(a,b,c){ZP=ZP||new Yh;ZP.add(a,b,c)}function aQ(a,b){var c=ZP=ZP||new Yh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=ib(b)?th(a,b):uh(a,b)}
function bQ(){return function(a){var b;var c=ZP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Jb();if(g){Ah(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function cQ(){var a=function(c){return void aQ(c.M,c)},b=function(c){return void $P(c.publicName,c)};b(iF);b(pF);b(DG);b(FG);b(GG);b(NG);b(PG);b(KH);b(aP());b(MH);b(gL);b(hL);b(DL);b(EL);b(FL);b(LL);b(BO);b(EO);b(RO);b(eP);b(hP);b(kP);b(nP);b(pP);b(AP);b(DP);b(GP);b(KP);b(OP);b(SP);b(XP);$P("Math",yh());$P("Object",Wh);$P("TestHelper",$h());$P("assertApi",vh);$P("assertThat",wh);$P("decodeUri",Bh);$P("decodeUriComponent",Ch);$P("encodeUri",Dh);$P("encodeUriComponent",Eh);$P("fail",Jh);$P("generateRandom",
Kh);$P("getTimestamp",Lh);$P("getTimestampMillis",Lh);$P("getType",Mh);$P("makeInteger",Oh);$P("makeNumber",Ph);$P("makeString",Qh);$P("makeTableMap",Rh);$P("mock",Uh);$P("mockObject",Vh);$P("fromBase64",$K,!("atob"in x));$P("localStorage",dP,!cP());$P("toBase64",VP,!("btoa"in x));a(hF);a(lF);a(FF);a(RF);a(YF);a(cG);a(sG);a(BG);a(EG);a(HG);a(IG);a(JG);a(KG);a(LG);a(MG);a(OG);a(QG);a(JH);a(LH);a(NH);a(OH);a(PH);a(QH);a(RH);a(SH);a(XH);a(eI);a(fI);a(qI);a(vI);a(AI);a(JI);a(OI);a(aJ);a(cJ);a(qJ);a(rJ);
a(tJ);a(YK);a(ZK);a(aL);a(bL);a(cL);a(dL);a(eL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(wL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(GL);a(HL);a(IL);a(JL);a(KL);a(NL);a(zO);a(DO);a(GO);a(PO);a(QO);a(SO);a(TO);a(UO);a(VO);a(WO);a(XO);a(YO);a(ZO);a($O);a(bP);a(qG);a(fP);a(gP);a(iP);a(jP);a(lP);a(oP);a(qP);a(rP);a(tP);a(uP);a(vP);a(xP);a(yP);a(zP);a(BP);a(CP);a(EP);a(FP);a(HP);a(IP);a(JP);a(LP);a(MP);a(NP);a(PP);a(QP);a(RP);a(UP);a(WP);a(YP);aQ("internal.IframingStateSchema",
CO());
D(104)&&a(iL);D(160)?b(PO):b(MO);D(177)&&b(mP);return bQ()};var fF;
function dQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;fF=new Ve;eQ();Cf=eF();var e=fF,f=cQ(),g=new td("require",f);g.Ua();e.C.C.set("require",g);Pa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Yf(n,d[m]);try{fF.execute(n),D(120)&&hl&&n[0]===50&&h.push(n[1])}catch(r){}}D(120)&&(Qf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");jk[q]=["sandboxedScripts"]}fQ(b)}function eQ(){fF.Vc(function(a,b,c){up.SANDBOXED_JS_SEMAPHORE=up.SANDBOXED_JS_SEMAPHORE||0;up.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{up.SANDBOXED_JS_SEMAPHORE--}})}function fQ(a){a&&qb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");jk[e]=jk[e]||[];jk[e].push(b)}})};function gQ(a){Ow(Iw("developer_id."+a,!0),0,{})};var hQ=Array.isArray;function iQ(a,b){return ld(a,b||null)}function V(a){return window.encodeURIComponent(a)}function jQ(a,b,c){Ic(a,b,c)}
function kQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Nk(Tk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function lQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function mQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=lQ(b,"parameter","parameterValue");e&&(c=iQ(e,c))}return c}function nQ(a,b,c){if(Hr()){b&&Lc(b)}else return Ec(a,b,c,void 0)}function oQ(){return x.location.href}function pQ(a,b){return tk(a,b||2)}function qQ(a,b){x[a]=b}function rQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function sQ(a,b){if(Hr()){b&&Lc(b)}else Gc(a,b)}
var tQ={};var Z={securityGroups:{}};
Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},T:function(){return{}}}},Z.__access_template_storage.F="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage["5"]=!1;

Z.securityGroups.access_element_values=["google"],function(){function a(b,c,d,e){return{element:c,operation:d,newValue:e}}(function(b){Z.__access_element_values=b;Z.__access_element_values.F="access_element_values";Z.__access_element_values.isVendorTemplate=!0;Z.__access_element_values.priorityOverride=0;Z.__access_element_values.isInfrastructure=!1;Z.__access_element_values["5"]=!1})(function(b){var c=b.vtp_allowRead,d=b.vtp_allowWrite,e=b.vtp_createPermissionError;return{assert:function(f,g,h,m){if(!(g instanceof
HTMLElement))throw e(f,{},"Element must be a HTMLElement.");if(h!=="read"&&h!=="write")throw e(f,{},"Unknown operation: "+h+".");if(h=="read"&&!c)throw e(f,{},"Attempting to perform disallowed operation: read.");if(h=="write"){if(!d)throw e(f,{},"Attempting to perform disallowed operation: write.");if(!jb(m))throw e(f,{},"Attempting to write value without valid new value.");}},T:a}})}();
Z.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){Z.__access_globals=b;Z.__access_globals.F="access_globals";Z.__access_globals.isVendorTemplate=!0;Z.__access_globals.priorityOverride=0;Z.__access_globals.isInfrastructure=!1;
Z.__access_globals["5"]=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var m=c[h],n=m.key;m.read&&e.push(n);m.write&&f.push(n);m.execute&&g.push(n)}return{assert:function(p,q,r){if(!jb(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},T:a}})}();
Z.securityGroups.access_dom_element_properties=["google"],function(){function a(b,c,d,e){var f={property:e,read:!1,write:!1};switch(d){case "read":f.read=!0;break;case "write":f.write=!0;break;default:throw Error("Invalid "+b+" operation "+d);}return f}(function(b){Z.__access_dom_element_properties=b;Z.__access_dom_element_properties.F="access_dom_element_properties";Z.__access_dom_element_properties.isVendorTemplate=!0;Z.__access_dom_element_properties.priorityOverride=0;Z.__access_dom_element_properties.isInfrastructure=
!1;Z.__access_dom_element_properties["5"]=!1})(function(b){for(var c=b.vtp_properties||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],m=h.property;h.read&&e.push(m);h.write&&f.push(m)}return{assert:function(n,p,q,r){if(!jb(r))throw d(n,{},"Property must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else throw d(n,{},'Operation must be either "read" or "write"');throw d(n,{},'"'+q+'" operation is not allowed.');},T:a}})}();

Z.securityGroups.read_dom_element_text=["google"],function(){function a(b,c){return{element:c}}(function(b){Z.__read_dom_element_text=b;Z.__read_dom_element_text.F="read_dom_element_text";Z.__read_dom_element_text.isVendorTemplate=!0;Z.__read_dom_element_text.priorityOverride=0;Z.__read_dom_element_text.isInfrastructure=!1;Z.__read_dom_element_text["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(!(e instanceof HTMLElement))throw c(d,{},"Wrong element type. Must be HTMLElement.");
},T:a}})}();
Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.F="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),
b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!jb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!jb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!jb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Jg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();
Z.securityGroups.process_dom_events=["google"],function(){function a(b,c,d){return{targetType:c,eventName:d}}(function(b){Z.__process_dom_events=b;Z.__process_dom_events.F="process_dom_events";Z.__process_dom_events.isVendorTemplate=!0;Z.__process_dom_events.priorityOverride=0;Z.__process_dom_events.isInfrastructure=!1;Z.__process_dom_events["5"]=!1})(function(b){for(var c=b.vtp_targets||[],d=b.vtp_createPermissionError,e={},f=0;f<c.length;f++){var g=c[f];e[g.targetType]=e[g.targetType]||[];e[g.targetType].push(g.eventName)}return{assert:function(h,
m,n){if(!e[m])throw d(h,{},"Prohibited event target "+m+".");if(e[m].indexOf(n)===-1)throw d(h,{},"Prohibited listener registration for DOM event "+n+".");},T:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!jb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Jg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},T:a}})}();




Z.securityGroups.gaawe=["google"],function(){function a(f,g,h){for(var m=0;m<g.length;m++)f.hasOwnProperty(g[m])&&(f[g[m]]=h(f[g[m]]))}function b(f,g,h){var m={},n=function(u,v){m[u]=m[u]||v},p=function(u,v,w){w=w===void 0?!1:w;c.push(6);if(u){m.items=m.items||[];for(var y={},A=0;A<u.length;y={jg:void 0},A++)y.jg={},qb(u[A],function(E){return function(G,I){w&&G==="id"?E.jg.promotion_id=I:w&&G==="name"?E.jg.promotion_name=I:E.jg[G]=I}}(y)),m.items.push(y.jg)}if(v)for(var C in v)d.hasOwnProperty(C)?n(d[C],
v[C]):n(C,v[C])},q;f.vtp_getEcommerceDataFrom==="dataLayer"?(q=f.vtp_gtmCachedValues.eventModel)||(q=f.vtp_gtmCachedValues.ecommerce):(q=f.vtp_ecommerceMacroData,kd(q)&&q.ecommerce&&!q.items&&(q=q.ecommerce));if(kd(q)){var r=!1,t;for(t in q)q.hasOwnProperty(t)&&(r||(c.push(5),r=!0),t==="currencyCode"?n("currency",q.currencyCode):t==="impressions"&&g===J.m.jc?p(q.impressions,null):t==="promoClick"&&g===J.m.Gc?p(q.promoClick.promotions,q.promoClick.actionField,!0):t==="promoView"&&g===J.m.kc?p(q.promoView.promotions,
q.promoView.actionField,!0):e.hasOwnProperty(t)?g===e[t]&&p(q[t].products,q[t].actionField):m[t]=q[t]);iQ(m,h)}}var c=[],d={id:"transaction_id",revenue:"value",list:"item_list_name"},e={click:"select_item",detail:"view_item",add:"add_to_cart",remove:"remove_from_cart",checkout:"begin_checkout",checkout_option:"checkout_option",purchase:"purchase",refund:"refund"};(function(f){Z.__gaawe=f;Z.__gaawe.F="gaawe";Z.__gaawe.isVendorTemplate=!0;Z.__gaawe.priorityOverride=0;Z.__gaawe.isInfrastructure=!1;Z.__gaawe["5"]=
!0})(function(f){var g;g=f.vtp_migratedToV2?String(f.vtp_measurementIdOverride):String(f.vtp_measurementIdOverride||f.vtp_measurementId);if(jb(g)&&g.indexOf("G-")===0){var h=String(f.vtp_eventName),m={};c=[];f.vtp_sendEcommerceData&&(vo.hasOwnProperty(h)||h==="checkout_option")&&b(f,h,m);var n=f.vtp_eventSettingsVariable;if(n)for(var p in n)n.hasOwnProperty(p)&&(m[p]=n[p]);if(f.vtp_eventSettingsTable){var q=lQ(f.vtp_eventSettingsTable,"parameter","parameterValue"),r;for(r in q)m[r]=q[r]}var t=lQ(f.vtp_eventParameters,
"name","value"),u;for(u in t)t.hasOwnProperty(u)&&(m[u]=t[u]);var v=f.vtp_userDataVariable;v&&(m[J.m.eb]=v);if(m.hasOwnProperty(J.m.Wb)||f.vtp_userProperties){var w=m[J.m.Wb]||{};iQ(lQ(f.vtp_userProperties,"name","value"),w);m[J.m.Wb]=w}var y={originatingEntity:SB(1,f.vtp_gtmEntityIndex,f.vtp_gtmEntityName)};if(c.length>0){var A={};y.eventMetadata=(A[P.A.Yk]=c,A)}a(m,wo,function(E){return ub(E)});a(m,yo,function(E){return Number(E)});var C=f.vtp_gtmEventId;y.noGtmEvent=!0;Ow(Lw(g,h,m),C,y);Lc(f.vtp_gtmOnSuccess)}else Lc(f.vtp_gtmOnFailure)})}();


Z.securityGroups.get_element_attributes=["google"],function(){function a(b,c,d){return{element:c,attribute:d}}(function(b){Z.__get_element_attributes=b;Z.__get_element_attributes.F="get_element_attributes";Z.__get_element_attributes.isVendorTemplate=!0;Z.__get_element_attributes.priorityOverride=0;Z.__get_element_attributes.isInfrastructure=!1;Z.__get_element_attributes["5"]=!1})(function(b){var c=b.vtp_allowedAttributes||"specific",d=b.vtp_attributes||[],e=b.vtp_createPermissionError;return{assert:function(f,
g,h){if(!jb(h))throw e(f,{},"Attribute must be a string.");if(!(g instanceof HTMLElement))throw e(f,{},"Wrong element type. Must be HTMLElement.");if(h==="value"||c!=="any"&&(c!=="specific"||d.indexOf(h)===-1))throw e(f,{},'Reading attribute "'+h+'" is not allowed.');},T:a}})}();
Z.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){Z.__load_google_tags=b;Z.__load_google_tags.F="load_google_tags";Z.__load_google_tags.isVendorTemplate=!0;Z.__load_google_tags.priorityOverride=0;Z.__load_google_tags.isInfrastructure=!1;Z.__load_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||[],h=b.vtp_createPermissionError;
return{assert:function(m,n,p){(function(q){if(!jb(q))throw h(m,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(m,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!jb(q))throw h(m,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(ah(Tk(q),f))return}catch(r){throw h(m,{},"Invalid first party URL filter.");}}throw h(m,{},"Prohibited first party URL: "+q);}})(p)},T:a}})}();




Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!jb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!jb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();

Z.securityGroups.unsafe_run_arbitrary_javascript=["google"],function(){function a(){return{}}(function(b){Z.__unsafe_run_arbitrary_javascript=b;Z.__unsafe_run_arbitrary_javascript.F="unsafe_run_arbitrary_javascript";Z.__unsafe_run_arbitrary_javascript.isVendorTemplate=!0;Z.__unsafe_run_arbitrary_javascript.priorityOverride=0;Z.__unsafe_run_arbitrary_javascript.isInfrastructure=!1;Z.__unsafe_run_arbitrary_javascript["5"]=!1})(function(){return{assert:function(){},T:a}})}();

Z.securityGroups.gas=["google"],Z.__gas=function(a){var b=iQ(a),c=b;c[ff.Ra]=null;c[ff.Ai]=null;var d=b=c;d.vtp_fieldsToSet=d.vtp_fieldsToSet||[];var e=d.vtp_cookieDomain;e!==void 0&&(d.vtp_fieldsToSet.push({fieldName:"cookieDomain",value:e}),delete d.vtp_cookieDomain);return b},Z.__gas.F="gas",Z.__gas.isVendorTemplate=!0,Z.__gas.priorityOverride=0,Z.__gas.isInfrastructure=!1,Z.__gas["5"]=!0;


Z.securityGroups.unsafe_inject_arbitrary_html=["google"],function(){function a(b,c,d){return{useIframe:c,supportDocumentWrite:d}}(function(b){Z.__unsafe_inject_arbitrary_html=b;Z.__unsafe_inject_arbitrary_html.F="unsafe_inject_arbitrary_html";Z.__unsafe_inject_arbitrary_html.isVendorTemplate=!0;Z.__unsafe_inject_arbitrary_html.priorityOverride=0;Z.__unsafe_inject_arbitrary_html.isInfrastructure=!1;Z.__unsafe_inject_arbitrary_html["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,
e,f){if(e&&f)throw c(d,{},"Only one of useIframe and supportDocumentWrite can be true.");if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"useIframe must be a boolean.");if(f!==void 0&&typeof f!=="boolean")throw c(d,{},"supportDocumentWrite must be a boolean.");},T:a}})}();
Z.securityGroups.remm=["google"],Z.__remm=function(a){for(var b=a.vtp_input,c=a.vtp_map||[],d=a.vtp_fullMatch,e=a.vtp_ignoreCase?"gi":"g",f=a.vtp_defaultValue,g=0;g<c.length;g++){var h=c[g].key||"";d&&(h="^"+h+"$");var m=new RegExp(h,e);if(m.test(b)){var n=c[g].value;a.vtp_replaceAfterMatch&&(n=String(b).replace(m,n));f=n;break}}return f},Z.__remm.F="remm",Z.__remm.isVendorTemplate=!0,Z.__remm.priorityOverride=0,Z.__remm.isInfrastructure=!0,Z.__remm["5"]=!0;

Z.securityGroups.detect_click_events=["google"],function(){function a(b,c,d){return{matchCommonButtons:c,cssSelector:d}}(function(b){Z.__detect_click_events=b;Z.__detect_click_events.F="detect_click_events";Z.__detect_click_events.isVendorTemplate=!0;Z.__detect_click_events.priorityOverride=0;Z.__detect_click_events.isInfrastructure=!1;Z.__detect_click_events["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"matchCommonButtons must be a boolean.");
if(f!==void 0&&typeof f!=="string")throw c(d,{},"cssSelector must be a string.");},T:a}})}();
Z.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){Z.__logging=b;Z.__logging.F="logging";Z.__logging.isVendorTemplate=!0;Z.__logging.priorityOverride=0;Z.__logging.isInfrastructure=!1;Z.__logging["5"]=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},T:a}})}();
Z.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){Z.__configure_google_tags=b;Z.__configure_google_tags.F="configure_google_tags";Z.__configure_google_tags.isVendorTemplate=!0;Z.__configure_google_tags.priorityOverride=0;Z.__configure_google_tags.isInfrastructure=!1;Z.__configure_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!jb(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},T:a}})}();


Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.F="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();



var xp={dataLayer:uk,callback:function(a){ik.hasOwnProperty(a)&&ib(ik[a])&&ik[a]();delete ik[a]},bootstrap:0};xp.onHtmlSuccess=EE(!0),xp.onHtmlFailure=EE(!1);
function uQ(){wp();Gm();NB();Bb(jk,Z.securityGroups);var a=Dm(sm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Wo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);AE(),Mf({Rp:function(d){return d===yE},Vo:function(d){return new BE(d)},Sp:function(d){for(var e=!1,f=!1,g=2;g<d.length;g++)e=e||d[g]===8,f=f||d[g]===16;return e&&f},iq:function(d){var e;if(d===yE)e=d;else{var f=yp();zE[f]=d;e='google_tag_manager["rm"]["'+Am()+'"]('+f+")"}return e}});
Pf={Qo:dg}}var vQ=!1;
function go(){try{if(vQ||!Nm()){Rj();Oj.P=Ri(18,"");
Oj.qb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Oj.Sa="ad_storage|analytics_storage|ad_user_data";Oj.Ba="56n0";Oj.Ba="5770";if(D(109)){}Ha[7]=!0;var a=vp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});cp(a);tp();WE();ir();zp();if(Hm()){nG();DC().removeExternalRestrictions(Am());}else{Uy();Nf();Jf=Z;Kf=GE;fg=new mg;dQ();uQ();Ir();eo||(co=io());
qp();SD();eD();yD=!1;z.readyState==="complete"?AD():Jc(x,"load",AD);ZC();hl&&(mq(Aq),x.setInterval(zq,864E5),mq(XE),mq(qC),mq(bA),mq(Dq),mq(bF),mq(BC),D(120)&&(mq(vC),mq(wC),mq(xC)),YE={},mq(ZE),Ui());il&&(Rn(),Tp(),UD(),YD(),WD(),Hn("bt",String(Oj.C?2:Oj.N?1:0)),Hn("ct",String(Oj.C?0:Oj.N?1:Hr()?2:3)),VD());wE();ao(1);oG();bE();hk=yb();xp.bootstrap=hk;Oj.ka&&RD();D(109)&&xA();D(134)&&(typeof x.name==="string"&&Db(x.name,"web-pixel-sandbox-CUSTOM")&&ad()?gQ("dMDg0Yz"):x.Shopify&&(gQ("dN2ZkMj"),ad()&&gQ("dNTU0Yz")))}}}catch(b){ao(4),wq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Jo(n)&&(m=h.Zk)}function c(){m&&vc?g(m):a()}if(!x[Ri(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Tk(z.referrer);d=Pk(e,"host")===Ri(38,"cct.google")}if(!d){var f=js(Ri(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Ri(37,"__TAGGY_INSTALLED")]=!0,Ec(Ri(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";bk&&(v="OGT",w="GTAG");
var y=Ri(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Ec("https://"+Sj.wg+"/debug/bootstrap?id="+jg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Mr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:vc,containerProduct:v,debug:!1,id:jg.ctid,targetRef:{ctid:jg.ctid,isDestination:ym()},aliases:Bm(),destinations:zm()}};C.data.resume=function(){a()};Sj.Sm&&(C.data.initialPublish=!0);A.push(C)},h={co:1,fl:2,yl:3,Yj:4,Zk:5};h[h.co]="GTM_DEBUG_LEGACY_PARAM";h[h.fl]="GTM_DEBUG_PARAM";h[h.yl]="REFERRER";
h[h.Yj]="COOKIE";h[h.Zk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Nk(x.location,"query",!1,void 0,"gtm_debug");Jo(p)&&(m=h.fl);if(!m&&z.referrer){var q=Tk(z.referrer);Pk(q,"host")===Ri(24,"tagassistant.google.com")&&(m=h.yl)}if(!m){var r=js("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Yj)}m||b();if(!m&&Io(n)){var t=!1;Jc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){D(83)&&vQ&&!io()["0"]?fo():go()});

})()

