#!/usr/bin/env python3
"""
Debug script to check what data is being loaded
"""

import json
import os
from pathlib import Path

def debug_data_loading():
    """Debug what's being loaded from NCT02065791"""
    
    study_dir = Path("output/canagliflozin/NCT02065791")
    researcher_view_path = study_dir / "researcher_view.json"
    
    if not researcher_view_path.exists():
        print(f"❌ File not found: {researcher_view_path}")
        return
    
    print(f"📁 Loading researcher_view.json for NCT02065791")
    
    with open(researcher_view_path, 'r') as f:
        researcher_view = json.load(f)
    
    print(f"📋 Top-level keys: {list(researcher_view.keys())}")
    
    # Check if fullStudyData exists
    if 'fullStudyData' in researcher_view:
        full_study_data = researcher_view['fullStudyData']
        print(f"✅ Found fullStudyData, keys: {list(full_study_data.keys())}")
        
        if 'protocolSection' in full_study_data:
            protocol_section = full_study_data['protocolSection']
            print(f"✅ Found protocolSection, keys: {list(protocol_section.keys())}")
            
            if 'identificationModule' in protocol_section:
                identification_module = protocol_section['identificationModule']
                print(f"✅ Found identificationModule, keys: {list(identification_module.keys())}")
                
                brief_title = identification_module.get('briefTitle', 'NOT FOUND')
                official_title = identification_module.get('officialTitle', 'NOT FOUND')
                
                print(f"📋 Brief Title: {brief_title}")
                print(f"📋 Official Title: {official_title}")
                
                # Check for diabetes terms
                diabetes_terms = ['diabetes', 'diabetic']
                for term in diabetes_terms:
                    if term.lower() in brief_title.lower():
                        print(f"✅ Found '{term}' in brief title")
                    if term.lower() in official_title.lower():
                        print(f"✅ Found '{term}' in official title")
            else:
                print(f"❌ No identificationModule found")
        else:
            print(f"❌ No protocolSection found")
    else:
        print(f"❌ No fullStudyData found")

if __name__ == "__main__":
    debug_data_loading()
