#!/usr/bin/env python3
"""
Test script to see what prompt is being sent to the LLM for NCT01106677
"""

import json
import os
from pathlib import Path

# Import the function from ai_analyze_studies
import sys
sys.path.append('.')
from ai_analyze_studies import load_study_data, create_analysis_prompt

def test_llm_prompt():
    """Test what prompt is being generated for NCT01106677"""
    
    study_dir = Path("output/canagliflozin/NCT01106677")
    
    if not study_dir.exists():
        print(f"❌ Study directory not found: {study_dir}")
        return
    
    print(f"📁 Testing LLM prompt generation for NCT01106677")
    print(f"🔍 Looking for 'fibrillation' in the generated prompt")
    
    # Load study data using the same function as the AI analysis
    study_data = load_study_data(study_dir)
    
    # Create the analysis prompt
    search_criteria = "diabetes, arrythmia, fibrillation"
    nct_id = "NCT01106677"
    
    prompt = create_analysis_prompt(study_data, search_criteria, nct_id)
    
    print(f"\n📝 Generated prompt length: {len(prompt)} characters")
    
    # Check if fibrillation appears in the prompt
    if 'fibrillation' in prompt.lower():
        print(f"✅ SUCCESS: 'fibrillation' found in the prompt!")
        
        # Find all occurrences
        lines = prompt.split('\n')
        for i, line in enumerate(lines):
            if 'fibrillation' in line.lower():
                print(f"   Line {i+1}: {line.strip()}")
    else:
        print(f"❌ FAILED: 'fibrillation' NOT found in the prompt")
    
    # Show the adverse events section specifically
    print(f"\n📋 ADVERSE EVENTS SECTION:")
    lines = prompt.split('\n')
    in_adverse_events = False
    adverse_events_lines = []
    
    for line in lines:
        if '=== ADVERSE EVENTS ===' in line:
            in_adverse_events = True
            adverse_events_lines.append(line)
        elif in_adverse_events and line.startswith('==='):
            break
        elif in_adverse_events:
            adverse_events_lines.append(line)
    
    if adverse_events_lines:
        for line in adverse_events_lines:
            print(f"   {line}")
    else:
        print("   No adverse events section found!")
    
    # Save the full prompt to a file for inspection
    with open('debug_prompt_NCT01106677.txt', 'w') as f:
        f.write(prompt)
    
    print(f"\n💾 Full prompt saved to: debug_prompt_NCT01106677.txt")

if __name__ == "__main__":
    test_llm_prompt()
