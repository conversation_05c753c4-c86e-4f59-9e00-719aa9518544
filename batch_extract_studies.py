#!/usr/bin/env python3
"""
Batch extract clinical trial data for multiple studies matching specific criteria
"""

import requests
import json
import os
import subprocess
import sys
from pathlib import Path
import time

def search_studies_api(intervention="Canagliflozin", max_studies=50):
    """
    Search for studies using ClinicalTrials.gov API v2
    """
    print(f"🔍 Searching for studies with intervention: {intervention}")
    
    # API URL for searching studies
    api_url = f"https://clinicaltrials.gov/api/v2/studies?query.term={intervention}&pageSize={max_studies}&format=json"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    try:
        response = requests.get(api_url, headers=headers, timeout=60)
        print(f"API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            studies = data.get('studies', [])
            print(f"✅ Found {len(studies)} total studies mentioning {intervention}")
            return studies
        else:
            print(f"❌ API error: {response.status_code}")
            print("Response:", response.text[:500])
            return []
            
    except Exception as e:
        print(f"❌ Error searching studies: {e}")
        return []

def filter_studies(studies):
    """
    Filter studies based on criteria:
    - Status: COMPLETED
    - Has results data
    """
    print("\n🔍 Filtering studies...")
    
    filtered_studies = []
    
    for study in studies:
        try:
            protocol_section = study.get('protocolSection', {})
            results_section = study.get('resultsSection', {})
            
            # Get study info
            nct_id = protocol_section.get('identificationModule', {}).get('nctId', '')
            status = protocol_section.get('statusModule', {}).get('overallStatus', '')
            brief_title = protocol_section.get('identificationModule', {}).get('briefTitle', '')
            
            # Check if study meets criteria
            is_completed = status == 'COMPLETED'
            has_results = bool(results_section)  # Has results section
            
            if is_completed and has_results:
                filtered_studies.append({
                    'nct_id': nct_id,
                    'title': brief_title,
                    'status': status,
                    'has_results': has_results
                })
                print(f"✅ {nct_id}: {brief_title[:60]}...")
            else:
                print(f"❌ {nct_id}: Status={status}, HasResults={has_results}")
                
        except Exception as e:
            print(f"❌ Error processing study: {e}")
            continue
    
    print(f"\n📊 Filtering Results:")
    print(f"Total studies found: {len(studies)}")
    print(f"Completed with results: {len(filtered_studies)}")
    
    return filtered_studies

def create_study_folder(nct_id, base_dir="studies"):
    """
    Create a folder for the study
    """
    study_dir = Path(base_dir) / nct_id
    study_dir.mkdir(parents=True, exist_ok=True)
    return study_dir

def extract_study_data(nct_id, study_dir):
    """
    Extract all data for a single study using our existing scripts
    """
    print(f"\n📁 Extracting data for {nct_id}...")
    
    # Change to study directory
    original_dir = os.getcwd()
    os.chdir(study_dir)
    
    try:
        # Copy extraction scripts to study directory
        script_files = [
            'extract_study_details.py',
            'extract_researcher_view.py', 
            'extract_results_posted.py',
            'extract2csv.py'
        ]
        
        for script in script_files:
            script_path = Path(original_dir) / script
            if script_path.exists():
                # Read the script and update NCT ID
                with open(script_path, 'r') as f:
                    content = f.read()
                
                # Replace NCT ID in the script
                updated_content = content.replace('NCT02324842', nct_id)
                
                # Write updated script to study directory
                with open(script, 'w') as f:
                    f.write(updated_content)
        
        # Run extraction scripts
        extraction_results = {}
        
        # 1. Extract Study Details
        print(f"  📋 Extracting Study Details...")
        result = subprocess.run([sys.executable, 'extract_study_details.py'], 
                              capture_output=True, text=True, timeout=60)
        extraction_results['study_details'] = result.returncode == 0
        
        # 2. Extract Researcher View
        print(f"  🔬 Extracting Researcher View...")
        result = subprocess.run([sys.executable, 'extract_researcher_view.py'], 
                              capture_output=True, text=True, timeout=60)
        extraction_results['researcher_view'] = result.returncode == 0
        
        # 3. Extract Results Posted
        print(f"  📊 Extracting Results Posted...")
        result = subprocess.run([sys.executable, 'extract_results_posted.py'], 
                              capture_output=True, text=True, timeout=60)
        extraction_results['results_posted'] = result.returncode == 0
        
        # 4. Extract Adverse Events CSV
        if extraction_results['results_posted']:
            print(f"  ⚠️  Extracting Adverse Events...")
            result = subprocess.run([sys.executable, 'extract2csv.py'], 
                                  capture_output=True, text=True, timeout=60)
            extraction_results['adverse_events'] = result.returncode == 0
        else:
            extraction_results['adverse_events'] = False
        
        # Clean up script files
        for script in script_files:
            if os.path.exists(script):
                os.remove(script)
        
        # Check what files were created
        expected_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json', 'adverse_events.csv']
        created_files = [f for f in expected_files if os.path.exists(f)]
        
        print(f"  ✅ Created {len(created_files)}/{len(expected_files)} files: {created_files}")
        
        return extraction_results
        
    except Exception as e:
        print(f"  ❌ Error extracting {nct_id}: {e}")
        return {'error': str(e)}
    finally:
        os.chdir(original_dir)

def batch_extract_all_studies(filtered_studies, base_dir="studies"):
    """
    Extract data for all filtered studies
    """
    print(f"\n🚀 Starting batch extraction for {len(filtered_studies)} studies...")
    
    # Create base directory
    Path(base_dir).mkdir(exist_ok=True)
    
    results = []
    
    for i, study in enumerate(filtered_studies, 1):
        nct_id = study['nct_id']
        print(f"\n{'='*60}")
        print(f"Processing {i}/{len(filtered_studies)}: {nct_id}")
        print(f"Title: {study['title']}")
        print(f"{'='*60}")
        
        # Create study folder
        study_dir = create_study_folder(nct_id, base_dir)
        
        # Extract study data
        extraction_result = extract_study_data(nct_id, study_dir)
        
        results.append({
            'nct_id': nct_id,
            'title': study['title'],
            'extraction_result': extraction_result
        })
        
        # Add delay to be respectful to the API
        time.sleep(2)
    
    return results

def main():
    print("🏥 CLINICAL TRIALS BATCH EXTRACTION")
    print("=" * 60)
    print("Criteria:")
    print("- Intervention: Canagliflozin")
    print("- Status: Completed")
    print("- Results: With results")
    print("=" * 60)
    
    # 1. Search for studies
    studies = search_studies_api("Canagliflozin")
    
    if not studies:
        print("❌ No studies found. Exiting.")
        return
    
    # 2. Filter studies
    filtered_studies = filter_studies(studies)
    
    if not filtered_studies:
        print("❌ No studies match the criteria. Exiting.")
        return
    
    print(f"\n📋 Found {len(filtered_studies)} studies matching criteria:")
    for study in filtered_studies[:10]:  # Show first 10
        print(f"  - {study['nct_id']}: {study['title'][:50]}...")
    
    if len(filtered_studies) > 10:
        print(f"  ... and {len(filtered_studies) - 10} more")
    
    # Ask for confirmation
    response = input(f"\nProceed with batch extraction of {len(filtered_studies)} studies? (y/N): ")
    if response.lower() != 'y':
        print("❌ Extraction cancelled.")
        return
    
    # 3. Batch extract all studies
    results = batch_extract_all_studies(filtered_studies)
    
    # 4. Summary
    print(f"\n{'='*60}")
    print("BATCH EXTRACTION SUMMARY")
    print(f"{'='*60}")
    
    successful = sum(1 for r in results if not r['extraction_result'].get('error'))
    print(f"Total studies processed: {len(results)}")
    print(f"Successful extractions: {successful}")
    print(f"Failed extractions: {len(results) - successful}")
    
    # Save summary
    with open('batch_extraction_summary.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\nSaved detailed summary to: batch_extraction_summary.json")
    
    print(f"\n🎉 Batch extraction completed!")
    print(f"Check the 'studies' directory for individual study folders.")

if __name__ == "__main__":
    main()
