<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Trials Data Extractor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .terminal {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            border: 1px solid #333;
        }

        .terminal-response {
            color: #00ff00;
        }
        .terminal-warning {
            color: #ffff00;
        }
        .terminal-error {
            color: #ff0000;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
        .terminal-container {
            width: 100%;
            max-width: none;
        }
        .terminal-line {
            margin: 1px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            display: block;
            width: 100%;
            hyphens: auto;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Clinical Trials Data Extractor</h1>
            <p class="text-gray-600">Extract and analyze clinical trial data from ClinicalTrials.gov</p>
        </div>

        <!-- Step 1: Data Extraction -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Step 1: Data Extraction</h2>
            <div class="grid grid-cols-1 gap-4 mb-4">
                <div>
                    <label for="drug" class="block text-sm font-medium text-gray-700 mb-2">Drug/Intervention Name</label>
                    <input type="text" id="drug" placeholder="e.g., Canagliflozin"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="text-xs text-gray-500 mt-1">Keywords for extraction - finds completed studies with results</div>
                </div>
                <div class="flex gap-4">
                    <button id="extract-btn"
                            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition duration-200 text-lg">
                        📁 Extract Study Data
                    </button>
                    <button id="stop-extract-btn"
                            class="hidden bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-md transition duration-200 text-lg">
                        🛑 Stop
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: AI Analysis -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Step 2: AI Analysis</h2>
            <div class="grid grid-cols-1 gap-4 mb-4">
                <div>
                    <label for="drug-folder" class="block text-sm font-medium text-gray-700 mb-2">Select Drug to Analyze</label>
                    <select id="drug-folder" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Loading available drugs...</option>
                    </select>
                    <div id="drug-folder-status" class="text-xs text-gray-500 mt-1">Checking extracted data...</div>
                </div>
                <div>
                    <label for="search-criteria" class="block text-sm font-medium text-gray-700 mb-2">Search Criteria</label>
                    <input type="text" id="search-criteria" placeholder="e.g., diabetes ventricular arrhythmia"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="text-xs text-gray-500 mt-1">Keywords for AI analysis - filters extracted studies</div>
                </div>
                <div>
                    <label for="llm" class="block text-sm font-medium text-gray-700 mb-2">Select AI Model</label>
                    <select id="llm" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Loading models...</option>
                    </select>
                    <div id="llm-status" class="text-xs text-gray-500 mt-1">Checking Ollama models...</div>
                </div>
                <div class="flex gap-4">
                    <button id="analyze-btn"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-md transition duration-200 text-lg disabled:bg-gray-400 disabled:cursor-not-allowed"
                            disabled>
                        🤖 Analyze Studies
                    </button>
                    <button id="stop-analyze-btn"
                            class="hidden bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-md transition duration-200 text-lg">
                        🛑 Stop
                    </button>
                </div>
                <div class="text-xs text-gray-500">
                    <span id="studies-status">Select a drug to enable analysis</span>
                </div>
            </div>
        </div>

        <!-- Extraction Progress -->
        <div id="extraction-progress" class="hidden bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">Data Extraction Progress</h3>
                <span id="extract-percent" class="text-sm text-gray-600">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div id="extract-bar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
            <p id="extract-text" class="text-sm text-gray-600">Initializing...</p>
        </div>

        <!-- Analysis Progress -->
        <div id="analysis-progress" class="hidden bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-medium text-gray-800">AI Analysis Progress</h3>
                <span id="analyze-percent" class="text-sm text-gray-600">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div id="analyze-bar" class="progress-bar bg-green-600 h-2 rounded-full" style="width: 0%"></div>
            </div>
            <p id="analyze-text" class="text-sm text-gray-600">Waiting...</p>
        </div>

        <!-- Terminal Output -->
        <div class="bg-white rounded-lg shadow-md p-6 terminal-container">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Terminal Output</h3>
                <div class="space-x-2">
                    <button id="copy-btn" class="bg-gray-500 hover:bg-gray-600 text-white text-sm px-3 py-1 rounded">
                        Copy
                    </button>
                    <button id="clear-btn" class="bg-red-500 hover:bg-red-600 text-white text-sm px-3 py-1 rounded">
                        Clear
                    </button>
                </div>
            </div>
            <div id="terminal" class="terminal p-4 rounded-md h-[500px] overflow-y-auto overflow-x-auto w-full max-w-full">
                <div class="terminal-line">Clinical Trials Data Extractor v2.0</div>
                <div class="terminal-line">Ready to extract clinical trial data...</div>
                <div class="terminal-line">Terminal display improved - text should now wrap properly and show full content without cutoff</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');

            const extractBtn = document.getElementById('extract-btn');
            const stopExtractBtn = document.getElementById('stop-extract-btn');
            const analyzeBtn = document.getElementById('analyze-btn');
            const stopAnalyzeBtn = document.getElementById('stop-analyze-btn');
            const extractionProgress = document.getElementById('extraction-progress');
            const analysisProgress = document.getElementById('analysis-progress');
            const terminal = document.getElementById('terminal');
            const copyBtn = document.getElementById('copy-btn');
            const clearBtn = document.getElementById('clear-btn');
            const llmSelect = document.getElementById('llm');
            const llmStatus = document.getElementById('llm-status');
            const drugFolderSelect = document.getElementById('drug-folder');
            const drugFolderStatus = document.getElementById('drug-folder-status');
            const studiesStatus = document.getElementById('studies-status');

            // Load models and drug folders when page loads
            loadModels();
            loadDrugFolders();

            // Auto-load studies when drug is selected
            drugFolderSelect.addEventListener('change', function() {
                const drugFolder = this.value;

                if (!drugFolder) {
                    analyzeBtn.disabled = true;
                    analyzeBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    studiesStatus.textContent = 'Select a drug to enable analysis';
                    studiesStatus.className = 'text-xs text-gray-500';
                    return;
                }

                // Auto-load studies for selected drug
                fetch('/api/load-studies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ drug_folder: drugFolder })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        analyzeBtn.disabled = false;
                        analyzeBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                        studiesStatus.textContent = `✅ ${data.study_count} studies loaded from ${drugFolder} - Ready for analysis`;
                        studiesStatus.className = 'text-xs text-green-600';
                    } else {
                        analyzeBtn.disabled = true;
                        analyzeBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                        studiesStatus.textContent = `❌ ${data.message}`;
                        studiesStatus.className = 'text-xs text-red-600';
                    }
                })
                .catch(error => {
                    analyzeBtn.disabled = true;
                    analyzeBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    studiesStatus.textContent = `❌ Error loading studies: ${error}`;
                    studiesStatus.className = 'text-xs text-red-600';
                });
            });

            // Stop processes when page is closed
            window.addEventListener('beforeunload', function(e) {
                const extractionRunning = !stopExtractBtn.classList.contains('hidden');
                const analysisRunning = !stopAnalyzeBtn.classList.contains('hidden');

                if (extractionRunning || analysisRunning) {
                    // Try to stop the process
                    fetch('/api/stop', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    // Show confirmation dialog
                    e.preventDefault();
                    const operation = extractionRunning ? 'Extraction' : 'Analysis';
                    e.returnValue = `${operation} is running. Are you sure you want to leave?`;
                    return e.returnValue;
                }
            });

            function loadModels() {
                console.log('Loading models...');
                llmStatus.textContent = 'Loading models...';

                // First, show fallback models immediately
                llmSelect.innerHTML = `
                    <option value="">Select AI Model</option>
                    <option value="deepseek-r1:32b">deepseek-r1:32b (18.5 GB)</option>
                    <option value="llama3.1:latest">llama3.1:latest (4.6 GB)</option>
                    <option value="qwen2.5:latest">qwen2.5:latest (4.4 GB)</option>
                    <option value="gemma3:27b">gemma3:27b (16.2 GB)</option>
                `;
                llmStatus.textContent = '✅ Models loaded';
                llmStatus.className = 'text-xs text-green-600 mt-1';

                // Try to get models from API
                fetch('/api/models')
                    .then(response => {
                        console.log('API response:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('API data:', data);
                        if (data.success && data.models) {
                            llmSelect.innerHTML = '<option value="">Select AI Model</option>';

                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.display;
                                llmSelect.appendChild(option);
                            });

                            llmStatus.textContent = `✅ Found ${data.models.length} Ollama models`;
                            llmStatus.className = 'text-xs text-green-600 mt-1';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading models:', error);
                        llmStatus.textContent = '⚠️ Using fallback models';
                        llmStatus.className = 'text-xs text-yellow-600 mt-1';
                    });
            }

            function loadDrugFolders() {
                console.log('Loading drug folders...');
                drugFolderStatus.textContent = 'Loading available drugs...';

                fetch('/api/drug-folders')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Drug folders loaded:', data);
                        if (data.folders && data.folders.length > 0) {
                            drugFolderSelect.innerHTML = '<option value="">Select Drug to Analyze</option>';
                            data.folders.forEach(folder => {
                                const option = document.createElement('option');
                                option.value = folder.name;
                                option.textContent = `${folder.name} (${folder.study_count} studies)`;
                                drugFolderSelect.appendChild(option);
                            });
                            drugFolderStatus.textContent = `Found ${data.folders.length} drug datasets`;
                        } else {
                            drugFolderSelect.innerHTML = '<option value="">No extracted data found</option>';
                            drugFolderStatus.textContent = 'No extracted data found - run extraction first';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading drug folders:', error);
                        drugFolderStatus.textContent = 'Error loading drug folders';
                    });
            }

            // Extract button handler
            extractBtn.addEventListener('click', function() {
                const drug = document.getElementById('drug').value.trim();

                if (!drug) {
                    alert('Please enter a drug name');
                    return;
                }

                extractionProgress.classList.remove('hidden');
                extractBtn.classList.add('hidden');
                stopExtractBtn.classList.remove('hidden');
                analyzeBtn.disabled = true;

                terminal.innerHTML = `
                    <div class="terminal-line">📁 Clinical Trials Data Extraction</div>
                    <div class="terminal-line">Drug: ${drug}</div>
                    <div class="terminal-line">Starting extraction...</div>
                `;

                // Send extraction request to backend
                fetch('/api/extract', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ drug: drug })
                })
                .then(response => response.json())
                .then(data => {
                    addTerminalLine(`✅ ${data.message}`, 'terminal-response');
                    monitorExtractionProgress();
                })
                .catch(error => {
                    addTerminalLine(`❌ Error: ${error}`, 'terminal-error');
                    resetExtractionButtons();
                });
            });

            // Analyze button handler
            analyzeBtn.addEventListener('click', function() {
                const searchCriteria = document.getElementById('search-criteria').value.trim();
                const llm = document.getElementById('llm').value;

                if (!searchCriteria) {
                    alert('Please enter search criteria');
                    return;
                }

                if (!llm) {
                    alert('Please select an AI model');
                    return;
                }

                analysisProgress.classList.remove('hidden');
                analyzeBtn.classList.add('hidden');
                stopAnalyzeBtn.classList.remove('hidden');

                addTerminalLine('==================================================', 'terminal-response');
                addTerminalLine(`🤖 AI Analysis Starting`, 'terminal-warning');
                addTerminalLine(`Search Criteria: ${searchCriteria}`, 'terminal-response');
                addTerminalLine(`AI Model: ${llm}`, 'terminal-response');

                // Send analysis request to backend
                fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        search_criteria: searchCriteria,
                        llm: llm
                    })
                })
                .then(response => response.json())
                .then(data => {
                    addTerminalLine(`✅ ${data.message}`, 'terminal-response');
                    monitorAnalysisProgress();
                })
                .catch(error => {
                    addTerminalLine(`❌ Error: ${error}`, 'terminal-error');
                    resetAnalysisButtons();
                });
            });

            // Stop extraction button handler
            stopExtractBtn.addEventListener('click', function() {
                fetch('/api/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    addTerminalLine(`🛑 ${data.message}`, 'terminal-warning');
                    resetExtractionButtons();
                })
                .catch(error => {
                    addTerminalLine(`❌ Stop failed: ${error}`, 'terminal-error');
                });
            });

            // Stop analysis button handler
            stopAnalyzeBtn.addEventListener('click', function() {
                fetch('/api/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    addTerminalLine(`🛑 ${data.message}`, 'terminal-warning');
                    resetAnalysisButtons();
                })
                .catch(error => {
                    addTerminalLine(`❌ Stop failed: ${error}`, 'terminal-error');
                });
            });

            function monitorExtractionProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            updateExtractionProgress(data);
                            updateTerminalOutput(data);

                            if (data.status === 'running' && data.operation === 'extract') {
                                setTimeout(checkProgress, 1000);
                            } else if (data.status === 'completed' && data.operation === 'extract') {
                                resetExtractionButtons();
                                enableAnalysis(data);
                            } else if (data.status === 'error' || data.status === 'stopped') {
                                addTerminalLine(`❌ ${data.message}`, 'terminal-error');
                                resetExtractionButtons();
                            }
                        })
                        .catch(error => {
                            addTerminalLine(`❌ Progress check failed: ${error}`, 'terminal-error');
                        });
                };
                checkProgress();
            }

            function monitorAnalysisProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            updateAnalysisProgress(data);
                            updateTerminalOutput(data);

                            if (data.status === 'running' && data.operation === 'analyze') {
                                setTimeout(checkProgress, 1000);
                            } else if (data.status === 'completed' && data.operation === 'analyze') {
                                showFinalResults(data);
                                resetAnalysisButtons();
                            } else if (data.status === 'error' || data.status === 'stopped') {
                                addTerminalLine(`❌ ${data.message}`, 'terminal-error');
                                resetAnalysisButtons();
                            }
                        })
                        .catch(error => {
                            addTerminalLine(`❌ Progress check failed: ${error}`, 'terminal-error');
                        });
                };
                checkProgress();
            }

            function updateExtractionProgress(data) {
                if (data.operation === 'extract') {
                    document.getElementById('extract-bar').style.width = `${data.progress}%`;
                    document.getElementById('extract-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('extract-text').textContent = data.message;
                }
            }

            function updateAnalysisProgress(data) {
                if (data.operation === 'analyze') {
                    document.getElementById('analyze-bar').style.width = `${data.progress}%`;
                    document.getElementById('analyze-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('analyze-text').textContent = data.message;
                }
            }

            function enableAnalysis(data) {
                // Load drug folders to refresh the dropdown
                loadDrugFolders();

                const studyCount = data.study_count || 'Unknown';
                const drugName = data.drug_name || 'Unknown';
                studiesStatus.textContent = `✅ ${studyCount} studies extracted for ${drugName} - Select from dropdown to analyze`;
                studiesStatus.className = 'text-xs text-green-600';
            }

            let lastOutputLength = 0;
            function updateTerminalOutput(data) {
                if (data.terminal_output && data.terminal_output.length > lastOutputLength) {
                    // Add new terminal lines
                    for (let i = lastOutputLength; i < data.terminal_output.length; i++) {
                        const line = data.terminal_output[i];
                        let className = 'terminal-response';
                        if (line.includes('❌') || line.includes('Error')) {
                            className = 'terminal-error';
                        } else if (line.includes('🎉') || line.includes('===')) {
                            className = 'terminal-warning';
                        }
                        addTerminalLine(line, className);
                    }
                    lastOutputLength = data.terminal_output.length;
                }
            }

            function showFinalResults(data) {
                if (data.results && data.results.length > 0) {
                    addTerminalLine('📊 MATCHING STUDIES:', 'terminal-warning');
                    data.results.forEach((study, index) => {
                        const confidence = study.analysis?.confidence || 0;
                        const title = study.study_title || 'Unknown title';
                        addTerminalLine(`${index + 1}. ${study.nct_id} (confidence: ${confidence.toFixed(2)})`, 'terminal-response');
                        addTerminalLine(`   ${title}`, 'terminal-response');
                    });
                } else {
                    addTerminalLine('No studies matched the search criteria.', 'terminal-warning');
                }
            }

            function resetExtractionButtons() {
                extractBtn.classList.remove('hidden');
                stopExtractBtn.classList.add('hidden');
            }

            function resetAnalysisButtons() {
                analyzeBtn.classList.remove('hidden');
                stopAnalyzeBtn.classList.add('hidden');
            }



            function addTerminalLine(text, className) {
                const line = document.createElement('div');
                line.className = `terminal-line ${className} fade-in`;
                line.textContent = text;
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
            }

            // Copy terminal content
            copyBtn.addEventListener('click', function() {
                const range = document.createRange();
                range.selectNode(terminal);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                document.execCommand('copy');
                window.getSelection().removeAllRanges();

                // Show copied notification
                const notification = document.createElement('div');
                notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg';
                notification.textContent = 'Copied to clipboard!';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 2000);
            });

            // Clear terminal
            clearBtn.addEventListener('click', function() {
                terminal.innerHTML = `
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Terminal cleared. Ready for new search...</div>
                `;
            });
        });
    </script>
</body>
</html>
