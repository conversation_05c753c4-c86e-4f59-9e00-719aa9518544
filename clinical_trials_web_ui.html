<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Trials Data Extractor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .terminal {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .terminal-line {
            margin: 2px 0;
        }
        .terminal-response {
            color: #00ff00;
        }
        .terminal-warning {
            color: #ffff00;
        }
        .terminal-error {
            color: #ff0000;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Clinical Trials Data Extractor</h1>
            <p class="text-gray-600">Extract and analyze clinical trial data from ClinicalTrials.gov</p>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 gap-4 mb-4">
                <div>
                    <label for="drug" class="block text-sm font-medium text-gray-700 mb-2">1. Drug/Intervention Name</label>
                    <input type="text" id="drug" placeholder="e.g., Canagliflozin"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="search-criteria" class="block text-sm font-medium text-gray-700 mb-2">2. Search Criteria</label>
                    <input type="text" id="search-criteria" placeholder="e.g., diabetes cardiovascular outcomes safety"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="text-xs text-gray-500 mt-1">Keywords for AI analysis (Step 2)</div>
                </div>
                <div>
                    <label for="llm" class="block text-sm font-medium text-gray-700 mb-2">3. Select AI Model</label>
                    <select id="llm" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Loading models...</option>
                    </select>
                    <div id="llm-status" class="text-xs text-gray-500 mt-1">Checking Ollama models...</div>
                </div>
                <div>
                    <button id="search-btn"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition duration-200 text-lg">
                        🚀 Start Extraction & Analysis
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div id="progress-section" class="hidden bg-white rounded-lg shadow-md p-6 mb-8">
            <!-- Step 1: Retrieving Studies -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="text-lg font-medium text-gray-800">Step 1: Retrieving Studies</h3>
                    <span id="step1-percent" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div id="step1-bar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <p id="step1-text" class="text-sm text-gray-600">Initializing...</p>
            </div>

            <!-- Step 2: AI Analysis -->
            <div id="step2-progress" class="hidden">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="text-lg font-medium text-gray-800">Step 2: AI Analysis</h3>
                    <span id="step2-percent" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div id="step2-bar" class="progress-bar bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <p id="step2-text" class="text-sm text-gray-600">Waiting...</p>
            </div>
        </div>

        <!-- Terminal Output -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Terminal Output</h3>
                <div class="space-x-2">
                    <button id="copy-btn" class="bg-gray-500 hover:bg-gray-600 text-white text-sm px-3 py-1 rounded">
                        Copy
                    </button>
                    <button id="clear-btn" class="bg-red-500 hover:bg-red-600 text-white text-sm px-3 py-1 rounded">
                        Clear
                    </button>
                </div>
            </div>
            <div id="terminal" class="terminal p-4 rounded-md h-96 overflow-y-auto">
                <div class="terminal-line">Clinical Trials Data Extractor v1.0</div>
                <div class="terminal-line">Ready to extract clinical trial data...</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');

            const searchBtn = document.getElementById('search-btn');
            const progressSection = document.getElementById('progress-section');
            const terminal = document.getElementById('terminal');
            const copyBtn = document.getElementById('copy-btn');
            const clearBtn = document.getElementById('clear-btn');
            const llmSelect = document.getElementById('llm');
            const llmStatus = document.getElementById('llm-status');

            // Load models when page loads
            loadModels();

            function loadModels() {
                console.log('Loading models...');
                llmStatus.textContent = 'Loading models...';

                // First, show fallback models immediately
                llmSelect.innerHTML = `
                    <option value="">Select AI Model</option>
                    <option value="deepseek-r1:32b">deepseek-r1:32b (18.5 GB)</option>
                    <option value="llama3.1:latest">llama3.1:latest (4.6 GB)</option>
                    <option value="qwen2.5:latest">qwen2.5:latest (4.4 GB)</option>
                    <option value="gemma3:27b">gemma3:27b (16.2 GB)</option>
                `;
                llmStatus.textContent = '✅ Models loaded';
                llmStatus.className = 'text-xs text-green-600 mt-1';

                // Try to get models from API
                fetch('/api/models')
                    .then(response => {
                        console.log('API response:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('API data:', data);
                        if (data.success && data.models) {
                            llmSelect.innerHTML = '<option value="">Select AI Model</option>';

                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.display;
                                llmSelect.appendChild(option);
                            });

                            llmStatus.textContent = `✅ Found ${data.models.length} Ollama models`;
                            llmStatus.className = 'text-xs text-green-600 mt-1';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading models:', error);
                        llmStatus.textContent = '⚠️ Using fallback models';
                        llmStatus.className = 'text-xs text-yellow-600 mt-1';
                    });
            }

            // Search button handler
            searchBtn.addEventListener('click', function() {
                const drug = document.getElementById('drug').value.trim();
                const searchCriteria = document.getElementById('search-criteria').value.trim();
                const llm = document.getElementById('llm').value;

                if (!drug) {
                    alert('Please enter a drug name');
                    return;
                }

                if (!searchCriteria) {
                    alert('Please enter search criteria');
                    return;
                }

                if (!llm) {
                    alert('Please select an AI model');
                    return;
                }

                progressSection.classList.remove('hidden');

                terminal.innerHTML = `
                    <div class="terminal-line">🏥 Clinical Trials Extraction & Analysis</div>
                    <div class="terminal-line">Drug: ${drug}</div>
                    <div class="terminal-line">Search Criteria: ${searchCriteria}</div>
                    <div class="terminal-line">AI Model: ${llm}</div>
                    <div class="terminal-line">Starting extraction...</div>
                `;

                // Send to backend
                fetch('/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        drug: drug,
                        search_criteria: searchCriteria,
                        llm: llm
                    })
                })
                .then(response => response.json())
                .then(data => {
                    addTerminalLine(`✅ ${data.message}`, 'terminal-response');
                    monitorRealProgress();
                })
                .catch(error => {
                    addTerminalLine(`❌ Error: ${error}`, 'terminal-error');
                });
            });

            function monitorRealProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            updateProgressBars(data);
                            updateTerminalOutput(data);

                            if (data.status === 'running') {
                                setTimeout(checkProgress, 1000); // Check every second
                            } else if (data.status === 'completed') {
                                showFinalResults(data);
                            } else if (data.status === 'error') {
                                addTerminalLine(`❌ ${data.message}`, 'terminal-error');
                            }
                        })
                        .catch(error => {
                            addTerminalLine(`❌ Progress check failed: ${error}`, 'terminal-error');
                        });
                };
                checkProgress();
            }

            function updateProgressBars(data) {
                if (data.step === 1) {
                    document.getElementById('step1-bar').style.width = `${data.progress}%`;
                    document.getElementById('step1-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('step1-text').textContent = data.message;

                    if (data.progress === 100) {
                        document.getElementById('step2-progress').classList.remove('hidden');
                    }
                } else if (data.step === 2) {
                    document.getElementById('step2-bar').style.width = `${data.progress}%`;
                    document.getElementById('step2-percent').textContent = `${Math.floor(data.progress)}%`;
                    document.getElementById('step2-text').textContent = data.message;
                }
            }

            let lastOutputLength = 0;
            function updateTerminalOutput(data) {
                if (data.terminal_output && data.terminal_output.length > lastOutputLength) {
                    // Add new terminal lines
                    for (let i = lastOutputLength; i < data.terminal_output.length; i++) {
                        const line = data.terminal_output[i];
                        let className = 'terminal-response';
                        if (line.includes('❌') || line.includes('Error')) {
                            className = 'terminal-error';
                        } else if (line.includes('🎉') || line.includes('===')) {
                            className = 'terminal-warning';
                        }
                        addTerminalLine(line, className);
                    }
                    lastOutputLength = data.terminal_output.length;
                }
            }

            function showFinalResults(data) {
                if (data.results && data.results.length > 0) {
                    addTerminalLine('📊 MATCHING STUDIES:', 'terminal-warning');
                    data.results.forEach((study, index) => {
                        const confidence = study.analysis?.confidence || 0;
                        const title = study.study_title || 'Unknown title';
                        addTerminalLine(`${index + 1}. ${study.nct_id} (confidence: ${confidence.toFixed(2)})`, 'terminal-response');
                        addTerminalLine(`   ${title.substring(0, 70)}...`, 'terminal-response');
                    });
                } else {
                    addTerminalLine('No studies matched the search criteria.', 'terminal-warning');
                }
            }



            function addTerminalLine(text, className) {
                const line = document.createElement('div');
                line.className = `terminal-line ${className} fade-in`;
                line.textContent = text;
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
            }

            // Copy terminal content
            copyBtn.addEventListener('click', function() {
                const range = document.createRange();
                range.selectNode(terminal);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                document.execCommand('copy');
                window.getSelection().removeAllRanges();

                // Show copied notification
                const notification = document.createElement('div');
                notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg';
                notification.textContent = 'Copied to clipboard!';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 2000);
            });

            // Clear terminal
            clearBtn.addEventListener('click', function() {
                terminal.innerHTML = `
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Terminal cleared. Ready for new search...</div>
                `;
            });
        });
    </script>
</body>
</html>
