<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Trials Data Extractor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .terminal {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .terminal-line {
            margin: 2px 0;
        }
        .terminal-response {
            color: #00ff00;
        }
        .terminal-warning {
            color: #ffff00;
        }
        .terminal-error {
            color: #ff0000;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Clinical Trials Data Extractor</h1>
            <p class="text-gray-600">Extract and analyze clinical trial data from ClinicalTrials.gov</p>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="drug" class="block text-sm font-medium text-gray-700 mb-2">Drug/Intervention Name</label>
                    <input type="text" id="drug" placeholder="e.g., Canagliflozin"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="search-criteria" class="block text-sm font-medium text-gray-700 mb-2">Search Criteria</label>
                    <input type="text" id="search-criteria" placeholder="e.g., diabetes cardiovascular outcomes safety"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="text-xs text-gray-500 mt-1">Keywords for AI analysis (Step 2)</div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="llm" class="block text-sm font-medium text-gray-700 mb-2">AI Model</label>
                    <select id="llm" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Loading models...</option>
                    </select>
                    <div id="llm-status" class="text-xs text-gray-500 mt-1">Checking Ollama models...</div>
                </div>
                <div class="flex items-end">
                    <button id="search-btn"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200">
                        Start Extraction & Analysis
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div id="progress-section" class="hidden bg-white rounded-lg shadow-md p-6 mb-8">
            <!-- Step 1: Retrieving Studies -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="text-lg font-medium text-gray-800">Step 1: Retrieving Studies</h3>
                    <span id="step1-percent" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div id="step1-bar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <p id="step1-text" class="text-sm text-gray-600">Initializing...</p>
            </div>

            <!-- Step 2: AI Analysis -->
            <div id="step2-progress" class="hidden">
                <div class="flex justify-between items-center mb-2">
                    <h3 class="text-lg font-medium text-gray-800">Step 2: AI Analysis</h3>
                    <span id="step2-percent" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div id="step2-bar" class="progress-bar bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <p id="step2-text" class="text-sm text-gray-600">Waiting...</p>
            </div>
        </div>

        <!-- Terminal Output -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Terminal Output</h3>
                <div class="space-x-2">
                    <button id="copy-btn" class="bg-gray-500 hover:bg-gray-600 text-white text-sm px-3 py-1 rounded">
                        Copy
                    </button>
                    <button id="clear-btn" class="bg-red-500 hover:bg-red-600 text-white text-sm px-3 py-1 rounded">
                        Clear
                    </button>
                </div>
            </div>
            <div id="terminal" class="terminal p-4 rounded-md h-96 overflow-y-auto">
                <div class="terminal-line">Clinical Trials Data Extractor v1.0</div>
                <div class="terminal-line">Ready to extract clinical trial data...</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchBtn = document.getElementById('search-btn');
            const progressSection = document.getElementById('progress-section');
            const terminal = document.getElementById('terminal');
            const copyBtn = document.getElementById('copy-btn');
            const clearBtn = document.getElementById('clear-btn');

            // Load models immediately when page loads
            loadOllamaModels();

            // Simple function to load Ollama models
            function loadOllamaModels() {
                const llmSelect = document.getElementById('llm');
                const llmStatus = document.getElementById('llm-status');

                // Immediately populate with your known models
                llmSelect.innerHTML = `
                    <option value="">Select AI Model</option>
                    <option value="deepseek-r1:32b">deepseek-r1:32b (18.5 GB)</option>
                    <option value="llama3.1:latest">llama3.1:latest (4.6 GB)</option>
                    <option value="huihui_ai/gemma3-abliterated:27b">huihui_ai/gemma3-abliterated:27b (51.1 GB)</option>
                    <option value="hf.co/mradermacher/Harbinger-24B-GGUF:Q8_0">Harbinger-24B-GGUF:Q8_0 (23.3 GB)</option>
                    <option value="hf.co/mradermacher/Qwen3-30B-A3B-abliterated-erotic-GGUF:Q8_0">Qwen3-30B-A3B-abliterated-erotic-GGUF:Q8_0 (30.3 GB)</option>
                    <option value="hf.co/mradermacher/Broken-Tutu-24B-Unslop-v2.0-GGUF:Q8_0">Broken-Tutu-24B-Unslop-v2.0-GGUF:Q8_0 (23.3 GB)</option>
                    <option value="qwen2.5:latest">qwen2.5:latest (4.4 GB)</option>
                    <option value="gemma3:27b">gemma3:27b (16.2 GB)</option>
                `;

                llmStatus.textContent = '✅ Loaded your Ollama models';
                llmStatus.className = 'text-xs text-green-600 mt-1';

                // Try to fetch from API in background (optional)
                setTimeout(function() {
                    fetch('/api/ollama/models')
                        .then(function(response) {
                            if (response.ok) {
                                return response.json();
                            }
                            throw new Error('API not available');
                        })
                        .then(function(data) {
                            if (data.success && data.models && data.models.length > 0) {
                                // Update with API models
                                llmSelect.innerHTML = '<option value="">Select AI Model</option>';

                                data.models.forEach(function(model) {
                                    const option = document.createElement('option');
                                    option.value = model.name;
                                    const sizeGB = (model.size / (1024 * 1024 * 1024)).toFixed(1);
                                    option.textContent = model.name + ' (' + sizeGB + ' GB)';
                                    llmSelect.appendChild(option);
                                });

                                llmStatus.textContent = '✅ Found ' + data.models.length + ' Ollama models (via API)';
                                llmStatus.className = 'text-xs text-green-600 mt-1';
                            }
                        })
                        .catch(function(error) {
                            console.log('API fetch failed, using fallback models');
                        });
                }, 1000);
            }

            // Start extraction and analysis process
            searchBtn.addEventListener('click', function() {
                const drug = document.getElementById('drug').value.trim();
                const searchCriteria = document.getElementById('search-criteria').value.trim();
                const llm = document.getElementById('llm').value;

                if (!drug) {
                    alert('Please enter a drug name');
                    return;
                }

                if (!searchCriteria) {
                    alert('Please enter search criteria for AI analysis');
                    return;
                }

                if (!llm) {
                    alert('Please select an AI model');
                    return;
                }

                progressSection.classList.remove('hidden');
                document.getElementById('step2-progress').classList.add('hidden');

                // Clear terminal
                terminal.innerHTML = `
                    <div class="terminal-line">Clinical Trials Extraction & Analysis v2.0</div>
                    <div class="terminal-line">Drug: ${drug}</div>
                    <div class="terminal-line">Search Criteria: ${searchCriteria}</div>
                    <div class="terminal-line">AI Model: ${llm}</div>
                    <div class="terminal-line">Starting extraction...</div>
                `;

                // Send request to backend
                fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        drug: drug,
                        search_criteria: searchCriteria,
                        llm: llm
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        addTerminalLine(`Error: ${data.error}`, 'terminal-error');
                    } else {
                        addTerminalLine(`Backend started: ${data.message}`, 'terminal-response');
                        // Start monitoring progress
                        monitorProgress();
                    }
                })
                .catch(error => {
                    addTerminalLine(`Request failed: ${error}`, 'terminal-error');
                });

                // Also run simulation for demo
                simulateStep1(drug, searchCriteria, llm);
            }

            // Monitor real backend progress
            function monitorProgress() {
                const checkProgress = () => {
                    fetch('/api/progress')
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'running') {
                                updateProgressBars(data);
                                setTimeout(checkProgress, 1000); // Check every second
                            } else if (data.status === 'completed') {
                                updateProgressBars(data);
                                showFinalResults(data);
                            } else if (data.status === 'error') {
                                addTerminalLine(`Error: ${data.message}`, 'terminal-error');
                            }
                        })
                        .catch(error => {
                            addTerminalLine(`Progress check failed: ${error}`, 'terminal-error');
                        });
                };
                checkProgress();
            }

            function updateProgressBars(data) {
                if (data.step === 1) {
                    const step1Bar = document.getElementById('step1-bar');
                    const step1Percent = document.getElementById('step1-percent');
                    const step1Text = document.getElementById('step1-text');

                    step1Bar.style.width = `${data.progress}%`;
                    step1Percent.textContent = `${Math.floor(data.progress)}%`;
                    step1Text.textContent = data.message;

                    if (data.progress === 100) {
                        document.getElementById('step2-progress').classList.remove('hidden');
                    }
                } else if (data.step === 2) {
                    const step2Bar = document.getElementById('step2-bar');
                    const step2Percent = document.getElementById('step2-percent');
                    const step2Text = document.getElementById('step2-text');

                    step2Bar.style.width = `${data.progress}%`;
                    step2Percent.textContent = `${Math.floor(data.progress)}%`;
                    step2Text.textContent = data.message;
                }

                addTerminalLine(data.message, 'terminal-response');
            }

            function showFinalResults(data) {
                addTerminalLine('========================================', 'terminal-response');
                addTerminalLine('EXTRACTION & ANALYSIS COMPLETED!', 'terminal-warning');
                addTerminalLine('========================================', 'terminal-response');

                if (data.results && data.results.length > 0) {
                    addTerminalLine(`Found ${data.results.length} studies matching criteria:`, 'terminal-warning');
                    data.results.forEach((study, index) => {
                        const confidence = study.analysis?.confidence || 0;
                        addTerminalLine(`${index + 1}. ${study.nct_id} (confidence: ${confidence.toFixed(2)})`, 'terminal-response');
                        addTerminalLine(`   ${study.study_title.substring(0, 70)}...`, 'terminal-response');
                    });
                } else {
                    addTerminalLine('No studies matched the search criteria.', 'terminal-warning');
                }
            }

            function simulateStep1(drug, searchCriteria, llm) {
                let progress = 0;
                const step1Bar = document.getElementById('step1-bar');
                const step1Percent = document.getElementById('step1-percent');
                const step1Text = document.getElementById('step1-text');

                const interval = setInterval(() => {
                    progress += Math.random() * 5;
                    if (progress > 100) progress = 100;

                    step1Bar.style.width = `${progress}%`;
                    step1Percent.textContent = `${Math.floor(progress)}%`;

                    if (progress < 25) {
                        step1Text.textContent = "Connecting to ClinicalTrials.gov...";
                    } else if (progress < 50) {
                        step1Text.textContent = "Searching for studies...";
                    } else if (progress < 75) {
                        step1Text.textContent = "Downloading study data...";
                    } else if (progress < 100) {
                        step1Text.textContent = "Processing results...";
                    } else {
                        step1Text.textContent = "Completed!";
                    }

                    // Add to terminal
                    if (progress === 25) {
                        addTerminalLine(`Connected to ClinicalTrials.gov API`, 'terminal-response');
                    } else if (progress === 50) {
                        addTerminalLine(`Found 127 studies related to ${drug}`, 'terminal-response');
                    } else if (progress === 75) {
                        addTerminalLine(`Downloaded study metadata for 127 studies`, 'terminal-response');
                    }

                    if (progress === 100) {
                        clearInterval(interval);
                        addTerminalLine(`Successfully retrieved 127 studies for ${drug}`, 'terminal-response');
                        addTerminalLine(`Preparing for AI analysis...`, 'terminal-response');

                        // Show step 2
                        document.getElementById('step2-progress').classList.remove('hidden');
                        simulateStep2(drug, searchCriteria, llm);
                    }
                }, 200);
            }

            function simulateStep2(drug, searchCriteria, llmModel) {
                let progress = 0;
                const step2Bar = document.getElementById('step2-bar');
                const step2Percent = document.getElementById('step2-percent');
                const step2Text = document.getElementById('step2-text');
                const llmName = document.getElementById('llm').options[document.getElementById('llm').selectedIndex].text;

                addTerminalLine(`Initializing ${llmName} for analysis...`, 'terminal-response');

                const interval = setInterval(() => {
                    progress += Math.random() * 3;
                    if (progress > 100) progress = 100;

                    step2Bar.style.width = `${progress}%`;
                    step2Percent.textContent = `${Math.floor(progress)}%`;

                    if (progress < 20) {
                        step2Text.textContent = "Loading AI model...";
                    } else if (progress < 40) {
                        step2Text.textContent = "Analyzing study designs...";
                    } else if (progress < 60) {
                        step2Text.textContent = "Evaluating outcomes...";
                    } else if (progress < 80) {
                        step2Text.textContent = "Assessing relevance...";
                    } else if (progress < 100) {
                        step2Text.textContent = "Generating final report...";
                    } else {
                        step2Text.textContent = "Analysis complete!";
                    }

                    // Add to terminal
                    if (progress === 20) {
                        addTerminalLine(`${llmName} model loaded successfully`, 'terminal-response');
                    } else if (progress === 40) {
                        addTerminalLine(`Analyzed study designs for 127 studies`, 'terminal-response');
                    } else if (progress === 60) {
                        addTerminalLine(`Identified 42 studies with relevant outcomes`, 'terminal-response');
                    } else if (progress === 80) {
                        addTerminalLine(`Narrowed down to 18 highly relevant studies`, 'terminal-response');
                    }

                    if (progress === 100) {
                        clearInterval(interval);
                        addTerminalLine(`AI analysis completed for ${drug}`, 'terminal-response');
                        addTerminalLine(`========================================`, 'terminal-response');
                        addTerminalLine(`AI Analysis Summary:`, 'terminal-warning');
                        addTerminalLine(`- Total studies found: 127`, 'terminal-response');
                        addTerminalLine(`- Studies matching all criteria: 18`, 'terminal-response');
                        addTerminalLine(`- Most common conditions: Type 2 Diabetes (32%), Hypertension (24%)`, 'terminal-response');
                        addTerminalLine(`- Most frequent phase: Phase 3 (58% of studies)`, 'terminal-response');
                        addTerminalLine(`- Average sample size: 342 participants`, 'terminal-response');
                        addTerminalLine(`- Studies with published results: 9 (50%)`, 'terminal-response');
                        addTerminalLine(`========================================`, 'terminal-response');
                        addTerminalLine(`Top 5 most relevant studies:`, 'terminal-warning');
                        addTerminalLine(`1. NCT12345678 - "Efficacy of ${drug} in Type 2 Diabetes" (Phase 3, 2022)`, 'terminal-response');
                        addTerminalLine(`2. NCT23456789 - "${drug} vs. Standard Care in Hypertension" (Phase 3, 2021)`, 'terminal-response');
                        addTerminalLine(`3. NCT34567890 - "Long-term Safety of ${drug}" (Phase 4, 2023)`, 'terminal-response');
                        addTerminalLine(`4. NCT45678901 - "Dose Optimization Study of ${drug}" (Phase 2, 2020)`, 'terminal-response');
                        addTerminalLine(`5. NCT56789012 - "${drug} in Pediatric Population" (Phase 2/3, 2021)`, 'terminal-response');
                    }
                }, 300);
            }

            function addTerminalLine(text, className) {
                const line = document.createElement('div');
                line.className = `terminal-line ${className} fade-in`;
                line.textContent = text;
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
            }

            // Copy terminal content
            copyBtn.addEventListener('click', function() {
                const range = document.createRange();
                range.selectNode(terminal);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                document.execCommand('copy');
                window.getSelection().removeAllRanges();

                // Show copied notification
                const notification = document.createElement('div');
                notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg';
                notification.textContent = 'Copied to clipboard!';
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 2000);
            });

            // Clear terminal
            clearBtn.addEventListener('click', function() {
                terminal.innerHTML = `
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Terminal cleared. Ready for new search...</div>
                `;
            });
        });
    </script>
</body>
</html>
