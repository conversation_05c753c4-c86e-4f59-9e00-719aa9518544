#!/usr/bin/env python3
"""
Backend integration for the web UI
This could be used to connect the web interface to our actual extraction system
"""

from flask import Flask, render_template, request, jsonify, Response
import json
import subprocess
import sys
import threading
import time
from pathlib import Path

app = Flask(__name__)

# Global variable to track extraction progress
extraction_progress = {
    'status': 'idle',
    'step': 0,
    'progress': 0,
    'message': '',
    'results': []
}

@app.route('/')
def index():
    """Serve the main web interface"""
    return render_template('clinical_trials_ui.html')

@app.route('/api/search', methods=['POST'])
def start_search():
    """Start the clinical trials search and extraction"""
    global extraction_progress
    
    data = request.json
    drug_name = data.get('drug', '').strip()
    llm_model = data.get('llm', '')
    
    if not drug_name or not llm_model:
        return jsonify({'error': 'Drug name and LLM model are required'}), 400
    
    # Reset progress
    extraction_progress = {
        'status': 'running',
        'step': 1,
        'progress': 0,
        'message': f'Starting search for {drug_name}...',
        'results': []
    }
    
    # Start extraction in background thread
    thread = threading.Thread(target=run_extraction, args=(drug_name, llm_model))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'started'})

@app.route('/api/progress')
def get_progress():
    """Get current extraction progress"""
    return jsonify(extraction_progress)

@app.route('/api/stream')
def stream_progress():
    """Stream progress updates using Server-Sent Events"""
    def generate():
        while extraction_progress['status'] == 'running':
            yield f"data: {json.dumps(extraction_progress)}\n\n"
            time.sleep(1)
        
        # Send final status
        yield f"data: {json.dumps(extraction_progress)}\n\n"
    
    return Response(generate(), mimetype='text/plain')

def run_extraction(drug_name, llm_model):
    """Run the actual extraction process"""
    global extraction_progress
    
    try:
        # Step 1: Search for studies
        extraction_progress.update({
            'step': 1,
            'progress': 0,
            'message': 'Connecting to ClinicalTrials.gov...'
        })
        
        # Simulate search process (replace with actual search)
        for i in range(0, 101, 5):
            extraction_progress['progress'] = i
            if i < 25:
                extraction_progress['message'] = 'Connecting to ClinicalTrials.gov...'
            elif i < 50:
                extraction_progress['message'] = 'Searching for studies...'
            elif i < 75:
                extraction_progress['message'] = 'Downloading study data...'
            else:
                extraction_progress['message'] = 'Processing results...'
            
            time.sleep(0.2)
        
        # Step 2: AI Analysis
        extraction_progress.update({
            'step': 2,
            'progress': 0,
            'message': f'Initializing {llm_model} for analysis...'
        })
        
        # Simulate AI analysis (replace with actual analysis)
        for i in range(0, 101, 3):
            extraction_progress['progress'] = i
            if i < 20:
                extraction_progress['message'] = 'Loading AI model...'
            elif i < 40:
                extraction_progress['message'] = 'Analyzing study designs...'
            elif i < 60:
                extraction_progress['message'] = 'Evaluating outcomes...'
            elif i < 80:
                extraction_progress['message'] = 'Assessing relevance...'
            else:
                extraction_progress['message'] = 'Generating final report...'
            
            time.sleep(0.3)
        
        # Complete
        extraction_progress.update({
            'status': 'completed',
            'step': 2,
            'progress': 100,
            'message': 'Analysis complete!',
            'results': [
                {'nct_id': 'NCT12345678', 'title': f'Efficacy of {drug_name} in Type 2 Diabetes', 'phase': 'Phase 3', 'year': '2022'},
                {'nct_id': 'NCT23456789', 'title': f'{drug_name} vs. Standard Care in Hypertension', 'phase': 'Phase 3', 'year': '2021'},
                {'nct_id': 'NCT34567890', 'title': f'Long-term Safety of {drug_name}', 'phase': 'Phase 4', 'year': '2023'},
                {'nct_id': 'NCT45678901', 'title': f'Dose Optimization Study of {drug_name}', 'phase': 'Phase 2', 'year': '2020'},
                {'nct_id': 'NCT56789012', 'title': f'{drug_name} in Pediatric Population', 'phase': 'Phase 2/3', 'year': '2021'},
            ]
        })
        
    except Exception as e:
        extraction_progress.update({
            'status': 'error',
            'message': f'Error: {str(e)}'
        })

def run_actual_extraction(drug_name):
    """
    This function would run our actual extraction scripts
    Replace the simulation above with this for real functionality
    """
    try:
        # Step 1: Search for studies
        result = subprocess.run([sys.executable, 'test_search_only.py'], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            raise Exception(f"Search failed: {result.stderr}")
        
        # Step 2: Run batch extraction
        result = subprocess.run([sys.executable, 'batch_extract_all_canagliflozin.py'], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode != 0:
            raise Exception(f"Extraction failed: {result.stderr}")
        
        # Load results
        with open('canagliflozin_batch_extraction_summary.json', 'r') as f:
            results = json.load(f)
        
        return results
        
    except Exception as e:
        raise Exception(f"Extraction error: {str(e)}")

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    
    # Copy the HTML file to templates directory for Flask
    import shutil
    shutil.copy2('clinical_trials_web_ui.html', templates_dir / 'clinical_trials_ui.html')
    
    print("🌐 Starting Clinical Trials Web Interface...")
    print("📍 Open your browser to: http://localhost:5000")
    print("🔧 Backend integration ready for real extraction system")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
