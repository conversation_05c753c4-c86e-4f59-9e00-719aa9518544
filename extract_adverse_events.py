#!/usr/bin/env python3
"""
Script to extract adverse events table from ClinicalTrials.gov study page
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import sys
from urllib.parse import urljoin
import time
import json
import re

def extract_adverse_events_table(url):
    """
    Extract adverse events table from a ClinicalTrials.gov study page
    
    Args:
        url (str): The URL of the clinical trial study page
        
    Returns:
        pd.DataFrame or None: DataFrame containing the adverse events data
    """
    
    # Set up headers to mimic a real browser
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }
    
    try:
        print(f"Fetching URL: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"Response status: {response.status_code}")
        print(f"Content length: {len(response.content)}")

        # Save raw HTML for debugging
        with open('raw_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("Saved raw HTML to raw_page.html for inspection")

        # Parse the HTML content
        soup = BeautifulSoup(response.content, 'html.parser')

        # Look for adverse events section
        adverse_events_data = []

        # First, let's examine the page structure and look for JSON data
        print("Examining page structure...")

        # Look for script tags that might contain JSON data
        script_tags = soup.find_all('script')
        print(f"Found {len(script_tags)} script tags")

        # Look for data that might be in JSON format
        for i, script in enumerate(script_tags):
            if script.string:
                script_content = script.string.strip()
                if 'adverse' in script_content.lower() or 'safety' in script_content.lower():
                    print(f"Script {i} might contain adverse events data")
                    # Try to extract JSON data
                    try:
                        # Look for JSON objects in the script
                        json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)
                        for json_str in json_matches:
                            try:
                                data = json.loads(json_str)
                                if isinstance(data, dict) and any(key.lower().find('adverse') != -1 for key in str(data).lower().split()):
                                    print(f"Found potential adverse events JSON data in script {i}")
                            except:
                                continue
                    except:
                        continue

        # Try different selectors for adverse events tables
        selectors_to_try = [
            'table[summary*="adverse"]',
            'table[summary*="Adverse"]',
            'table:contains("Adverse Events")',
            'table:contains("adverse events")',
            'div[id*="adverse"] table',
            'div[class*="adverse"] table',
            'section[id*="adverse"] table',
            '.adverse-events table',
            '#adverse-events table'
        ]

        tables_found = []

        # First, let's find all tables and examine them
        all_tables = soup.find_all('table')
        print(f"Found {len(all_tables)} tables on the page")
        
        for i, table in enumerate(all_tables):
            # Check table content for adverse events keywords
            table_text = table.get_text().lower()
            if any(keyword in table_text for keyword in ['adverse', 'side effect', 'safety', 'toxicity']):
                print(f"Table {i+1} might contain adverse events data")
                tables_found.append((i+1, table))
        
        if not tables_found:
            print("No tables with adverse events keywords found")

            # Let's look for any results section
            results_section = soup.find('div', {'id': 'results'}) or soup.find('section', string=lambda text: text and 'results' in text.lower())
            if results_section:
                print("Found results section, looking for tables within it")
                results_tables = results_section.find_all('table')
                for i, table in enumerate(results_tables):
                    tables_found.append((f"results-{i+1}", table))

            # Look for div elements that might contain structured data
            print("Looking for div elements with adverse events content...")
            adverse_divs = soup.find_all('div', string=lambda text: text and 'adverse' in text.lower())
            safety_divs = soup.find_all('div', string=lambda text: text and 'safety' in text.lower())

            all_content_divs = adverse_divs + safety_divs
            for i, div in enumerate(all_content_divs):
                print(f"Found div {i+1} with potential adverse events content:")
                print(div.get_text()[:200] + "..." if len(div.get_text()) > 200 else div.get_text())

            # Look for any structured data in the page
            print("\nLooking for any structured content...")
            # Find elements with specific classes or IDs that might contain results
            potential_containers = soup.find_all(['div', 'section', 'article'],
                                                class_=lambda x: x and any(keyword in x.lower() for keyword in ['result', 'outcome', 'safety', 'adverse']))

            for i, container in enumerate(potential_containers):
                print(f"Found potential container {i+1}: {container.name} with class {container.get('class')}")
                # Look for any tabular data within these containers
                container_tables = container.find_all('table')
                for j, table in enumerate(container_tables):
                    tables_found.append((f"container-{i+1}-table-{j+1}", table))
        
        # Process found tables
        for table_id, table in tables_found:
            print(f"\nProcessing table {table_id}:")
            
            # Extract table headers
            headers = []
            header_row = table.find('tr')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(th.get_text().strip())
            
            print(f"Headers: {headers}")
            
            # Extract table rows
            rows = []
            for tr in table.find_all('tr')[1:]:  # Skip header row
                row = []
                for td in tr.find_all(['td', 'th']):
                    row.append(td.get_text().strip())
                if row:  # Only add non-empty rows
                    rows.append(row)
            
            if rows:
                print(f"Found {len(rows)} data rows")
                # Create DataFrame
                if len(headers) == len(rows[0]):
                    df = pd.DataFrame(rows, columns=headers)
                else:
                    # Handle mismatched columns
                    max_cols = max(len(headers), max(len(row) for row in rows) if rows else 0)
                    headers_padded = headers + [f'Column_{i}' for i in range(len(headers), max_cols)]
                    rows_padded = [row + [''] * (max_cols - len(row)) for row in rows]
                    df = pd.DataFrame(rows_padded, columns=headers_padded)
                
                adverse_events_data.append({
                    'table_id': table_id,
                    'dataframe': df
                })
        
        return adverse_events_data
        
    except requests.RequestException as e:
        print(f"Error fetching the webpage: {e}")
        return None
    except Exception as e:
        print(f"Error processing the webpage: {e}")
        return None

def main():
    url = "https://clinicaltrials.gov/study/NCT02673138?intr=Canagliflozin&aggFilters=results:with,status:com&viewType=Card&rank=1&tab=results"
    
    print("Extracting adverse events table from ClinicalTrials.gov...")
    
    adverse_events_data = extract_adverse_events_table(url)
    
    if adverse_events_data:
        print(f"\nFound {len(adverse_events_data)} potential adverse events tables:")
        
        for data in adverse_events_data:
            table_id = data['table_id']
            df = data['dataframe']
            
            print(f"\n=== Table {table_id} ===")
            print(f"Shape: {df.shape}")
            print("\nFirst few rows:")
            print(df.head())
            
            # Save to CSV
            filename = f"adverse_events_table_{table_id}.csv"
            df.to_csv(filename, index=False)
            print(f"Saved to: {filename}")
    else:
        print("No adverse events tables found or error occurred")

if __name__ == "__main__":
    main()
