#!/usr/bin/env python3
"""
Simple web server to serve the clinical trials UI and proxy Ollama API calls
"""

from flask import Flask, send_file, jsonify, request
from flask_cors import CORS
import requests
import json
import os

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def serve_ui():
    """Serve the main HTML file"""
    return send_file('clinical_trials_web_ui.html')

@app.route('/api/ollama/models')
def get_ollama_models():
    """Proxy endpoint to get Ollama models (avoids CORS issues)"""
    try:
        # Try to connect to Ollama API
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            # Format models for the frontend
            formatted_models = []
            for model in models:
                formatted_models.append({
                    'name': model.get('name', ''),
                    'size': model.get('size', 0),
                    'modified_at': model.get('modified_at', ''),
                    'digest': model.get('digest', '')
                })
            
            return jsonify({
                'success': True,
                'models': formatted_models,
                'count': len(formatted_models)
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Ollama API returned status {response.status_code}',
                'models': []
            }), 500
            
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'error': 'Could not connect to Ollama. Make sure Ollama is running on localhost:11434',
            'models': []
        }), 503
        
    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'error': 'Ollama API request timed out',
            'models': []
        }), 504
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'models': []
        }), 500

@app.route('/api/ollama/status')
def ollama_status():
    """Check if Ollama is running"""
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            version_data = response.json()
            return jsonify({
                'running': True,
                'version': version_data.get('version', 'unknown')
            })
        else:
            return jsonify({'running': False, 'error': 'Ollama API not responding'})
    except:
        return jsonify({'running': False, 'error': 'Cannot connect to Ollama'})

@app.route('/api/search', methods=['POST'])
def start_search():
    """Handle search requests and trigger real extraction and analysis"""
    data = request.json
    drug_name = data.get('drug', '').strip()
    search_criteria = data.get('search_criteria', '').strip()
    llm_model = data.get('llm', '')

    if not drug_name:
        return jsonify({'error': 'Drug name is required'}), 400

    if not search_criteria:
        return jsonify({'error': 'Search criteria is required'}), 400

    if not llm_model:
        return jsonify({'error': 'LLM model is required'}), 400

    # Start the real extraction and analysis process
    thread = threading.Thread(target=run_real_extraction_and_analysis,
                             args=(drug_name, search_criteria, llm_model))
    thread.daemon = True
    thread.start()

    return jsonify({
        'status': 'started',
        'drug': drug_name,
        'search_criteria': search_criteria,
        'llm': llm_model,
        'message': f'Extraction and analysis started for {drug_name}'
    })

def run_real_extraction_and_analysis(drug_name, search_criteria, llm_model):
    """Run the actual extraction and analysis process"""
    global extraction_progress

    try:
        # Step 1: Search and extract studies
        extraction_progress.update({
            'step': 1,
            'progress': 0,
            'message': f'Searching for {drug_name} studies...'
        })

        # Run search script
        result = subprocess.run([sys.executable, 'test_search_only.py', drug_name],
                              capture_output=True, text=True, timeout=120)

        if result.returncode != 0:
            raise Exception(f"Search failed: {result.stderr}")

        extraction_progress.update({
            'progress': 50,
            'message': 'Running batch extraction...'
        })

        # Run batch extraction
        result = subprocess.run([sys.executable, 'batch_extract_all_studies.py'],
                              capture_output=True, text=True, timeout=3600)

        if result.returncode != 0:
            raise Exception(f"Extraction failed: {result.stderr}")

        extraction_progress.update({
            'step': 1,
            'progress': 100,
            'message': 'Study extraction completed!'
        })

        time.sleep(1)

        # Step 2: AI Analysis
        extraction_progress.update({
            'step': 2,
            'progress': 0,
            'message': f'Starting AI analysis with {llm_model}...'
        })

        # Run AI analysis
        result = subprocess.run([sys.executable, 'ai_analyze_studies.py', search_criteria, llm_model],
                              capture_output=True, text=True, timeout=1800)

        if result.returncode != 0:
            raise Exception(f"AI analysis failed: {result.stderr}")

        # Load analysis results
        try:
            with open('ai_analysis_results.json', 'r') as f:
                analysis_results = json.load(f)

            matching_studies = analysis_results.get('matching_studies', [])

            extraction_progress.update({
                'status': 'completed',
                'step': 2,
                'progress': 100,
                'message': f'Analysis complete! Found {len(matching_studies)} matching studies',
                'results': matching_studies
            })

        except Exception as e:
            raise Exception(f"Failed to load analysis results: {str(e)}")

    except Exception as e:
        extraction_progress.update({
            'status': 'error',
            'message': f'Error: {str(e)}'
        })

def check_ollama_running():
    """Check if Ollama is running and print status"""
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=3)
        if response.status_code == 200:
            version_data = response.json()
            print(f"✅ Ollama is running (version: {version_data.get('version', 'unknown')})")
            
            # Also check available models
            models_response = requests.get('http://localhost:11434/api/tags', timeout=3)
            if models_response.status_code == 200:
                models_data = models_response.json()
                models = models_data.get('models', [])
                print(f"📦 Found {len(models)} Ollama models:")
                for model in models[:5]:  # Show first 5 models
                    size_gb = model.get('size', 0) / (1024**3)
                    print(f"   - {model.get('name', 'unknown')} ({size_gb:.1f} GB)")
                if len(models) > 5:
                    print(f"   ... and {len(models) - 5} more")
            return True
        else:
            print("❌ Ollama API not responding properly")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Ollama is not running or not accessible on localhost:11434")
        print("💡 To start Ollama, run: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

if __name__ == '__main__':
    print("🌐 Clinical Trials Web UI Server")
    print("=" * 40)
    
    # Check if the HTML file exists
    if not os.path.exists('clinical_trials_web_ui.html'):
        print("❌ clinical_trials_web_ui.html not found!")
        print("Make sure you're running this from the correct directory.")
        exit(1)
    
    # Check Ollama status
    print("🔍 Checking Ollama status...")
    ollama_running = check_ollama_running()
    
    if not ollama_running:
        print("\n⚠️  Ollama is not running. The web UI will show fallback models.")
        print("To use your local Ollama models:")
        print("1. Start Ollama: ollama serve")
        print("2. Refresh the web page")
    
    print(f"\n🚀 Starting web server...")
    print(f"📍 Open your browser to: http://localhost:8080")
    print(f"🔧 Ollama proxy available at: http://localhost:8080/api/ollama/models")
    print(f"⏹️  Press Ctrl+C to stop the server")

    try:
        app.run(debug=True, host='0.0.0.0', port=8080)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
