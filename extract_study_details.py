#!/usr/bin/env python3
"""
Extract Study Details tab data from ClinicalTrials.gov
"""

import requests
import json

def extract_study_details(nct_id):
    """
    Extract Study Details data using the ClinicalTrials.gov API v2
    This corresponds to the default "Study Details" tab
    """
    
    # API URL for the study details
    api_url = f"https://clinicaltrials.gov/api/v2/studies/{nct_id}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    print(f"Extracting Study Details for {nct_id}...")
    print(f"API URL: {api_url}")
    
    try:
        response = requests.get(api_url, headers=headers, timeout=30)
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("Successfully retrieved Study Details JSON data")
                
                # Extract the protocol section which contains study details
                if 'protocolSection' in data:
                    protocol_section = data['protocolSection']
                    
                    # Create a structured study details object
                    study_details = {
                        'nctId': nct_id,
                        'extractedAt': requests.utils.default_headers()['User-Agent'],
                        'dataSource': 'ClinicalTrials.gov API v2',
                        'tabType': 'Study Details',
                        'protocolSection': protocol_section
                    }
                    
                    # Save the study details data
                    filename = 'study_details.json'
                    with open(filename, 'w') as f:
                        json.dump(study_details, f, indent=2)
                    print(f"Saved Study Details data to {filename}")
                    
                    # Print summary of what was extracted
                    print("\n=== STUDY DETAILS SUMMARY ===")
                    
                    # Basic identification
                    identification = protocol_section.get('identificationModule', {})
                    print(f"NCT ID: {identification.get('nctId', 'N/A')}")
                    print(f"Brief Title: {identification.get('briefTitle', 'N/A')}")
                    print(f"Official Title: {identification.get('officialTitle', 'N/A')}")
                    
                    # Status information
                    status = protocol_section.get('statusModule', {})
                    print(f"Overall Status: {status.get('overallStatus', 'N/A')}")
                    print(f"Study First Posted: {status.get('studyFirstPostDate', 'N/A')}")
                    print(f"Last Update Posted: {status.get('lastUpdatePostDate', 'N/A')}")
                    
                    # Study design
                    design = protocol_section.get('designModule', {})
                    print(f"Study Type: {design.get('studyType', 'N/A')}")
                    print(f"Phases: {design.get('phases', 'N/A')}")
                    
                    # Conditions and interventions
                    conditions = protocol_section.get('conditionsModule', {})
                    print(f"Conditions: {conditions.get('conditions', 'N/A')}")
                    
                    interventions = protocol_section.get('armsInterventionsModule', {})
                    if 'interventions' in interventions:
                        intervention_names = [i.get('name', 'N/A') for i in interventions['interventions']]
                        print(f"Interventions: {intervention_names}")
                    
                    # Eligibility
                    eligibility = protocol_section.get('eligibilityModule', {})
                    print(f"Eligibility Criteria: {eligibility.get('eligibilityCriteria', 'N/A')[:100]}...")
                    
                    return study_details
                else:
                    print("No protocolSection found in API response")
                    return None
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                print("Response content:", response.text[:500])
                return None
        else:
            print(f"HTTP error: {response.status_code}")
            print("Response:", response.text[:500])
            return None
            
    except requests.RequestException as e:
        print(f"Request failed: {e}")
        return None

def main():
    nct_id = "NCT02324842"
    
    print("=== EXTRACTING STUDY DETAILS TAB ===")
    study_details = extract_study_details(nct_id)
    
    if study_details:
        print("\n✅ Successfully extracted Study Details data!")
        print("Generated file: study_details.json")
    else:
        print("\n❌ Failed to extract Study Details data")

if __name__ == "__main__":
    main()
