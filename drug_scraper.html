<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClinicalTrials.gov Scraper</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .terminal {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #f0f0f0;
            height: 400px;
            overflow-y: auto;
            padding: 1rem;
            border-radius: 0.5rem;
            white-space: pre-wrap;
        }
        .terminal-line {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        .terminal-command {
            color: #4CAF50;
        }
        .terminal-response {
            color: #f0f0f0;
        }
        .terminal-error {
            color: #f44336;
        }
        .terminal-warning {
            color: #FFC107;
        }
        .progress-container {
            position: relative;
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-blue-800 mb-2">ClinicalTrials.gov Scraper</h1>
            <p class="text-gray-600">Search and analyze clinical trials with AI assistance</p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div>
                    <label for="drug" class="block text-sm font-medium text-gray-700 mb-1">Drug Name</label>
                    <div class="relative">
                        <input type="text" id="drug" placeholder="Enter drug name (e.g., Ibuprofen)" 
                            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-pills text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label for="llm" class="block text-sm font-medium text-gray-700 mb-1">AI Assistant</label>
                    <div class="relative">
                        <select id="llm" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">Select AI Model</option>
                            <option value="llama3">Llama 3 (70B)</option>
                            <option value="mistral">Mistral 7B</option>
                            <option value="phi3">Phi-3 (3.8B)</option>
                            <option value="gemma">Gemma (7B)</option>
                            <option value="gpt4all">GPT4All (13B)</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-robot text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Study Status</label>
                    <div class="relative">
                        <select id="status" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">All Statuses</option>
                            <option value="recruiting">Recruiting</option>
                            <option value="completed">Completed</option>
                            <option value="active">Active, not recruiting</option>
                            <option value="terminated">Terminated</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-clipboard-check text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div>
                    <label for="phase" class="block text-sm font-medium text-gray-700 mb-1">Phase</label>
                    <select id="phase" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Phases</option>
                        <option value="1">Phase 1</option>
                        <option value="2">Phase 2</option>
                        <option value="3">Phase 3</option>
                        <option value="4">Phase 4</option>
                    </select>
                </div>
                
                <div>
                    <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Start Date From</label>
                    <input type="date" id="start-date" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">End Date To</label>
                    <input type="date" id="end-date" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="results" class="block text-sm font-medium text-gray-700 mb-1">Results Available</label>
                    <select id="results" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Any</option>
                        <option value="yes">With Results</option>
                        <option value="no">Without Results</option>
                    </select>
                </div>
            </div>

            <div class="mb-6">
                <label for="conditions" class="block text-sm font-medium text-gray-700 mb-1">Conditions (comma separated)</label>
                <input type="text" id="conditions" placeholder="e.g., diabetes, hypertension" 
                    class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div class="flex justify-center">
                <button id="search-btn" class="px-8 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center">
                    <i class="fas fa-search mr-2"></i> Search Clinical Trials
                </button>
            </div>
        </div>

        <div id="progress-section" class="hidden">
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Search Progress</h2>
                
                <div id="step1-progress" class="mb-8">
                    <div class="flex justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Step 1: Retrieving studies from ClinicalTrials.gov</span>
                        <span id="step1-percent" class="text-sm font-medium text-gray-700">0%</span>
                    </div>
                    <div class="progress-container w-full bg-gray-200 rounded-full h-4">
                        <div id="step1-bar" class="bg-blue-600 h-4 rounded-full" style="width: 0%"></div>
                        <div id="step1-text" class="progress-text text-xs">Starting...</div>
                    </div>
                </div>
                
                <div id="step2-progress" class="hidden">
                    <div class="flex justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Step 2: Analyzing studies with AI</span>
                        <span id="step2-percent" class="text-sm font-medium text-gray-700">0%</span>
                    </div>
                    <div class="progress-container w-full bg-gray-200 rounded-full h-4">
                        <div id="step2-bar" class="bg-green-600 h-4 rounded-full" style="width: 0%"></div>
                        <div id="step2-text" class="progress-text text-xs">Waiting for studies...</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Results Analysis</h2>
                    <div class="flex space-x-2">
                        <button id="copy-btn" class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm flex items-center">
                            <i class="fas fa-copy mr-1"></i> Copy
                        </button>
                        <button id="clear-btn" class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm flex items-center">
                            <i class="fas fa-trash-alt mr-1"></i> Clear
                        </button>
                    </div>
                </div>
                
                <div id="terminal" class="terminal">
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Ready to search for clinical trials...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchBtn = document.getElementById('search-btn');
            const progressSection = document.getElementById('progress-section');
            const terminal = document.getElementById('terminal');
            const copyBtn = document.getElementById('copy-btn');
            const clearBtn = document.getElementById('clear-btn');
            
            // Simulate search process
            searchBtn.addEventListener('click', function() {
                const drug = document.getElementById('drug').value.trim();
                const llm = document.getElementById('llm').value;
                
                if (!drug) {
                    alert('Please enter a drug name');
                    return;
                }
                
                if (!llm) {
                    alert('Please select an AI model');
                    return;
                }
                
                progressSection.classList.remove('hidden');
                document.getElementById('step2-progress').classList.add('hidden');
                
                // Clear terminal
                terminal.innerHTML = `
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Starting search for "${drug}"...</div>
                `;
                
                // Step 1: Retrieving studies
                simulateStep1(drug);
            });
            
            function simulateStep1(drug) {
                let progress = 0;
                const step1Bar = document.getElementById('step1-bar');
                const step1Percent = document.getElementById('step1-percent');
                const step1Text = document.getElementById('step1-text');
                
                const interval = setInterval(() => {
                    progress += Math.random() * 5;
                    if (progress > 100) progress = 100;
                    
                    step1Bar.style.width = `${progress}%`;
                    step1Percent.textContent = `${Math.floor(progress)}%`;
                    
                    if (progress < 25) {
                        step1Text.textContent = "Connecting to ClinicalTrials.gov...";
                    } else if (progress < 50) {
                        step1Text.textContent = "Searching for studies...";
                    } else if (progress < 75) {
                        step1Text.textContent = "Downloading study data...";
                    } else if (progress < 100) {
                        step1Text.textContent = "Processing results...";
                    } else {
                        step1Text.textContent = "Completed!";
                    }
                    
                    // Add to terminal
                    if (progress === 25) {
                        addTerminalLine(`Connected to ClinicalTrials.gov API`, 'terminal-response');
                    } else if (progress === 50) {
                        addTerminalLine(`Found 127 studies related to ${drug}`, 'terminal-response');
                    } else if (progress === 75) {
                        addTerminalLine(`Downloaded study metadata for 127 studies`, 'terminal-response');
                    }
                    
                    if (progress === 100) {
                        clearInterval(interval);
                        addTerminalLine(`Successfully retrieved 127 studies for ${drug}`, 'terminal-response');
                        addTerminalLine(`Preparing for AI analysis...`, 'terminal-response');
                        
                        // Show step 2
                        document.getElementById('step2-progress').classList.remove('hidden');
                        simulateStep2(drug);
                    }
                }, 200);
            }
            
            function simulateStep2(drug) {
                let progress = 0;
                const step2Bar = document.getElementById('step2-bar');
                const step2Percent = document.getElementById('step2-percent');
                const step2Text = document.getElementById('step2-text');
                const llm = document.getElementById('llm').value;
                const llmName = document.getElementById('llm').options[document.getElementById('llm').selectedIndex].text;
                
                addTerminalLine(`Initializing ${llmName} for analysis...`, 'terminal-response');
                
                const interval = setInterval(() => {
                    progress += Math.random() * 3;
                    if (progress > 100) progress = 100;
                    
                    step2Bar.style.width = `${progress}%`;
                    step2Percent.textContent = `${Math.floor(progress)}%`;
                    
                    if (progress < 20) {
                        step2Text.textContent = "Loading AI model...";
                    } else if (progress < 40) {
                        step2Text.textContent = "Analyzing study designs...";
                    } else if (progress < 60) {
                        step2Text.textContent = "Evaluating outcomes...";
                    } else if (progress < 80) {
                        step2Text.textContent = "Assessing relevance...";
                    } else if (progress < 100) {
                        step2Text.textContent = "Generating final report...";
                    } else {
                        step2Text.textContent = "Analysis complete!";
                    }
                    
                    // Add to terminal
                    if (progress === 20) {
                        addTerminalLine(`${llmName} model loaded successfully`, 'terminal-response');
                    } else if (progress === 40) {
                        addTerminalLine(`Analyzed study designs for 127 studies`, 'terminal-response');
                    } else if (progress === 60) {
                        addTerminalLine(`Identified 42 studies with relevant outcomes`, 'terminal-response');
                    } else if (progress === 80) {
                        addTerminalLine(`Narrowed down to 18 highly relevant studies`, 'terminal-response');
                    }
                    
                    if (progress === 100) {
                        clearInterval(interval);
                        addTerminalLine(`AI analysis completed for ${drug}`, 'terminal-response');
                        addTerminalLine(`========================================`, 'terminal-response');
                        addTerminalLine(`AI Analysis Summary:`, 'terminal-warning');
                        addTerminalLine(`- Total studies found: 127`, 'terminal-response');
                        addTerminalLine(`- Studies matching all criteria: 18`, 'terminal-response');
                        addTerminalLine(`- Most common conditions: Type 2 Diabetes (32%), Hypertension (24%)`, 'terminal-response');
                        addTerminalLine(`- Most frequent phase: Phase 3 (58% of studies)`, 'terminal-response');
                        addTerminalLine(`- Average sample size: 342 participants`, 'terminal-response');
                        addTerminalLine(`- Studies with published results: 9 (50%)`, 'terminal-response');
                        addTerminalLine(`========================================`, 'terminal-response');
                        addTerminalLine(`Top 5 most relevant studies:`, 'terminal-warning');
                        addTerminalLine(`1. NCT12345678 - "Efficacy of ${drug} in Type 2 Diabetes" (Phase 3, 2022)`, 'terminal-response');
                        addTerminalLine(`2. NCT23456789 - "${drug} vs. Standard Care in Hypertension" (Phase 3, 2021)`, 'terminal-response');
                        addTerminalLine(`3. NCT34567890 - "Long-term Safety of ${drug}" (Phase 4, 2023)`, 'terminal-response');
                        addTerminalLine(`4. NCT45678901 - "Dose Optimization Study of ${drug}" (Phase 2, 2020)`, 'terminal-response');
                        addTerminalLine(`5. NCT56789012 - "${drug} in Pediatric Population" (Phase 2/3, 2021)`, 'terminal-response');
                    }
                }, 300);
            }
            
            function addTerminalLine(text, className) {
                const line = document.createElement('div');
                line.className = `terminal-line ${className} fade-in`;
                line.textContent = text;
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
            }
            
            // Copy terminal content
            copyBtn.addEventListener('click', function() {
                const range = document.createRange();
                range.selectNode(terminal);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                document.execCommand('copy');
                window.getSelection().removeAllRanges();
                
                // Show copied notification
                const notification = document.createElement('div');
                notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg';
                notification.textContent = 'Copied to clipboard!';
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 2000);
            });
            
            // Clear terminal
            clearBtn.addEventListener('click', function() {
                terminal.innerHTML = `
                    <div class="terminal-line">ClinicalTrials.gov Scraper v1.0</div>
                    <div class="terminal-line">Terminal cleared. Ready for new search...</div>
                `;
            });
        });
    </script>
</body>
</html>