#!/usr/bin/env python3
"""
Test script to check adverse events extraction for NCT01106677
"""

import json
import os
from pathlib import Path

def test_adverse_events_extraction():
    """Test the adverse events extraction for NCT01106677"""
    
    study_dir = Path("output/canagliflozin/NCT01106677")
    results_posted_path = study_dir / "results_posted.json"
    
    if not results_posted_path.exists():
        print(f"❌ File not found: {results_posted_path}")
        return
    
    print(f"📁 Testing adverse events extraction for NCT01106677")
    print(f"🔍 Looking for 'fibrillation' in results_posted.json")
    
    with open(results_posted_path, 'r') as f:
        results_posted = json.load(f)
    
    # Extract adverse events using the new method
    adverse_events_list = []
    
    # Check for adverseEventsModule structure in resultsSection
    results_section = results_posted.get('resultsSection', {})
    adverse_events_module = results_section.get('adverseEventsModule', {})
    if adverse_events_module:
        print(f"✅ Found adverseEventsModule")
        print(f"📋 Keys in adverseEventsModule: {list(adverse_events_module.keys())}")

        # Extract serious adverse events
        serious_events = adverse_events_module.get('seriousEvents', [])
        print(f"📊 Found {len(serious_events)} serious events")
        for i, event in enumerate(serious_events):
            if 'term' in event:
                term = event['term']
                adverse_events_list.append(f"SERIOUS: {term}")
                if 'fibrillation' in term.lower():
                    print(f"🎯 FOUND FIBRILLATION IN SERIOUS EVENTS: {term}")
            else:
                print(f"⚠️  Serious event {i} missing 'term' field: {list(event.keys())}")

        # Extract other adverse events
        other_events = adverse_events_module.get('otherEvents', [])
        print(f"📊 Found {len(other_events)} other events")
        for i, event in enumerate(other_events):
            if 'term' in event:
                term = event['term']
                adverse_events_list.append(f"OTHER: {term}")
                if 'fibrillation' in term.lower():
                    print(f"🎯 FOUND FIBRILLATION IN OTHER EVENTS: {term}")
            else:
                print(f"⚠️  Other event {i} missing 'term' field: {list(event.keys())}")
    else:
        print(f"❌ No adverseEventsModule found")
        print(f"📋 Keys in results_posted: {list(results_posted.keys())}")
    
    print(f"\n📋 Total adverse events extracted: {len(adverse_events_list)}")
    
    # Check if fibrillation is in the list
    fibrillation_events = [event for event in adverse_events_list if 'fibrillation' in event.lower()]
    if fibrillation_events:
        print(f"✅ SUCCESS: Found {len(fibrillation_events)} fibrillation-related events:")
        for event in fibrillation_events:
            print(f"   - {event}")
    else:
        print(f"❌ FAILED: No fibrillation events found in extracted list")
    
    # Show first 10 events for debugging
    print(f"\n📝 First 10 extracted events:")
    for i, event in enumerate(adverse_events_list[:10]):
        print(f"   {i+1}. {event}")

if __name__ == "__main__":
    test_adverse_events_extraction()
