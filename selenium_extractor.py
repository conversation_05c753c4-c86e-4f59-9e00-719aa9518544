#!/usr/bin/env python3
"""
Script to extract adverse events table from ClinicalTrials.gov using Selenium
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import sys

def setup_driver():
    """Set up Chrome driver with appropriate options"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in headless mode
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error setting up Chrome driver: {e}")
        print("Make sure ChromeDriver is installed and in PATH")
        return None

def extract_adverse_events_selenium(url):
    """
    Extract adverse events table using Selenium
    """
    driver = setup_driver()
    if not driver:
        return None
    
    try:
        print(f"Loading URL: {url}")
        driver.get(url)
        
        # Wait for page to load
        wait = WebDriverWait(driver, 20)
        
        # Wait for the page to be fully loaded
        print("Waiting for page to load...")
        time.sleep(5)
        
        # Save page source for debugging
        with open('selenium_page_source.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("Saved page source to selenium_page_source.html")
        
        # Look for adverse events content
        adverse_events_data = []
        
        # Try different selectors for adverse events
        selectors_to_try = [
            "//table[contains(@summary, 'adverse') or contains(@summary, 'Adverse')]",
            "//table[contains(., 'Adverse Events') or contains(., 'adverse events')]",
            "//div[contains(@class, 'adverse') or contains(@id, 'adverse')]//table",
            "//section[contains(., 'Adverse') or contains(., 'Safety')]//table",
            "//div[contains(., 'Adverse Events')]//table",
            "//table",  # Get all tables as fallback
        ]
        
        tables_found = []
        
        for i, selector in enumerate(selectors_to_try):
            try:
                print(f"Trying selector {i+1}: {selector}")
                tables = driver.find_elements(By.XPATH, selector)
                print(f"Found {len(tables)} tables with selector {i+1}")
                
                for j, table in enumerate(tables):
                    table_text = table.text.lower()
                    if any(keyword in table_text for keyword in ['adverse', 'safety', 'side effect', 'toxicity']):
                        print(f"Table {j+1} from selector {i+1} contains adverse events keywords")
                        tables_found.append((f"selector-{i+1}-table-{j+1}", table))
                    elif selector == "//table":  # For fallback, include all tables
                        print(f"Including table {j+1} from fallback selector")
                        tables_found.append((f"fallback-table-{j+1}", table))
                        
            except Exception as e:
                print(f"Error with selector {i+1}: {e}")
                continue
        
        # Process found tables
        for table_id, table in tables_found:
            try:
                print(f"\nProcessing {table_id}:")
                
                # Get table HTML for more detailed parsing
                table_html = table.get_attribute('outerHTML')
                
                # Try to extract table data
                rows = table.find_elements(By.TAG_NAME, "tr")
                print(f"Found {len(rows)} rows")
                
                if len(rows) == 0:
                    continue
                
                # Extract headers
                header_cells = rows[0].find_elements(By.TAG_NAME, "th")
                if not header_cells:
                    header_cells = rows[0].find_elements(By.TAG_NAME, "td")
                
                headers = [cell.text.strip() for cell in header_cells]
                print(f"Headers: {headers}")
                
                # Extract data rows
                data_rows = []
                for row in rows[1:]:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if not cells:
                        cells = row.find_elements(By.TAG_NAME, "th")
                    
                    row_data = [cell.text.strip() for cell in cells]
                    if any(cell for cell in row_data):  # Only add non-empty rows
                        data_rows.append(row_data)
                
                print(f"Found {len(data_rows)} data rows")
                
                if data_rows and headers:
                    # Create DataFrame
                    max_cols = max(len(headers), max(len(row) for row in data_rows) if data_rows else 0)
                    headers_padded = headers + [f'Column_{i}' for i in range(len(headers), max_cols)]
                    rows_padded = [row + [''] * (max_cols - len(row)) for row in data_rows]
                    
                    df = pd.DataFrame(rows_padded, columns=headers_padded)
                    adverse_events_data.append({
                        'table_id': table_id,
                        'dataframe': df,
                        'html': table_html
                    })
                    
                    print(f"Successfully processed {table_id}")
                    print(f"DataFrame shape: {df.shape}")
                    print("First few rows:")
                    print(df.head())
                    
            except Exception as e:
                print(f"Error processing {table_id}: {e}")
                continue
        
        return adverse_events_data
        
    except Exception as e:
        print(f"Error during extraction: {e}")
        return None
    finally:
        driver.quit()

def main():
    url = "https://clinicaltrials.gov/study/NCT02673138?intr=Canagliflozin&aggFilters=results:with,status:com&viewType=Card&rank=1&tab=results"
    
    print("Extracting adverse events table using Selenium...")
    
    adverse_events_data = extract_adverse_events_selenium(url)
    
    if adverse_events_data:
        print(f"\nFound {len(adverse_events_data)} potential adverse events tables:")
        
        for data in adverse_events_data:
            table_id = data['table_id']
            df = data['dataframe']
            
            print(f"\n=== {table_id} ===")
            print(f"Shape: {df.shape}")
            print("\nFirst few rows:")
            print(df.head())
            
            # Save to CSV
            filename = f"adverse_events_{table_id}.csv"
            df.to_csv(filename, index=False)
            print(f"Saved to: {filename}")
            
            # Save HTML for reference
            html_filename = f"table_{table_id}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(data['html'])
            print(f"Saved HTML to: {html_filename}")
    else:
        print("No adverse events tables found or error occurred")

if __name__ == "__main__":
    main()
