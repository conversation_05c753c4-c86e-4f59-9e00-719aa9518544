#!/usr/bin/env python3
"""
AI Analysis: Use LLM to analyze study JSON files based on search criteria
"""

import json
import os
import requests
from pathlib import Path
import sys

def load_study_data(study_folder):
    """Load all JSON files from a study folder"""
    study_data = {}
    json_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json']
    
    for json_file in json_files:
        file_path = study_folder / json_file
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    study_data[json_file.replace('.json', '')] = json.load(f)
            except Exception as e:
                print(f"  ⚠️  Error loading {json_file}: {e}")
                study_data[json_file.replace('.json', '')] = None
        else:
            study_data[json_file.replace('.json', '')] = None
    
    return study_data

def create_analysis_prompt(study_data, search_criteria, nct_id):
    """Create a prompt for the LLM to analyze the study"""
    
    # Extract key information for analysis
    study_details = study_data.get('study_details', {})
    researcher_view = study_data.get('researcher_view', {})
    results_posted = study_data.get('results_posted', {})
    
    # Get basic study info
    protocol_section = study_details.get('protocolSection', {}) if study_details else {}
    identification = protocol_section.get('identificationModule', {})
    description = protocol_section.get('descriptionModule', {})
    conditions = protocol_section.get('conditionsModule', {})
    design = protocol_section.get('designModule', {})
    
    brief_title = identification.get('briefTitle', 'N/A')
    official_title = identification.get('officialTitle', 'N/A')
    brief_summary = description.get('briefSummary', 'N/A')
    detailed_description = description.get('detailedDescription', 'N/A')
    conditions_list = conditions.get('conditions', [])
    study_type = design.get('studyType', 'N/A')
    
    prompt = f"""
You are analyzing clinical trial study {nct_id} to determine if it matches the following search criteria:

SEARCH CRITERIA: {search_criteria}

STUDY INFORMATION:
- NCT ID: {nct_id}
- Brief Title: {brief_title}
- Official Title: {official_title}
- Study Type: {study_type}
- Conditions: {', '.join(conditions_list) if conditions_list else 'N/A'}

BRIEF SUMMARY:
{brief_summary}

DETAILED DESCRIPTION:
{detailed_description[:2000]}{'...' if len(str(detailed_description)) > 2000 else ''}

TASK:
Analyze this study and determine if it matches ALL the keywords/criteria specified in the search criteria.

Respond with ONLY a JSON object in this exact format:
{{
    "matches": true/false,
    "confidence": 0.0-1.0,
    "matching_aspects": ["list", "of", "matching", "aspects"],
    "reasoning": "Brief explanation of why it matches or doesn't match"
}}

Do not include any other text or formatting.
"""
    
    return prompt

def query_ollama(prompt, model_name):
    """Query Ollama with the analysis prompt"""
    try:
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': model_name,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.1,  # Low temperature for consistent analysis
                    'top_p': 0.9
                }
            },
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '').strip()
        else:
            return f"Error: HTTP {response.status_code}"
            
    except requests.exceptions.ConnectionError:
        return "Error: Cannot connect to Ollama. Make sure Ollama is running."
    except requests.exceptions.Timeout:
        return "Error: Ollama request timed out."
    except Exception as e:
        return f"Error: {str(e)}"

def parse_llm_response(response_text):
    """Parse the LLM response and extract JSON"""
    try:
        # Try to find JSON in the response
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return {
                "matches": False,
                "confidence": 0.0,
                "matching_aspects": [],
                "reasoning": f"Could not parse LLM response: {response_text}"
            }
    except json.JSONDecodeError:
        return {
            "matches": False,
            "confidence": 0.0,
            "matching_aspects": [],
            "reasoning": f"Invalid JSON in LLM response: {response_text}"
        }

def analyze_studies(search_criteria, llm_model, studies_dir="studies"):
    """Analyze all studies in the studies directory"""
    
    print(f"🤖 AI ANALYSIS STARTING")
    print(f"Search Criteria: {search_criteria}")
    print(f"LLM Model: {llm_model}")
    print(f"Studies Directory: {studies_dir}")
    print("=" * 60)
    
    studies_path = Path(studies_dir)
    if not studies_path.exists():
        print(f"❌ Studies directory '{studies_dir}' not found!")
        return []
    
    # Get all study folders (NCT directories)
    study_folders = [d for d in studies_path.iterdir() if d.is_dir() and d.name.startswith('NCT')]
    
    if not study_folders:
        print(f"❌ No study folders found in '{studies_dir}'!")
        return []
    
    print(f"📁 Found {len(study_folders)} study folders")
    
    matching_studies = []
    analysis_results = []
    
    for i, study_folder in enumerate(study_folders, 1):
        nct_id = study_folder.name
        print(f"\n[{i:2d}/{len(study_folders)}] Analyzing {nct_id}...", flush=True)

        # Load study data
        study_data = load_study_data(study_folder)

        # Check if we have the required data
        if not study_data.get('study_details'):
            print(f"  ❌ Missing study_details.json - skipping", flush=True)
            continue

        # Create analysis prompt
        prompt = create_analysis_prompt(study_data, search_criteria, nct_id)

        # Query LLM
        print(f"  🤖 Querying {llm_model}...", flush=True)
        llm_response = query_ollama(prompt, llm_model)

        if llm_response.startswith("Error:"):
            print(f"  ❌ {llm_response}", flush=True)
            continue
        
        # Parse response
        analysis = parse_llm_response(llm_response)
        
        # Add to results
        result = {
            'nct_id': nct_id,
            'analysis': analysis,
            'study_title': study_data.get('study_details', {}).get('protocolSection', {}).get('identificationModule', {}).get('briefTitle', 'N/A')
        }
        analysis_results.append(result)
        
        # Check if it matches
        if analysis.get('matches', False):
            matching_studies.append(result)
            confidence = analysis.get('confidence', 0.0)
            print(f"  ✅ MATCH (confidence: {confidence:.2f}) - {analysis.get('reasoning', '')}", flush=True)
        else:
            print(f"  ❌ No match - {analysis.get('reasoning', '')}", flush=True)
    
    return matching_studies, analysis_results

def main():
    if len(sys.argv) < 3:
        print("Usage: python ai_analyze_studies.py '<search_criteria>' '<llm_model>'")
        print("Example: python ai_analyze_studies.py 'diabetes cardiovascular outcomes' 'llama3.1:latest'")
        return
    
    search_criteria = sys.argv[1]
    llm_model = sys.argv[2]
    
    # Run analysis
    matching_studies, all_results = analyze_studies(search_criteria, llm_model)
    
    # Summary
    print(f"\n{'='*60}")
    print("AI ANALYSIS SUMMARY")
    print(f"{'='*60}")
    print(f"Total studies analyzed: {len(all_results)}")
    print(f"Studies matching criteria: {len(matching_studies)}")
    print(f"Match rate: {len(matching_studies)/len(all_results)*100:.1f}%" if all_results else "0%")
    
    if matching_studies:
        print(f"\n✅ MATCHING STUDIES:")
        for i, study in enumerate(matching_studies, 1):
            confidence = study['analysis'].get('confidence', 0.0)
            print(f"{i:2d}. {study['nct_id']} (confidence: {confidence:.2f})")
            print(f"    {study['study_title']}")
            print(f"    Reasoning: {study['analysis'].get('reasoning', 'N/A')}")
    
    # Save results
    results_summary = {
        'search_criteria': search_criteria,
        'llm_model': llm_model,
        'total_analyzed': len(all_results),
        'total_matching': len(matching_studies),
        'match_rate': len(matching_studies)/len(all_results)*100 if all_results else 0,
        'matching_studies': matching_studies,
        'all_results': all_results
    }
    
    with open('ai_analysis_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: ai_analysis_results.json")

if __name__ == "__main__":
    main()
