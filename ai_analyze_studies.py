#!/usr/bin/env python3
"""
AI Analysis: Use LLM to analyze study JSON files based on search criteria
"""

import json
import os
import requests
from pathlib import Path
import sys

def load_study_data(study_folder):
    """Load all JSON files from a study folder"""
    study_data = {}
    json_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json']
    
    for json_file in json_files:
        file_path = study_folder / json_file
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    study_data[json_file.replace('.json', '')] = json.load(f)
            except Exception as e:
                print(f"  ⚠️  Error loading {json_file}: {e}")
                study_data[json_file.replace('.json', '')] = None
        else:
            study_data[json_file.replace('.json', '')] = None
    
    return study_data

def create_analysis_prompt(study_data, search_criteria, nct_id):
    """Create a prompt for the LLM to analyze the study using researcher_view and results_posted data"""

    # Extract key information from researcher_view and results_posted only
    researcher_view = study_data.get('researcher_view', {})
    results_posted = study_data.get('results_posted', {})

    # Extract from researcher_view.json (nested structure)
    full_study_data = researcher_view.get('fullStudyData', {})
    protocol_section = full_study_data.get('protocolSection', {})
    identification_module = protocol_section.get('identificationModule', {})
    description_module = protocol_section.get('descriptionModule', {})
    conditions_module = protocol_section.get('conditionsModule', {})
    design_module = protocol_section.get('designModule', {})
    status_module = protocol_section.get('statusModule', {})
    eligibility_module = protocol_section.get('eligibilityModule', {})
    outcomes_module = protocol_section.get('outcomesModule', {})

    brief_title = identification_module.get('briefTitle', 'N/A')
    official_title = identification_module.get('officialTitle', 'N/A')
    brief_summary = description_module.get('briefSummary', 'N/A')
    detailed_description = description_module.get('detailedDescription', 'N/A')
    conditions_list = conditions_module.get('conditions', [])
    interventions_list = protocol_section.get('armsInterventionsModule', {}).get('interventions', [])
    study_type = design_module.get('studyType', 'N/A')
    phase = design_module.get('phases', ['N/A'])
    overall_status = status_module.get('overallStatus', 'N/A')
    enrollment = design_module.get('enrollmentInfo', {}).get('count', 'N/A')
    eligibility_criteria = eligibility_module.get('eligibilityCriteria', 'N/A')
    primary_outcomes = outcomes_module.get('primaryOutcomes', [])
    secondary_outcomes = outcomes_module.get('secondaryOutcomes', [])

    # Extract from results_posted.json
    outcome_measures = results_posted.get('outcome_measures', []) if results_posted else []
    results_summary = results_posted.get('results_summary', 'N/A') if results_posted else 'N/A'

    # Extract adverse events from the complex JSON structure
    adverse_events_list = []
    if results_posted:
        # Check for adverseEventsModule structure in resultsSection
        results_section = results_posted.get('resultsSection', {})
        adverse_events_module = results_section.get('adverseEventsModule', {})

        if adverse_events_module:
            # Extract serious adverse events
            serious_events = adverse_events_module.get('seriousEvents', [])
            for event in serious_events:
                if 'term' in event:
                    adverse_events_list.append(f"SERIOUS: {event['term']}")

            # Extract other adverse events
            other_events = adverse_events_module.get('otherEvents', [])
            for event in other_events:
                if 'term' in event:
                    adverse_events_list.append(f"OTHER: {event['term']}")

        # Also check for simple adverse_events array (fallback)
        simple_adverse_events = results_posted.get('adverse_events', [])
        if simple_adverse_events:
            adverse_events_list.extend(simple_adverse_events)
    
    # Format outcome measures
    outcome_measures_text = ""
    if primary_outcomes:
        outcome_measures_text += "PRIMARY OUTCOMES:\n"
        for outcome in primary_outcomes:
            outcome_measures_text += f"- {outcome}\n"
    if secondary_outcomes:
        outcome_measures_text += "SECONDARY OUTCOMES:\n"
        for outcome in secondary_outcomes:
            outcome_measures_text += f"- {outcome}\n"
    if outcome_measures:
        outcome_measures_text += "RESULTS OUTCOME MEASURES:\n"
        for outcome in outcome_measures:
            outcome_measures_text += f"- {outcome}\n"

    # Format adverse events
    adverse_events_text = ""
    if adverse_events_list:
        adverse_events_text = "ADVERSE EVENTS:\n"
        for event in adverse_events_list:
            adverse_events_text += f"- {event}\n"
    else:
        adverse_events_text = "No adverse events data available"

    # Parse search criteria into individual terms
    search_terms = [term.strip().lower() for term in search_criteria.split(',')]

    prompt = f"""
You are analyzing clinical trial study {nct_id} to determine if it matches the search criteria.

SEARCH CRITERIA: {search_criteria}

=== OUTCOME MEASURES ===
{outcome_measures_text if outcome_measures_text else 'No outcome measures available'}

=== TRIAL DESCRIPTION ===
Brief Title: {brief_title}
Official Title: {official_title}
Brief Summary: {brief_summary}
Detailed Description: {detailed_description[:1500]}{'...' if len(str(detailed_description)) > 1500 else ''}
Conditions: {', '.join(conditions_list) if conditions_list else 'N/A'}
Interventions: {', '.join([str(i) for i in interventions_list]) if interventions_list else 'N/A'}
Study Type: {study_type}
Phase: {phase}

=== RECRUITMENT INFORMATION ===
Overall Status: {overall_status}
Enrollment: {enrollment}
Eligibility Criteria: {eligibility_criteria[:1000]}{'...' if len(str(eligibility_criteria)) > 1000 else ''}

=== ADVERSE EVENTS ===
{adverse_events_text}

INSTRUCTIONS:
1. You must search for ONLY these exact terms: {search_criteria}
2. Do NOT search for any other terms not listed in the search criteria
3. In matching_aspects, list ONLY the search terms from the criteria that were found
4. In reasoning, mention ONLY the search terms from the criteria, never use other terms

SEARCH TERMS TO LOOK FOR: {search_criteria}

Respond with ONLY a JSON object:
{{
    "matches": true/false,
    "confidence": 0.0-1.0,
    "matching_aspects": ["only_search_terms_from_criteria"],
    "reasoning": "Found [search_term] in [location]: 'exact quote'"
}}

Do not include any other text or formatting.
"""
    
    return prompt

def query_ollama(prompt, model_name):
    """Query Ollama with the analysis prompt"""
    # Set longer timeout for reasoning models like deepseek-r1
    timeout_seconds = 300 if 'deepseek-r1' in model_name else 120
    max_retries = 2

    for attempt in range(max_retries):
        try:
            print(f"  🤖 Querying {model_name}... (attempt {attempt + 1}/{max_retries}, timeout: {timeout_seconds}s)", flush=True)

            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': 0.1,  # Low temperature for consistent analysis
                        'top_p': 0.9,
                        'num_predict': 500  # Limit response length to avoid very long outputs
                    }
                },
                timeout=timeout_seconds
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '').strip()
            else:
                print(f"  ⚠️  HTTP {response.status_code} on attempt {attempt + 1}", flush=True)
                if attempt == max_retries - 1:
                    return f"Error: HTTP {response.status_code}"

        except requests.exceptions.ConnectionError:
            return "Error: Cannot connect to Ollama. Make sure Ollama is running."
        except requests.exceptions.Timeout:
            print(f"  ⏰ Timeout on attempt {attempt + 1} ({timeout_seconds}s)", flush=True)
            if attempt == max_retries - 1:
                return "Error: Ollama request timed out after multiple attempts."
        except Exception as e:
            print(f"  ❌ Unexpected error on attempt {attempt + 1}: {e}", flush=True)
            if attempt == max_retries - 1:
                return f"Error: {str(e)}"

    return "Error: All retry attempts failed."

def parse_llm_response(response_text):
    """Parse the LLM response and extract JSON"""
    try:
        # Try to find JSON in the response
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return {
                "matches": False,
                "confidence": 0.0,
                "matching_aspects": [],
                "reasoning": f"Could not parse LLM response: {response_text}"
            }
    except json.JSONDecodeError:
        return {
            "matches": False,
            "confidence": 0.0,
            "matching_aspects": [],
            "reasoning": f"Invalid JSON in LLM response: {response_text}"
        }

def analyze_studies(search_criteria, llm_model, studies_dir="studies"):
    """Analyze all studies in the studies directory"""
    
    print(f"🤖 AI ANALYSIS STARTING")
    print(f"Search Criteria: {search_criteria}")
    print(f"LLM Model: {llm_model}")
    print(f"Studies Directory: {studies_dir}")
    print("=" * 60)
    
    studies_path = Path(studies_dir)
    if not studies_path.exists():
        print(f"❌ Studies directory '{studies_dir}' not found!")
        return []
    
    # Get all study folders (NCT directories)
    study_folders = [d for d in studies_path.iterdir() if d.is_dir() and d.name.startswith('NCT')]
    
    if not study_folders:
        print(f"❌ No study folders found in '{studies_dir}'!")
        return []
    
    print(f"📁 Found {len(study_folders)} study folders")
    
    matching_studies = []
    analysis_results = []
    
    for i, study_folder in enumerate(study_folders, 1):
        nct_id = study_folder.name
        print(f"\n[{i:2d}/{len(study_folders)}] Analyzing {nct_id}...", flush=True)

        # Load study data
        study_data = load_study_data(study_folder)

        # Check if we have the required data
        if not study_data.get('study_details'):
            print(f"  ❌ Missing study_details.json - skipping", flush=True)
            continue

        # Create analysis prompt
        prompt = create_analysis_prompt(study_data, search_criteria, nct_id)

        # Query LLM
        print(f"  🤖 Querying {llm_model}...", flush=True)
        llm_response = query_ollama(prompt, llm_model)

        if llm_response.startswith("Error:"):
            print(f"  ❌ {llm_response}", flush=True)
            continue
        
        # Parse response
        analysis = parse_llm_response(llm_response)
        
        # Add to results
        result = {
            'nct_id': nct_id,
            'analysis': analysis,
            'study_title': study_data.get('study_details', {}).get('protocolSection', {}).get('identificationModule', {}).get('briefTitle', 'N/A')
        }
        analysis_results.append(result)
        
        # Check if it matches
        if analysis.get('matches', False):
            matching_studies.append(result)
            confidence = analysis.get('confidence', 0.0)
            print(f"  ✅ MATCH (confidence: {confidence:.2f}) - {analysis.get('reasoning', '')}", flush=True)
        else:
            print(f"  ❌ No match - {analysis.get('reasoning', '')}", flush=True)
    
    return matching_studies, analysis_results

def main():
    import argparse

    parser = argparse.ArgumentParser(description='AI Analysis of Clinical Studies')
    parser.add_argument('search_criteria', help='Search criteria for analysis')
    parser.add_argument('llm_model', help='LLM model to use')
    parser.add_argument('--studies-dir', default='studies', help='Directory containing study data')

    args = parser.parse_args()

    # Run analysis
    matching_studies, all_results = analyze_studies(args.search_criteria, args.llm_model, args.studies_dir)
    
    # Summary
    print(f"\n{'='*60}")
    print("AI ANALYSIS SUMMARY")
    print(f"{'='*60}")
    print(f"Total studies analyzed: {len(all_results)}")
    print(f"Studies matching criteria: {len(matching_studies)}")
    print(f"Match rate: {len(matching_studies)/len(all_results)*100:.1f}%" if all_results else "0%")
    
    if matching_studies:
        print(f"\n✅ MATCHING STUDIES:")
        for i, study in enumerate(matching_studies, 1):
            confidence = study['analysis'].get('confidence', 0.0)
            print(f"{i:2d}. {study['nct_id']} (confidence: {confidence:.2f})")
            print(f"    {study['study_title']}")
            print(f"    Reasoning: {study['analysis'].get('reasoning', 'N/A')}")
    
    # Save results
    results_summary = {
        'search_criteria': args.search_criteria,
        'llm_model': args.llm_model,
        'total_analyzed': len(all_results),
        'total_matching': len(matching_studies),
        'match_rate': len(matching_studies)/len(all_results)*100 if all_results else 0,
        'matching_studies': matching_studies,
        'all_results': all_results
    }
    
    with open('ai_analysis_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: ai_analysis_results.json")

if __name__ == "__main__":
    main()
