#!/usr/bin/env python3
"""
Test ClinicalTrials.gov API v2 search capabilities
"""

import requests
import json

def test_api_search():
    """
    Test different API endpoints to find studies with filters
    """
    
    # Test different API search endpoints
    api_urls = [
        # API v2 studies search
        "https://clinicaltrials.gov/api/v2/studies?query.term=Canagliflozin&filter.overallStatus=COMPLETED&filter.hasResults=true&format=json",
        "https://clinicaltrials.gov/api/v2/studies?query.intr=Canagliflozin&filter.overallStatus=COMPLETED&filter.hasResults=true&format=json",
        "https://clinicaltrials.gov/api/v2/studies?filter.intervention=Canagliflozin&filter.overallStatus=COMPLETED&filter.hasResults=true&format=json",
        
        # Try with different parameter names
        "https://clinicaltrials.gov/api/v2/studies?query.term=Canagliflozin&pageSize=1000&format=json",
        "https://clinicaltrials.gov/api/v2/studies?query.intervention=Canagliflozin&format=json",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    for i, url in enumerate(api_urls):
        print(f"\n{'='*60}")
        print(f"Testing API URL {i+1}:")
        print(f"URL: {url}")
        print(f"{'='*60}")
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("✅ Successfully retrieved JSON data")
                    
                    # Save response for inspection
                    filename = f"api_test_response_{i+1}.json"
                    with open(filename, 'w') as f:
                        json.dump(data, f, indent=2)
                    print(f"Saved response to {filename}")
                    
                    # Analyze the response structure
                    if isinstance(data, dict):
                        print(f"Response keys: {list(data.keys())}")
                        
                        if 'studies' in data:
                            studies = data['studies']
                            print(f"Number of studies found: {len(studies)}")
                            
                            if studies:
                                print("First study NCT ID:", studies[0].get('protocolSection', {}).get('identificationModule', {}).get('nctId', 'N/A'))
                        
                        if 'totalCount' in data:
                            print(f"Total count: {data['totalCount']}")
                        
                        if 'nextPageToken' in data:
                            print("Has next page token (pagination available)")
                    
                    return True  # Found a working endpoint
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON: {e}")
                    print("Response content:", response.text[:500])
            else:
                print(f"❌ HTTP error: {response.status_code}")
                print("Response:", response.text[:500])
                
        except requests.RequestException as e:
            print(f"❌ Request failed: {e}")
    
    return False

def test_crawl4ai_approach():
    """
    Test using crawl4ai to scrape the search results page
    """
    print(f"\n{'='*60}")
    print("Testing crawl4ai approach:")
    print(f"{'='*60}")
    
    # The search URL with filters applied
    search_url = "https://clinicaltrials.gov/search?intr=Canagliflozin&aggFilters=results:with,status:com"
    
    print(f"Search URL: {search_url}")
    
    try:
        import asyncio
        from crawl4ai import AsyncWebCrawler
        
        async def crawl_search_page():
            async with AsyncWebCrawler(verbose=True) as crawler:
                result = await crawler.arun(
                    url=search_url,
                    word_count_threshold=10,
                    bypass_cache=True,
                    js_code="""
                    // Wait for page to load
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    
                    // Look for study count and study links
                    const studyCount = document.querySelector('[data-testid="results-count"]') || 
                                     document.querySelector('.results-count') ||
                                     document.querySelector('*[class*="count"]');
                    
                    if (studyCount) {
                        console.log('Found study count:', studyCount.textContent);
                    }
                    
                    // Look for study links (NCT numbers)
                    const studyLinks = document.querySelectorAll('a[href*="/study/NCT"]');
                    console.log('Found study links:', studyLinks.length);
                    
                    // Scroll to load more results if needed
                    window.scrollTo(0, document.body.scrollHeight);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    """,
                    wait_for="networkidle"
                )
                
                if result.success:
                    print("✅ Successfully crawled search page")
                    
                    # Save the HTML for inspection
                    with open('search_page.html', 'w', encoding='utf-8') as f:
                        f.write(result.html)
                    print("Saved search page HTML to search_page.html")
                    
                    # Look for NCT numbers in the HTML
                    import re
                    nct_pattern = r'NCT\d{8}'
                    nct_matches = re.findall(nct_pattern, result.html)
                    unique_ncts = list(set(nct_matches))
                    
                    print(f"Found {len(unique_ncts)} unique NCT numbers")
                    if unique_ncts:
                        print("Sample NCT numbers:", unique_ncts[:5])
                    
                    return unique_ncts
                else:
                    print(f"❌ Failed to crawl search page: {result.error_message}")
                    return []
        
        # Run the async function
        nct_numbers = asyncio.run(crawl_search_page())
        return nct_numbers
        
    except ImportError:
        print("❌ crawl4ai not available")
        return []
    except Exception as e:
        print(f"❌ Error with crawl4ai approach: {e}")
        return []

def main():
    print("🔍 TESTING CLINICALTRIALS.GOV SEARCH METHODS")
    print("=" * 60)
    
    # Test API approach first
    print("\n📡 TESTING API APPROACH")
    api_success = test_api_search()
    
    # Test crawl4ai approach
    print("\n🕷️ TESTING CRAWL4AI APPROACH")
    nct_numbers = test_crawl4ai_approach()
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    print(f"API approach: {'✅ Success' if api_success else '❌ Failed'}")
    print(f"crawl4ai approach: {'✅ Success' if nct_numbers else '❌ Failed'}")
    
    if nct_numbers:
        print(f"Found {len(nct_numbers)} studies with crawl4ai")
    
    print("\nRecommendation:")
    if api_success:
        print("✅ Use API approach - more reliable and structured")
    elif nct_numbers:
        print("✅ Use crawl4ai approach - API not working but crawling successful")
    else:
        print("❌ Both approaches failed - need alternative method")

if __name__ == "__main__":
    main()
