#!/usr/bin/env python3
"""
Test search and filtering only (no extraction)
"""

import requests
import json

def search_and_filter_studies():
    """
    Search and filter studies to see how many match our criteria
    """
    print("🔍 Searching for Canagliflozin studies...")
    
    # Search for all Canagliflozin studies
    api_url = "https://clinicaltrials.gov/api/v2/studies?query.term=Canagliflozin&pageSize=1000&format=json"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    try:
        response = requests.get(api_url, headers=headers, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            studies = data.get('studies', [])
            print(f"✅ Found {len(studies)} total studies")
            
            # Filter studies
            completed_with_results = []
            completed_without_results = []
            not_completed = []
            
            for study in studies:
                try:
                    protocol_section = study.get('protocolSection', {})
                    results_section = study.get('resultsSection', {})
                    
                    nct_id = protocol_section.get('identificationModule', {}).get('nctId', '')
                    status = protocol_section.get('statusModule', {}).get('overallStatus', '')
                    brief_title = protocol_section.get('identificationModule', {}).get('briefTitle', '')
                    
                    is_completed = status == 'COMPLETED'
                    has_results = bool(results_section)
                    
                    if is_completed and has_results:
                        completed_with_results.append({
                            'nct_id': nct_id,
                            'title': brief_title,
                            'status': status
                        })
                    elif is_completed and not has_results:
                        completed_without_results.append({
                            'nct_id': nct_id,
                            'title': brief_title,
                            'status': status
                        })
                    else:
                        not_completed.append({
                            'nct_id': nct_id,
                            'title': brief_title,
                            'status': status
                        })
                        
                except Exception as e:
                    print(f"Error processing study: {e}")
                    continue
            
            # Print results
            print(f"\n📊 FILTERING RESULTS:")
            print(f"Total studies: {len(studies)}")
            print(f"Completed with results: {len(completed_with_results)}")
            print(f"Completed without results: {len(completed_without_results)}")
            print(f"Not completed: {len(not_completed)}")
            
            print(f"\n✅ STUDIES MATCHING CRITERIA ({len(completed_with_results)}):")
            for i, study in enumerate(completed_with_results, 1):
                print(f"{i:2d}. {study['nct_id']}: {study['title'][:60]}...")
            
            # Save the matching studies
            with open('matching_studies.json', 'w') as f:
                json.dump(completed_with_results, f, indent=2)
            print(f"\nSaved {len(completed_with_results)} matching studies to matching_studies.json")
            
            return completed_with_results
            
        else:
            print(f"❌ API error: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

if __name__ == "__main__":
    matching_studies = search_and_filter_studies()
    print(f"\n🎯 Found {len(matching_studies)} studies that match all criteria!")
