#!/usr/bin/env python3
"""
Process the adverse events data into readable tables
"""

import json
import pandas as pd

def process_adverse_events_data():
    """
    Process the adverse events JSON data into structured tables
    """
    
    # Load the adverse events data
    with open('adverse_events_api_v2.json', 'r') as f:
        adverse_events = json.load(f)
    
    print("=== ADVERSE EVENTS DATA SUMMARY ===")
    print(f"Frequency Threshold: {adverse_events.get('frequencyThreshold', 'Not specified')}%")
    print()
    
    # Process event groups (treatment arms)
    event_groups = adverse_events.get('eventGroups', [])
    print("=== TREATMENT GROUPS ===")
    
    groups_data = []
    for group in event_groups:
        groups_data.append({
            'Group ID': group.get('id', ''),
            'Title': group.get('title', ''),
            'Description': group.get('description', '').replace('\n', ' '),
            'Deaths (Affected/At Risk)': f"{group.get('deathsNumAffected', 0)}/{group.get('deathsNumAtRisk', 0)}",
            'Serious AEs (Affected/At Risk)': f"{group.get('seriousNumAffected', 0)}/{group.get('seriousNumAtRisk', 0)}",
            'Other AEs (Affected/At Risk)': f"{group.get('otherNumAffected', 0)}/{group.get('otherNumAtRisk', 0)}"
        })
    
    groups_df = pd.DataFrame(groups_data)
    print(groups_df.to_string(index=False))
    print()
    
    # Save groups table
    groups_df.to_csv('treatment_groups.csv', index=False)
    print("Saved treatment groups to treatment_groups.csv")
    print()
    
    # Process serious adverse events if available
    serious_events = adverse_events.get('seriousEvents', [])
    if serious_events:
        print("=== SERIOUS ADVERSE EVENTS ===")
        serious_data = []
        for event in serious_events:
            for stat in event.get('stats', []):
                group_title = next((g['title'] for g in event_groups if g['id'] == stat['groupId']), stat['groupId'])
                serious_data.append({
                    'Event Term': event.get('term', ''),
                    'Organ System': event.get('organSystem', ''),
                    'Assessment Type': event.get('assessmentType', ''),
                    'Treatment Group': group_title,
                    'Number of Events': stat.get('numEvents', 0),
                    'Number Affected': stat.get('numAffected', 0),
                    'Number at Risk': stat.get('numAtRisk', 0),
                    'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
                })
        
        if serious_data:
            serious_df = pd.DataFrame(serious_data)
            print(serious_df.to_string(index=False))
            serious_df.to_csv('serious_adverse_events.csv', index=False)
            print("Saved serious adverse events to serious_adverse_events.csv")
        print()
    
    # Process other adverse events
    other_events = adverse_events.get('otherEvents', [])
    if other_events:
        print("=== OTHER ADVERSE EVENTS ===")
        other_data = []
        for event in other_events:
            for stat in event.get('stats', []):
                group_title = next((g['title'] for g in event_groups if g['id'] == stat['groupId']), stat['groupId'])
                other_data.append({
                    'Event Term': event.get('term', ''),
                    'Organ System': event.get('organSystem', ''),
                    'Assessment Type': event.get('assessmentType', ''),
                    'Treatment Group': group_title,
                    'Number of Events': stat.get('numEvents', 0),
                    'Number Affected': stat.get('numAffected', 0),
                    'Number at Risk': stat.get('numAtRisk', 0),
                    'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
                })
        
        if other_data:
            other_df = pd.DataFrame(other_data)
            print(other_df.to_string(index=False))
            other_df.to_csv('other_adverse_events.csv', index=False)
            print("Saved other adverse events to other_adverse_events.csv")
        print()
    
    # Create a comprehensive adverse events table
    all_adverse_events = []
    
    # Add serious events
    for event in serious_events:
        for stat in event.get('stats', []):
            group_title = next((g['title'] for g in event_groups if g['id'] == stat['groupId']), stat['groupId'])
            all_adverse_events.append({
                'Event Category': 'Serious',
                'Event Term': event.get('term', ''),
                'Organ System': event.get('organSystem', ''),
                'Assessment Type': event.get('assessmentType', ''),
                'Treatment Group': group_title,
                'Number of Events': stat.get('numEvents', 0),
                'Number Affected': stat.get('numAffected', 0),
                'Number at Risk': stat.get('numAtRisk', 0),
                'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
            })
    
    # Add other events
    for event in other_events:
        for stat in event.get('stats', []):
            group_title = next((g['title'] for g in event_groups if g['id'] == stat['groupId']), stat['groupId'])
            all_adverse_events.append({
                'Event Category': 'Other',
                'Event Term': event.get('term', ''),
                'Organ System': event.get('organSystem', ''),
                'Assessment Type': event.get('assessmentType', ''),
                'Treatment Group': group_title,
                'Number of Events': stat.get('numEvents', 0),
                'Number Affected': stat.get('numAffected', 0),
                'Number at Risk': stat.get('numAtRisk', 0),
                'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
            })
    
    if all_adverse_events:
        print("=== COMPREHENSIVE ADVERSE EVENTS TABLE ===")
        all_df = pd.DataFrame(all_adverse_events)
        print(all_df.to_string(index=False))
        all_df.to_csv('comprehensive_adverse_events.csv', index=False)
        print("Saved comprehensive adverse events table to comprehensive_adverse_events.csv")
    
    # Summary statistics
    print("\n=== SUMMARY STATISTICS ===")
    total_participants = sum(group.get('deathsNumAtRisk', 0) for group in event_groups)
    total_deaths = sum(group.get('deathsNumAffected', 0) for group in event_groups)
    total_serious = sum(group.get('seriousNumAffected', 0) for group in event_groups)
    total_other = sum(group.get('otherNumAffected', 0) for group in event_groups)
    
    print(f"Total participants: {total_participants}")
    print(f"Total deaths: {total_deaths}")
    print(f"Total serious adverse events: {total_serious}")
    print(f"Total other adverse events: {total_other}")
    
    if total_participants > 0:
        print(f"Death rate: {(total_deaths / total_participants * 100):.1f}%")
        print(f"Serious AE rate: {(total_serious / total_participants * 100):.1f}%")
        print(f"Other AE rate: {(total_other / total_participants * 100):.1f}%")

def main():
    try:
        process_adverse_events_data()
        print("\n✅ Successfully processed adverse events data!")
        print("\nGenerated files:")
        print("- treatment_groups.csv")
        print("- serious_adverse_events.csv (if any)")
        print("- other_adverse_events.csv")
        print("- comprehensive_adverse_events.csv")
        
    except FileNotFoundError:
        print("❌ adverse_events_api_v2.json not found. Please run the API extractor first.")
    except Exception as e:
        print(f"❌ Error processing adverse events data: {e}")

if __name__ == "__main__":
    main()
