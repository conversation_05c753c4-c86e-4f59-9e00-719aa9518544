#!/usr/bin/env python3
"""
PRODUCTION: Batch extract all 44 Canagliflozin studies matching criteria:
- Intervention: Canagliflozin
- Status: Completed  
- Results: With results
"""

import json
import os
import subprocess
import sys
from pathlib import Path
import shutil
import time
from datetime import datetime

def load_matching_studies():
    """Load the 44 matching studies"""
    try:
        with open('matching_studies.json', 'r') as f:
            studies = json.load(f)
        print(f"✅ Loaded {len(studies)} matching studies")
        return studies
    except FileNotFoundError:
        print("❌ matching_studies.json not found. Run test_search_only.py first.")
        return []

def update_script_nct_id(script_path, new_nct_id):
    """Update the NCT ID in a script file"""
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Replace the current NCT ID with the new one
    updated_content = content.replace('NCT02324842', new_nct_id)
    
    with open(script_path, 'w') as f:
        f.write(updated_content)

def extract_single_study(nct_id, study_title, base_dir="canagliflozin_studies"):
    """Extract data for a single study"""
    print(f"📁 {nct_id}: {study_title[:50]}...")
    
    # Create study directory
    study_dir = Path(base_dir) / nct_id
    study_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy extraction scripts to study directory
    script_files = [
        'extract_study_details.py',
        'extract_researcher_view.py', 
        'extract_results_posted.py',
        'extract2csv.py'
    ]
    
    original_dir = os.getcwd()
    
    try:
        # Copy and update scripts
        for script in script_files:
            if os.path.exists(script):
                shutil.copy2(script, study_dir / script)
                update_script_nct_id(study_dir / script, nct_id)
        
        # Change to study directory and run extractions
        os.chdir(study_dir)
        
        results = {}
        
        # 1. Extract Study Details
        result = subprocess.run([sys.executable, 'extract_study_details.py'], 
                              capture_output=True, text=True, timeout=60)
        results['study_details'] = result.returncode == 0
        
        # 2. Extract Researcher View
        result = subprocess.run([sys.executable, 'extract_researcher_view.py'], 
                              capture_output=True, text=True, timeout=60)
        results['researcher_view'] = result.returncode == 0
        
        # 3. Extract Results Posted
        result = subprocess.run([sys.executable, 'extract_results_posted.py'], 
                              capture_output=True, text=True, timeout=60)
        results['results_posted'] = result.returncode == 0
        
        # 4. Extract Adverse Events CSV
        if results['results_posted']:
            result = subprocess.run([sys.executable, 'extract2csv.py'], 
                                  capture_output=True, text=True, timeout=60)
            results['adverse_events'] = result.returncode == 0
        else:
            results['adverse_events'] = False
        
        # Clean up script files
        for script in script_files:
            script_path = Path(script)
            if script_path.exists():
                script_path.unlink()
        
        # Check created files
        expected_files = ['study_details.json', 'researcher_view.json', 'results_posted.json', 'adverse_events.json', 'adverse_events.csv']
        created_files = [f for f in expected_files if os.path.exists(f)]
        
        success_count = sum(results.values())
        status = "✅" if success_count == 4 else "⚠️" if success_count >= 2 else "❌"
        print(f"  {status} {success_count}/4 extractions, {len(created_files)} files")
        
        return results, created_files
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {'error': str(e)}, []
    finally:
        os.chdir(original_dir)

def main():
    print("🏥 CANAGLIFLOZIN STUDIES - BATCH EXTRACTION")
    print("=" * 60)
    print("Extracting data from all 44 completed Canagliflozin studies with results")
    print("=" * 60)
    
    # Load matching studies
    studies = load_matching_studies()
    if not studies:
        return
    
    print(f"\nStudies to extract:")
    for i, study in enumerate(studies, 1):
        print(f"{i:2d}. {study['nct_id']}: {study['title'][:50]}...")
    
    print(f"\nThis will create:")
    print(f"- {len(studies)} study folders (named by NCT ID)")
    print(f"- 5 files per study (3 JSON + 1 intermediate JSON + 1 CSV)")
    print(f"- Total: ~{len(studies) * 5} files")
    
    response = input(f"\nProceed with full batch extraction? (y/N): ")
    if response.lower() != 'y':
        print("❌ Extraction cancelled.")
        return
    
    # Start batch extraction
    start_time = datetime.now()
    print(f"\n🚀 Starting batch extraction at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    successful = 0
    failed = 0
    
    for i, study in enumerate(studies, 1):
        print(f"\n[{i:2d}/{len(studies)}] ", end="")
        
        extraction_results, created_files = extract_single_study(
            study['nct_id'], 
            study['title']
        )
        
        if 'error' in extraction_results:
            failed += 1
        else:
            success_count = sum(extraction_results.values())
            if success_count >= 3:  # At least 3/4 successful
                successful += 1
            else:
                failed += 1
        
        results.append({
            'nct_id': study['nct_id'],
            'title': study['title'],
            'extraction_results': extraction_results,
            'created_files': created_files
        })
        
        # Add small delay to be respectful
        time.sleep(1)
    
    # Final summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("BATCH EXTRACTION COMPLETED")
    print(f"{'='*60}")
    print(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Duration: {duration}")
    print(f"Studies processed: {len(studies)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Success rate: {successful/len(studies)*100:.1f}%")
    
    # Save detailed results
    summary = {
        'extraction_date': start_time.isoformat(),
        'duration_seconds': duration.total_seconds(),
        'total_studies': len(studies),
        'successful': successful,
        'failed': failed,
        'success_rate': successful/len(studies)*100,
        'results': results
    }
    
    with open('canagliflozin_batch_extraction_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\nDetailed results saved to: canagliflozin_batch_extraction_summary.json")
    print(f"Study data saved to: canagliflozin_studies/ directory")
    
    print(f"\n🎉 Batch extraction completed!")
    print(f"Each study folder contains:")
    print(f"  - study_details.json (Study Details tab)")
    print(f"  - researcher_view.json (Researcher View tab)")  
    print(f"  - results_posted.json (Results Posted tab)")
    print(f"  - adverse_events.json (Raw adverse events)")
    print(f"  - adverse_events.csv (Serious & non-serious adverse events)")

if __name__ == "__main__":
    main()
