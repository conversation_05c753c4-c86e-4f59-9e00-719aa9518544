#!/usr/bin/env python3
"""
Master script to extract all three tabs from ClinicalTrials.gov study page:
1. Study Details
2. Researcher View  
3. Results Posted
"""

import subprocess
import sys
import os

def run_script(script_name, description):
    """
    Run a Python script and return success status
    """
    print(f"\n{'='*50}")
    print(f"RUNNING: {description}")
    print(f"Script: {script_name}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              timeout=120)
        
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True
        else:
            print(f"❌ {description} - FAILED (return code: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def main():
    nct_id = "NCT02324842"
    
    print("="*60)
    print("CLINICALTRIALS.GOV COMPLETE DATA EXTRACTION")
    print("="*60)
    print(f"Study ID: {nct_id}")
    print(f"URL: https://clinicaltrials.gov/study/{nct_id}")
    print()
    
    # Track success of each extraction
    results = {}
    
    # 1. Extract Study Details tab
    results['study_details'] = run_script('extract_study_details.py', 'Study Details Tab')
    
    # 2. Extract Researcher View tab
    results['researcher_view'] = run_script('extract_researcher_view.py', 'Researcher View Tab')
    
    # 3. Extract Results Posted tab (using existing api_extractor.py)
    results['results_posted'] = run_script('api_extractor.py', 'Results Posted Tab')
    
    # 4. Process adverse events from results
    if results['results_posted']:
        results['adverse_events'] = run_script('process_adverse_events.py', 'Adverse Events Processing')
    else:
        results['adverse_events'] = False
        print("\n❌ Skipping adverse events processing - Results extraction failed")
    
    # Summary
    print(f"\n{'='*60}")
    print("EXTRACTION SUMMARY")
    print(f"{'='*60}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for tab, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{tab.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall Success Rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    # List generated files
    print(f"\n{'='*60}")
    print("GENERATED FILES")
    print(f"{'='*60}")
    
    expected_files = [
        'study_details.json',
        'researcher_view.json', 
        'api_v2_response_1.json',
        'adverse_events_api_v2.json',
        'comprehensive_adverse_events.csv',
        'treatment_groups.csv',
        'other_adverse_events.csv',
        'serious_adverse_events.csv'
    ]
    
    for filename in expected_files:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ {filename} ({file_size:,} bytes)")
        else:
            print(f"❌ {filename} (missing)")
    
    if success_count == total_count:
        print(f"\n🎉 ALL EXTRACTIONS COMPLETED SUCCESSFULLY!")
        print(f"Complete clinical trial data extracted for {nct_id}")
    else:
        print(f"\n⚠️  PARTIAL SUCCESS - {total_count - success_count} extraction(s) failed")

if __name__ == "__main__":
    main()
