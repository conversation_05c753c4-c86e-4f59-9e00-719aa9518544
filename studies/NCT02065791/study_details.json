{"nctId": "NCT02065791", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT02065791", "orgStudyIdInfo": {"id": "CR103517"}, "secondaryIdInfos": [{"id": "2013-004494-28", "type": "EUDRACT_NUMBER"}, {"id": "28431754DNE3001", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "Evaluation of the Effects of Canagliflozin on Renal and Cardiovascular Outcomes in Participants With Diabetic Nephropathy", "officialTitle": "A Randomized, Double-blind, Event-driven, Placebo-controlled, Multicenter Study of the Effects of Canagliflozin on Renal and Cardiovascular Outcomes in Subjects With Type 2 Diabetes Mellitus and Diabetic Nephropathy", "acronym": "CREDENCE"}, "statusModule": {"statusVerifiedDate": "2019-12", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2014-02-17", "type": "ACTUAL"}, "primaryCompletionDateStruct": {"date": "2018-10-30", "type": "ACTUAL"}, "completionDateStruct": {"date": "2018-10-30", "type": "ACTUAL"}, "studyFirstSubmitDate": "2014-02-17", "studyFirstSubmitQcDate": "2014-02-17", "studyFirstPostDateStruct": {"date": "2014-02-19", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2019-10-30", "resultsFirstSubmitQcDate": "2019-10-30", "resultsFirstPostDateStruct": {"date": "2019-11-20", "type": "ACTUAL"}, "lastUpdateSubmitDate": "2019-12-04", "lastUpdatePostDateStruct": {"date": "2019-12-05", "type": "ACTUAL"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "collaborators": [{"name": "The George Institute for Global Health, Australia", "class": "OTHER"}]}, "oversightModule": {"oversightHasDmc": true, "isFdaRegulatedDrug": true, "isFdaRegulatedDevice": false}, "descriptionModule": {"briefSummary": "The goal of this study is to assess whether canagliflozin has a renal and vascular protective effect in reducing the progression of renal impairment relative to placebo in participants with type 2 diabetes mellitus (T2DM), Stage 2 or 3 chronic kidney disease (CKD) and macroalbuminuria, who are receiving standard of care including a maximum tolerated labeled daily dose of an angiotensin-converting enzyme inhibitor (ACEi) or angiotensin receptor blocker (ARB).", "detailedDescription": "This is a randomized (the study medication is assigned by chance), double-blind (neither physician nor participant knows the identity of the assigned treatment), placebo-controlled (an inactive substance that is compared with a medication to test whether the medication has a real effect), parallel-group, multicenter study of the effects of canagliflozin on renal and cardiovascular outcomes in participants with type 2 diabetes mellitus (T2DM) and diabetic nephropathy, who are receiving standard of care including a maximum tolerated daily dose of an angiotensin-converting enzyme inhibitor (ACEi) or angiotensin receptor blocker (ARB).\n\nThe study will consist of a pretreatment phase (several weeks), and a double-blind treatment phase (up to approximately 66 months). During the pretreatment phase all participants will also receive diet/exercise counseling for lipid and blood pressure management as well as counseling on renal and cardiovascular (CV) risk factor medication. A post-treatment follow-up contact or visit will take place approximately 30 days after the last dose of study drug or the completion of the study. The total duration of the study is estimated to be about 5 to 5.5 years. Approximately 4,200 participants will be randomized in a 1:1 ratio to canagliflozin or matching placebo. Participants randomized to canagliflozin will receive a dose of 100 mg once daily. The overall safety and tolerability of canagliflozin will be evaluated by collecting information on adverse events, laboratory tests, vital signs (pulse, blood pressure), physical examination, and body weight."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2", "Diabetic Nephropathy"], "keywords": ["Diabetes Mellitus, Type 2", "Diabetic Nephropathies", "Canagliflozin", "JNJ-28431754", "End-Stage Kidney Disease", "Chronic Kidney Disease", "Macroalbuminuria"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE3"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "DOUBLE", "whoMasked": ["PARTICIPANT", "INVESTIGATOR"]}}, "enrollmentInfo": {"count": 4401, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 100 mg", "type": "EXPERIMENTAL", "description": "Each participant will receive 100 mg of canagliflozin once daily", "interventionNames": ["Drug: Canagliflozin"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each participant will receive matching placebo once daily", "interventionNames": ["Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Canagliflozin", "description": "One 100 mg over-encapsulated tablet orally once daily", "armGroupLabels": ["Canagliflozin 100 mg"]}, {"type": "DRUG", "name": "Placebo", "description": "One matching placebo capsule orally (by mouth) once daily", "armGroupLabels": ["Placebo"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Primary Composite Endpoint of Doubling of Serum Creatinine (DoSC), End-stage Kidney Disease (ESKD), and Renal or Cardiovascular (CV) Death", "description": "Primary composite endpoint is the composite of DoSC, ESKD, and renal or CV death. DoSC: from baseline average determination (sustained and confirmed by repeat central laboratory measure after at least 30 days and preferably within 60 days). ESKD: as initiation of maintenance dialysis for at least 30 days, or renal transplantation, or an estimated glomerular filtration rate (eGFR) value of less than (\\<)15 milliliters per minute per 1.73 square meter (mL/min/1.73 m\\^2) (sustained and confirmed by repeat central laboratory measure after at least 30 days and preferably within 60 days). Renal death: death in participants who had reached ESKD, died without initiating renal replacement therapy, and no other cause of death was determined via adjudication. Adjudication of these events by Endpoint Adjudication Committee (EAC) was performed in blinded fashion. Event rate estimated based on time to first occurrence of primary composite endpoint are presented.", "timeFrame": "Up to 4.6 years"}], "secondaryOutcomes": [{"measure": "Composite Endpoint of CV Death and Hospitalized Heart Failure (HHF)", "description": "The composite endpoint included CV death and HHF. CV death included death due to myocardial infarction (MI), stroke, heart failure, sudden death, death during a CV procedure or as a result of procedure-related complications, or death due to other CV causes. For analytic purposes, undetermined causes of death were considered CV deaths. In determining whether a death event was CV in nature, the EAC took into consideration both the proximate and underlying causes. Adjudication of these events by the EAC was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of the composite endpoint of CV death and HHF are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "Major Adverse Cardiac Event (MACE)", "description": "The composite endpoint included CV death, non-fatal MI, and non-fatal stroke (that is, 3-point MACE). Adjudication of these events by the EAC was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of MACE are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "Hospitalized Heart Failure (HHF)", "description": "Adjudication of these events by the Endpoint Adjudication Committee (EAC) was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of hospitalized heart failure are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "Renal Composite Endpoint", "description": "The renal composite endpoint included composite of DoSC, ESKD and Renal death. DoSC: from the baseline average determination (sustained and confirmed by repeat central laboratory measure after at least 30 days and preferably within 60 days). ESKD: initiation of maintenance dialysis for at least 30 days, or renal transplantation, or an eGFR value of \\<15 mL/min/1.73 m\\^2 (sustained and confirmed by repeat central laboratory measure after at least 30 days and preferably within 60 days). Renal death: death in participants who have reached ESKD, died without initiating renal replacement therapy, and no other cause of death was determined via adjudication. Adjudication of these events by the EAC was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of the renal composite endpoint are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "Cardiovascular (CV) Death", "description": "CV death included death due to MI, stroke, heart failure, sudden death, death during a CV procedure or as a result of procedure-related complications, or death due to other CV causes. For analytic purposes, undetermined causes of death were considered CV deaths. In determining whether a death event was a CV in nature, the EAC took into consideration both the proximate and underlying causes. Adjudication of these events by the EAC was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of CV death are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "All-cause Mortality", "description": "Adjudication of these events by Endpoint Adjudication Committee (EAC) was performed in a blinded fashion. Event rate estimated based on time to first occurrence of all-cause mortality are presented.", "timeFrame": "Up to 4.6 years"}, {"measure": "CV Composite Endpoint", "description": "The CV composite endpoint included the CV death, non-fatal MI, non-fatal stroke, hospitalized heart failure, and hospitalized unstable angina. CV death included death due to MI, stroke, heart failure, sudden death, death during a CV procedure or as a result of procedure-related complications, or death due to other CV causes. For analytic purposes, undetermined causes of death were considered CV deaths. In determining whether a death event was a CV in nature, the EAC took into consideration both the proximate and underlying causes. Adjudication of these events by the EAC was performed in a blinded fashion. Event rate estimated based on the time to the first occurrence of the CV composite endpoint are presented.", "timeFrame": "Up to 4.6 years"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Type 2 diabetes mellitus with a hemoglobin A1c (HbA1c) greater than or equal to (\\>=) 6.5 percent (%) and less than or equal to (\\<=) 12.0%, with an estimated glomerular filtration rate (eGFR) of \\>= 30 milliliter (mL)/minute (min)/1.73meter (m)\\^2 and less than (\\<) 90 mL/min/1.73 m\\^2\n* Participants need to be on a stable maximum tolerated labeled daily dose of an angiotensin-converting enzyme inhibitor (ACEi) or angiotensin receptor blocker (ARB) for at least 4 weeks prior to randomization\n* Must have a urine albumin to creatinine ratio (UACR) of greater than (\\>) 300 milligram (mg)/gram (g) and \\<= 5000 mg/g\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis or type 1 diabetes mellitus\n* History of hereditary glucose-galactose malabsorption or primary renal glucosuria\n* Renal disease that required treatment with immunosuppressive therapy\n* Known significant liver disease\n* Current or history of New York Heart Association (NYHA) Class IV heart failure\n* Blood potassium level \\>5.5 millimole (mmol)/liter (L) during Screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "30 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Birmingham", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.52066, "lon": -86.80249}}, {"city": "<PERSON>", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 32.36681, "lon": -86.29997}}, {"city": "Tempe", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.41477, "lon": -111.90931}}, {"city": "Tucson", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 32.22174, "lon": -110.92648}}, {"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "<PERSON>", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.81509, "lon": -92.22432}}, {"city": "Bakersfield", "state": "California", "country": "United States", "geoPoint": {"lat": 35.37329, "lon": -119.01871}}, {"city": "Chula Vista", "state": "California", "country": "United States", "geoPoint": {"lat": 32.64005, "lon": -117.0842}}, {"city": "<PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 34.09001, "lon": -117.89034}}, {"city": "<PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.94001, "lon": -118.13257}}, {"city": "Fountain Valley", "state": "California", "country": "United States", "geoPoint": {"lat": 33.70918, "lon": -117.95367}}, {"city": "Hawthorne", "state": "California", "country": "United States", "geoPoint": {"lat": 33.9164, "lon": -118.35257}}, {"city": "Inglewood", "state": "California", "country": "United States", "geoPoint": {"lat": 33.96168, "lon": -118.35313}}, {"city": "Laguna Hills", "state": "California", "country": "United States", "geoPoint": {"lat": 33.61252, "lon": -117.71283}}, {"city": "Lakewood", "state": "California", "country": "United States", "geoPoint": {"lat": 33.85363, "lon": -118.13396}}, {"city": "Long Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 33.76696, "lon": -118.18923}}, {"city": "Los Angeles", "state": "California", "country": "United States", "geoPoint": {"lat": 34.05223, "lon": -118.24368}}, {"city": "<PERSON><PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.93029, "lon": -118.21146}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Oakland", "state": "California", "country": "United States", "geoPoint": {"lat": 37.80437, "lon": -122.2708}}, {"city": "Orange", "state": "California", "country": "United States", "geoPoint": {"lat": 33.78779, "lon": -117.85311}}, {"city": "Pasadena", "state": "California", "country": "United States", "geoPoint": {"lat": 34.14778, "lon": -118.14452}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Sacramento", "state": "California", "country": "United States", "geoPoint": {"lat": 38.58157, "lon": -121.4944}}, {"city": "Salinas", "state": "California", "country": "United States", "geoPoint": {"lat": 36.67774, "lon": -121.6555}}, {"city": "San Diego", "state": "California", "country": "United States", "geoPoint": {"lat": 32.71533, "lon": -117.15726}}, {"city": "San Dimas", "state": "California", "country": "United States", "geoPoint": {"lat": 34.10668, "lon": -117.80673}}, {"city": "Stanford", "state": "California", "country": "United States", "geoPoint": {"lat": 37.42411, "lon": -122.16608}}, {"city": "<PERSON><PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.74585, "lon": -117.82617}}, {"city": "<PERSON><PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.97918, "lon": -118.03284}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Golden", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.75554, "lon": -105.2211}}, {"city": "Lakewood", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.70471, "lon": -105.08137}}, {"city": "Washington", "state": "Colorado", "country": "United States"}, {"city": "Washington", "state": "District of Columbia", "country": "United States", "geoPoint": {"lat": 38.89511, "lon": -77.03637}}, {"city": "Coral Springs", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.27119, "lon": -80.2706}}, {"city": "<PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.81954, "lon": -80.35533}}, {"city": "Hialeah", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.8576, "lon": -80.27811}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Miami Springs", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.82232, "lon": -80.2895}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "North Miami Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.93315, "lon": -80.16255}}, {"city": "Ocala", "state": "Florida", "country": "United States", "geoPoint": {"lat": 29.1872, "lon": -82.14009}}, {"city": "Port Charlotte", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.97617, "lon": -82.09064}}, {"city": "Saint Petersburg", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.77086, "lon": -82.67927}}, {"city": "Winter Park", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.6, "lon": -81.33924}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Augusta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.47097, "lon": -81.97484}}, {"city": "Columbus", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.46098, "lon": -84.98771}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 34.02316, "lon": -84.36159}}, {"city": "Snellville", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.85733, "lon": -84.01991}}, {"city": "Stockbridge", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.54428, "lon": -84.23381}}, {"city": "Chicago", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.85003, "lon": -87.65005}}, {"city": "Crystal Lake", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 42.24113, "lon": -88.3162}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 42.3703, "lon": -87.90202}}, {"city": "Mount Prospect", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 42.06642, "lon": -87.93729}}, {"city": "<PERSON>", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.8617, "lon": -88.1609}}, {"city": "Fort Wayne", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.1306, "lon": -85.12886}}, {"city": "Indianapolis", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.76838, "lon": -86.15804}}, {"city": "Merrillville", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.48281, "lon": -87.33281}}, {"city": "Michigan City", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.70754, "lon": -86.89503}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 40.19338, "lon": -85.38636}}, {"city": "Council Bluffs", "state": "Iowa", "country": "United States", "geoPoint": {"lat": 41.26194, "lon": -95.86083}}, {"city": "Louisville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 38.25424, "lon": -85.75941}}, {"city": "Paducah", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.08339, "lon": -88.60005}}, {"city": "<PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.99409, "lon": -90.24174}}, {"city": "Rockport", "state": "Maine", "country": "United States", "geoPoint": {"lat": 44.18452, "lon": -69.07615}}, {"city": "Scarborough", "state": "Maine", "country": "United States", "geoPoint": {"lat": 43.57814, "lon": -70.32172}}, {"city": "Annapolis", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 38.97845, "lon": -76.49218}}, {"city": "Baltimore", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.29038, "lon": -76.61219}}, {"city": "Beltsville", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.03483, "lon": -76.90747}}, {"city": "Hyattsville", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 38.95594, "lon": -76.94553}}, {"city": "Silver Spring", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 38.99067, "lon": -77.02609}}, {"city": "Boston", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.35843, "lon": -71.05977}}, {"city": "Plymouth", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 41.95844, "lon": -70.66726}}, {"city": "Springfield", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.10148, "lon": -72.58981}}, {"city": "Worcester", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.26259, "lon": -71.80229}}, {"city": "Flint", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 43.01253, "lon": -83.68746}}, {"city": "<PERSON>", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 32.29876, "lon": -90.18481}}, {"city": "Chesterfield", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.66311, "lon": -90.57707}}, {"city": "Kansas City", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 39.09973, "lon": -94.57857}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Fremont", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 41.43333, "lon": -96.49808}}, {"city": "Lincoln", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 40.8, "lon": -96.66696}}, {"city": "Omaha", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 41.25626, "lon": -95.94043}}, {"city": "Reno", "state": "Nebraska", "country": "United States"}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "Eatontown", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 40.29622, "lon": -74.05097}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "Albany", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.65258, "lon": -73.75623}}, {"city": "<PERSON><PERSON>", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.09869, "lon": -75.91797}}, {"city": "Buffalo", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.88645, "lon": -78.87837}}, {"city": "Great Neck", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.80066, "lon": -73.72846}}, {"city": "Lake Success", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.77066, "lon": -73.71763}}, {"city": "New Hyde Park", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.7351, "lon": -73.68791}}, {"city": "New York", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.71427, "lon": -74.00597}}, {"city": "Ridgewood", "state": "New York", "country": "United States", "geoPoint": {"lat": 43.25033, "lon": -78.64725}}, {"city": "Riverhead", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.91704, "lon": -72.66204}}, {"city": "Smithtown", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.85593, "lon": -73.20067}}, {"city": "Springfield Gardens", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.66322, "lon": -73.76211}}, {"city": "Asheville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.60095, "lon": -82.55402}}, {"city": "Calabash", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 33.89073, "lon": -78.56834}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Durham", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.99403, "lon": -78.89862}}, {"city": "Greensboro", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 36.07264, "lon": -79.79198}}, {"city": "Greenville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.61266, "lon": -77.36635}}, {"city": "High Point", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.95569, "lon": -80.00532}}, {"city": "Morehead City", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.72294, "lon": -76.72604}}, {"city": "<PERSON><PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.74541, "lon": -81.68482}}, {"city": "Raleigh", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.7721, "lon": -78.63861}}, {"city": "Tabor City", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.14878, "lon": -78.87669}}, {"city": "Whiteville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.33878, "lon": -78.70307}}, {"city": "Cincinnati", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.12713, "lon": -84.51435}}, {"city": "Columbus", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.96118, "lon": -82.99879}}, {"city": "Ma<PERSON><PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.56283, "lon": -83.65382}}, {"city": "Roseburg", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 43.2165, "lon": -123.34174}}, {"city": "Beaver", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.69534, "lon": -80.30478}}, {"city": "Bethlehem", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.62593, "lon": -75.37046}}, {"city": "Philadelphia", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 39.95233, "lon": -75.16379}}, {"city": "Pittsburgh", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.44062, "lon": -79.99589}}, {"city": "<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.50344, "lon": -82.65013}}, {"city": "Columbia", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.00071, "lon": -81.03481}}, {"city": "Lancaster", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.72043, "lon": -80.7709}}, {"city": "<PERSON><PERSON><PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 33.551, "lon": -79.04143}}, {"city": "Orangeburg", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 33.49182, "lon": -80.85565}}, {"city": "<PERSON><PERSON><PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 33.92044, "lon": -80.34147}}, {"city": "Chattanooga", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 35.04563, "lon": -85.30968}}, {"city": "Knoxville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 35.96064, "lon": -83.92074}}, {"city": "Memphis", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 35.14953, "lon": -90.04898}}, {"city": "Arlington", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.73569, "lon": -97.10807}}, {"city": "Corpus Christi", "state": "Texas", "country": "United States", "geoPoint": {"lat": 27.80058, "lon": -97.39638}}, {"city": "Corsicana", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.09543, "lon": -96.46887}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Edinburg", "state": "Texas", "country": "United States", "geoPoint": {"lat": 26.30174, "lon": -98.16334}}, {"city": "Fort Worth", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.72541, "lon": -97.32085}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "Mesquite", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.7668, "lon": -96.59916}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.55217, "lon": -98.26973}}, {"city": "Sugar Land", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.61968, "lon": -95.63495}}, {"city": "Temple", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.09823, "lon": -97.34278}}, {"city": "Waco", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.54933, "lon": -97.14667}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "Bennington", "state": "Vermont", "country": "United States", "geoPoint": {"lat": 42.87813, "lon": -73.19677}}, {"city": "Burlington", "state": "Vermont", "country": "United States", "geoPoint": {"lat": 44.47588, "lon": -73.21207}}, {"city": "Bluefield", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.25262, "lon": -81.27121}}, {"city": "Mechanicsville", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.60876, "lon": -77.37331}}, {"city": "Norfolk", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.84681, "lon": -76.28522}}, {"city": "Suffolk", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.72836, "lon": -76.58496}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Huntington", "state": "West Virginia", "country": "United States", "geoPoint": {"lat": 38.41925, "lon": -82.44515}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "Bahia Blanca", "country": "Argentina", "geoPoint": {"lat": -38.71959, "lon": -62.27243}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Cordoba", "country": "Argentina", "geoPoint": {"lat": -31.4135, "lon": -64.18105}}, {"city": "Corrientes", "country": "Argentina", "geoPoint": {"lat": -27.4806, "lon": -58.8341}}, {"city": "Córdoba", "country": "Argentina", "geoPoint": {"lat": -31.4135, "lon": -64.18105}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -32.92863, "lon": -68.8351}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.58382, "lon": -60.94332}}, {"city": "La Plata Lpl Lpl", "country": "Argentina"}, {"city": "La Plata", "country": "Argentina", "geoPoint": {"lat": -34.92145, "lon": -57.95453}}, {"city": "Mar del Plata", "country": "Argentina", "geoPoint": {"lat": -38.00228, "lon": -57.55754}}, {"city": "Mendoza", "country": "Argentina", "geoPoint": {"lat": -32.89084, "lon": -68.82717}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.6509, "lon": -58.61956}}, {"city": "<PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.53021, "lon": -58.52421}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -31.73197, "lon": -60.5238}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.72904, "lon": -58.26374}}, {"city": "<PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.6551, "lon": -58.55318}}, {"city": "Rosario", "country": "Argentina", "geoPoint": {"lat": -32.94682, "lon": -60.63932}}, {"city": "Salta", "country": "Argentina", "geoPoint": {"lat": -24.7859, "lon": -65.41166}}, {"city": "San Luis", "country": "Argentina", "geoPoint": {"lat": -33.29501, "lon": -66.33563}}, {"city": "San Martin", "country": "Argentina", "geoPoint": {"lat": -33.08103, "lon": -68.46814}}, {"city": "San Migeul De Tucuman", "country": "Argentina"}, {"city": "San Miguel <PERSON>", "country": "Argentina", "geoPoint": {"lat": -26.82414, "lon": -65.2226}}, {"city": "San Nicolas", "country": "Argentina"}, {"city": "Santa Fe", "country": "Argentina", "geoPoint": {"lat": -31.63333, "lon": -60.7}}, {"city": "Sarandi", "country": "Argentina", "geoPoint": {"lat": -34.68324, "lon": -58.35117}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.70025, "lon": -58.51186}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.77988, "lon": -58.40312}}, {"city": "Villa Maria", "country": "Argentina", "geoPoint": {"lat": -32.40751, "lon": -63.24016}}, {"city": "Zarate, Buenos Aires", "country": "Argentina"}, {"city": "Cairns", "country": "Australia", "geoPoint": {"lat": -16.92304, "lon": 145.76625}}, {"city": "Concord", "country": "Australia", "geoPoint": {"lat": -33.84722, "lon": 151.10381}}, {"city": "Darlinghurst", "country": "Australia", "geoPoint": {"lat": -33.87939, "lon": 151.21925}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -34.74857, "lon": 138.66819}}, {"city": "Fremantle", "country": "Australia", "geoPoint": {"lat": -32.05629, "lon": 115.74565}}, {"city": "Gosford", "country": "Australia", "geoPoint": {"lat": -33.4244, "lon": 151.34399}}, {"city": "Heidelberg", "country": "Australia", "geoPoint": {"lat": -37.75, "lon": 145.06667}}, {"city": "Keswick", "country": "Australia", "geoPoint": {"lat": -34.97487, "lon": 149.27077}}, {"city": "Meadowbrook", "country": "Australia", "geoPoint": {"lat": -27.66401, "lon": 153.14465}}, {"city": "Parkville", "country": "Australia", "geoPoint": {"lat": -37.78333, "lon": 144.95}}, {"city": "Reservoir", "country": "Australia", "geoPoint": {"lat": -37.71667, "lon": 145.0}}, {"city": "Richmond", "country": "Australia", "geoPoint": {"lat": -37.81819, "lon": 145.00176}}, {"city": "St Albans", "country": "Australia", "geoPoint": {"lat": -37.73901, "lon": 144.80024}}, {"city": "St Leonards", "country": "Australia", "geoPoint": {"lat": -33.82344, "lon": 151.19836}}, {"city": "Sydney", "country": "Australia", "geoPoint": {"lat": -33.86785, "lon": 151.20732}}, {"city": "Woolloongabba", "country": "Australia", "geoPoint": {"lat": -27.48855, "lon": 153.03655}}, {"city": "Aparecida de Goiânia", "country": "Brazil", "geoPoint": {"lat": -16.82333, "lon": -49.24389}}, {"city": "Belo Horizonte", "country": "Brazil", "geoPoint": {"lat": -19.92083, "lon": -43.93778}}, {"city": "Belém", "country": "Brazil", "geoPoint": {"lat": -1.45583, "lon": -48.50444}}, {"city": "Botucatu", "country": "Brazil", "geoPoint": {"lat": -22.88583, "lon": -48.445}}, {"city": "Brasilia", "country": "Brazil", "geoPoint": {"lat": -15.77972, "lon": -47.92972}}, {"city": "Campina Grande Do Sul", "country": "Brazil", "geoPoint": {"lat": -25.30556, "lon": -49.05528}}, {"city": "Campinas", "country": "Brazil", "geoPoint": {"lat": -22.90556, "lon": -47.06083}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Brazil", "geoPoint": {"lat": -29.91778, "lon": -51.18361}}, {"city": "Caxias do Sul", "country": "Brazil", "geoPoint": {"lat": -29.16806, "lon": -51.17944}}, {"city": "Curitiba", "country": "Brazil", "geoPoint": {"lat": -25.42778, "lon": -49.27306}}, {"city": "Fortaleza", "country": "Brazil", "geoPoint": {"lat": -3.71722, "lon": -38.54306}}, {"city": "Joinville", "country": "Brazil", "geoPoint": {"lat": -26.30444, "lon": -48.84556}}, {"city": "<PERSON><PERSON>", "country": "Brazil", "geoPoint": {"lat": -21.76417, "lon": -43.35028}}, {"city": "Maringá", "country": "Brazil", "geoPoint": {"lat": -23.42528, "lon": -51.93861}}, {"city": "Passo Fundo", "country": "Brazil", "geoPoint": {"lat": -28.26278, "lon": -52.40667}}, {"city": "Porto Alegre", "country": "Brazil", "geoPoint": {"lat": -30.03306, "lon": -51.23}}, {"city": "Rio de Janeiro", "country": "Brazil", "geoPoint": {"lat": -22.90278, "lon": -43.2075}}, {"city": "<PERSON>", "country": "Brazil", "geoPoint": {"lat": -23.66389, "lon": -46.53833}}, {"city": "<PERSON>", "country": "Brazil", "geoPoint": {"lat": -23.66389, "lon": -46.53833}}, {"city": "Sao Jose Do Rio Preto", "country": "Brazil", "geoPoint": {"lat": -20.81972, "lon": -49.37944}}, {"city": "Sao Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "São José dos Campos", "country": "Brazil", "geoPoint": {"lat": -23.17944, "lon": -45.88694}}, {"city": "São Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "Tatuí", "country": "Brazil", "geoPoint": {"lat": -23.35556, "lon": -47.85694}}, {"city": "Blagoevgrad", "country": "Bulgaria", "geoPoint": {"lat": 42.01667, "lon": 23.1}}, {"city": "<PERSON><PERSON>", "country": "Bulgaria", "geoPoint": {"lat": 42.87463, "lon": 27.88843}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Bulgaria", "geoPoint": {"lat": 43.64088, "lon": 24.69385}}, {"city": "Pazardzhik", "country": "Bulgaria", "geoPoint": {"lat": 42.2, "lon": 24.33333}}, {"city": "Pleven", "country": "Bulgaria", "geoPoint": {"lat": 43.41667, "lon": 24.61667}}, {"city": "Plovdiv", "country": "Bulgaria", "geoPoint": {"lat": 42.15, "lon": 24.75}}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Bulgaria", "geoPoint": {"lat": 43.21667, "lon": 27.91667}}, {"city": "Edmonton", "state": "Alberta", "country": "Canada", "geoPoint": {"lat": 53.55014, "lon": -113.46871}}, {"city": "New Westminster", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.20678, "lon": -122.91092}}, {"city": "Vancouver", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "Winnipeg", "state": "Manitoba", "country": "Canada", "geoPoint": {"lat": 49.8844, "lon": -97.14704}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "London", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.98339, "lon": -81.23304}}, {"city": "NewMarket", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 44.05011, "lon": -79.46631}}, {"city": "Oshawa", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.90012, "lon": -78.84957}}, {"city": "Smiths Falls", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 44.90452, "lon": -76.02333}}, {"city": "Thornhill", "state": "Ontario", "country": "Canada"}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Weston", "state": "Ontario", "country": "Canada"}, {"city": "Greenfield Park", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.48649, "lon": -73.46223}}, {"city": "Montreal", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.50884, "lon": -73.58781}}, {"city": "Québec", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.81228, "lon": -71.21454}}, {"city": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.81228, "lon": -71.21454}}, {"city": "Québec", "country": "Canada", "geoPoint": {"lat": 46.81228, "lon": -71.21454}}, {"city": "Toronto", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Vancouver", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "Concepcion", "country": "Chile", "geoPoint": {"lat": -36.82699, "lon": -73.04977}}, {"city": "Santiago", "country": "Chile", "geoPoint": {"lat": -33.45694, "lon": -70.64827}}, {"city": "Temuco", "country": "Chile", "geoPoint": {"lat": -38.73965, "lon": -72.59842}}, {"city": "Beijing", "country": "China", "geoPoint": {"lat": 39.9075, "lon": 116.39723}}, {"city": "<PERSON><PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 43.88, "lon": 125.32278}}, {"city": "<PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 29.04638, "lon": 111.6783}}, {"city": "Changsha", "country": "China", "geoPoint": {"lat": 28.19874, "lon": 112.97087}}, {"city": "Chengdu", "country": "China", "geoPoint": {"lat": 30.66667, "lon": 104.06667}}, {"city": "Chenzhou", "country": "China", "geoPoint": {"lat": 25.8, "lon": 113.03333}}, {"city": "<PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 38.91222, "lon": 121.60222}}, {"city": "<PERSON><PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 23.02677, "lon": 113.13148}}, {"city": "Guangzhou", "country": "China", "geoPoint": {"lat": 23.11667, "lon": 113.25}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 33.50389, "lon": 119.14417}}, {"city": "Nanchang", "country": "China", "geoPoint": {"lat": 28.68396, "lon": 115.85306}}, {"city": "Nanjing", "country": "China", "geoPoint": {"lat": 32.06167, "lon": 118.77778}}, {"city": "Shanghai", "country": "China", "geoPoint": {"lat": 31.22222, "lon": 121.45806}}, {"city": "Shenyang", "country": "China", "geoPoint": {"lat": 41.79222, "lon": 123.43278}}, {"city": "<PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 30.58333, "lon": 114.26667}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "China"}, {"city": "<PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 34.25833, "lon": 108.92861}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 10.96854, "lon": -74.78132}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "Bucaramanga", "country": "Colombia", "geoPoint": {"lat": 7.12539, "lon": -73.1198}}, {"city": "Cali", "country": "Colombia", "geoPoint": {"lat": 3.43722, "lon": -76.5225}}, {"city": "Floridablanca", "country": "Colombia", "geoPoint": {"lat": 7.06222, "lon": -73.08644}}, {"city": "Man<PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 5.06889, "lon": -75.51738}}, {"city": "Medellin", "country": "Colombia", "geoPoint": {"lat": 6.25184, "lon": -75.56359}}, {"city": "Pasto", "country": "Colombia", "geoPoint": {"lat": 1.21361, "lon": -77.28111}}, {"city": "<PERSON>", "country": "Colombia", "geoPoint": {"lat": 4.81333, "lon": -75.69611}}, {"city": "Ceske Budejovice", "country": "Czechia", "geoPoint": {"lat": 48.97447, "lon": 14.47434}}, {"city": "<PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 48.81091, "lon": 14.31521}}, {"city": "Chocen", "country": "Czechia", "geoPoint": {"lat": 50.00161, "lon": 16.22303}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 49.60785, "lon": 15.58069}}, {"city": "Hradec Kralove-Vekose", "country": "Czechia"}, {"city": "Hradec Nad Svitavou", "country": "Czechia", "geoPoint": {"lat": 49.71143, "lon": 16.48058}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 49.14404, "lon": 15.00301}}, {"city": "Olomouc", "country": "Czechia", "geoPoint": {"lat": 49.59552, "lon": 17.25175}}, {"city": "Praha 10", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 2", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 4", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 5", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.23046, "lon": 14.08693}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.56101, "lon": 15.9127}}, {"city": "Uherske Hradiste", "country": "Czechia", "geoPoint": {"lat": 49.06975, "lon": 17.45969}}, {"city": "<PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.6607, "lon": 14.03227}}, {"city": "Besancon Cedex", "country": "France", "geoPoint": {"lat": 47.24878, "lon": 6.01815}}, {"city": "Brest cedex", "country": "France", "geoPoint": {"lat": 48.3903, "lon": -4.48628}}, {"city": "Colmar N/a", "country": "France"}, {"city": "<PERSON><PERSON><PERSON> Cedex", "country": "France", "geoPoint": {"lat": 48.60603, "lon": 2.48757}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 48.78333, "lon": 2.46667}}, {"city": "Grenoble", "country": "France", "geoPoint": {"lat": 45.16667, "lon": 5.71667}}, {"city": "Lyon cedex 03", "country": "France", "geoPoint": {"lat": 45.74848, "lon": 4.84669}}, {"city": "Marseille Cedex 05", "country": "France", "geoPoint": {"lat": 43.29551, "lon": 5.38958}}, {"city": "Metz cedex 03", "country": "France", "geoPoint": {"lat": 49.11911, "lon": 6.17269}}, {"city": "Nantes Cedex 01", "country": "France", "geoPoint": {"lat": 47.21725, "lon": -1.55336}}, {"city": "Paris Cedex 15", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "Paris Cedex 18", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "Paris", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "France"}, {"city": "Poitiers", "country": "France", "geoPoint": {"lat": 46.58333, "lon": 0.33333}}, {"city": "St Priest-en-Jarez", "country": "France", "geoPoint": {"lat": 45.47501, "lon": 4.37614}}, {"city": "Strasbourg", "country": "France", "geoPoint": {"lat": 48.58392, "lon": 7.74553}}, {"city": "Toulouse Cedex 9", "country": "France", "geoPoint": {"lat": 43.60426, "lon": 1.44367}}, {"city": "Bad Kreuznach - Bosenheim", "country": "Germany"}, {"city": "Dietzenbach", "country": "Germany", "geoPoint": {"lat": 50.00976, "lon": 8.77783}}, {"city": "Dresden", "country": "Germany", "geoPoint": {"lat": 51.05089, "lon": 13.73832}}, {"city": "Essen", "country": "Germany", "geoPoint": {"lat": 51.45657, "lon": 7.01228}}, {"city": "Hamburg", "country": "Germany", "geoPoint": {"lat": 53.57532, "lon": 10.01534}}, {"city": "Hannover", "country": "Germany", "geoPoint": {"lat": 52.37052, "lon": 9.73322}}, {"city": "Köln", "country": "Germany", "geoPoint": {"lat": 50.93333, "lon": 6.95}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 51.96236, "lon": 7.62571}}, {"city": "Guatemala City", "country": "Guatemala", "geoPoint": {"lat": 14.64072, "lon": -90.51327}}, {"city": "Guatemala", "country": "Guatemala", "geoPoint": {"lat": 14.64072, "lon": -90.51327}}, {"city": "Baja", "country": "Hungary", "geoPoint": {"lat": 46.17496, "lon": 18.95639}}, {"city": "Balatonfured", "country": "Hungary", "geoPoint": {"lat": 46.96188, "lon": 17.87187}}, {"city": "Budapest", "country": "Hungary", "geoPoint": {"lat": 47.49801, "lon": 19.03991}}, {"city": "Debrecen", "country": "Hungary", "geoPoint": {"lat": 47.53333, "lon": 21.63333}}, {"city": "<PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.90265, "lon": 20.37329}}, {"city": "Esztergom", "country": "Hungary", "geoPoint": {"lat": 47.7928, "lon": 18.74148}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.59657, "lon": 19.35515}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 46.65, "lon": 21.28333}}, {"city": "Hu-4012 Debrecen N/a", "country": "Hungary"}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 46.36667, "lon": 17.8}}, {"city": "Kecskemet N/a", "country": "Hungary"}, {"city": "Komar<PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.74318, "lon": 18.11913}}, {"city": "Nyiregyhaza", "country": "Hungary", "geoPoint": {"lat": 47.95539, "lon": 21.71671}}, {"city": "Pecs", "country": "Hungary", "geoPoint": {"lat": 46.08333, "lon": 18.23333}}, {"city": "Szeged N/a", "country": "Hungary"}, {"city": "Szigetvar", "country": "Hungary", "geoPoint": {"lat": 46.04865, "lon": 17.80554}}, {"city": "Veszprem", "country": "Hungary", "geoPoint": {"lat": 47.09327, "lon": 17.91149}}, {"city": "Zalaegerszeg", "country": "Hungary", "geoPoint": {"lat": 46.84, "lon": 16.84389}}, {"city": "Calicut", "country": "India", "geoPoint": {"lat": 11.24802, "lon": 75.7804}}, {"city": "Chennai", "country": "India", "geoPoint": {"lat": 13.08784, "lon": 80.27847}}, {"city": "Delhi", "country": "India", "geoPoint": {"lat": 28.65195, "lon": 77.23149}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Kozhikode", "country": "India", "geoPoint": {"lat": 11.24802, "lon": 75.7804}}, {"city": "Lucknow", "country": "India", "geoPoint": {"lat": 26.83928, "lon": 80.92313}}, {"city": "<PERSON><PERSON><PERSON>", "country": "India", "geoPoint": {"lat": 30.71542, "lon": 77.45095}}, {"city": "Mangalore", "country": "India", "geoPoint": {"lat": 12.91723, "lon": 74.85603}}, {"city": "Mumbai", "country": "India", "geoPoint": {"lat": 19.07283, "lon": 72.88261}}, {"city": "Mysore", "country": "India", "geoPoint": {"lat": 12.29791, "lon": 76.63925}}, {"city": "Secunderabad", "country": "India", "geoPoint": {"lat": 17.50427, "lon": 78.54263}}, {"city": "Trivandrum", "country": "India", "geoPoint": {"lat": 8.4855, "lon": 76.94924}}, {"city": "Vijayawada", "country": "India", "geoPoint": {"lat": 16.50745, "lon": 80.6466}}, {"city": "Vishakhapatnam", "country": "India"}, {"city": "<PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 35.97145, "lon": 139.61382}}, {"city": "Chiyoda-ku", "country": "Japan"}, {"city": "<PERSON><PERSON><PERSON>", "country": "Japan", "geoPoint": {"lat": 33.6, "lon": 130.41667}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 34.7, "lon": 137.73333}}, {"city": "<PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan"}, {"city": "<PERSON><PERSON>-<PERSON>hi", "country": "Japan", "geoPoint": {"lat": 35.82756, "lon": 137.95378}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Japan", "geoPoint": {"lat": 34.41663, "lon": 135.31667}}, {"city": "Kamakura-shi", "country": "Japan", "geoPoint": {"lat": 35.30889, "lon": 139.55028}}, {"city": "Kanazawa", "country": "Japan", "geoPoint": {"lat": 36.6, "lon": 136.61667}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 34.81671, "lon": 135.41666}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 37.4, "lon": 140.38333}}, {"city": "<PERSON><PERSON><PERSON>shi", "country": "Japan"}, {"city": "Kyoto", "country": "Japan", "geoPoint": {"lat": 35.02107, "lon": 135.75385}}, {"city": "<PERSON><PERSON><PERSON>-<PERSON>hi", "country": "Japan", "geoPoint": {"lat": 33.83916, "lon": 132.76574}}, {"city": "<PERSON><PERSON>-shi", "country": "Japan"}, {"city": "Minato-ku", "country": "Japan", "geoPoint": {"lat": 34.2152, "lon": 135.1501}}, {"city": "Nagoya-shi", "country": "Japan", "geoPoint": {"lat": 35.18147, "lon": 136.90641}}, {"city": "Osaka-shi", "country": "Japan", "geoPoint": {"lat": 34.69374, "lon": 135.50218}}, {"city": "<PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 36.3, "lon": 139.36667}}, {"city": "<PERSON><PERSON><PERSON><PERSON>hi", "country": "Japan", "geoPoint": {"lat": 32.95, "lon": 131.9}}, {"city": "Sashima-gun", "country": "Japan", "geoPoint": {"lat": 35.87201, "lon": 139.7294}}, {"city": "Shinjuku-ku", "country": "Japan", "geoPoint": {"lat": 35.2946, "lon": 139.57059}}, {"city": "<PERSON><PERSON>-shi", "country": "Japan", "geoPoint": {"lat": 36.7, "lon": 137.21667}}, {"city": "Uwajima-shi", "country": "Japan", "geoPoint": {"lat": 33.22375, "lon": 132.56001}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Japan"}, {"city": "Yokohama-City", "country": "Japan", "geoPoint": {"lat": 35.43333, "lon": 139.65}}, {"city": "Yokohama-shi", "country": "Japan", "geoPoint": {"lat": 35.43333, "lon": 139.65}}, {"city": "Busan", "country": "Korea, Republic of", "geoPoint": {"lat": 35.10278, "lon": 129.04028}}, {"city": "Daegu", "country": "Korea, Republic of", "geoPoint": {"lat": 35.87028, "lon": 128.59111}}, {"city": "Incheon", "country": "Korea, Republic of", "geoPoint": {"lat": 37.45646, "lon": 126.70515}}, {"city": "Se<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.43861, "lon": 127.13778}}, {"city": "Seoul", "country": "Korea, Republic of", "geoPoint": {"lat": 37.566, "lon": 126.9784}}, {"city": "Kaunas", "country": "Lithuania", "geoPoint": {"lat": 54.90272, "lon": 23.90961}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Lithuania", "geoPoint": {"lat": 55.70888, "lon": 21.14311}}, {"city": "Siauliai", "country": "Lithuania", "geoPoint": {"lat": 55.93333, "lon": 23.31667}}, {"city": "Batu Caves", "country": "Malaysia", "geoPoint": {"lat": 3.23333, "lon": 101.66667}}, {"city": "Ipoh", "country": "Malaysia", "geoPoint": {"lat": 4.5841, "lon": 101.0829}}, {"city": "<PERSON><PERSON>", "country": "Malaysia"}, {"city": "Kuala Lumpur N/a", "country": "Malaysia"}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 3.8077, "lon": 103.326}}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 2.196, "lon": 102.2405}}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 5.38333, "lon": 100.38333}}, {"city": "Petaling Jaya", "country": "Malaysia", "geoPoint": {"lat": 3.10726, "lon": 101.60671}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 2.7297, "lon": 101.9381}}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 4.1987, "lon": 100.67}}, {"city": "Sungai Petani, Kedah", "country": "Malaysia"}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 2.4701, "lon": 102.2302}}, {"city": "Temerloh", "country": "Malaysia"}, {"city": "Aguascalientes", "country": "Mexico", "geoPoint": {"lat": 21.88234, "lon": -102.28259}}, {"city": "Celaya", "country": "Mexico", "geoPoint": {"lat": 20.52353, "lon": -100.8157}}, {"city": "Chihuahua", "country": "Mexico", "geoPoint": {"lat": 28.63528, "lon": -106.08889}}, {"city": "Ciudad De Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Ciudad De Mexic", "country": "Mexico"}, {"city": "Cuautitlan <PERSON>zcalli", "country": "Mexico", "geoPoint": {"lat": 19.64388, "lon": -99.21598}}, {"city": "Culiacan", "country": "Mexico", "geoPoint": {"lat": 24.79032, "lon": -107.38782}}, {"city": "Culiacán", "country": "Mexico", "geoPoint": {"lat": 24.79032, "lon": -107.38782}}, {"city": "Durango", "country": "Mexico", "geoPoint": {"lat": 24.02032, "lon": -104.65756}}, {"city": "El Salto", "country": "Mexico", "geoPoint": {"lat": 20.33333, "lon": -104.5}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "<PERSON>", "country": "Mexico", "geoPoint": {"lat": 21.12908, "lon": -101.67374}}, {"city": "<PERSON><PERSON>", "country": "Mexico", "geoPoint": {"lat": 20.97537, "lon": -89.61696}}, {"city": "Mexico City", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Morelia", "country": "Mexico", "geoPoint": {"lat": 19.70078, "lon": -101.18443}}, {"city": "Orizaba", "country": "Mexico", "geoPoint": {"lat": 18.85195, "lon": -97.09957}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Mexico", "geoPoint": {"lat": 20.11697, "lon": -98.73329}}, {"city": "Pachuca", "country": "Mexico", "geoPoint": {"lat": 20.11697, "lon": -98.73329}}, {"city": "Que<PERSON>ro", "country": "Mexico"}, {"city": "San Luis Potosi", "country": "Mexico", "geoPoint": {"lat": 22.14982, "lon": -100.97916}}, {"city": "Tlalnepantla de Baz", "country": "Mexico", "geoPoint": {"lat": 19.54005, "lon": -99.19538}}, {"city": "Veracruz", "country": "Mexico", "geoPoint": {"lat": 19.18095, "lon": -96.1429}}, {"city": "Zapopan", "country": "Mexico", "geoPoint": {"lat": 20.72356, "lon": -103.38479}}, {"city": "Auckland", "country": "New Zealand", "geoPoint": {"lat": -36.84853, "lon": 174.76349}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Dunedin", "country": "New Zealand", "geoPoint": {"lat": -45.87416, "lon": 170.50361}}, {"city": "Palmerston North", "country": "New Zealand", "geoPoint": {"lat": -40.35636, "lon": 175.61113}}, {"city": "Rotoru<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -38.13874, "lon": 176.24516}}, {"city": "Takapuna Auckland", "country": "New Zealand"}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Cagayan De Oro City", "country": "Philippines"}, {"city": "Cebuu City", "country": "Philippines"}, {"city": "Cebu", "country": "Philippines", "geoPoint": {"lat": 10.31672, "lon": 123.89071}}, {"city": "Davao City", "country": "Philippines", "geoPoint": {"lat": 7.07306, "lon": 125.61278}}, {"city": "Iloilo City", "country": "Philippines", "geoPoint": {"lat": 10.69694, "lon": 122.56444}}, {"city": "Lipa City", "country": "Philippines", "geoPoint": {"lat": 13.9411, "lon": 121.1631}}, {"city": "Marikina City", "country": "Philippines", "geoPoint": {"lat": 14.6481, "lon": 121.1133}}, {"city": "Pasay City", "country": "Philippines"}, {"city": "Pasig", "country": "Philippines", "geoPoint": {"lat": 14.58691, "lon": 121.0614}}, {"city": "Quezon City", "country": "Philippines", "geoPoint": {"lat": 14.6488, "lon": 121.0509}}, {"city": "San Fernando", "country": "Philippines", "geoPoint": {"lat": 15.03425, "lon": 120.68445}}, {"city": "Tagbilaran City", "country": "Philippines", "geoPoint": {"lat": 9.65556, "lon": 123.85219}}, {"city": "Taytay", "country": "Philippines", "geoPoint": {"lat": 14.55883, "lon": 121.13285}}, {"city": "Bydgoszcz", "country": "Poland", "geoPoint": {"lat": 53.1235, "lon": 18.00762}}, {"city": "Chorzow", "country": "Poland", "geoPoint": {"lat": 50.30582, "lon": 18.9742}}, {"city": "Katowice", "country": "Poland", "geoPoint": {"lat": 50.25841, "lon": 19.02754}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "Pa<PERSON><PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 51.64021, "lon": 22.90057}}, {"city": "Poznan", "country": "Poland", "geoPoint": {"lat": 52.40692, "lon": 16.92993}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 51.40253, "lon": 21.14714}}, {"city": "Rzeszow", "country": "Poland", "geoPoint": {"lat": 50.04132, "lon": 21.99901}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warswa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Zgierz", "country": "Poland", "geoPoint": {"lat": 51.85561, "lon": 19.40623}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 52.84958, "lon": 17.71992}}, {"city": "Caguas", "country": "Puerto Rico", "geoPoint": {"lat": 18.23412, "lon": -66.0485}}, {"city": "Ponce", "country": "Puerto Rico", "geoPoint": {"lat": 18.01108, "lon": -66.61406}}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 44.42802, "lon": 26.09665}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.76667, "lon": 23.6}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.88333, "lon": 22.9}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 47.16667, "lon": 27.6}}, {"city": "Oradea", "country": "Romania", "geoPoint": {"lat": 47.0458, "lon": 21.91833}}, {"city": "P<PERSON>iesti", "country": "Romania", "geoPoint": {"lat": 44.95, "lon": 26.01667}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.54245, "lon": 24.55747}}, {"city": "Timisoara", "country": "Romania", "geoPoint": {"lat": 45.75372, "lon": 21.22571}}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Barnaul", "country": "Russian Federation", "geoPoint": {"lat": 53.36056, "lon": 83.76361}}, {"city": "Ivanovo", "country": "Russian Federation", "geoPoint": {"lat": 56.99719, "lon": 40.97139}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Novosibirsk", "country": "Russian Federation", "geoPoint": {"lat": 55.0415, "lon": 82.9346}}, {"city": "Petrozavodsk", "country": "Russian Federation", "geoPoint": {"lat": 61.78491, "lon": 34.34691}}, {"city": "Rostov-on-Don", "country": "Russian Federation", "geoPoint": {"lat": 47.23135, "lon": 39.72328}}, {"city": "R<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 54.6269, "lon": 39.6916}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Saint-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "Sestroretsk, Saint-Petersburg", "country": "Russian Federation"}, {"city": "St. Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Tomsk", "country": "Russian Federation", "geoPoint": {"lat": 56.49771, "lon": 84.97437}}, {"city": "Ufa", "country": "Russian Federation", "geoPoint": {"lat": 54.74306, "lon": 55.96779}}, {"city": "Volgograd", "country": "Russian Federation", "geoPoint": {"lat": 48.71939, "lon": 44.50183}}, {"city": "Yaroslavl", "country": "Russian Federation", "geoPoint": {"lat": 57.62987, "lon": 39.87368}}, {"city": "Belgrade", "country": "Serbia", "geoPoint": {"lat": 44.80401, "lon": 20.46513}}, {"city": "<PERSON><PERSON>", "country": "Serbia", "geoPoint": {"lat": 43.32472, "lon": 21.90333}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Serbia", "geoPoint": {"lat": 43.90358, "lon": 22.26405}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 49.29175, "lon": 21.27271}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.71395, "lon": 21.25808}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 49.12011, "lon": 19.16891}}, {"city": "Ni<PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.30763, "lon": 18.08453}}, {"city": "Prievidza", "country": "Slovakia", "geoPoint": {"lat": 48.77446, "lon": 18.6275}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 49.12494, "lon": 18.32597}}, {"city": "Rimavska Sobota", "country": "Slovakia", "geoPoint": {"lat": 48.38284, "lon": 20.02239}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 47.79495, "lon": 18.7175}}, {"city": "Svidnik", "country": "Slovakia", "geoPoint": {"lat": 49.30819, "lon": 21.5703}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 49.22315, "lon": 18.73941}}, {"city": "Cape Town", "country": "South Africa", "geoPoint": {"lat": -33.92584, "lon": 18.42322}}, {"city": "Durban", "country": "South Africa", "geoPoint": {"lat": -29.8579, "lon": 31.0292}}, {"city": "Isipingo Rail", "country": "South Africa"}, {"city": "Johannesburg", "country": "South Africa", "geoPoint": {"lat": -26.20227, "lon": 28.04363}}, {"city": "Krugersdorp", "country": "South Africa", "geoPoint": {"lat": -26.08577, "lon": 27.77515}}, {"city": "Port Elizabeth", "country": "South Africa", "geoPoint": {"lat": -33.91799, "lon": 25.57007}}, {"city": "Pretoria Gauteng", "country": "South Africa"}, {"city": "Pretoria", "country": "South Africa", "geoPoint": {"lat": -25.74486, "lon": 28.18783}}, {"city": "Soweto", "country": "South Africa", "geoPoint": {"lat": -26.26781, "lon": 27.85849}}, {"city": "A Coruna", "country": "Spain", "geoPoint": {"lat": 43.37135, "lon": -8.396}}, {"city": "Almeria", "country": "Spain", "geoPoint": {"lat": 36.83814, "lon": -2.45974}}, {"city": "Barcelona", "country": "Spain", "geoPoint": {"lat": 41.38879, "lon": 2.15899}}, {"city": "B<PERSON><PERSON>", "country": "Spain"}, {"city": "Ciudad Real", "country": "Spain", "geoPoint": {"lat": 38.98626, "lon": -3.92907}}, {"city": "Getafe", "country": "Spain", "geoPoint": {"lat": 40.30571, "lon": -3.73295}}, {"city": "Girona", "country": "Spain", "geoPoint": {"lat": 41.98311, "lon": 2.82493}}, {"city": "Granada", "country": "Spain", "geoPoint": {"lat": 37.18817, "lon": -3.60667}}, {"city": "La Roca del Vallès", "country": "Spain", "geoPoint": {"lat": 41.58333, "lon": 2.33333}}, {"city": "Madrid", "country": "Spain", "geoPoint": {"lat": 40.4165, "lon": -3.70256}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 40.47353, "lon": -3.87182}}, {"city": "Málaga", "country": "Spain", "geoPoint": {"lat": 36.72016, "lon": -4.42034}}, {"city": "Palma de Mallorca", "country": "Spain", "geoPoint": {"lat": 39.56939, "lon": 2.65024}}, {"city": "Pozuelo de Alarcón", "country": "Spain", "geoPoint": {"lat": 40.43293, "lon": -3.81338}}, {"city": "Sagunto", "country": "Spain", "geoPoint": {"lat": 39.68333, "lon": -0.26667}}, {"city": "Santander", "country": "Spain", "geoPoint": {"lat": 43.46472, "lon": -3.80444}}, {"city": "Sevilla", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Valencia", "country": "Spain", "geoPoint": {"lat": 39.46975, "lon": -0.37739}}, {"city": "Vic", "country": "Spain", "geoPoint": {"lat": 41.93012, "lon": 2.25486}}, {"city": "Viladecans", "country": "Spain", "geoPoint": {"lat": 41.31405, "lon": 2.01427}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Taiwan", "geoPoint": {"lat": 22.61626, "lon": 120.31333}}, {"city": "New Taipei City", "country": "Taiwan", "geoPoint": {"lat": 25.01111, "lon": 121.44583}}, {"city": "Taichung", "country": "Taiwan", "geoPoint": {"lat": 24.1469, "lon": 120.6839}}, {"city": "Taipei", "country": "Taiwan", "geoPoint": {"lat": 25.04776, "lon": 121.53185}}, {"city": "Chernivtsi", "country": "Ukraine", "geoPoint": {"lat": 48.29149, "lon": 25.94034}}, {"city": "Dnipropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "Ivano-Frankivsk", "country": "Ukraine", "geoPoint": {"lat": 48.9215, "lon": 24.70972}}, {"city": "Kharkiv", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Kyiv", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Ternopil", "country": "Ukraine", "geoPoint": {"lat": 49.55589, "lon": 25.60556}}, {"city": "Vinnitsya", "country": "Ukraine", "geoPoint": {"lat": 49.23278, "lon": 28.48097}}, {"city": "Vinnytsia", "country": "Ukraine", "geoPoint": {"lat": 49.23278, "lon": 28.48097}}, {"city": "Zaporizhzhia", "country": "Ukraine", "geoPoint": {"lat": 50.60727, "lon": 31.78792}}, {"city": "<PERSON><PERSON><PERSON>", "country": "United Arab Emirates", "geoPoint": {"lat": 25.41111, "lon": 55.43504}}, {"city": "Dubai", "country": "United Arab Emirates", "geoPoint": {"lat": 25.0657, "lon": 55.17128}}, {"city": "Sharjah", "country": "United Arab Emirates", "geoPoint": {"lat": 25.33737, "lon": 55.41206}}, {"city": "Aberdeen", "country": "United Kingdom", "geoPoint": {"lat": 57.14369, "lon": -2.09814}}, {"city": "Antrim", "country": "United Kingdom", "geoPoint": {"lat": 54.7, "lon": -6.2}}, {"city": "<PERSON>", "country": "United Kingdom", "geoPoint": {"lat": 51.39979, "lon": -3.2838}}, {"city": "Birmingham", "country": "United Kingdom", "geoPoint": {"lat": 52.48142, "lon": -1.89983}}, {"city": "Blackburn", "country": "United Kingdom", "geoPoint": {"lat": 53.75, "lon": -2.48333}}, {"city": "Bolton", "country": "United Kingdom", "geoPoint": {"lat": 53.58333, "lon": -2.43333}}, {"city": "Bournemouth", "country": "United Kingdom", "geoPoint": {"lat": 50.72048, "lon": -1.8795}}, {"city": "Bradford", "country": "United Kingdom", "geoPoint": {"lat": 53.79391, "lon": -1.75206}}, {"city": "Bristol", "country": "United Kingdom", "geoPoint": {"lat": 51.45523, "lon": -2.59665}}, {"city": "Burbage", "country": "United Kingdom", "geoPoint": {"lat": 51.35184, "lon": -1.67087}}, {"city": "<PERSON><PERSON>", "country": "United Kingdom"}, {"city": "Cardiff", "country": "United Kingdom", "geoPoint": {"lat": 51.48, "lon": -3.18}}, {"city": "<PERSON><PERSON><PERSON>", "country": "United Kingdom"}, {"city": "Carmarthen", "country": "United Kingdom", "geoPoint": {"lat": 51.85552, "lon": -4.30535}}, {"city": "Chester", "country": "United Kingdom", "geoPoint": {"lat": 53.1905, "lon": -2.89189}}, {"city": "Darlington", "country": "United Kingdom", "geoPoint": {"lat": 54.52429, "lon": -1.55039}}, {"city": "Doncaster", "country": "United Kingdom", "geoPoint": {"lat": 53.52285, "lon": -1.13116}}, {"city": "Durham", "country": "United Kingdom", "geoPoint": {"lat": 54.77676, "lon": -1.57566}}, {"city": "Ely", "country": "United Kingdom", "geoPoint": {"lat": 52.39964, "lon": 0.26196}}, {"city": "Hampstead", "country": "United Kingdom", "geoPoint": {"lat": 51.55744, "lon": -0.18213}}, {"city": "Harlow", "country": "United Kingdom", "geoPoint": {"lat": 51.77655, "lon": 0.11158}}, {"city": "Huntingdon", "country": "United Kingdom", "geoPoint": {"lat": 52.33049, "lon": -0.18651}}, {"city": "Leamington Spa", "country": "United Kingdom", "geoPoint": {"lat": 52.3, "lon": -1.53333}}, {"city": "Liskeard", "country": "United Kingdom", "geoPoint": {"lat": 50.4545, "lon": -4.46517}}, {"city": "London", "country": "United Kingdom", "geoPoint": {"lat": 51.50853, "lon": -0.12574}}, {"city": "Middlesbrough", "country": "United Kingdom", "geoPoint": {"lat": 54.57623, "lon": -1.23483}}, {"city": "Plymouth", "country": "United Kingdom", "geoPoint": {"lat": 50.37153, "lon": -4.14305}}, {"city": "Rhyl", "country": "United Kingdom", "geoPoint": {"lat": 53.31929, "lon": -3.49228}}, {"city": "Rugby", "country": "United Kingdom", "geoPoint": {"lat": 52.37092, "lon": -1.26417}}, {"city": "Salford", "country": "United Kingdom", "geoPoint": {"lat": 53.48771, "lon": -2.29042}}, {"city": "Stoke On Trent", "country": "United Kingdom", "geoPoint": {"lat": 53.00415, "lon": -2.18538}}, {"city": "Swansea", "country": "United Kingdom", "geoPoint": {"lat": 51.62079, "lon": -3.94323}}, {"city": "Torpoint", "country": "United Kingdom", "geoPoint": {"lat": 50.37505, "lon": -4.19566}}, {"city": "Watford", "country": "United Kingdom", "geoPoint": {"lat": 51.65531, "lon": -0.39602}}, {"city": "Welwyn Garden City", "country": "United Kingdom", "geoPoint": {"lat": 51.80174, "lon": -0.20691}}, {"city": "Westcliff on Sea", "country": "United Kingdom", "geoPoint": {"lat": 51.54424, "lon": 0.69179}}, {"city": "Wokingham", "country": "United Kingdom", "geoPoint": {"lat": 51.4112, "lon": -0.83565}}, {"city": "Wolverhampton", "country": "United Kingdom", "geoPoint": {"lat": 52.58547, "lon": -2.12296}}]}, "referencesModule": {"references": [{"pmid": "36302584", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Figtree GA, <PERSON><PERSON> J, <PERSON>, <PERSON><PERSON><PERSON><PERSON>J<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Cardiovascular Effects of Canagliflozin in Relation to Renal Function and Albuminuria. J Am Coll Cardiol. 2022 Nov 1;80(18):1721-1731. doi: 10.1016/j.jacc.2022.08.772."}, {"pmid": "35929472", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MBiostat QL, Cannon CP, Wheeler DC, Cha<PERSON><PERSON> DM, Barraclough J, Figtree GA, <PERSON><PERSON><PERSON> R, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effect of Canagliflozin on Total Cardiovascular Burden in Patients With Diabetes and Chronic Kidney Disease: A Post Hoc Analysis From the CREDENCE Trial. J Am Heart Assoc. 2022 Aug 16;11(16):e025045. doi: 10.1161/JAHA.121.025045. Epub 2022 Aug 5."}, {"pmid": "35063969", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; CREDENCE study investigators. Potential Effects of Elimination of the Black Race Coefficient in eGFR Calculations in the CREDENCE Trial. Clin J Am Soc Nephrol. 2022 Mar;17(3):361-373. doi: 10.2215/CJN.08980621. Epub 2022 Jan 21."}, {"pmid": "34029680", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON> HJL, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>zin and <PERSON><PERSON>-Related Adverse Events in Type 2 Diabetes and CKD: Findings From the Randomized CREDENCE Trial. Am J Kidney Dis. 2022 Feb;79(2):244-256.e1. doi: 10.1053/j.ajkd.2021.05.005. Epub 2021 May 23."}, {"pmid": "33874750", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>; CREDENCE Trial Investigators*. Effect of SGLT2 Inhibitors on Stroke and Atrial Fibrillation in Diabetic Kidney Disease: Results From the CREDENCE Trial and Meta-Analysis. Stroke. 2021 May;52(5):1545-1556. doi: 10.1161/STROKEAHA.120.031623. Epub 2021 Apr 20."}, {"pmid": "33826709", "type": "DERIVED", "citation": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of canagliflozin on myocardial infarction: a post hoc analysis of the CANVAS programme and CREDENCE trial. Cardiovasc Res. 2022 Mar 16;118(4):1103-1114. doi: 10.1093/cvr/cvab128."}, {"pmid": "33619120", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Cardiovascular, and Safety Outcomes of Canagliflozin according to <PERSON><PERSON>: A CREDENCE Secondary Analysis. Clin J Am Soc Nephrol. 2021 Mar 8;16(3):384-395. doi: 10.2215/CJN.15260920. Epub 2021 Feb 22."}, {"pmid": "33554616", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> HJ<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>L. Blood Pressure Effects of Canagliflozin and Clinical Outcomes in Type 2 Diabetes and Chronic Kidney Disease: Insights From the CREDENCE Trial. Circulation. 2021 May 4;143(18):1735-1749. doi: 10.1161/CIRCULATIONAHA.120.048740. Epub 2021 Feb 8."}, {"pmid": "33358942", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Effects of canagliflozin on cardiovascular, renal, and safety outcomes in participants with type 2 diabetes and chronic kidney disease according to history of heart failure: Results from the CREDENCE trial. Am Heart J. 2021 Mar;233:141-148. doi: 10.1016/j.ahj.2020.12.008. Epub 2020 Dec 22."}, {"pmid": "33340064", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>ion of Estimated Glomerular Filtration Rate Decline with Canagliflozin and Implications for Dialysis Utilization and Cost in Diabetic Nephropathy. Diabetes Ther. 2021 Feb;12(2):499-508. doi: 10.1007/s13300-020-00953-4. Epub 2020 Dec 18."}, {"pmid": "33263893", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. <PERSON>st-Effectiveness of Canagliflozin Added to Standard of Care for Treating Diabetic Kidney Disease (DKD) in Patients with Type 2 Diabetes Mellitus (T2DM) in England: Estimates Using the CREDEM-DKD Model. Diabetes Ther. 2021 Jan;12(1):313-328. doi: 10.1007/s13300-020-00968-x. Epub 2020 Dec 2."}, {"pmid": "32930969", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Development and Internal Validation of a Discrete Event Simulation Model of Diabetic Kidney Disease Using CREDENCE Trial Data. Diabetes Ther. 2020 Nov;11(11):2657-2676. doi: 10.1007/s13300-020-00923-w. Epub 2020 Sep 15."}, {"pmid": "32354987", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>; CREDENCE Study Investigators. Renal, Cardiovascular, and Safety Outcomes of Canagliflozin by Baseline Kidney Function: A Secondary Analysis of the CREDENCE Randomized Trial. J Am Soc Nephrol. 2020 May;31(5):1128-1139. doi: 10.1681/ASN.**********."}, {"pmid": "31707795", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Evaluating the Effects of Canagliflozin on Cardiovascular and Renal Events in Patients With Type 2 Diabetes Mellitus and Chronic Kidney Disease According to Baseline HbA1c, Including Those With HbA1c <7%: Results From the CREDENCE Trial. Circulation. 2020 Feb 4;141(5):407-410. doi: 10.1161/CIRCULATIONAHA.119.044359. Epub 2019 Nov 11. No abstract available."}, {"pmid": "31291786", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and Cardiovascular and Renal Outcomes in Type 2 Diabetes Mellitus and Chronic Kidney Disease in Primary and Secondary Cardiovascular Prevention Groups. Circulation. 2019 Aug 27;140(9):739-750. doi: 10.1161/CIRCULATIONAHA.119.042007. Epub 2019 Jul 11."}, {"pmid": "30990260", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; CREDENCE Trial Investigators. Canagliflozin and Renal Outcomes in Type 2 Diabetes and Nephropathy. N Engl J Med. 2019 Jun 13;380(24):2295-2306. doi: 10.1056/NEJMoa1811744. Epub 2019 Apr 14."}, {"pmid": "29253846", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>; CREDENCE study investigators. The Canagliflozin and Renal Endpoints in Diabetes with Established Nephropathy Clinical Evaluation (CREDENCE) Study Rationale, Design, and Baseline Characteristics. Am J Nephrol. 2017 Dec 13;46(6):462-472. doi: 10.1159/000484633. Online ahead of print."}]}}}