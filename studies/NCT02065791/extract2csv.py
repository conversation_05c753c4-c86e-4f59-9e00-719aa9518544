#!/usr/bin/env python3
"""
Extract adverse events from JSON and convert to CSV
Only includes serious and non-serious adverse events
"""

import json
import pandas as pd

def extract_adverse_events_to_csv():
    """
    Extract adverse events from JSON and create a single CSV with both serious and non-serious events
    """
    
    # Load the adverse events data
    try:
        with open('adverse_events.json', 'r') as f:
            adverse_events = json.load(f)
    except FileNotFoundError:
        print("❌ adverse_events.json not found. Please run extract_results_posted.py first.")
        return False
    
    print("=== EXTRACTING ADVERSE EVENTS TO CSV ===")
    print(f"Frequency Threshold: {adverse_events.get('frequencyThreshold', 'Not specified')}%")
    
    # Get event groups (treatment arms)
    event_groups = adverse_events.get('eventGroups', [])
    print(f"Found {len(event_groups)} treatment groups")
    
    # Create a mapping of group IDs to group titles
    group_mapping = {}
    for group in event_groups:
        group_mapping[group.get('id', '')] = group.get('title', '')
    
    # Collect all adverse events data
    all_adverse_events = []
    
    # Process serious adverse events
    serious_events = adverse_events.get('seriousEvents', [])
    print(f"Found {len(serious_events)} serious adverse event types")
    
    for event in serious_events:
        event_term = event.get('term', '')
        organ_system = event.get('organSystem', '')
        assessment_type = event.get('assessmentType', '')
        
        for stat in event.get('stats', []):
            group_id = stat.get('groupId', '')
            group_title = group_mapping.get(group_id, group_id)
            
            all_adverse_events.append({
                'Event_Category': 'Serious',
                'Event_Term': event_term,
                'Organ_System': organ_system,
                'Assessment_Type': assessment_type,
                'Treatment_Group': group_title,
                'Number_of_Events': stat.get('numEvents', 0),
                'Number_Affected': stat.get('numAffected', 0),
                'Number_at_Risk': stat.get('numAtRisk', 0),
                'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
            })
    
    # Process other (non-serious) adverse events
    other_events = adverse_events.get('otherEvents', [])
    print(f"Found {len(other_events)} other adverse event types")
    
    for event in other_events:
        event_term = event.get('term', '')
        organ_system = event.get('organSystem', '')
        assessment_type = event.get('assessmentType', '')
        
        for stat in event.get('stats', []):
            group_id = stat.get('groupId', '')
            group_title = group_mapping.get(group_id, group_id)
            
            all_adverse_events.append({
                'Event_Category': 'Non-Serious',
                'Event_Term': event_term,
                'Organ_System': organ_system,
                'Assessment_Type': assessment_type,
                'Treatment_Group': group_title,
                'Number_of_Events': stat.get('numEvents', 0),
                'Number_Affected': stat.get('numAffected', 0),
                'Number_at_Risk': stat.get('numAtRisk', 0),
                'Percentage': f"{(stat.get('numAffected', 0) / stat.get('numAtRisk', 1) * 100):.1f}%" if stat.get('numAtRisk', 0) > 0 else "0.0%"
            })
    
    # Create DataFrame and save to CSV
    if all_adverse_events:
        df = pd.DataFrame(all_adverse_events)
        
        # Sort by Event Category (Serious first), then by Event Term
        df = df.sort_values(['Event_Category', 'Event_Term', 'Treatment_Group'])
        
        # Save to CSV
        filename = 'adverse_events.csv'
        df.to_csv(filename, index=False)
        
        print(f"\n=== ADVERSE EVENTS SUMMARY ===")
        print(f"Total adverse event records: {len(df)}")
        print(f"Serious adverse events: {len(df[df['Event_Category'] == 'Serious'])}")
        print(f"Non-serious adverse events: {len(df[df['Event_Category'] == 'Non-Serious'])}")
        print(f"Unique event terms: {df['Event_Term'].nunique()}")
        print(f"Treatment groups: {df['Treatment_Group'].nunique()}")
        
        print(f"\n=== SAMPLE DATA ===")
        print(df.head(10).to_string(index=False))
        
        print(f"\n✅ Successfully saved adverse events to {filename}")
        return True
    else:
        print("❌ No adverse events data found")
        return False

def main():
    success = extract_adverse_events_to_csv()
    
    if success:
        print("\n🎉 Adverse events extraction completed successfully!")
        print("Generated file: adverse_events.csv")
    else:
        print("\n❌ Failed to extract adverse events data")

if __name__ == "__main__":
    main()
