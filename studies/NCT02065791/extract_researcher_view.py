#!/usr/bin/env python3
"""
Extract Researcher View tab data from ClinicalTrials.gov
"""

import requests
import json

def extract_researcher_view(nct_id):
    """
    Extract Researcher View data using the ClinicalTrials.gov API v2
    This corresponds to the "Researcher View" tab which shows more detailed/technical information
    """
    
    # API URL for the full study data (researcher view typically shows more complete data)
    api_url = f"https://clinicaltrials.gov/api/v2/studies/{nct_id}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    print(f"Extracting Researcher View for {nct_id}...")
    print(f"API URL: {api_url}")
    
    try:
        response = requests.get(api_url, headers=headers, timeout=30)
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("Successfully retrieved Researcher View JSON data")
                
                # For researcher view, we want the complete data structure
                # This includes both protocol and results sections with full detail
                researcher_view = {
                    'nctId': nct_id,
                    'extractedAt': requests.utils.default_headers()['User-Agent'],
                    'dataSource': 'ClinicalTrials.gov API v2',
                    'tabType': 'Researcher View',
                    'fullStudyData': data  # Complete data structure
                }
                
                # Save the researcher view data
                filename = 'researcher_view.json'
                with open(filename, 'w') as f:
                    json.dump(researcher_view, f, indent=2)
                print(f"Saved Researcher View data to {filename}")
                
                # Print summary of what was extracted
                print("\n=== RESEARCHER VIEW SUMMARY ===")
                
                # Show the main sections available
                print("Available data sections:")
                if 'protocolSection' in data:
                    protocol_modules = list(data['protocolSection'].keys())
                    print(f"  Protocol Section modules: {protocol_modules}")
                
                if 'resultsSection' in data:
                    results_modules = list(data['resultsSection'].keys())
                    print(f"  Results Section modules: {results_modules}")
                
                if 'derivedSection' in data:
                    derived_modules = list(data['derivedSection'].keys())
                    print(f"  Derived Section modules: {derived_modules}")
                
                # Show some key researcher-relevant information
                if 'protocolSection' in data:
                    protocol = data['protocolSection']
                    
                    # Sponsor information
                    sponsor_info = protocol.get('sponsorCollaboratorsModule', {})
                    if 'leadSponsor' in sponsor_info:
                        print(f"Lead Sponsor: {sponsor_info['leadSponsor'].get('name', 'N/A')}")
                    
                    # Oversight information
                    oversight = protocol.get('oversightModule', {})
                    print(f"FDA Regulated Drug: {oversight.get('isFdaRegulatedDrug', 'N/A')}")
                    print(f"FDA Regulated Device: {oversight.get('isFdaRegulatedDevice', 'N/A')}")
                    
                    # Detailed design information
                    design = protocol.get('designModule', {})
                    if 'designInfo' in design:
                        design_info = design['designInfo']
                        print(f"Allocation: {design_info.get('allocation', 'N/A')}")
                        print(f"Intervention Model: {design_info.get('interventionModel', 'N/A')}")
                        print(f"Masking: {design_info.get('maskingInfo', {}).get('masking', 'N/A')}")
                        print(f"Primary Purpose: {design_info.get('primaryPurpose', 'N/A')}")
                    
                    # Outcome measures
                    outcomes = protocol.get('outcomesModule', {})
                    if 'primaryOutcomes' in outcomes:
                        print(f"Number of Primary Outcomes: {len(outcomes['primaryOutcomes'])}")
                    if 'secondaryOutcomes' in outcomes:
                        print(f"Number of Secondary Outcomes: {len(outcomes['secondaryOutcomes'])}")
                    
                    # Contact information
                    contacts = protocol.get('contactsLocationsModule', {})
                    if 'centralContacts' in contacts:
                        print(f"Central Contacts Available: {len(contacts['centralContacts'])}")
                    if 'locations' in contacts:
                        print(f"Study Locations: {len(contacts['locations'])}")
                
                return researcher_view
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                print("Response content:", response.text[:500])
                return None
        else:
            print(f"HTTP error: {response.status_code}")
            print("Response:", response.text[:500])
            return None
            
    except requests.RequestException as e:
        print(f"Request failed: {e}")
        return None

def main():
    nct_id = "NCT02065791"
    
    print("=== EXTRACTING RESEARCHER VIEW TAB ===")
    researcher_view = extract_researcher_view(nct_id)
    
    if researcher_view:
        print("\n✅ Successfully extracted Researcher View data!")
        print("Generated file: researcher_view.json")
    else:
        print("\n❌ Failed to extract Researcher View data")

if __name__ == "__main__":
    main()
