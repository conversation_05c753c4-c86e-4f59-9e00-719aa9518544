{"nctId": "NCT01137812", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Researcher View", "fullStudyData": {"protocolSection": {"identificationModule": {"nctId": "NCT01137812", "orgStudyIdInfo": {"id": "CR017185"}, "secondaryIdInfos": [{"id": "28431754DIA3015", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "The CANTATA-D2 Trial (CANagliflozin Treatment And Trial Analysis - DPP-4 Inhibitor Second Comparator Trial)", "officialTitle": "A Randomized, Double-Blind, Active-Controlled, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin Versus Sitagliptin in the Treatment of Subjects With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin and Sulphonylurea Therapy"}, "statusModule": {"statusVerifiedDate": "2015-01", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2010-07"}, "primaryCompletionDateStruct": {"date": "2012-03", "type": "ACTUAL"}, "completionDateStruct": {"date": "2012-03", "type": "ACTUAL"}, "studyFirstSubmitDate": "2010-06-03", "studyFirstSubmitQcDate": "2010-06-03", "studyFirstPostDateStruct": {"date": "2010-06-04", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2013-04-02", "resultsFirstSubmitQcDate": "2013-04-02", "resultsFirstPostDateStruct": {"date": "2013-05-17", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2012-06-01", "dispFirstSubmitQcDate": "2012-06-12", "dispFirstPostDateStruct": {"date": "2012-06-14", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2015-01-05", "lastUpdatePostDateStruct": {"date": "2015-01-15", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the efficacy and safety of canagliflozin compared with sitagliptin in patients with type 2 diabetes mellitus who are receiving treatment with metformin and sulphonylurea and have inadequate glycemic (blood sugar) control.", "detailedDescription": "Canagliflozin is a drug that is being tested to see if it may be useful in treating patients diagnosed with type 2 diabetes mellitus (T2DM). This is a randomized (study drug assigned by chance), double-blind (neither the patient or the study doctor will know the name of the assigned treatment), multicenter study to determine the efficacy, safety, and tolerability of canagliflozin 300 mg compared to sitagliptin 100 mg (an antihyperglycemic drug) in patients with T2DM who are not achieving an adequate response from current antihyperglycemic therapy with metformin and sulphonylurea to control their diabetes. Approximately 720 patients with T2DM who are receiving combination therapy with metformin and sulphonylurea will receive the addition of once-daily treatment with canagliflozin 300 mg or sitagliptin 100 mg capsules for 52 weeks. Patients will participate in the study for approximately 59 to 72 weeks. During treatment, patients will be monitored for safety by review of adverse events, results from laboratory tests, 12-lead electrocardiograms (ECGs), vital signs measurements, body weight, physical examinations, and self monitored blood glucose (SMBG) measurements. The primary outcome measure in the study is the effect of canagliflozin compared to sitagliptin on hemoglobin A1c (HbA1c) after 52 weeks of treatment. Study drug will be taken orally (by mouth) once daily before the first meal each day unless otherwise specified. Patients will take single-blind placebo for 2 weeks before randomization. After randomization, patients in the study will take double-blind canagliflozin 300 mg or matching sitagliptin 100 mg for 52 weeks."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Canagliflozin", "<PERSON><PERSON><PERSON><PERSON> (Januvia)", "Metformin", "Sulphonylurea", "Hemoglobin A1c", "Type 2 diabetes mellitus"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE3"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "TRIPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR"]}}, "enrollmentInfo": {"count": 756, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 300 mg", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea", "interventionNames": ["Drug: Canagliflozin 300 mg", "Drug: Metformin", "Drug: Sulphonylurea"]}, {"label": "Sitagliptin 100 mg", "type": "ACTIVE_COMPARATOR", "description": "Each patient will receive 100 mg of sitagliptin once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea", "interventionNames": ["Drug: Sitagliptin 100 mg", "Drug: Metformin", "Drug: Sulphonylurea"]}], "interventions": [{"type": "DRUG", "name": "Sitagliptin 100 mg", "description": "One 100 mg capsule once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea", "armGroupLabels": ["Sitagliptin 100 mg"]}, {"type": "DRUG", "name": "Canagliflozin 300 mg", "description": "One 300 mg capsule once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea", "armGroupLabels": ["Canagliflozin 300 mg"]}, {"type": "DRUG", "name": "Metformin", "description": "Patients will continue to take background therapy with Metformin for T2DM at maximally or near-maximally effective protocol-specified doses for the duration of the study.", "armGroupLabels": ["Canagliflozin 300 mg", "Sitagliptin 100 mg"]}, {"type": "DRUG", "name": "Sulphonylurea", "description": "Patients will continue to take background therapy with Sulphonylurea for T2DM at maximally or near-maximally effective protocol-specified doses for the duration of the study.", "armGroupLabels": ["Canagliflozin 300 mg", "Sitagliptin 100 mg"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}], "secondaryOutcomes": [{"measure": "Percentage of Patients With HbA1c <7% at Week 52", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 52 in each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the percentage.", "timeFrame": "Week 52"}, {"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in Body Weight From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean percent change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Change in Systolic Blood Pressure (SBP) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in SBP from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in Triglycerides From Baseline to Week 52", "description": "The table below shows the mean percent change in triglycerides from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in High-density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 52", "description": "The table below shows the mean percent change in HDL-C from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* All patients must have a diagnosis of T2DM and be currently treated with metformin and sulphonylurea\n* Patients in the study must have a HbA1c between \\>=7 and \\<=10.5% and a fasting plasma glucose (FPG) \\<300 mg/dL (16.7 mmol/L)\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus (T1DM), pancreas or beta cell transplantation, or diabetes secondary to pancreatitis or pancreatectomy\n* or a severe hypoglycemic episode within 6 months before screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC C. Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Mobile", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 30.69436, "lon": -88.04305}}, {"city": "Goodyear", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.43532, "lon": -112.35821}}, {"city": "Mesa", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.42227, "lon": -111.82264}}, {"city": "<PERSON>scondi<PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.11921, "lon": -117.08642}}, {"city": "Fresno", "state": "California", "country": "United States", "geoPoint": {"lat": 36.74773, "lon": -119.77237}}, {"city": "Greenbrae", "state": "California", "country": "United States", "geoPoint": {"lat": 37.94854, "lon": -122.5247}}, {"city": "La Mesa", "state": "California", "country": "United States", "geoPoint": {"lat": 32.76783, "lon": -117.02308}}, {"city": "Laguna Hills", "state": "California", "country": "United States", "geoPoint": {"lat": 33.61252, "lon": -117.71283}}, {"city": "Lancaster", "state": "California", "country": "United States", "geoPoint": {"lat": 34.69804, "lon": -118.13674}}, {"city": "Long Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 33.76696, "lon": -118.18923}}, {"city": "Los Angeles", "state": "California", "country": "United States", "geoPoint": {"lat": 34.05223, "lon": -118.24368}}, {"city": "Norwalk", "state": "California", "country": "United States", "geoPoint": {"lat": 33.90224, "lon": -118.08173}}, {"city": "Pismo Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 35.14275, "lon": -120.64128}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Spring Valley", "state": "California", "country": "United States", "geoPoint": {"lat": 32.74477, "lon": -116.99892}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "Watsonville", "state": "California", "country": "United States", "geoPoint": {"lat": 36.91023, "lon": -121.75689}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Northglenn", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.88554, "lon": -104.9872}}, {"city": "Milford", "state": "Connecticut", "country": "United States", "geoPoint": {"lat": 41.22232, "lon": -73.0565}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.49893, "lon": -82.57482}}, {"city": "Cape Coral", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.56285, "lon": -81.94953}}, {"city": "Clearwater", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.96585, "lon": -82.8001}}, {"city": "Delray Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.46146, "lon": -80.07282}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "Pembroke Pines", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.00315, "lon": -80.22394}}, {"city": "Tampa", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.94752, "lon": -82.45843}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "<PERSON><PERSON>", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 46.41655, "lon": -117.01766}}, {"city": "Chicago", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.85003, "lon": -87.65005}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 38.59227, "lon": -89.91121}}, {"city": "Springfield", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 39.80172, "lon": -89.64371}}, {"city": "Topeka", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04833, "lon": -95.67804}}, {"city": "Bowling Green", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 36.99032, "lon": -86.4436}}, {"city": "Lexington", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.98869, "lon": -84.47772}}, {"city": "Madisonville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.3281, "lon": -87.49889}}, {"city": "Baton Rouge", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.45075, "lon": -91.15455}}, {"city": "Mandeville", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.35825, "lon": -90.06563}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Sunset", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.41131, "lon": -92.06845}}, {"city": "Brockton", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.08343, "lon": -71.01838}}, {"city": "Saint Clair Shores", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.49698, "lon": -82.88881}}, {"city": "Southfield", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.47337, "lon": -83.22187}}, {"city": "Dakota Dunes", "state": "Nebraska", "country": "United States"}, {"city": "Toms River", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 39.95373, "lon": -74.19792}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "West Seneca", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.85006, "lon": -78.79975}}, {"city": "Calabash", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 33.89073, "lon": -78.56834}}, {"city": "Greensboro", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 36.07264, "lon": -79.79198}}, {"city": "Moerhead City", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.72294, "lon": -76.72604}}, {"city": "<PERSON><PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.74541, "lon": -81.68482}}, {"city": "Cincinnati", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.12713, "lon": -84.51435}}, {"city": "Gallipolis", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 38.8098, "lon": -82.20237}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 40.58867, "lon": -83.12852}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.36006, "lon": -84.30994}}, {"city": "Toledo", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.66394, "lon": -83.55521}}, {"city": "Zanesville", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.94035, "lon": -82.01319}}, {"city": "Beaver", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.69534, "lon": -80.30478}}, {"city": "Bensalem", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.10455, "lon": -74.95128}}, {"city": "Norristown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.1215, "lon": -75.3399}}, {"city": "Perryopolis", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.08702, "lon": -79.7506}}, {"city": "Philadelphia", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 39.95233, "lon": -75.16379}}, {"city": "Pittsburgh", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.44062, "lon": -79.99589}}, {"city": "Pottstown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.24537, "lon": -75.64963}}, {"city": "Greenville", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.85262, "lon": -82.39401}}, {"city": "Spartanburg", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.94957, "lon": -81.93205}}, {"city": "Johnson City", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.31344, "lon": -82.35347}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Fort Worth", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.72541, "lon": -97.32085}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "<PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.11712, "lon": -97.7278}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.94818, "lon": -96.72972}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.55217, "lon": -98.26973}}, {"city": "Sugar Land", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.61968, "lon": -95.63495}}, {"city": "<PERSON>", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 38.79345, "lon": -77.27165}}, {"city": "Danville", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.58597, "lon": -79.39502}}, {"city": "Falls Church", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 38.88233, "lon": -77.17109}}, {"city": "<PERSON>", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.02987, "lon": -76.34522}}, {"city": "Henrico", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.59264, "lon": -78.61611}}, {"city": "Manassas", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 38.75095, "lon": -77.47527}}, {"city": "Norfolk", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.84681, "lon": -76.28522}}, {"city": "Richmond", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.55376, "lon": -77.46026}}, {"city": "<PERSON><PERSON>", "state": "Washington", "country": "United States", "geoPoint": {"lat": 46.65402, "lon": -120.53007}}, {"city": "Tacoma", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.25288, "lon": -122.44429}}, {"city": "Salzburg", "country": "Austria", "geoPoint": {"lat": 47.79941, "lon": 13.04399}}, {"city": "Wien", "country": "Austria", "geoPoint": {"lat": 48.20849, "lon": 16.37208}}, {"city": "<PERSON><PERSON><PERSON> (Aalst)", "country": "Belgium"}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 50.99231, "lon": 4.70807}}, {"city": "Belem", "country": "Brazil", "geoPoint": {"lat": -1.45583, "lon": -48.50444}}, {"city": "Brasilia", "country": "Brazil", "geoPoint": {"lat": -15.77972, "lon": -47.92972}}, {"city": "Fortaleza", "country": "Brazil", "geoPoint": {"lat": -3.71722, "lon": -38.54306}}, {"city": "Goiânia", "country": "Brazil", "geoPoint": {"lat": -16.67861, "lon": -49.25389}}, {"city": "Marília", "country": "Brazil", "geoPoint": {"lat": -22.21389, "lon": -49.94583}}, {"city": "Passo Fundo", "country": "Brazil", "geoPoint": {"lat": -28.26278, "lon": -52.40667}}, {"city": "Porto Alegre", "country": "Brazil", "geoPoint": {"lat": -30.03306, "lon": -51.23}}, {"city": "Rio De Janeiro", "country": "Brazil", "geoPoint": {"lat": -22.90278, "lon": -43.2075}}, {"city": "Sao Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "Calgary", "state": "Alberta", "country": "Canada", "geoPoint": {"lat": 51.05011, "lon": -114.08529}}, {"city": "Red Deer", "state": "Alberta", "country": "Canada", "geoPoint": {"lat": 52.26682, "lon": -113.802}}, {"city": "Vancouver", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "Halifax", "state": "Nova Scotia", "country": "Canada", "geoPoint": {"lat": 44.64533, "lon": -63.57239}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "Oshawa", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.90012, "lon": -78.84957}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Charlottetown", "state": "Prince Edward Island", "country": "Canada", "geoPoint": {"lat": 46.23899, "lon": -63.13414}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.40008, "lon": -71.89908}}, {"city": "Ville, Laint-Laurent", "state": "Quebec", "country": "Canada"}, {"city": "Pointe<PERSON><PERSON>", "country": "Canada", "geoPoint": {"lat": 45.44868, "lon": -73.81669}}, {"city": "Toronto", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Aalborg", "country": "Denmark", "geoPoint": {"lat": 57.048, "lon": 9.9187}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Denmark", "geoPoint": {"lat": 55.73165, "lon": 12.36328}}, {"city": "Gentofte", "country": "Denmark", "geoPoint": {"lat": 55.74903, "lon": 12.54601}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Denmark", "geoPoint": {"lat": 55.70927, "lon": 9.5357}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Denmark"}, {"city": "<PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 48.9018, "lon": 2.48931}}, {"city": "Dijon", "country": "France", "geoPoint": {"lat": 47.31667, "lon": 5.01667}}, {"city": "Marseille", "country": "France", "geoPoint": {"lat": 43.29551, "lon": 5.38958}}, {"city": "Nantes", "country": "France", "geoPoint": {"lat": 47.21725, "lon": -1.55336}}, {"city": "Narbonne Cedex", "country": "France", "geoPoint": {"lat": 43.18396, "lon": 3.00141}}, {"city": "Paris", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "<PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 44.81011, "lon": -0.64129}}, {"city": "St Etienne", "country": "France", "geoPoint": {"lat": 45.43389, "lon": 4.39}}, {"city": "Berlin", "country": "Germany", "geoPoint": {"lat": 52.52437, "lon": 13.41053}}, {"city": "Dresden", "country": "Germany", "geoPoint": {"lat": 51.05089, "lon": 13.73832}}, {"city": "Eisenach", "country": "Germany", "geoPoint": {"lat": 50.9807, "lon": 10.31522}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 50.1763, "lon": 10.95048}}, {"city": "Meißen", "country": "Germany", "geoPoint": {"lat": 51.16158, "lon": 13.4737}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Coimbatore", "country": "India", "geoPoint": {"lat": 11.00555, "lon": 76.96612}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Jaipur", "country": "India", "geoPoint": {"lat": 26.91962, "lon": 75.78781}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "<PERSON><PERSON>", "country": "India", "geoPoint": {"lat": 19.99727, "lon": 73.79096}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Beer Sheba", "country": "Israel", "geoPoint": {"lat": 31.25181, "lon": 34.7913}}, {"city": "Jerusalem", "country": "Israel", "geoPoint": {"lat": 31.76904, "lon": 35.21633}}, {"city": "Kfar Saba", "country": "Israel", "geoPoint": {"lat": 32.175, "lon": 34.90694}}, {"city": "Nazareth", "country": "Israel", "geoPoint": {"lat": 32.69925, "lon": 35.30483}}, {"city": "Rishon Lezion", "country": "Israel", "geoPoint": {"lat": 31.97102, "lon": 34.78939}}, {"city": "Tel-Aviv", "country": "Israel", "geoPoint": {"lat": 32.08088, "lon": 34.78057}}, {"city": "Busan", "country": "Korea, Republic of", "geoPoint": {"lat": 35.10278, "lon": 129.04028}}, {"city": "Daegu", "country": "Korea, Republic of", "geoPoint": {"lat": 35.87028, "lon": 128.59111}}, {"city": "Jeonju<PERSON><PERSON>", "country": "Korea, Republic of"}, {"city": "Se<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.43861, "lon": 127.13778}}, {"city": "Seoul", "country": "Korea, Republic of", "geoPoint": {"lat": 37.566, "lon": 126.9784}}, {"city": "Ipoh", "country": "Malaysia", "geoPoint": {"lat": 4.5841, "lon": 101.0829}}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 1.4655, "lon": 103.7578}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia"}, {"city": "Selangor", "country": "Malaysia"}, {"city": "Breda", "country": "Netherlands", "geoPoint": {"lat": 51.58656, "lon": 4.77596}}, {"city": "Eindhoven", "country": "Netherlands", "geoPoint": {"lat": 51.44083, "lon": 5.47778}}, {"city": "Groningen", "country": "Netherlands", "geoPoint": {"lat": 53.21917, "lon": 6.56667}}, {"city": "Leiderdorp", "country": "Netherlands", "geoPoint": {"lat": 52.15833, "lon": 4.52917}}, {"city": "Rotterdam", "country": "Netherlands", "geoPoint": {"lat": 51.9225, "lon": 4.47917}}, {"city": "Velp Gld", "country": "Netherlands"}, {"city": "Zoetermeer", "country": "Netherlands", "geoPoint": {"lat": 52.0575, "lon": 4.49306}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Dunedin", "country": "New Zealand", "geoPoint": {"lat": -45.87416, "lon": 170.50361}}, {"city": "Rotoru<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -38.13874, "lon": 176.24516}}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Bialystok", "country": "Poland", "geoPoint": {"lat": 53.13333, "lon": 23.16433}}, {"city": "Chrzanow", "country": "Poland", "geoPoint": {"lat": 50.13546, "lon": 19.40203}}, {"city": "Gdansk", "country": "Poland", "geoPoint": {"lat": 54.35205, "lon": 18.64637}}, {"city": "Kamieniec Zabkowicki", "country": "Poland", "geoPoint": {"lat": 50.52541, "lon": 16.87921}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 50.89992, "lon": 16.74441}}, {"city": "Sopot", "country": "Poland", "geoPoint": {"lat": 54.4418, "lon": 18.56003}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Zabrze", "country": "Poland", "geoPoint": {"lat": 50.32492, "lon": 18.78576}}, {"city": "Singapore", "country": "Singapore", "geoPoint": {"lat": 1.28967, "lon": 103.85007}}, {"city": "Dnepropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 48.9215, "lon": 24.70972}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Odessa", "country": "Ukraine", "geoPoint": {"lat": 46.47747, "lon": 30.73262}}, {"city": "Poltava", "country": "Ukraine", "geoPoint": {"lat": 49.59373, "lon": 34.54073}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 50.9216, "lon": 34.80029}}, {"city": "Ternopil", "country": "Ukraine", "geoPoint": {"lat": 49.55589, "lon": 25.60556}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}, {"city": "Zaporozhye", "country": "Ukraine", "geoPoint": {"lat": 47.82289, "lon": 35.19031}}]}, "referencesModule": {"references": [{"pmid": "29313267", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Traina S. Impact of Canagliflozin Treatment on Health-Related Quality of Life among People with Type 2 Diabetes Mellitus: A Pooled Analysis of Patient-Reported Outcomes from Randomized Controlled Trials. Patient. 2018 Jun;11(3):341-352. doi: 10.1007/s40271-017-0290-4."}, {"pmid": "27977934", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>-term safety and tolerability of canagliflozin in patients with type 2 diabetes: a pooled analysis. Curr Med Res Opin. 2017 Mar;33(3):553-562. doi: 10.1080/03007995.2016.1271780. Epub 2017 Jan 4."}, {"pmid": "27391951", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>zin provides greater attainment of both HbA1c and body weight reduction versus sitagliptin in patients with type 2 diabetes. Postgrad Med. 2016 Nov;128(8):725-730. doi: 10.1080/00325481.2016.1210988. Epub 2016 Jul 26."}, {"pmid": "26580237", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of Canagliflozin on Fracture Risk in Patients With Type 2 Diabetes Mellitus. J Clin Endocrinol Metab. 2016 Jan;101(1):157-66. doi: 10.1210/jc.2015-3167. Epub 2015 Nov 18."}, {"pmid": "26579834", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Alba M. <PERSON>fficacy and safety of canagliflozin in patients with type 2 diabetes mellitus from Latin America. Curr Med Res Opin. 2016;32(3):427-39. doi: 10.1185/03007995.2015.1121865. Epub 2016 Jan 14."}, {"pmid": "25795432", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Blon<PERSON> L<PERSON>-Related Composite Quality End Point Attainment: Canagliflozin Versus Sitagliptin Based on a Pooled Analysis of 2 Clinical Trials. Clin Ther. 2015 May 1;37(5):1045-54. doi: 10.1016/j.clinthera.2015.02.020. Epub 2015 Mar 18."}, {"pmid": "24585202", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>, a sodium glucose co-transporter 2 inhibitor, improves model-based indices of beta cell function in patients with type 2 diabetes. Diabetologia. 2014 May;57(5):891-901. doi: 10.1007/s00125-014-3196-x. Epub 2014 Mar 1."}, {"pmid": "23564919", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON> compared with sitagliptin for patients with type 2 diabetes who do not have adequate glycemic control with metformin plus sulfonylurea: a 52-week randomized trial. Diabetes Care. 2013 Sep;36(9):2508-15. doi: 10.2337/dc12-2491. Epub 2013 Apr 5."}]}}, "resultsSection": {"participantFlowModule": {"preAssignmentDetails": "A total of 756 patients were randomly allocated to the 2 treatment arms in the study. 755 patients received at least 1 dose of study drug and were included in the modified intent-to-treat (mITT) analysis set and the safety analysis set.", "recruitmentDetails": "This study evaluated the efficacy and safety of canagliflozin compared with sitaglitin in patients with type 2 diabetes mellitus with inadequate control, despite treatment with metformin and sulphonylureas. The study was conducted between 30 June 2010 and 09 March 2012 and recruited patients from 140 study centers located in 17 countries worldwide.", "groups": [{"id": "FG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "FG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "377"}, {"groupId": "FG001", "numSubjects": "378"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "254"}, {"groupId": "FG001", "numSubjects": "210"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "123"}, {"groupId": "FG001", "numSubjects": "168"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "21"}, {"groupId": "FG001", "numSubjects": "14"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "6"}, {"groupId": "FG001", "numSubjects": "8"}]}, {"type": "Physician Decision", "reasons": [{"groupId": "FG000", "numSubjects": "25"}, {"groupId": "FG001", "numSubjects": "17"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "5"}, {"groupId": "FG001", "numSubjects": "4"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "5"}, {"groupId": "FG001", "numSubjects": "13"}]}, {"type": "Death", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "0"}]}, {"type": "Patient met glycemic withdrawal criteria", "reasons": [{"groupId": "FG000", "numSubjects": "40"}, {"groupId": "FG001", "numSubjects": "85"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "19"}, {"groupId": "FG001", "numSubjects": "27"}]}]}]}, "baselineCharacteristicsModule": {"groups": [{"id": "BG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "BG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "BG002", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "377"}, {"groupId": "BG001", "value": "378"}, {"groupId": "BG002", "value": "755"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "304"}, {"groupId": "BG001", "value": "307"}, {"groupId": "BG002", "value": "611"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "73"}, {"groupId": "BG001", "value": "71"}, {"groupId": "BG002", "value": "144"}]}]}]}, {"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "56.5", "spread": "9.62"}, {"groupId": "BG001", "value": "56.6", "spread": "9.33"}, {"groupId": "BG002", "value": "56.5", "spread": "9.47"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "170"}, {"groupId": "BG001", "value": "163"}, {"groupId": "BG002", "value": "333"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "207"}, {"groupId": "BG001", "value": "215"}, {"groupId": "BG002", "value": "422"}]}]}]}, {"title": "Region Treated", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "AUSTRIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "6"}]}]}, {"title": "BELGIUM", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "5"}]}]}, {"title": "BRAZIL", "categories": [{"measurements": [{"groupId": "BG000", "value": "76"}, {"groupId": "BG001", "value": "80"}, {"groupId": "BG002", "value": "156"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "41"}, {"groupId": "BG001", "value": "45"}, {"groupId": "BG002", "value": "86"}]}]}, {"title": "DENMARK", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "7"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "11"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "10"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "33"}, {"groupId": "BG001", "value": "23"}, {"groupId": "BG002", "value": "56"}]}]}, {"title": "ISRAEL", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "10"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "14"}, {"groupId": "BG002", "value": "24"}]}]}, {"title": "NETHERLANDS", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "3"}]}]}, {"title": "NEW ZEALAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "24"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "20"}, {"groupId": "BG001", "value": "25"}, {"groupId": "BG002", "value": "45"}]}]}, {"title": "SINGAPORE", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "4"}]}]}, {"title": "SOUTH KOREA", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "19"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "19"}, {"groupId": "BG002", "value": "46"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "122"}, {"groupId": "BG001", "value": "121"}, {"groupId": "BG002", "value": "243"}]}]}]}]}, "outcomeMeasuresModule": {"outcomeMeasures": [{"type": "PRIMARY", "title": "Change in HbA1c From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "374"}, {"groupId": "OG001", "value": "365"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-1.03", "spread": "0.048"}, {"groupId": "OG001", "value": "-0.66", "spread": "0.049"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "groupDescription": "If the hypothesis of non-inferiority of canagliflozin to sitagliptin at Week 52 was demonstrated (ie, upper bound of the 95% Confidence Interval of the treatment difference \\[canagliflozin minus sitagliptin\\] was less than 0.3) and the upper bound was less than 0.0, the superiority of the canagliflozin dose relative to sitagliptin would be concluded.", "testedNonInferiority": true, "nonInferiorityType": "NON_INFERIORITY_OR_EQUIVALENCE", "nonInferiorityComment": "Power calculation: assuming a difference between canagliflozin and sitagliptin of 0.0% and a common standard deviation of 1.0%, and using a 2-sample, 1-sided t-test with a Type I error rate of 0.025, it was estimated that 234 patients per group would provide approximately 90% power to demonstrate non-inferiority with the non-inferiority margin of 0.3, comparing canagliflozin with sitagliptin.", "pValue": "<0.05", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.37", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.500", "ciUpperLimit": "-0.250", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.064"}]}, {"type": "SECONDARY", "title": "Percentage of Patients With HbA1c <7% at Week 52", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 52 in each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the percentage.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "NUMBER", "unitOfMeasure": "Percentage of patients", "timeFrame": "Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "374"}, {"groupId": "OG001", "value": "365"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "47.6"}, {"groupId": "OG001", "value": "35.3"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "statisticalMethod": "Regression, Logistic", "paramType": "<PERSON><PERSON> (OR)", "paramValue": "1.80", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "1.30", "ciUpperLimit": "2.48"}]}, {"type": "SECONDARY", "title": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "mg/dL", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "373"}, {"groupId": "OG001", "value": "365"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-29.9", "spread": "2.201"}, {"groupId": "OG001", "value": "-5.85", "spread": "2.232"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-24.1", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-29.89", "ciUpperLimit": "-18.24"}]}, {"type": "SECONDARY", "title": "Percent Change in Body Weight From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean percent change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "375"}, {"groupId": "OG001", "value": "367"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-2.5", "spread": "0.2"}, {"groupId": "OG001", "value": "0.3", "spread": "0.2"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-2.8", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.3", "ciUpperLimit": "-2.2", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.3"}]}, {"type": "SECONDARY", "title": "Change in Systolic Blood Pressure (SBP) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in SBP from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "mmHg", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "375"}, {"groupId": "OG001", "value": "367"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-5.06", "spread": "0.656"}, {"groupId": "OG001", "value": "0.85", "spread": "0.666"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-5.91", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-7.642", "ciUpperLimit": "-4.175", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.883"}]}, {"type": "SECONDARY", "title": "Percent Change in Triglycerides From Baseline to Week 52", "description": "The table below shows the mean percent change in triglycerides from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "365"}, {"groupId": "OG001", "value": "353"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "9.6", "spread": "2.8"}, {"groupId": "OG001", "value": "11.9", "spread": "2.9"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.554", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-2.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-9.8", "ciUpperLimit": "5.3", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "3.9"}]}, {"type": "SECONDARY", "title": "Percent Change in High-density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 52", "description": "The table below shows the mean percent change in HDL-C from Baseline to Week 52 for each treatment group. The statistical analysis shows the treatment difference (ie, between canagliflozin and sitagliptin) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when the Week 52 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (<PERSON>ine) and Week 52", "groups": [{"id": "OG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "OG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "364"}, {"groupId": "OG001", "value": "353"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "7.6", "spread": "0.9"}, {"groupId": "OG001", "value": "0.6", "spread": "0.9"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "7.0", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "4.6", "ciUpperLimit": "9.3", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "1.2"}]}]}, "adverseEventsModule": {"frequencyThreshold": "5", "timeFrame": "Adverse events were reported for the duration of the study; each patient participated in the study for approximately 52 weeks.", "description": "The total number of adverse events listed in the \"Other (non-Serious) Adverse Events\" table are based upon a cut-off of greater than or equal to 5 percent of patients experiencing the adverse event in any treatment arm.", "eventGroups": [{"id": "EG000", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea.", "seriousNumAffected": 24, "seriousNumAtRisk": 377, "otherNumAffected": 157, "otherNumAtRisk": 377}, {"id": "EG001", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once a day for 52 weeks with protocol-specified doses of metformin and sulphonylurea.", "seriousNumAffected": 21, "seriousNumAtRisk": 378, "otherNumAffected": 159, "otherNumAtRisk": 378}], "seriousEvents": [{"term": "<PERSON><PERSON>", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Arteriosclerosis coronary artery", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Cardiac arrest", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 2, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Coronary artery disease", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Ischaemic cardiomyopathy", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Myocardial infarction", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 2, "numAtRisk": 378}]}, {"term": "Pancreatitis", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Small intestinal obstruction", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Bile duct obstruction", "organSystem": "Hepatobiliary disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "organSystem": "Hepatobiliary disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Gallbladder oedema", "organSystem": "Hepatobiliary disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Bronchopneumonia", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Leptospirosis", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Pneumonia", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 2, "numAtRisk": 378}]}, {"term": "Pneumonia bacterial", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Pyelonephritis chronic", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Arthropod bite", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Contusion", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Hand fracture", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Hip fracture", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Incisional hernia, obstructive", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Lower limb fracture", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Meniscus lesion", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Splenic rupture", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Tendon rupture", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Toxicity to various agents", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Wound", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Obesity", "organSystem": "Metabolism and nutrition disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Lumbar spinal stenosis", "organSystem": "Musculoskeletal and connective tissue disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Oste<PERSON>th<PERSON>is", "organSystem": "Musculoskeletal and connective tissue disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Cervix carcinoma", "organSystem": "Neoplasms benign, malignant and unspecified (incl cysts and polyps)", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Lung neoplasm malignant", "organSystem": "Neoplasms benign, malignant and unspecified (incl cysts and polyps)", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Oropharyngeal cancer stage unspecified", "organSystem": "Neoplasms benign, malignant and unspecified (incl cysts and polyps)", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Uterine leiomyoma", "organSystem": "Neoplasms benign, malignant and unspecified (incl cysts and polyps)", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Cerebrovascular accident", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Cervical cord compression", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Convulsion", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Transient ischaemic attack", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}, {"term": "Suicide attempt", "organSystem": "Psychiatric disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Renal colic", "organSystem": "Renal and urinary disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Vaginal haemorrhage", "organSystem": "Reproductive system and breast disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 2, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Respiratory arrest", "organSystem": "Respiratory, thoracic and mediastinal disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Leukocytoclastic vasculitis", "organSystem": "Skin and subcutaneous tissue disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Arterial thrombosis limb", "organSystem": "Vascular disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 378}]}, {"term": "Arteriosclerosis", "organSystem": "Vascular disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 378}]}], "otherEvents": [{"term": "Diarr<PERSON>a", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 17, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 26, "numAtRisk": 378}]}, {"term": "Influenza", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 22, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 15, "numAtRisk": 378}]}, {"term": "Nasopharyngitis", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 33, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 38, "numAtRisk": 378}]}, {"term": "Upper respiratory tract infection", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 33, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 21, "numAtRisk": 378}]}, {"term": "Urinary tract infection", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 15, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 19, "numAtRisk": 378}]}, {"term": "Hypoglycaemia", "organSystem": "Metabolism and nutrition disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 66, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 75, "numAtRisk": 378}]}, {"term": "Headache", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 14.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 29, "numAtRisk": 377}, {"groupId": "EG001", "numAffected": 27, "numAtRisk": 378}]}]}, "moreInfoModule": {"certainAgreement": {"piSponsorEmployee": false, "restrictionType": "OTHER", "restrictiveAgreement": true, "otherDetails": "A copy of the manuscript must be provided to the sponsor for review at least 60 days before submission for publication or presentation. If requested in writing, such publication will be withheld for up to an additional 60 days."}, "pointOfContact": {"title": "Vice President, Franchise Medical Leader, Cardiovascular & Metabolism Franchise", "organization": "Janssen Research & Development, LLC", "phone": "**************"}}}, "derivedSection": {"miscInfoModule": {"versionHolder": "2025-07-08"}, "conditionBrowseModule": {"meshes": [{"id": "D003920", "term": "Diabe<PERSON>"}, {"id": "D003924", "term": "Diabetes Mellitus, Type 2"}], "ancestors": [{"id": "D044882", "term": "Glucose Metabolism Disorders"}, {"id": "D008659", "term": "Metabolic Diseases"}, {"id": "D004700", "term": "Endocrine System Diseases"}], "browseLeaves": [{"id": "M7115", "name": "Diabe<PERSON>", "asFound": "Diabe<PERSON>", "relevance": "HIGH"}, {"id": "M7119", "name": "Diabetes Mellitus, Type 2", "asFound": "Diabetes Mellitus, Type 2", "relevance": "HIGH"}, {"id": "M11639", "name": "Metabolic Diseases", "relevance": "LOW"}, {"id": "M25403", "name": "Glucose Metabolism Disorders", "relevance": "LOW"}, {"id": "M7862", "name": "Endocrine System Diseases", "relevance": "LOW"}], "browseBranches": [{"abbrev": "BC18", "name": "Nutritional and Metabolic Diseases"}, {"abbrev": "BC19", "name": "Gland and Hormone Related Diseases"}, {"abbrev": "All", "name": "All Conditions"}]}, "interventionBrowseModule": {"meshes": [{"id": "D008687", "term": "Metformin"}, {"id": "D000068900", "term": "Sitagliptin Phosphate"}, {"id": "D000068896", "term": "Canagliflozin"}], "ancestors": [{"id": "D007004", "term": "Hypoglycemic Agents"}, {"id": "D045505", "term": "Physiological Effects of Drugs"}, {"id": "D054795", "term": "Incretins"}, {"id": "D006728", "term": "Hormones"}, {"id": "D006730", "term": "Hormones, Hormone Substitutes, and Hormone Antagonists"}, {"id": "D054873", "term": "Dipeptidyl-Peptidase IV Inhibitors"}, {"id": "D011480", "term": "Protease Inhibitors"}, {"id": "D004791", "term": "Enzyme Inhibitors"}, {"id": "D045504", "term": "Molecular Mechanisms of Pharmacological Action"}, {"id": "D000077203", "term": "Sodium-Glucose Transporter 2 Inhibitors"}], "browseLeaves": [{"id": "M11667", "name": "Metformin", "asFound": "Chemotherapy", "relevance": "HIGH"}, {"id": "M335", "name": "Sitagliptin Phosphate", "asFound": "Mothers", "relevance": "HIGH"}, {"id": "M331", "name": "Canagliflozin", "asFound": "Lip", "relevance": "HIGH"}, {"id": "M207501", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relevance": "LOW"}, {"id": "M27957", "name": "Dipeptidyl-Peptidase IV Inhibitors", "relevance": "LOW"}, {"id": "M10054", "name": "Hypoglycemic Agents", "relevance": "LOW"}, {"id": "M27905", "name": "Incretins", "relevance": "LOW"}, {"id": "M9789", "name": "Hormones", "relevance": "LOW"}, {"id": "M9788", "name": "Hormone Antagonists", "relevance": "LOW"}, {"id": "M19609", "name": "HIV Protease Inhibitors", "relevance": "LOW"}, {"id": "M14343", "name": "Protease Inhibitors", "relevance": "LOW"}, {"id": "M7951", "name": "Enzyme Inhibitors", "relevance": "LOW"}, {"id": "M1691", "name": "Sodium-Glucose Transporter 2 Inhibitors", "relevance": "LOW"}], "browseBranches": [{"abbrev": "Hypo", "name": "Hypoglycemic Agents"}, {"abbrev": "All", "name": "All Drugs and Chemicals"}, {"abbrev": "Infl", "name": "Anti-Inflammatory Agents"}, {"abbrev": "<PERSON><PERSON>", "name": "Antirheumatic Agents"}, {"abbrev": "Analg", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"abbrev": "Infe", "name": "Anti-Infective Agents"}]}}, "hasResults": true}}