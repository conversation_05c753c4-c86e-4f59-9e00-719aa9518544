{"nctId": "NCT01989754", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT01989754", "orgStudyIdInfo": {"id": "CR102647"}, "secondaryIdInfos": [{"id": "2013-003050-25", "type": "EUDRACT_NUMBER"}, {"id": "28431754DIA4003", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "A Study of the Effects of Canagliflozin (JNJ-28431754) on Renal Endpoints in Adult Participants With Type 2 Diabetes Mellitus", "officialTitle": "A Randomized, Multicenter, Double-Blind, Parallel, Placebo-Controlled Study of the Effects of Canagliflozin on Renal Endpoints in Adult Subjects With Type 2 Diabetes Mellitus", "acronym": "CANVAS-R"}, "statusModule": {"statusVerifiedDate": "2018-11", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2014-01-16", "type": "ACTUAL"}, "primaryCompletionDateStruct": {"date": "2017-02-23", "type": "ACTUAL"}, "completionDateStruct": {"date": "2017-02-23", "type": "ACTUAL"}, "studyFirstSubmitDate": "2013-10-17", "studyFirstSubmitQcDate": "2013-11-20", "studyFirstPostDateStruct": {"date": "2013-11-21", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2018-11-19", "resultsFirstSubmitQcDate": "2018-11-19", "resultsFirstPostDateStruct": {"date": "2018-12-11", "type": "ACTUAL"}, "dispFirstSubmitDate": "2018-02-20", "dispFirstSubmitQcDate": "2018-02-20", "dispFirstPostDateStruct": {"date": "2018-02-22", "type": "ACTUAL"}, "lastUpdateSubmitDate": "2018-11-19", "lastUpdatePostDateStruct": {"date": "2018-12-11", "type": "ACTUAL"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "collaborators": [{"name": "The George Institute for Global Health, Australia", "class": "OTHER"}]}, "oversightModule": {"oversightHasDmc": true, "isFdaRegulatedDevice": false}, "descriptionModule": {"briefSummary": "The purpose of this study is to assess the effect of canagliflozin compared to placebo on progression of albuminuria in participants with Type 2 Diabetes Mellitus receiving standard care but with inadequate glycemic control and at elevated risk of cardiovascular events.", "detailedDescription": "The study will be conducted in adult participants with Type 2 Diabetes Mellitus (T2DM), receiving standard of care for hyperglycemia and cardiovascular (CV) risk factors, who have either a history of a prior CV event or 2 or more risk factors for a CV event. Participants will be randomly assigned in a 1:1 ratio to canagliflozin or matching placebo to be taken once daily. Canagliflozin will be provided at a dose of 100 mg/day through Week 13 and then increased at the discretion of the investigator to a dose of 300 mg/day, if the participant requires additional glycemic control and is tolerating the 100 mg dose.\n\nThe study consists of a 2-week screening period and a double-blind treatment period lasting between 78 and 156 weeks; study completion is targeted for when the last subject randomized has approximately 78 weeks of follow-up or when 688 major adverse cardiovascular events are accumulated between CANVAS and CANVAS-R. A total of 5,700 participants are targeted to be recruited into the study. Participants can be either drug naïve to antihyperglycemic agents, using monotherapy, or using combination of antihyperglycemic therapy for the control of blood glucose levels.\n\nThe completion target was reached in February 2017."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2", "Albuminuria"], "keywords": ["Albuminuria", "Canagliflozin", "Cardiovascular outcomes", "Type 2 Diabetes Mellitus", "T2DM", "JNJ-28431754", "Antihyperglycemic Agent"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE4"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 5813, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canaglif<PERSON>zin (JNJ-28431754)", "type": "EXPERIMENTAL", "description": "Each patient will receive canagliflozin (JNJ-28431754) 100 mg once daily during the first 13 weeks, then the dose may be increased to 300 mg once daily.", "interventionNames": ["Drug: Canagliflozin, 100 mg", "Drug: Canagliflozin, 300 mg"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each patient will receive placebo (inactive medication) once daily.", "interventionNames": ["Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Placebo", "description": "One placebo capsule taken orally (by mouth) once daily for 156 weeks", "armGroupLabels": ["Placebo"]}, {"type": "DRUG", "name": "Canagliflozin, 100 mg", "description": "One 100 mg capsule taken orally (by mouth) once daily", "armGroupLabels": ["Canaglif<PERSON>zin (JNJ-28431754)"]}, {"type": "DRUG", "name": "Canagliflozin, 300 mg", "description": "One 300 mg capsule taken orally (by mouth) once daily", "armGroupLabels": ["Canaglif<PERSON>zin (JNJ-28431754)"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Progression of Albuminuria", "description": "Progression defined as the development of micro-albuminuria (Urine Albumin Creatinine Ratio \\[UACR\\] 30 to 300 milligram per gram \\[mg/g\\]) or macroalbuminuria (Albumin/creatinine ratio \\[ACR\\] of greater than \\[\\>\\] 300 mg/g) in a participant with baseline normoalbuminuria (ACR less than \\[\\<\\] 30 mg/g) or the development of macro-albuminuria in a participant with baseline microalbuminuria with an ACR increase greater than or equal to (\\>=) 30 percent from baseline. Participants with macroalbuminuria at baseline (ACR\\>300 mg/g) were excluded from the analysis. Event rate was estimated based on the time to the first occurrence of the event.", "timeFrame": "Up to 3 years"}], "secondaryOutcomes": [{"measure": "Composite of Cardiovascular (CV) Death Events or Hospitalization for Heart Failure", "description": "Analyses were using adjudicated events, that is (i.e.) CV death events or hospitalization due to heart failure, and adjudication of these outcomes by the Endpoint Adjudication Committee (EAC) were done in a blinded fashion. Event rate was estimated based on the time to the first occurrence of the event.", "timeFrame": "Approximately 3 years"}, {"measure": "Cardiovascular (CV) Death", "description": "Analyses were using adjudicated events, i.e. CV death events, and adjudication of these outcomes by the Endpoint Adjudication Committee (EAC) were done in a blinded fashion. Event rate was estimated based on the time to the first occurrence of the event.", "timeFrame": "Approximately 3 years"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Must have a diagnosis of type 2 diabetes mellitus\n* Must have inadequate diabetes control (as defined by glycosylated hemoglobin level \\>=7.0% to \\<=10.5% at screening)\n* Greater than or equal to (\\>=) 30 yrs old with history of cardiovascular (CV) event, or \\>= 50 yrs old with high risk of CV events\n* Must be either not on antihyperglycemic agents (AHA) therapy, or on AHA monotherapy, or combination AHA therapy with any approved agent for the control of blood glucose levels.\n\nExclusion Criteria\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus, pancreas or beta-cell transplantation, or diabetes secondary to pancreatitis or pancreatectomy\n* History of one or more severe hypoglycemic episode within 6 months before screening\n* History of hereditary glucose-galactose malabsorption or primary renal glucosuria\n* Ongoing, inadequately controlled thyroid disorder\n* Renal disease that required treatment with immunosuppressive therapy or a history of chronic dialysis or renal transplant\n* Myocardial infarction, unstable angina, revascularization procedure, or cerebrovascular accident within 3 months before screening.", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "30 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "<PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 38.61713, "lon": -121.32828}}, {"city": "Concord", "state": "California", "country": "United States", "geoPoint": {"lat": 37.97798, "lon": -122.03107}}, {"city": "Pismo Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 35.14275, "lon": -120.64128}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Thousand Oaks", "state": "California", "country": "United States", "geoPoint": {"lat": 34.17056, "lon": -118.83759}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Hollywood", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.0112, "lon": -80.14949}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "New Port Richey", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.24418, "lon": -82.71927}}, {"city": "Opa-locka", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.90232, "lon": -80.25033}}, {"city": "Orlando", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.53834, "lon": -81.37924}}, {"city": "Palm Harbor", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.07807, "lon": -82.76371}}, {"city": "Tampa", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.94752, "lon": -82.45843}}, {"city": "Meridian", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.61211, "lon": -116.39151}}, {"city": "Chicago", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.85003, "lon": -87.65005}}, {"city": "Valparaiso", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.47309, "lon": -87.06114}}, {"city": "Topeka", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04833, "lon": -95.67804}}, {"city": "Louisville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 38.25424, "lon": -85.75941}}, {"city": "Mandeville", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.35825, "lon": -90.06563}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Elkridge", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.21261, "lon": -76.71358}}, {"city": "Flint", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 43.01253, "lon": -83.68746}}, {"city": "Picayune", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 30.52556, "lon": -89.67788}}, {"city": "Kansas City", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 39.09973, "lon": -94.57857}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Washington", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.55811, "lon": -91.01209}}, {"city": "Omaha", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 41.25626, "lon": -95.94043}}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "<PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.4868, "lon": -80.86007}}, {"city": "<PERSON><PERSON><PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.73319, "lon": -81.3412}}, {"city": "Mooresville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.58486, "lon": -80.81007}}, {"city": "Canal Fulton", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 40.88978, "lon": -81.59762}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.55895, "lon": -84.30411}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.36006, "lon": -84.30994}}, {"city": "Oklahoma City", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.46756, "lon": -97.51643}}, {"city": "Beaver", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.69534, "lon": -80.30478}}, {"city": "Levittown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.15511, "lon": -74.82877}}, {"city": "Norristown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.1215, "lon": -75.3399}}, {"city": "Pittsburgh", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.44062, "lon": -79.99589}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.6359, "lon": -78.29585}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Jefferson City", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.12231, "lon": -83.4924}}, {"city": "Austin", "state": "Texas", "country": "United States", "geoPoint": {"lat": 30.26715, "lon": -97.74306}}, {"city": "Pearland", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.56357, "lon": -95.28605}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Draper", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.52467, "lon": -111.86382}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "West Jordan", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.60967, "lon": -111.9391}}, {"city": "South Burlington", "state": "Vermont", "country": "United States", "geoPoint": {"lat": 44.46699, "lon": -73.17096}}, {"city": "Falls Church", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 38.88233, "lon": -77.17109}}, {"city": "Norfolk", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.84681, "lon": -76.28522}}, {"city": "Virginia Beach", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.85293, "lon": -75.97799}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.04946, "lon": -88.00759}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma De Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad De Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Cordoba", "country": "Argentina", "geoPoint": {"lat": -31.4135, "lon": -64.18105}}, {"city": "Mar Del Plata", "country": "Argentina", "geoPoint": {"lat": -38.00228, "lon": -57.55754}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.6509, "lon": -58.61956}}, {"city": "Rosario", "country": "Argentina", "geoPoint": {"lat": -32.94682, "lon": -60.63932}}, {"city": "Zarate, Buenos Aires", "country": "Argentina"}, {"city": "Box Hill", "country": "Australia", "geoPoint": {"lat": -37.81887, "lon": 145.12545}}, {"city": "Cairns", "country": "Australia", "geoPoint": {"lat": -16.92304, "lon": 145.76625}}, {"city": "Daw Park", "country": "Australia", "geoPoint": {"lat": -34.98975, "lon": 138.58407}}, {"city": "Freemantle", "country": "Australia"}, {"city": "Liverpool", "country": "Australia", "geoPoint": {"lat": -33.90011, "lon": 150.93328}}, {"city": "Melbourne", "country": "Australia", "geoPoint": {"lat": -37.814, "lon": 144.96332}}, {"city": "Newcastle", "country": "Australia", "geoPoint": {"lat": -32.92715, "lon": 151.77647}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -31.06485, "lon": 152.72879}}, {"city": "Sydney", "country": "Australia", "geoPoint": {"lat": -33.86785, "lon": 151.20732}}, {"city": "Tasmania", "country": "Australia"}, {"city": "Woden", "country": "Australia"}, {"city": "Woolloongabba", "country": "Australia", "geoPoint": {"lat": -27.48855, "lon": 153.03655}}, {"city": "Bonheiden", "country": "Belgium", "geoPoint": {"lat": 51.02261, "lon": 4.54714}}, {"city": "Brugge", "country": "Belgium", "geoPoint": {"lat": 51.20892, "lon": 3.22424}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 51.15662, "lon": 4.44504}}, {"city": "Gent", "country": "Belgium", "geoPoint": {"lat": 51.05, "lon": 3.71667}}, {"city": "La Louvière", "country": "Belgium", "geoPoint": {"lat": 50.48657, "lon": 4.18785}}, {"city": "Leuven", "country": "Belgium", "geoPoint": {"lat": 50.87959, "lon": 4.70093}}, {"city": "Liege", "country": "Belgium", "geoPoint": {"lat": 50.63373, "lon": 5.56749}}, {"city": "Liÿge", "country": "Belgium"}, {"city": "Merksem", "country": "Belgium", "geoPoint": {"lat": 51.24623, "lon": 4.44903}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 50.46166, "lon": 4.47616}}, {"city": "Roeselare", "country": "Belgium", "geoPoint": {"lat": 50.94653, "lon": 3.12269}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 51.06513, "lon": 5.08856}}, {"city": "Belem", "country": "Brazil", "geoPoint": {"lat": -1.45583, "lon": -48.50444}}, {"city": "Belo Horizonte", "country": "Brazil", "geoPoint": {"lat": -19.92083, "lon": -43.93778}}, {"city": "Campinas", "country": "Brazil", "geoPoint": {"lat": -22.90556, "lon": -47.06083}}, {"city": "Caxias Do Sul", "country": "Brazil", "geoPoint": {"lat": -29.16806, "lon": -51.17944}}, {"city": "Curitiba", "country": "Brazil", "geoPoint": {"lat": -25.42778, "lon": -49.27306}}, {"city": "Fortaleza", "country": "Brazil", "geoPoint": {"lat": -3.71722, "lon": -38.54306}}, {"city": "<PERSON><PERSON>", "country": "Brazil", "geoPoint": {"lat": -23.52278, "lon": -46.18833}}, {"city": "Passo Fundo", "country": "Brazil", "geoPoint": {"lat": -28.26278, "lon": -52.40667}}, {"city": "Porto Alegre", "country": "Brazil", "geoPoint": {"lat": -30.03306, "lon": -51.23}}, {"city": "Rio De Janeiro", "country": "Brazil", "geoPoint": {"lat": -22.90278, "lon": -43.2075}}, {"city": "Sao Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "São José Do <PERSON> Preto", "country": "Brazil", "geoPoint": {"lat": -20.81972, "lon": -49.37944}}, {"city": "São Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "Coquitlam", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.28297, "lon": -122.75262}}, {"city": "Winnipeg", "state": "Manitoba", "country": "Canada", "geoPoint": {"lat": 49.8844, "lon": -97.14704}}, {"city": "Halifax", "state": "Nova Scotia", "country": "Canada", "geoPoint": {"lat": 44.64533, "lon": -63.57239}}, {"city": "Guelph", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.54594, "lon": -80.25599}}, {"city": "Mississauga", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.5789, "lon": -79.6583}}, {"city": "Oshawa", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.90012, "lon": -78.84957}}, {"city": "Sarnia", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.97866, "lon": -82.40407}}, {"city": "Thornhill", "state": "Ontario", "country": "Canada"}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.56995, "lon": -73.692}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.80326, "lon": -71.17793}}, {"city": "Quebec City", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.81228, "lon": -71.21454}}, {"city": "<PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.68335, "lon": -72.0491}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.40008, "lon": -71.89908}}, {"city": "Westmont", "state": "Quebec", "country": "Canada"}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "<PERSON><PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 40.65222, "lon": 109.82222}}, {"city": "Beijing", "country": "China", "geoPoint": {"lat": 39.9075, "lon": 116.39723}}, {"city": "Hangzhou", "country": "China", "geoPoint": {"lat": 30.29365, "lon": 120.16142}}, {"city": "<PERSON><PERSON>", "country": "China", "geoPoint": {"lat": 36.66833, "lon": 116.99722}}, {"city": "Shenyang", "country": "China", "geoPoint": {"lat": 41.79222, "lon": 123.43278}}, {"city": "Wuxi", "country": "China", "geoPoint": {"lat": 31.56887, "lon": 120.28857}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 49.96382, "lon": 14.072}}, {"city": "Ostrava", "country": "Czechia", "geoPoint": {"lat": 49.83465, "lon": 18.28204}}, {"city": "Plzen", "country": "Czechia", "geoPoint": {"lat": 49.74747, "lon": 13.37759}}, {"city": "Praha 10", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 1", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 4", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 8", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Znojmo", "country": "Czechia", "geoPoint": {"lat": 48.8555, "lon": 16.0488}}, {"city": "Amiens", "country": "France", "geoPoint": {"lat": 49.9, "lon": 2.3}}, {"city": "<PERSON>", "country": "France", "geoPoint": {"lat": 49.4602, "lon": 1.12219}}, {"city": "Bordeaux", "country": "France", "geoPoint": {"lat": 44.84044, "lon": -0.5805}}, {"city": "Dijon", "country": "France", "geoPoint": {"lat": 47.31667, "lon": 5.01667}}, {"city": "Grenoble", "country": "France", "geoPoint": {"lat": 45.16667, "lon": 5.71667}}, {"city": "La Rochelle Cedex 1", "country": "France", "geoPoint": {"lat": 46.16667, "lon": -1.15}}, {"city": "<PERSON>", "country": "France", "geoPoint": {"lat": 46.80714, "lon": 4.41632}}, {"city": "Narbonne Cedex", "country": "France", "geoPoint": {"lat": 43.18396, "lon": 3.00141}}, {"city": "Paris", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "Poitiers", "country": "France", "geoPoint": {"lat": 46.58333, "lon": 0.33333}}, {"city": "Aschaffenburg", "country": "Germany", "geoPoint": {"lat": 49.97704, "lon": 9.15214}}, {"city": "Aßlar", "country": "Germany", "geoPoint": {"lat": 50.59163, "lon": 8.46273}}, {"city": "Bad Oeynhausen", "country": "Germany", "geoPoint": {"lat": 52.20699, "lon": 8.80365}}, {"city": "Dortmund", "country": "Germany", "geoPoint": {"lat": 51.51494, "lon": 7.466}}, {"city": "Dresden", "country": "Germany", "geoPoint": {"lat": 51.05089, "lon": 13.73832}}, {"city": "Freiburg", "country": "Germany", "geoPoint": {"lat": 47.9959, "lon": 7.85222}}, {"city": "Hannover", "country": "Germany", "geoPoint": {"lat": 52.37052, "lon": 9.73322}}, {"city": "Kassel", "country": "Germany", "geoPoint": {"lat": 51.31667, "lon": 9.5}}, {"city": "Mainz", "country": "Germany", "geoPoint": {"lat": 49.98419, "lon": 8.2791}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 50.4336, "lon": 7.47057}}, {"city": "Saarbrücken", "country": "Germany", "geoPoint": {"lat": 49.23262, "lon": 7.0098}}, {"city": "Villingen-Schwenningen", "country": "Germany", "geoPoint": {"lat": 48.06226, "lon": 8.49358}}, {"city": "Balatonfured", "country": "Hungary", "geoPoint": {"lat": 46.96188, "lon": 17.87187}}, {"city": "Budapest", "country": "Hungary", "geoPoint": {"lat": 47.49801, "lon": 19.03991}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 46.96498, "lon": 18.93923}}, {"city": "<PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.90265, "lon": 20.37329}}, {"city": "Kecskemét", "country": "Hungary", "geoPoint": {"lat": 46.90618, "lon": 19.69128}}, {"city": "Nagykanizsa", "country": "Hungary", "geoPoint": {"lat": 46.45347, "lon": 16.99104}}, {"city": "Pecs", "country": "Hungary", "geoPoint": {"lat": 46.08333, "lon": 18.23333}}, {"city": "Szekesfehervar", "country": "Hungary", "geoPoint": {"lat": 47.18995, "lon": 18.41034}}, {"city": "Szikszó", "country": "Hungary", "geoPoint": {"lat": 48.2, "lon": 20.93333}}, {"city": "Szombathely", "country": "Hungary", "geoPoint": {"lat": 47.23088, "lon": 16.62155}}, {"city": "Zalaegerszeg", "country": "Hungary", "geoPoint": {"lat": 46.84, "lon": 16.84389}}, {"city": "*Osenza*", "country": "Italy"}, {"city": "Arenzano", "country": "Italy", "geoPoint": {"lat": 44.40521, "lon": 8.68315}}, {"city": "Bologna", "country": "Italy", "geoPoint": {"lat": 44.49381, "lon": 11.33875}}, {"city": "Cat<PERSON>ro", "country": "Italy", "geoPoint": {"lat": 38.88247, "lon": 16.60086}}, {"city": "<PERSON><PERSON> (Torino)", "country": "Italy"}, {"city": "Firenze", "country": "Italy", "geoPoint": {"lat": 43.77925, "lon": 11.24626}}, {"city": "Messina", "country": "Italy", "geoPoint": {"lat": 38.19394, "lon": 15.55256}}, {"city": "Milano", "country": "Italy", "geoPoint": {"lat": 45.46427, "lon": 9.18951}}, {"city": "Napoli", "country": "Italy", "geoPoint": {"lat": 40.85216, "lon": 14.26811}}, {"city": "<PERSON><PERSON>", "country": "Italy", "geoPoint": {"lat": 44.41344, "lon": 12.20121}}, {"city": "Roma N/A", "country": "Italy"}, {"city": "Roma", "country": "Italy", "geoPoint": {"lat": 41.89193, "lon": 12.51133}}, {"city": "<PERSON><PERSON> (Milano)", "country": "Italy"}, {"city": "Verona", "country": "Italy", "geoPoint": {"lat": 45.4299, "lon": 10.98444}}, {"city": "<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.32361, "lon": 126.82194}}, {"city": "Bucheon", "country": "Korea, Republic of", "geoPoint": {"lat": 37.49889, "lon": 126.78306}}, {"city": "Busan", "country": "Korea, Republic of", "geoPoint": {"lat": 35.10278, "lon": 129.04028}}, {"city": "Changwon", "country": "Korea, Republic of", "geoPoint": {"lat": 35.22806, "lon": 128.68111}}, {"city": "Cheongju", "country": "Korea, Republic of", "geoPoint": {"lat": 36.63722, "lon": 127.48972}}, {"city": "Daegu", "country": "Korea, Republic of", "geoPoint": {"lat": 35.87028, "lon": 128.59111}}, {"city": "Gyeonggi-Do", "country": "Korea, Republic of", "geoPoint": {"lat": 37.58944, "lon": 126.76917}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 35.94389, "lon": 126.95444}}, {"city": "Incheon", "country": "Korea, Republic of", "geoPoint": {"lat": 37.45646, "lon": 126.70515}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 35.82194, "lon": 127.14889}}, {"city": "Seongnam-Si", "country": "Korea, Republic of", "geoPoint": {"lat": 37.43861, "lon": 127.13778}}, {"city": "Seoul", "country": "Korea, Republic of", "geoPoint": {"lat": 37.566, "lon": 126.9784}}, {"city": "<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.29111, "lon": 127.00889}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 35.53722, "lon": 129.31667}}, {"city": "Wonju<PERSON><PERSON>", "country": "Korea, Republic of"}, {"city": "Batu Caves", "country": "Malaysia", "geoPoint": {"lat": 3.23333, "lon": 101.66667}}, {"city": "Georgetown", "country": "Malaysia"}, {"city": "Ipoh", "country": "Malaysia", "geoPoint": {"lat": 4.5841, "lon": 101.0829}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia"}, {"city": "Kota Bharu", "country": "Malaysia", "geoPoint": {"lat": 6.13328, "lon": 102.2386}}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "<PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 1.54999, "lon": 110.33333}}, {"city": "Aguascalientes", "country": "Mexico", "geoPoint": {"lat": 21.88234, "lon": -102.28259}}, {"city": "Celaya", "country": "Mexico", "geoPoint": {"lat": 20.52353, "lon": -100.8157}}, {"city": "Cuernavaca", "country": "Mexico", "geoPoint": {"lat": 18.9261, "lon": -99.23075}}, {"city": "Culiacan", "country": "Mexico", "geoPoint": {"lat": 24.79032, "lon": -107.38782}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Mexico", "geoPoint": {"lat": 20.11697, "lon": -98.73329}}, {"city": "Que<PERSON>ro", "country": "Mexico"}, {"city": "San Luis Potosi", "country": "Mexico", "geoPoint": {"lat": 22.14982, "lon": -100.97916}}, {"city": "Tampico", "country": "Mexico", "geoPoint": {"lat": 22.26695, "lon": -97.86815}}, {"city": "Almelo", "country": "Netherlands", "geoPoint": {"lat": 52.35667, "lon": 6.6625}}, {"city": "Almere", "country": "Netherlands", "geoPoint": {"lat": 52.37535, "lon": 5.25295}}, {"city": "Amersfoort", "country": "Netherlands", "geoPoint": {"lat": 52.155, "lon": 5.3875}}, {"city": "Amsterdam", "country": "Netherlands", "geoPoint": {"lat": 52.37403, "lon": 4.88969}}, {"city": "Dordrecht", "country": "Netherlands", "geoPoint": {"lat": 51.81, "lon": 4.67361}}, {"city": "Eindhoven", "country": "Netherlands", "geoPoint": {"lat": 51.44083, "lon": 5.47778}}, {"city": "Groningen", "country": "Netherlands", "geoPoint": {"lat": 53.21917, "lon": 6.56667}}, {"city": "<PERSON><PERSON>", "country": "Netherlands", "geoPoint": {"lat": 52.57583, "lon": 6.61944}}, {"city": "<PERSON>og<PERSON><PERSON>", "country": "Netherlands", "geoPoint": {"lat": 52.7225, "lon": 6.47639}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Netherlands", "geoPoint": {"lat": 53.16167, "lon": 6.76111}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Netherlands", "geoPoint": {"lat": 52.49667, "lon": 6.66944}}, {"city": "Leiden", "country": "Netherlands", "geoPoint": {"lat": 52.15833, "lon": 4.49306}}, {"city": "<PERSON><PERSON>", "country": "Netherlands", "geoPoint": {"lat": 52.69583, "lon": 6.19444}}, {"city": "Poortvliet", "country": "Netherlands", "geoPoint": {"lat": 51.54417, "lon": 4.14306}}, {"city": "Rotterdam", "country": "Netherlands", "geoPoint": {"lat": 51.9225, "lon": 4.47917}}, {"city": "Utrecht", "country": "Netherlands", "geoPoint": {"lat": 52.09083, "lon": 5.12222}}, {"city": "<PERSON>elp", "country": "Netherlands", "geoPoint": {"lat": 51.995, "lon": 5.97361}}, {"city": "Waalwijk", "country": "Netherlands", "geoPoint": {"lat": 51.6825, "lon": 5.07083}}, {"city": "Wamel", "country": "Netherlands", "geoPoint": {"lat": 51.87917, "lon": 5.46528}}, {"city": "Zoetermeer", "country": "Netherlands", "geoPoint": {"lat": 52.0575, "lon": 4.49306}}, {"city": "Zwijndrecht", "country": "Netherlands", "geoPoint": {"lat": 51.8175, "lon": 4.63333}}, {"city": "Auckland", "country": "New Zealand", "geoPoint": {"lat": -36.84853, "lon": 174.76349}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Newtown", "country": "New Zealand", "geoPoint": {"lat": -41.31115, "lon": 174.77935}}, {"city": "Palmerston North", "country": "New Zealand", "geoPoint": {"lat": -40.35636, "lon": 175.61113}}, {"city": "Rotoru<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -38.13874, "lon": 176.24516}}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Bialystok", "country": "Poland", "geoPoint": {"lat": 53.13333, "lon": 23.16433}}, {"city": "Bydgoszcz", "country": "Poland", "geoPoint": {"lat": 53.1235, "lon": 18.00762}}, {"city": "Chrzanow", "country": "Poland", "geoPoint": {"lat": 50.13546, "lon": 19.40203}}, {"city": "Gdansk", "country": "Poland", "geoPoint": {"lat": 54.35205, "lon": 18.64637}}, {"city": "Grodzisk Mazowiecki", "country": "Poland", "geoPoint": {"lat": 52.10387, "lon": 20.6337}}, {"city": "Katowice", "country": "Poland", "geoPoint": {"lat": 50.25841, "lon": 19.02754}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 50.03437, "lon": 19.21037}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Tychy", "country": "Poland", "geoPoint": {"lat": 50.13717, "lon": 18.96641}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Ponce Pr", "country": "Puerto Rico"}, {"city": "Trujillo Alto", "country": "Puerto Rico", "geoPoint": {"lat": 18.35467, "lon": -66.00739}}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Barnaul", "country": "Russian Federation", "geoPoint": {"lat": 53.36056, "lon": 83.76361}}, {"city": "Chelyabinsk", "country": "Russian Federation", "geoPoint": {"lat": 55.15402, "lon": 61.42915}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "Nizhny Novgorod", "country": "Russian Federation", "geoPoint": {"lat": 56.32867, "lon": 44.00205}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 53.20066, "lon": 45.00464}}, {"city": "Rostov-On-Don", "country": "Russian Federation", "geoPoint": {"lat": 47.23135, "lon": 39.72328}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Saint-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "Smolensk", "country": "Russian Federation", "geoPoint": {"lat": 54.7818, "lon": 32.0401}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 61.67642, "lon": 50.80994}}, {"city": "Tyumen", "country": "Russian Federation", "geoPoint": {"lat": 57.15222, "lon": 65.52722}}, {"city": "Vsevolzhsk", "country": "Russian Federation"}, {"city": "Yaroslavl", "country": "Russian Federation", "geoPoint": {"lat": 57.62987, "lon": 39.87368}}, {"city": "A Coruna", "country": "Spain", "geoPoint": {"lat": 43.37135, "lon": -8.396}}, {"city": "Alcalá <PERSON>", "country": "Spain", "geoPoint": {"lat": 40.48205, "lon": -3.35996}}, {"city": "Alicante", "country": "Spain", "geoPoint": {"lat": 38.34517, "lon": -0.48149}}, {"city": "Almeria", "country": "Spain", "geoPoint": {"lat": 36.83814, "lon": -2.45974}}, {"city": "Alzira", "country": "Spain", "geoPoint": {"lat": 39.15, "lon": -0.43333}}, {"city": "Barcelona", "country": "Spain", "geoPoint": {"lat": 41.38879, "lon": 2.15899}}, {"city": "Ciudad Real", "country": "Spain", "geoPoint": {"lat": 38.98626, "lon": -3.92907}}, {"city": "Cordoba", "country": "Spain", "geoPoint": {"lat": 37.89155, "lon": -4.77275}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 43.4896, "lon": -8.21942}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 42.26645, "lon": 2.96163}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 43.23333, "lon": -2.83333}}, {"city": "Madrid", "country": "Spain", "geoPoint": {"lat": 40.4165, "lon": -3.70256}}, {"city": "Malaga", "country": "Spain", "geoPoint": {"lat": 36.72016, "lon": -4.42034}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Spain", "geoPoint": {"lat": 40.32234, "lon": -3.86496}}, {"city": "Palma De Mallorca", "country": "Spain", "geoPoint": {"lat": 39.56939, "lon": 2.65024}}, {"city": "Sa<PERSON>ell", "country": "Spain", "geoPoint": {"lat": 41.54329, "lon": 2.10942}}, {"city": "San Sebastian <PERSON>", "country": "Spain", "geoPoint": {"lat": 40.55555, "lon": -3.62733}}, {"city": "<PERSON>", "country": "Spain"}, {"city": "Santa Cruz De Tenerife", "country": "Spain", "geoPoint": {"lat": 28.46824, "lon": -16.25462}}, {"city": "Santiago De Compostela", "country": "Spain", "geoPoint": {"lat": 42.88052, "lon": -8.54569}}, {"city": "Segovia", "country": "Spain", "geoPoint": {"lat": 40.94808, "lon": -4.11839}}, {"city": "Sevilla N/A", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Sevilla", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Valencia", "country": "Spain", "geoPoint": {"lat": 39.46975, "lon": -0.37739}}, {"city": "Viladecans", "country": "Spain", "geoPoint": {"lat": 41.31405, "lon": 2.01427}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Sweden", "geoPoint": {"lat": 57.72101, "lon": 12.9401}}, {"city": "Goteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Helsingborg", "country": "Sweden", "geoPoint": {"lat": 56.04673, "lon": 12.69437}}, {"city": "Karlstad", "country": "Sweden", "geoPoint": {"lat": 59.3793, "lon": 13.50357}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Sweden", "geoPoint": {"lat": 58.41086, "lon": 15.62157}}, {"city": "Lund", "country": "Sweden", "geoPoint": {"lat": 55.70584, "lon": 13.19321}}, {"city": "Malmo", "country": "Sweden", "geoPoint": {"lat": 55.60587, "lon": 13.00073}}, {"city": "Stockholm", "country": "Sweden", "geoPoint": {"lat": 59.33258, "lon": 18.0649}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Sweden", "geoPoint": {"lat": 58.34784, "lon": 11.9424}}, {"city": "Uppsala", "country": "Sweden", "geoPoint": {"lat": 59.85882, "lon": 17.63889}}, {"city": "Vallingby", "country": "Sweden", "geoPoint": {"lat": 59.36441, "lon": 17.87407}}, {"city": "<PERSON>ä<PERSON>", "country": "Sweden", "geoPoint": {"lat": 57.64667, "lon": 11.92944}}, {"city": "Kaohsiung County", "country": "Taiwan"}, {"city": "Taichung", "country": "Taiwan", "geoPoint": {"lat": 24.1469, "lon": 120.6839}}, {"city": "Tainan", "country": "Taiwan", "geoPoint": {"lat": 22.99083, "lon": 120.21333}}, {"city": "Taipei", "country": "Taiwan", "geoPoint": {"lat": 25.04776, "lon": 121.53185}}, {"city": "Tiachung", "country": "Taiwan", "geoPoint": {"lat": 24.1469, "lon": 120.6839}}, {"city": "Xindian", "country": "Taiwan", "geoPoint": {"lat": 24.96005, "lon": 121.53892}}, {"city": "Cherkasy", "country": "Ukraine", "geoPoint": {"lat": 49.42854, "lon": 32.06207}}, {"city": "Chernivtsy", "country": "Ukraine"}, {"city": "Dnipropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "Ivan-Frankivsk", "country": "Ukraine"}, {"city": "Ivano-Frankovsk", "country": "Ukraine"}, {"city": "Kharkiv", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Kyiv", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Lviv", "country": "Ukraine", "geoPoint": {"lat": 49.83826, "lon": 24.02324}}, {"city": "Odesa", "country": "Ukraine", "geoPoint": {"lat": 46.47747, "lon": 30.73262}}, {"city": "Odessa", "country": "Ukraine", "geoPoint": {"lat": 46.47747, "lon": 30.73262}}, {"city": "Poltava", "country": "Ukraine", "geoPoint": {"lat": 49.59373, "lon": 34.54073}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 50.9216, "lon": 34.80029}}, {"city": "Ternopil", "country": "Ukraine", "geoPoint": {"lat": 49.55589, "lon": 25.60556}}, {"city": "Uzhgorod", "country": "Ukraine", "geoPoint": {"lat": 48.61667, "lon": 22.3}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}, {"city": "Vinnytsya", "country": "Ukraine", "geoPoint": {"lat": 49.23278, "lon": 28.48097}}, {"city": "Zaporizhzhia", "country": "Ukraine", "geoPoint": {"lat": 50.60727, "lon": 31.78792}}, {"city": "Zaporizhzhya", "country": "Ukraine", "geoPoint": {"lat": 50.60727, "lon": 31.78792}}, {"city": "Addlestone", "country": "United Kingdom", "geoPoint": {"lat": 51.37135, "lon": -0.49353}}, {"city": "Belfast", "country": "United Kingdom", "geoPoint": {"lat": 54.59682, "lon": -5.92541}}, {"city": "Birmingham", "country": "United Kingdom", "geoPoint": {"lat": 52.48142, "lon": -1.89983}}, {"city": "Blackburn", "country": "United Kingdom", "geoPoint": {"lat": 53.75, "lon": -2.48333}}, {"city": "Bristol", "country": "United Kingdom", "geoPoint": {"lat": 51.45523, "lon": -2.59665}}, {"city": "Bury St Edmunds", "country": "United Kingdom", "geoPoint": {"lat": 52.2463, "lon": 0.71111}}, {"city": "Chesterfield", "country": "United Kingdom", "geoPoint": {"lat": 53.25, "lon": -1.41667}}, {"city": "Derby", "country": "United Kingdom", "geoPoint": {"lat": 52.92277, "lon": -1.47663}}, {"city": "Doncaster", "country": "United Kingdom", "geoPoint": {"lat": 53.52285, "lon": -1.13116}}, {"city": "Ipswich", "country": "United Kingdom", "geoPoint": {"lat": 52.05917, "lon": 1.15545}}, {"city": "Leicester", "country": "United Kingdom", "geoPoint": {"lat": 52.6386, "lon": -1.13169}}, {"city": "Liverpool", "country": "United Kingdom", "geoPoint": {"lat": 53.41058, "lon": -2.97794}}, {"city": "Manchester", "country": "United Kingdom", "geoPoint": {"lat": 53.48095, "lon": -2.23743}}, {"city": "Salford", "country": "United Kingdom", "geoPoint": {"lat": 53.48771, "lon": -2.29042}}, {"city": "<PERSON><PERSON>", "country": "United Kingdom", "geoPoint": {"lat": 51.01494, "lon": -3.10293}}, {"city": "Torquay", "country": "United Kingdom", "geoPoint": {"lat": 50.46198, "lon": -3.5252}}, {"city": "Truro", "country": "United Kingdom", "geoPoint": {"lat": 50.26526, "lon": -5.05436}}, {"city": "Wellingborough", "country": "United Kingdom", "geoPoint": {"lat": 52.30273, "lon": -0.69446}}, {"city": "Welwyn Garden City", "country": "United Kingdom", "geoPoint": {"lat": 51.80174, "lon": -0.20691}}]}, "referencesModule": {"references": [{"pmid": "34448033", "type": "DERIVED", "citation": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of canagliflozin compared with placebo on major adverse cardiovascular and kidney events in patient groups with different baseline levels of HbA1c, disease duration and treatment intensity: results from the CANVAS Program. Diabetologia. 2021 Nov;64(11):2402-2414. doi: 10.1007/s00125-021-05524-1. Epub 2021 Aug 26."}, {"pmid": "33826709", "type": "DERIVED", "citation": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of canagliflozin on myocardial infarction: a post hoc analysis of the CANVAS programme and CREDENCE trial. Cardiovasc Res. 2022 Mar 16;118(4):1103-1114. doi: 10.1093/cvr/cvab128."}, {"pmid": "33595905", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Khan <PERSON>, Baldridge AS, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Cardiovascular and renal outcomes with canagliflozin according to baseline diuretic use: a post hoc analysis from the CANVAS Program. ESC Heart Fail. 2021 Apr;8(2):1482-1493. doi: 10.1002/ehf2.13236. Epub 2021 Feb 17."}, {"pmid": "32971190", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Relative and Absolute Risk Reductions in Cardiovascular and Kidney Outcomes With Canagliflozin Across KDIGO Risk Categories: Findings From the CANVAS Program. Am J Kidney Dis. 2021 Jan;77(1):23-34.e1. doi: 10.1053/j.ajkd.2020.06.018. Epub 2020 Sep 21."}, {"pmid": "32694216", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Different eGFR Decline Thresholds and Renal Effects of Canagliflozin: Data from the CANVAS Program. J Am Soc Nephrol. 2020 Oct;31(10):2446-2456. doi: 10.1681/ASN.2019121312. Epub 2020 Jul 21."}, {"pmid": "32691499", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Effects of canagliflozin on initiation of insulin and other antihyperglycaemic agents in the CANVAS Program. Diabetes Obes Metab. 2020 Nov;22(11):2199-2203. doi: 10.1111/dom.14143. Epub 2020 Aug 24."}, {"pmid": "31676303", "type": "DERIVED", "citation": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> Mediators of the Effects of Canagliflozin on Heart Failure in Patients With Type 2 Diabetes. JACC Heart Fail. 2020 Jan;8(1):57-66. doi: 10.1016/j.jchf.2019.08.004. Epub 2019 Oct 29."}, {"pmid": "31399845", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and fracture risk in individuals with type 2 diabetes: results from the CANVAS Program. Diabetologia. 2019 Oct;62(10):1854-1867. doi: 10.1007/s00125-019-4955-5. Epub 2019 Aug 10."}, {"pmid": "30882240", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON> GA, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Effects of Canagliflozin on Heart Failure Outcomes Associated With Preserved and Reduced Ejection Fraction in Type 2 Diabetes Mellitus. Circulation. 2019 May 28;139(22):2591-2593. doi: 10.1161/CIRCULATIONAHA.119.040057. Epub 2019 Mar 17. No abstract available."}, {"pmid": "30591006", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> in Type 2 Diabetes Mellitus. Stroke. 2019 Feb;50(2):396-404. doi: 10.1161/STROKEAHA.118.023009."}, {"pmid": "29941478", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON> B<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Cardiovascular and Renal Outcomes With Canagliflozin According to Baseline <PERSON>ney Function. Circulation. 2018 Oct 9;138(15):1537-1550. doi: 10.1161/CIRCULATIONAHA.118.035901."}, {"pmid": "29937267", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and renal outcomes in type 2 diabetes: results from the CANVAS Program randomised clinical trials. Lancet Diabetes Endocrinol. 2018 Sep;6(9):691-704. doi: 10.1016/S2213-8587(18)30141-4. Epub 2018 Jun 21."}, {"pmid": "29693360", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>dt ET, Eudicone JM, Bell KF, <PERSON><PERSON><PERSON>, Latham K, Green JB. Eligibility varies among the 4 sodium-glucose cotransporter-2 inhibitor cardiovascular outcomes trials: implications for the general type 2 diabetes US population. Am J Manag Care. 2018 Apr;24(8 Suppl):S138-S145."}, {"pmid": "29526832", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> Failure in Type 2 Diabetes Mellitus: Results From the CANVAS Program. Circulation. 2018 Jul 31;138(5):458-468. doi: 10.1161/CIRCULATIONAHA.118.034222."}, {"pmid": "29133604", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>; CANVAS Program Collaborative Group. Canagliflozin for Primary and Secondary Prevention of Cardiovascular Events: Results From the CANVAS Program (Canagliflozin Cardiovascular Assessment Study). Circulation. 2018 Jan 23;137(4):323-334. doi: 10.1161/CIRCULATIONAHA.117.032038. Epub 2017 Nov 13."}, {"pmid": "28605608", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>; CANVAS Program Collaborative Group. Canagliflozin and Cardiovascular and Renal Events in Type 2 Diabetes. N Engl J Med. 2017 Aug 17;377(7):644-657. doi: 10.1056/NEJMoa1611925. Epub 2017 Jun 12."}]}}}