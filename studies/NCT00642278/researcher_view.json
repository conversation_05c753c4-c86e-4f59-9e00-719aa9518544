{"nctId": "NCT00642278", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Researcher View", "fullStudyData": {"protocolSection": {"identificationModule": {"nctId": "NCT00642278", "orgStudyIdInfo": {"id": "CR014587"}, "secondaryIdInfos": [{"id": "28431754DIA2001", "type": "OTHER", "domain": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C."}], "organization": {"fullName": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "class": "INDUSTRY"}, "briefTitle": "An Efficacy, Safety, and Tolerability Study of Canagliflozin (JNJ-28431754) in Patients With Type 2 Diabetes", "officialTitle": "A Randomized, Double-Blind, Placebo-Controlled, Double-Dummy, Parallel Group, Multicenter, Dose-Ranging Study in Subjects With Type 2 Diabetes Mellitus to Evaluate the Efficacy, Safety, and Tolerability of Orally Administered SGLT2 Inhibitor JNJ-28431754 With <PERSON>ag<PERSON><PERSON> as a Reference Arm"}, "statusModule": {"statusVerifiedDate": "2013-07", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2008-04"}, "primaryCompletionDateStruct": {"date": "2009-01", "type": "ACTUAL"}, "completionDateStruct": {"date": "2009-01", "type": "ACTUAL"}, "studyFirstSubmitDate": "2008-03-21", "studyFirstSubmitQcDate": "2008-03-24", "studyFirstPostDateStruct": {"date": "2008-03-25", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2013-04-01", "resultsFirstSubmitQcDate": "2013-04-01", "resultsFirstPostDateStruct": {"date": "2013-05-17", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2009-07-23", "dispFirstSubmitQcDate": "2009-07-23", "dispFirstPostDateStruct": {"date": "2009-08-10", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2013-07-15", "lastUpdatePostDateStruct": {"date": "2013-07-19", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": false}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the effectiveness, safety, and tolerability of JNJ-28431754 compared with placebo in patients with type 2 diabetes.", "detailedDescription": "Type 2 diabetes mellitus is a metabolic disorder that is characterized by decreased secretion of insulin by the pancreas and resistance to the action of insulin in various tissues (muscle, liver, and adipose), which results in impaired glucose uptake. Chronic hyperglycemia leads to progressive impairment of insulin secretion and to insulin resistance of peripheral tissues in diabetes (so-called glucose toxicity), which further worsens control of blood glucose. In addition, chronic hyperglycemia is a major risk factor for complications, including heart disease, retinopathy, nephropathy, and neuropathy. Although numerous treatments have been developed for the treatment of diabetes and individual agents may be highly effective for some patients, it is still difficult to maintain optimal glycemic control in most patients with diabetes. This is a randomized, double-blind, placebo-controlled, parallel group, multicenter, dose-ranging study to determine the efficacy, safety and tolerability of JNJ-28431754 taken orally over 12 weeks, compared with placebo, in the treatment of Type 2 diabetes mellitus. The primary clinical hypothesis is that JNJ-28431754 is superior to placebo as measured by the change in hemoglobin A1c from baseline through Week 12 in the treatment of type 2 diabetes mellitus. Subject safety will be monitored throughout the study using spontaneous adverse event reporting, clinical laboratory tests (hematology, serum chemistry, urinalysis); severe and serious hypoglycemic episodes, assessment of urinary albumin excretion and markers of proximal renal tubular function; pregnancy tests; electrocardiograms (ECGs); vital sign measurements; physical examinations, assessment of calcium and phosphate homeostasis, bone formation and resorption markers, and hormones regulating calcium and phosphorus homeostasis; and vaginal and urine sample collection for fungal and bacterial culture in subjects with symptoms consistent with vulvovaginal candidiasis (VVC) or urinary tract infection (UTI)."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type II", "Diabe<PERSON>, Non Insulin Dependent"], "keywords": ["Type 2 diabetes mellitus", "Metformin", "Hemoglobin A1c"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE2"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 451, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 50 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 100 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 200 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 300 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo capsule once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 300 mg twice daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks.", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)"]}, {"label": "Sitagliptin 100 mg daily", "type": "ACTIVE_COMPARATOR", "description": "Each patient will receive 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON>", "Drug: Placebo"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each patient will receive matching placebo twice daily for 12 weeks.", "interventionNames": ["Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Canaglif<PERSON>zin (JNJ-28431754)", "description": "One 50 mg, 100 mg, 200 mg, or 300 mg over-encapsulated tablet orally (by mouth) once daily for 12 weeks or one 300 mg over-encapsulated tablet orally twice daily for 12 weeks.", "armGroupLabels": ["Canagliflozin 100 mg daily", "Canagliflozin 200 mg daily", "Canagliflozin 300 mg daily", "Canagliflozin 300 mg twice daily", "Canagliflozin 50 mg daily"], "otherNames": ["JNJ-28431754"]}, {"type": "DRUG", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "One 100 mg over-encapsulated tablet orally (by mouth) once daily for 12 weeks.", "armGroupLabels": ["Sitagliptin 100 mg daily"]}, {"type": "DRUG", "name": "Placebo", "description": "One matching placebo capsule orally (by mouth) once or twice daily for 12 weeks.", "armGroupLabels": ["Canagliflozin 100 mg daily", "Canagliflozin 200 mg daily", "Canagliflozin 300 mg daily", "Canagliflozin 50 mg daily", "Placebo", "Sitagliptin 100 mg daily"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 12", "description": "The table below shows the mean change in HbA1c from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}], "secondaryOutcomes": [{"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 12", "description": "The table below shows the mean change in FPG from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Percentage of Patients With Symptoms of Hypoglycemia", "description": "The table below shows the percentage of patients who experienced symptomatic hypoglycemic events between Baseline and Week 12.", "timeFrame": "Up to Week 12"}, {"measure": "Change in Overnight Urine Glucose/Creatinine Ratio From Baseline to Week 12", "description": "The table below shows the mean change in overnight urine glucose/creatinine ratio from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Absolute Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean absolute change in body weight from Baseline to Week 12 for each treatment group.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Percent Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean percent change in body weight from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Patients must have a diagnosis of type 2 diabetes mellitus\n* Hemoglobin A1c levels \\>=7% and \\<=10.5%\n* taking a stable daily dose of metformin\n* Body mass index (BMI) 25 to 45 kg/m2 except those of Asian descent who must have a BMI of 24 to 45 kg/m2\n* Stable body weight\n* Serum creatinine \\<=1.5 mg/dL (132.6 umol/L) for men and \\<=1.4 mg/dL (123.76 umol/L) for women\n\nExclusion Criteria:\n\n* Patients must not have prior exposure or known contraindication or suspected hypersensitivity to canagliflozin (JNJ-28431754)\n* Known contraindication or suspected hypersensitivity to sitagliptin or metformin\n* A history of diabetic ketoacidosis or type 1 diabetes mellitus\n* History of pancreas or beta-cell transplantation\n* History of active proliferative diabetic retinopathy\n* History of hereditary glucose-galactose malabsorption or primary renal glucosuria", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "65 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Johnson & Johnson Pharmaceutical Research & Development, L.L. C. Clinical Trial", "affiliation": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Birmingham", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.52066, "lon": -86.80249}}, {"city": "Mesa", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.42227, "lon": -111.82264}}, {"city": "Tucson", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 32.22174, "lon": -110.92648}}, {"city": "Encinitas", "state": "California", "country": "United States", "geoPoint": {"lat": 33.03699, "lon": -117.29198}}, {"city": "Lincoln", "state": "California", "country": "United States", "geoPoint": {"lat": 38.89156, "lon": -121.29301}}, {"city": "Los Angeles", "state": "California", "country": "United States", "geoPoint": {"lat": 34.05223, "lon": -118.24368}}, {"city": "Merced", "state": "California", "country": "United States", "geoPoint": {"lat": 37.30216, "lon": -120.48297}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Golden", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.75554, "lon": -105.2211}}, {"city": "Hollywood", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.0112, "lon": -80.14949}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Boise", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.6135, "lon": -116.20345}}, {"city": "<PERSON><PERSON>", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.54072, "lon": -116.56346}}, {"city": "Topeka", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04833, "lon": -95.67804}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.7762, "lon": -71.07728}}, {"city": "Cherry Hill", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 39.93484, "lon": -75.03073}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "New Hyde Park", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.7351, "lon": -73.68791}}, {"city": "Salisbury", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.67097, "lon": -80.47423}}, {"city": "Oklahoma City", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.46756, "lon": -97.51643}}, {"city": "Tulsa", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 36.15398, "lon": -95.99277}}, {"city": "Portland", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.52345, "lon": -122.67621}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "New Braunfels", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.703, "lon": -98.12445}}, {"city": "Odessa", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.84568, "lon": -102.36764}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Bueos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Pleven", "country": "Bulgaria", "geoPoint": {"lat": 43.41667, "lon": 24.61667}}, {"city": "Sofia N/A", "country": "Bulgaria"}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "Chilliwack", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.16638, "lon": -121.95257}}, {"city": "Coquitlam", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.28297, "lon": -122.75262}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "Sarnia", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.97866, "lon": -82.40407}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Pointe<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.44868, "lon": -73.81669}}, {"city": "St Romuald", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.75818, "lon": -71.23921}}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "Olomouc 9", "country": "Czech Republic", "geoPoint": {"lat": 49.59552, "lon": 17.25175}}, {"city": "Pisek 1", "country": "Czech Republic", "geoPoint": {"lat": 49.3088, "lon": 14.1475}}, {"city": "Praha 28", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 5", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Kota Bharu", "country": "Malaysia", "geoPoint": {"lat": 6.13328, "lon": 102.2386}}, {"city": "Kuala Lumpur N/A", "country": "Malaysia"}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "Ciudad De Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Zapopan", "country": "Mexico", "geoPoint": {"lat": 20.72356, "lon": -103.38479}}, {"city": "Bydgoszcz", "country": "Poland", "geoPoint": {"lat": 53.1235, "lon": 18.00762}}, {"city": "Gdansk", "country": "Poland", "geoPoint": {"lat": 54.35205, "lon": 18.64637}}, {"city": "Kutno 001", "country": "Poland", "geoPoint": {"lat": 52.23064, "lon": 19.36409}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin N/A", "country": "Poland"}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Ponce Pr", "country": "Puerto Rico"}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "Baia Mare", "country": "Romania", "geoPoint": {"lat": 47.65529, "lon": 23.57381}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.76667, "lon": 23.6}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.45, "lon": 28.05}}, {"city": "P<PERSON>iesti", "country": "Romania", "geoPoint": {"lat": 44.95, "lon": 26.01667}}, {"city": "Moscow N/A", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "St Petersburg N/A", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Belfast", "country": "United Kingdom", "geoPoint": {"lat": 54.59682, "lon": -5.92541}}, {"city": "Bolton", "country": "United Kingdom", "geoPoint": {"lat": 53.58333, "lon": -2.43333}}, {"city": "Exeter", "country": "United Kingdom", "geoPoint": {"lat": 50.7236, "lon": -3.52751}}, {"city": "Lincoln", "country": "United Kingdom", "geoPoint": {"lat": 53.22683, "lon": -0.53792}}]}, "referencesModule": {"references": [{"pmid": "22632452", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Evaluation of vulvovaginal symptoms and Candida colonization in women with type 2 diabetes mellitus treated with canagliflozin, a sodium glucose co-transporter 2 inhibitor. Curr Med Res Opin. 2012 Jul;28(7):1173-8. doi: 10.1185/03007995.2012.697053. Epub 2012 Jun 14."}, {"pmid": "22548646", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> of canagliflozin, a sodium glucose co-transporter 2 (SGLT2) inhibitor, on bacteriuria and urinary tract infection in subjects with type 2 diabetes enrolled in a 12-week, phase 2 study. Curr Med Res Opin. 2012 Jul;28(7):1167-71. doi: 10.1185/03007995.2012.689956. Epub 2012 May 15."}, {"pmid": "22492586", "type": "DERIVED", "citation": "<PERSON><PERSON> J, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; Canagliflozin DIA 2001 Study Group. Dose-ranging effects of canagliflozin, a sodium-glucose cotransporter 2 inhibitor, as add-on to metformin in subjects with type 2 diabetes. Diabetes Care. 2012 Jun;35(6):1232-8. doi: 10.2337/dc11-1926. Epub 2012 Apr 9."}]}}, "resultsSection": {"participantFlowModule": {"preAssignmentDetails": "A total of 451 patients were randomly allocated to the 7 treatment arms in the study and comprised the intent-to-treat analysis set which was used for the efficacy analyses. All 451 patients received at least 1 dose of study drug and were included in the safety analysis set.", "recruitmentDetails": "This study evaluated the efficacy and safety of canagliflozin (JNJ-28431754) in patients with type 2 diabetes mellitus with sitagliptin as a reference arm. The study was conducted between 27 March 2008 and 28 January 2009 and recruited patients from 85 study centers located in 13 countries worldwide.", "groups": [{"id": "FG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "FG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "FG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "FG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "FG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "FG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "FG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "65"}, {"groupId": "FG001", "numSubjects": "64"}, {"groupId": "FG002", "numSubjects": "64"}, {"groupId": "FG003", "numSubjects": "65"}, {"groupId": "FG004", "numSubjects": "64"}, {"groupId": "FG005", "numSubjects": "64"}, {"groupId": "FG006", "numSubjects": "65"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "55"}, {"groupId": "FG001", "numSubjects": "59"}, {"groupId": "FG002", "numSubjects": "59"}, {"groupId": "FG003", "numSubjects": "56"}, {"groupId": "FG004", "numSubjects": "56"}, {"groupId": "FG005", "numSubjects": "57"}, {"groupId": "FG006", "numSubjects": "60"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "10"}, {"groupId": "FG001", "numSubjects": "5"}, {"groupId": "FG002", "numSubjects": "5"}, {"groupId": "FG003", "numSubjects": "9"}, {"groupId": "FG004", "numSubjects": "8"}, {"groupId": "FG005", "numSubjects": "7"}, {"groupId": "FG006", "numSubjects": "5"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "3"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "2"}, {"groupId": "FG005", "numSubjects": "2"}, {"groupId": "FG006", "numSubjects": "0"}]}, {"type": "Lack of Efficacy", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "0"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "0"}, {"groupId": "FG006", "numSubjects": "0"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "5"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "1"}, {"groupId": "FG006", "numSubjects": "1"}]}, {"type": "Physician Decision", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "0"}, {"groupId": "FG006", "numSubjects": "0"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "1"}, {"groupId": "FG006", "numSubjects": "2"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "1"}, {"groupId": "FG003", "numSubjects": "3"}, {"groupId": "FG004", "numSubjects": "6"}, {"groupId": "FG005", "numSubjects": "2"}, {"groupId": "FG006", "numSubjects": "2"}]}, {"type": "Noncompliance with study drug regimen", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "0"}, {"groupId": "FG006", "numSubjects": "0"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}, {"groupId": "FG004", "numSubjects": "0"}, {"groupId": "FG005", "numSubjects": "1"}, {"groupId": "FG006", "numSubjects": "0"}]}]}]}, "baselineCharacteristicsModule": {"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "BG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "BG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG007", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "65"}, {"groupId": "BG001", "value": "64"}, {"groupId": "BG002", "value": "64"}, {"groupId": "BG003", "value": "65"}, {"groupId": "BG004", "value": "64"}, {"groupId": "BG005", "value": "64"}, {"groupId": "BG006", "value": "65"}, {"groupId": "BG007", "value": "451"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "0"}, {"groupId": "BG005", "value": "0"}, {"groupId": "BG006", "value": "0"}, {"groupId": "BG007", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "63"}, {"groupId": "BG001", "value": "61"}, {"groupId": "BG002", "value": "63"}, {"groupId": "BG003", "value": "61"}, {"groupId": "BG004", "value": "63"}, {"groupId": "BG005", "value": "62"}, {"groupId": "BG006", "value": "65"}, {"groupId": "BG007", "value": "438"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "4"}, {"groupId": "BG004", "value": "1"}, {"groupId": "BG005", "value": "2"}, {"groupId": "BG006", "value": "0"}, {"groupId": "BG007", "value": "13"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "53.3", "spread": "7.82"}, {"groupId": "BG001", "value": "53.3", "spread": "8.48"}, {"groupId": "BG002", "value": "51.7", "spread": "7.95"}, {"groupId": "BG003", "value": "52.9", "spread": "9.56"}, {"groupId": "BG004", "value": "52.3", "spread": "6.88"}, {"groupId": "BG005", "value": "55.2", "spread": "7.14"}, {"groupId": "BG006", "value": "51.7", "spread": "8.09"}, {"groupId": "BG007", "value": "52.9", "spread": "8.06"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "34"}, {"groupId": "BG001", "value": "30"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "32"}, {"groupId": "BG004", "value": "28"}, {"groupId": "BG005", "value": "36"}, {"groupId": "BG006", "value": "27"}, {"groupId": "BG007", "value": "215"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "31"}, {"groupId": "BG001", "value": "34"}, {"groupId": "BG002", "value": "36"}, {"groupId": "BG003", "value": "33"}, {"groupId": "BG004", "value": "36"}, {"groupId": "BG005", "value": "28"}, {"groupId": "BG006", "value": "38"}, {"groupId": "BG007", "value": "236"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "2"}, {"groupId": "BG005", "value": "0"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "BULGARIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "3"}, {"groupId": "BG006", "value": "1"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "8"}, {"groupId": "BG004", "value": "8"}, {"groupId": "BG005", "value": "4"}, {"groupId": "BG006", "value": "7"}, {"groupId": "BG007", "value": "53"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "6"}, {"groupId": "BG004", "value": "0"}, {"groupId": "BG005", "value": "2"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "17"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "5"}, {"groupId": "BG004", "value": "2"}, {"groupId": "BG005", "value": "6"}, {"groupId": "BG006", "value": "4"}, {"groupId": "BG007", "value": "30"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "2"}, {"groupId": "BG004", "value": "4"}, {"groupId": "BG005", "value": "1"}, {"groupId": "BG006", "value": "3"}, {"groupId": "BG007", "value": "19"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "9"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "14"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "50"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "7"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "5"}, {"groupId": "BG006", "value": "9"}, {"groupId": "BG007", "value": "39"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "6"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "5"}, {"groupId": "BG006", "value": "8"}, {"groupId": "BG007", "value": "46"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "2"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "3"}, {"groupId": "BG006", "value": "4"}, {"groupId": "BG007", "value": "32"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "1"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "21"}, {"groupId": "BG001", "value": "21"}, {"groupId": "BG002", "value": "16"}, {"groupId": "BG003", "value": "18"}, {"groupId": "BG004", "value": "18"}, {"groupId": "BG005", "value": "20"}, {"groupId": "BG006", "value": "21"}, {"groupId": "BG007", "value": "135"}]}]}]}]}, "outcomeMeasuresModule": {"outcomeMeasures": [{"type": "PRIMARY", "title": "Change in HbA1c From Baseline to Week 12", "description": "The table below shows the mean change in HbA1c from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "Percent", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "61"}, {"groupId": "OG001", "value": "62"}, {"groupId": "OG002", "value": "62"}, {"groupId": "OG003", "value": "62"}, {"groupId": "OG004", "value": "60"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "62"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.22", "spread": "0.702"}, {"groupId": "OG001", "value": "-0.79", "spread": "0.749"}, {"groupId": "OG002", "value": "-0.76", "spread": "0.992"}, {"groupId": "OG003", "value": "-0.70", "spread": "0.720"}, {"groupId": "OG004", "value": "-0.92", "spread": "0.695"}, {"groupId": "OG005", "value": "-0.95", "spread": "0.704"}, {"groupId": "OG006", "value": "-0.74", "spread": "0.615"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.45", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.747", "ciUpperLimit": "-0.148", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.51", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.804", "ciUpperLimit": "-0.207", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.54", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.841", "ciUpperLimit": "-0.244", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.71", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.006", "ciUpperLimit": "-0.405", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.117"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.73", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.029", "ciUpperLimit": "-0.432", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.56", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.862", "ciUpperLimit": "-0.265", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}]}, {"type": "SECONDARY", "title": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 12", "description": "The table below shows the mean change in FPG from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "mmol/L", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "63"}, {"groupId": "OG003", "value": "62"}, {"groupId": "OG004", "value": "61"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "0.2", "spread": "1.58"}, {"groupId": "OG001", "value": "-0.9", "spread": "2.26"}, {"groupId": "OG002", "value": "-1.4", "spread": "1.70"}, {"groupId": "OG003", "value": "-1.5", "spread": "2.23"}, {"groupId": "OG004", "value": "-1.4", "spread": "1.87"}, {"groupId": "OG005", "value": "-1.3", "spread": "1.54"}, {"groupId": "OG006", "value": "-0.7", "spread": "1.77"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.9", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.39", "ciUpperLimit": "-0.34", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.4", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.98", "ciUpperLimit": "-0.92", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.8", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.33", "ciUpperLimit": "-1.27", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.8", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.32", "ciUpperLimit": "-1.26", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.7", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.25", "ciUpperLimit": "-1.19", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.0", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.51", "ciUpperLimit": "-0.46", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}]}, {"type": "SECONDARY", "title": "Percentage of Patients With Symptoms of Hypoglycemia", "description": "The table below shows the percentage of patients who experienced symptomatic hypoglycemic events between Baseline and Week 12.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomiy assigned to a treatment group.", "reportingStatus": "POSTED", "paramType": "NUMBER", "unitOfMeasure": "Percentage of patients", "timeFrame": "Up to Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "65"}, {"groupId": "OG001", "value": "64"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "65"}, {"groupId": "OG004", "value": "64"}, {"groupId": "OG005", "value": "64"}, {"groupId": "OG006", "value": "65"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "2"}, {"groupId": "OG001", "value": "0"}, {"groupId": "OG002", "value": "2"}, {"groupId": "OG003", "value": "6"}, {"groupId": "OG004", "value": "0"}, {"groupId": "OG005", "value": "3"}, {"groupId": "OG006", "value": "5"}]}]}]}, {"type": "SECONDARY", "title": "Change in Overnight Urine Glucose/Creatinine Ratio From Baseline to Week 12", "description": "The table below shows the mean change in overnight urine glucose/creatinine ratio from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "mg/mg", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "54"}, {"groupId": "OG001", "value": "58"}, {"groupId": "OG002", "value": "56"}, {"groupId": "OG003", "value": "53"}, {"groupId": "OG004", "value": "57"}, {"groupId": "OG005", "value": "56"}, {"groupId": "OG006", "value": "58"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "1.9", "spread": "12.34"}, {"groupId": "OG001", "value": "35.4", "spread": "28.98"}, {"groupId": "OG002", "value": "51.5", "spread": "28.83"}, {"groupId": "OG003", "value": "50.5", "spread": "24.38"}, {"groupId": "OG004", "value": "49.4", "spread": "38.41"}, {"groupId": "OG005", "value": "61.6", "spread": "37.85"}, {"groupId": "OG006", "value": "-1.9", "spread": "14.78"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "36.1", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "26.07", "ciUpperLimit": "46.13", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.10"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "49.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "39.17", "ciUpperLimit": "59.34", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.13"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "48.2", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "37.98", "ciUpperLimit": "58.42", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.2"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "49.0", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "38.91", "ciUpperLimit": "59.01", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.11"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "60.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "50.17", "ciUpperLimit": "70.35", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.13"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.513", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-3.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-13.33", "ciUpperLimit": "6.67", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.09"}]}, {"type": "SECONDARY", "title": "Absolute Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean absolute change in body weight from Baseline to Week 12 for each treatment group.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "kg", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "63"}, {"groupId": "OG004", "value": "62"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.78", "spread": "2.099"}, {"groupId": "OG001", "value": "-1.96", "spread": "2.334"}, {"groupId": "OG002", "value": "-2.25", "spread": "2.145"}, {"groupId": "OG003", "value": "-2.32", "spread": "2.842"}, {"groupId": "OG004", "value": "-2.88", "spread": "2.391"}, {"groupId": "OG005", "value": "-2.87", "spread": "2.344"}, {"groupId": "OG006", "value": "-0.43", "spread": "2.693"}]}]}]}, {"type": "SECONDARY", "title": "Percent Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean percent change in body weight from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "63"}, {"groupId": "OG004", "value": "62"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-1.1", "spread": "2.4"}, {"groupId": "OG001", "value": "-2.3", "spread": "2.8"}, {"groupId": "OG002", "value": "-2.6", "spread": "2.3"}, {"groupId": "OG003", "value": "-2.7", "spread": "3.0"}, {"groupId": "OG004", "value": "-3.4", "spread": "2.8"}, {"groupId": "OG005", "value": "-3.4", "spread": "2.6"}, {"groupId": "OG006", "value": "-0.6", "spread": "3.0"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.009", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.2", "ciUpperLimit": "-0.3", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.002", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.5", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.5", "ciUpperLimit": "-0.6", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and mixed meal tolerance test.", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.6", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.6", "ciUpperLimit": "-0.7", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-2.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.3", "ciUpperLimit": "-1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-2.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.3", "ciUpperLimit": "-1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.371", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "0.4", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.5", "ciUpperLimit": "1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}]}]}, "adverseEventsModule": {"frequencyThreshold": "5", "timeFrame": "Adverse events were reported for the duration of the study; each patient participated in the study for approximately 12 weeks.", "description": "The total number of adverse events listed in the \"Other (non-Serious) Adverse Event\" table are based upon a cut-off of greater than or equal to 5 percent of patients experiencing the adverse event in any treatment arm.", "eventGroups": [{"id": "EG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks.", "seriousNumAffected": 1, "seriousNumAtRisk": 65, "otherNumAffected": 9, "otherNumAtRisk": 65}, {"id": "EG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "seriousNumAffected": 1, "seriousNumAtRisk": 64, "otherNumAffected": 13, "otherNumAtRisk": 64}, {"id": "EG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "seriousNumAffected": 1, "seriousNumAtRisk": 64, "otherNumAffected": 10, "otherNumAtRisk": 64}, {"id": "EG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "seriousNumAffected": 1, "seriousNumAtRisk": 65, "otherNumAffected": 10, "otherNumAtRisk": 65}, {"id": "EG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "seriousNumAffected": 1, "seriousNumAtRisk": 64, "otherNumAffected": 10, "otherNumAtRisk": 64}, {"id": "EG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks.", "seriousNumAffected": 1, "seriousNumAtRisk": 64, "otherNumAffected": 12, "otherNumAtRisk": 64}, {"id": "EG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "seriousNumAffected": 0, "seriousNumAtRisk": 65, "otherNumAffected": 7, "otherNumAtRisk": 65}], "seriousEvents": [{"term": "Atrial fibrillation", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Cardiac failure congestive", "organSystem": "Cardiac disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Gastroenteritis", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Otitis externa", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Pneumonia", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Blood creatine phosphokinase increased", "organSystem": "Investigations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Gestational diabetes", "organSystem": "Metabolism and nutrition disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Spondylolisthesis", "organSystem": "Musculoskeletal and connective tissue disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "Hypoxia", "organSystem": "Respiratory, thoracic and mediastinal disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}], "otherEvents": [{"term": "Abdominal pain upper", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 2, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 4, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 0, "numAtRisk": 65}]}, {"term": "<PERSON><PERSON><PERSON>", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 1, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 5, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 1, "numAtRisk": 65}]}, {"term": "Nasopharyngitis", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 2, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 5, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 3, "numAtRisk": 65}]}, {"term": "Urinary tract infection", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 4, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 2, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 6, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 2, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 1, "numAtRisk": 65}]}, {"term": "Vulvovaginal mycotic infection", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 4, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 2, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 4, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 1, "numAtRisk": 65}]}, {"term": "Headache", "organSystem": "Nervous system disorders", "sourceVocabulary": "MEDDRA 11.1", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 2, "numAtRisk": 65}, {"groupId": "EG001", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG002", "numAffected": 5, "numAtRisk": 64}, {"groupId": "EG003", "numAffected": 2, "numAtRisk": 65}, {"groupId": "EG004", "numAffected": 3, "numAtRisk": 64}, {"groupId": "EG005", "numAffected": 1, "numAtRisk": 64}, {"groupId": "EG006", "numAffected": 1, "numAtRisk": 65}]}]}, "moreInfoModule": {"certainAgreement": {"piSponsorEmployee": false, "restrictionType": "OTHER", "restrictiveAgreement": true, "otherDetails": "A copy of the manuscript must be provided to the sponsor for review at least 60 days before submission for publication or presentation. If requested in writing, such publication will be withheld for up to an additional 60 days."}, "pointOfContact": {"title": "Vice President, Franchise Medical Leader, Cardiovascular & Metabolism Franchise", "organization": "Janssen Research & Development, LLC", "phone": "**************"}}}, "derivedSection": {"miscInfoModule": {"versionHolder": "2025-07-08", "removedCountries": ["Ireland", "Sweden"]}, "conditionBrowseModule": {"meshes": [{"id": "D003920", "term": "Diabe<PERSON>"}, {"id": "D003924", "term": "Diabetes Mellitus, Type 2"}], "ancestors": [{"id": "D044882", "term": "Glucose Metabolism Disorders"}, {"id": "D008659", "term": "Metabolic Diseases"}, {"id": "D004700", "term": "Endocrine System Diseases"}], "browseLeaves": [{"id": "M7115", "name": "Diabe<PERSON>", "asFound": "Diabe<PERSON>", "relevance": "HIGH"}, {"id": "M7119", "name": "Diabetes Mellitus, Type 2", "asFound": "Diabetes <PERSON>, Non-Insulin-Dependent", "relevance": "HIGH"}, {"id": "M11639", "name": "Metabolic Diseases", "relevance": "LOW"}, {"id": "M25403", "name": "Glucose Metabolism Disorders", "relevance": "LOW"}, {"id": "M7862", "name": "Endocrine System Diseases", "relevance": "LOW"}], "browseBranches": [{"abbrev": "BC18", "name": "Nutritional and Metabolic Diseases"}, {"abbrev": "BC19", "name": "Gland and Hormone Related Diseases"}, {"abbrev": "All", "name": "All Conditions"}]}, "interventionBrowseModule": {"meshes": [{"id": "D000068900", "term": "Sitagliptin Phosphate"}, {"id": "D000068896", "term": "Canagliflozin"}], "ancestors": [{"id": "D007004", "term": "Hypoglycemic Agents"}, {"id": "D045505", "term": "Physiological Effects of Drugs"}, {"id": "D054795", "term": "Incretins"}, {"id": "D006728", "term": "Hormones"}, {"id": "D006730", "term": "Hormones, Hormone Substitutes, and Hormone Antagonists"}, {"id": "D054873", "term": "Dipeptidyl-Peptidase IV Inhibitors"}, {"id": "D011480", "term": "Protease Inhibitors"}, {"id": "D004791", "term": "Enzyme Inhibitors"}, {"id": "D045504", "term": "Molecular Mechanisms of Pharmacological Action"}, {"id": "D000077203", "term": "Sodium-Glucose Transporter 2 Inhibitors"}], "browseLeaves": [{"id": "M11667", "name": "Metformin", "relevance": "LOW"}, {"id": "M1691", "name": "Sodium-Glucose Transporter 2 Inhibitors", "relevance": "LOW"}, {"id": "M335", "name": "Sitagliptin Phosphate", "asFound": "Mothers", "relevance": "HIGH"}, {"id": "M331", "name": "Canagliflozin", "asFound": "Lip", "relevance": "HIGH"}, {"id": "M207501", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relevance": "LOW"}, {"id": "M10054", "name": "Hypoglycemic Agents", "relevance": "LOW"}, {"id": "M27905", "name": "Incretins", "relevance": "LOW"}, {"id": "M9789", "name": "Hormones", "relevance": "LOW"}, {"id": "M9788", "name": "Hormone Antagonists", "relevance": "LOW"}, {"id": "M27957", "name": "Dipeptidyl-Peptidase IV Inhibitors", "relevance": "LOW"}, {"id": "M19609", "name": "HIV Protease Inhibitors", "relevance": "LOW"}, {"id": "M14343", "name": "Protease Inhibitors", "relevance": "LOW"}, {"id": "M7951", "name": "Enzyme Inhibitors", "relevance": "LOW"}], "browseBranches": [{"abbrev": "Hypo", "name": "Hypoglycemic Agents"}, {"abbrev": "All", "name": "All Drugs and Chemicals"}, {"abbrev": "Infl", "name": "Anti-Inflammatory Agents"}, {"abbrev": "<PERSON><PERSON>", "name": "Antirheumatic Agents"}, {"abbrev": "Analg", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"abbrev": "Infe", "name": "Anti-Infective Agents"}]}}, "hasResults": true}}