{"nctId": "NCT01106651", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT01106651", "orgStudyIdInfo": {"id": "CR017014"}, "secondaryIdInfos": [{"id": "28431754DIA3010", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "A Safety and Efficacy Study of Canagliflozin in Older Patients (55 to 80 Years of Age) With Type 2 Diabetes Mellitus", "officialTitle": "A Randomized, Double-Blind, Placebo-Controlled, Parallel-Group, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin Compared With Placebo in the Treatment of Older Subjects With Type 2 Diabetes Mellitus Inadequately Controlled on Glucose Lowering Therapy"}, "statusModule": {"statusVerifiedDate": "2014-10", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2010-06"}, "primaryCompletionDateStruct": {"date": "2011-11", "type": "ACTUAL"}, "completionDateStruct": {"date": "2013-05", "type": "ACTUAL"}, "studyFirstSubmitDate": "2010-04-01", "studyFirstSubmitQcDate": "2010-04-16", "studyFirstPostDateStruct": {"date": "2010-04-20", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2013-04-01", "resultsFirstSubmitQcDate": "2013-04-01", "resultsFirstPostDateStruct": {"date": "2013-05-29", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2012-03-30", "dispFirstSubmitQcDate": "2012-04-24", "dispFirstPostDateStruct": {"date": "2012-04-25", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2014-10-27", "lastUpdatePostDateStruct": {"date": "2014-11-04", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": true}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the efficacy and safety of 2 different doses of canagliflozin compared with placebo in older patients (55 to 80 years of age) with type 2 diabetes mellitus (T2DM) with inadequate control on their current diabetes treatment regimen.", "detailedDescription": "Canagliflozin is a drug that is being tested to see if it may be useful in treating patients diagnosed with type 2 diabetes mellitus (T2DM). This is a randomized (study drug assigned by chance), double-blind (neither the patient or the study doctor will know the name of the assigned treatment), placebo-controlled, parallel-group, 3-arm (3 treatment groups) multicenter study to determine the efficacy, safety, and tolerability of canagliflozin (100 mg and 300 mg) compared to placebo (a capsule that looks like all the other treatments but has no real medicine) in patients with T2DM who are not achieving an adequate response from current antihyperglycemic therapy to control their diabetes. Approximately 720 older (55 to 80 years of age) patients with T2DM who are either not on an antihyperglycemic agent or who are receiving treatment with a stable regimen of antihyperglycemic agent(s) and have inadequate glycemic (blood sugar) control will receive once daily treatment with canagliflozin (100 mg or 300 mg) or placebo capsules for 104 weeks (includes 26 weeks of double-blind treatment followed by a 78-week extension period). In addition, all patients will take stable doses of the antihyperglycemic agent(s) that they were taking before entry in the study for the duration of the study. Patients will participate in the study for approximately 108 weeks. During the study, if a patient's fasting blood sugar remains high despite treatment with study drug, the patient will receive treatment with an antihyperglycemic agent (rescue therapy) that is considered clinically appropriate and consistent with local prescribing information. During treatment, patients will be monitored for safety by review of adverse events, results from laboratory tests, measures of bone health, 12-lead electrocardiograms (ECGs), vital signs measurements, body weight, physical examinations, and self-monitored blood glucose (SMGB) measurements. The primary outcome measure in the study is the effect of canagliflozin relative to placebo on hemoglobin A1c (HbA1c) after 26 weeks of treatment. Study drug will be taken orally (by mouth) once daily before the first meal each day unless otherwise specified. All patients will take single-blind placebo capsules for 2 weeks before randomization. After randomization, patients will take double blind canagliflozin (100 mg or 300 mg) or matching placebo for 104 weeks."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Canagliflozin", "Placebo", "Hemoglobin A1c", "Bone", "Type 2 diabetes mellitus"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE3"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "TRIPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR"]}}, "enrollmentInfo": {"count": 716, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 100 mg", "type": "EXPERIMENTAL", "description": "Each patient will receive 100 mg of canagliflozin once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "interventionNames": ["Drug: Canagliflozin 100 mg", "Drug: Antihyperglycemic agent(s)"]}, {"label": "Canagliflozin 300 mg", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "interventionNames": ["Drug: Canagliflozin 300 mg", "Drug: Antihyperglycemic agent(s)"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each patient will receive matching placebo once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "interventionNames": ["Drug: Antihyperglycemic agent(s)", "Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Canagliflozin 100 mg", "description": "One 100 mg over-encapsulated tablet orally (by mouth) once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "armGroupLabels": ["Canagliflozin 100 mg"]}, {"type": "DRUG", "name": "Canagliflozin 300 mg", "description": "One 300 mg over-encapsulated tablet orally (by mouth) once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "armGroupLabels": ["Canagliflozin 300 mg"]}, {"type": "DRUG", "name": "Antihyperglycemic agent(s)", "description": "Stable doses of antihyperglycemic agents (sulfonylurea agent, thiazolidinediones, dipeptidyl peptidase 4 \\[DPP-4\\] inhibitors, metformin, insulin \\[all types\\]) and their combinations (sulfonylurea agent and insulin \\[all types\\], metformin and insulin \\[all types\\], metformin and sulfonylurea, alpha glucosidase inhibitors, thiazolidinediones, dipeptidyl peptidase 4 \\[DPP-4\\]) are used as per protocol specifications.", "armGroupLabels": ["Canagliflozin 100 mg", "Canagliflozin 300 mg", "Placebo"]}, {"type": "DRUG", "name": "Placebo", "description": "One matching placebo capsule orally once daily for 104 weeks with/without stable doses of antihyperglycemic agent(s) taken at the time of study entry.", "armGroupLabels": ["Placebo"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}], "secondaryOutcomes": [{"measure": "Percentage of Patients With HbA1c <7% at Week 26", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 26 in each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the percentage.", "timeFrame": "Week 26"}, {"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Body Weight From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in Total Fat From Baseline to Week 26 in a Subset of Patients Undergoing Specific Dual-energy X-ray Absorptiometry (DXA) Analysis for Body Composition", "description": "The table below shows the least-squares (LS) mean change in total fat from Baseline to Week 26 for each treatment group in patients randomized to the subset of patients undergoing specific DXA analysis for body composition. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in Region Percent Total Fat From Baseline to Week 26 in a Subset of Patients Undergoing Specific Dual-energy X-ray Absorptiometry (DXA) Analysis for Body Composition", "description": "Region percent total fat = body fat as a percentage of (body fat + lean body mass + bone mass content). The table below shows the least-squares (LS) mean change in region percent total fat from Baseline to Week 26 for each treatment group in patients randomized to the subset of patients undergoing specific dual-energy X-ray absorptiometry (DXA) analysis for body composition. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in Tissue Percent Total Fat From Baseline to Week 26 in a Subset of Patients Undergoing Specific Dual-energy X-ray Absorptiometry (DXA) Analysis for Body Composition", "description": "Tissue percent total fat = body fat as a percentage of body fat + lean body mass. The table below shows the least-squares (LS) mean change in tissue percent total fat from Baseline to Week 26 for each treatment group in patients randomized to the subset of patients undergoing specific DXA analysis for body composition. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in Systolic Blood Pressure (SBP) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in SBP from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Triglycerides From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in triglycerides from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in High-density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in HDL-C from Baseline to Week 26 or each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Lumbar Spine Bone Mineral Density (BMD) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change from Baseline to Week 26 in lumbar spine BMD for each treatment group as assessed by dual-energy X-ray absorptiometry (DXA). The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Distal Forearm Bone Mineral Density (BMD) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change from Baseline to Week 26 in distal forearm BMD for each treatment group as assessed by dual-energy X-ray absorptiometry (DXA). The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Femoral Neck Bone Mineral Density (BMD) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change from Baseline to Week 26 in femoral neck BMD for each treatment group as assessed by dual-energy X-ray absorptiometry (DXA). The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Total Hip Bone Mineral Density (BMD) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change from Baseline to Week 26 in total hip BMD for each treatment group as assessed by dual-energy X-ray absorptiometry (DXA). The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* All patients must have a diagnosis of T2DM and may be currently treated with a stable regimen of antihyperglycemic agent(s)\n* Patients in the study must have a HbA1c between \\>=7 and \\<=10.0%\n* Patients must have a fasting plasma glucose (FPG) \\<270 mg/dL (15 mmol/L)\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus (T1DM), pancreas or beta cell transplantation, or diabetes secondary to pancreatitis or pancreatectomy, or a severe hypoglycemic episode within 6 months before screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "55 Years", "maximumAge": "80 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Glendale", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.53865, "lon": -112.18599}}, {"city": "Phoenix", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.44838, "lon": -112.07404}}, {"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "<PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 38.61713, "lon": -121.32828}}, {"city": "Citrus Heights", "state": "California", "country": "United States", "geoPoint": {"lat": 38.70712, "lon": -121.28106}}, {"city": "Fair Oaks", "state": "California", "country": "United States", "geoPoint": {"lat": 38.64463, "lon": -121.27217}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Sacramento", "state": "California", "country": "United States", "geoPoint": {"lat": 38.58157, "lon": -121.4944}}, {"city": "San Diego", "state": "California", "country": "United States", "geoPoint": {"lat": 32.71533, "lon": -117.15726}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "Daytona Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 29.21081, "lon": -81.02283}}, {"city": "Fleming Island", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.0933, "lon": -81.71898}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Wichita", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 37.69224, "lon": -97.33754}}, {"city": "<PERSON><PERSON>", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.37649, "lon": -71.23561}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.20829, "lon": -115.98391}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "<PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.79154, "lon": -78.78112}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Wilmington", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.22573, "lon": -77.94471}}, {"city": "Bismarck", "state": "North Dakota", "country": "United States", "geoPoint": {"lat": 46.80833, "lon": -100.78374}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.55895, "lon": -84.30411}}, {"city": "Mount Pleasant", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 32.79407, "lon": -79.86259}}, {"city": "Bristol", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.59511, "lon": -82.18874}}, {"city": "<PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.95373, "lon": -96.89028}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.81402, "lon": -96.94889}}, {"city": "Plano", "state": "Texas", "country": "United States", "geoPoint": {"lat": 33.01984, "lon": -96.69889}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.94818, "lon": -96.72972}}, {"city": "Ren<PERSON>", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.48288, "lon": -122.21707}}, {"city": "Tacoma", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.25288, "lon": -122.44429}}, {"city": "Wenatchee", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.42346, "lon": -120.31035}}, {"city": "Fremantle", "country": "Australia", "geoPoint": {"lat": -32.05629, "lon": 115.74565}}, {"city": "Heidelberg Heights", "country": "Australia", "geoPoint": {"lat": -37.74313, "lon": 145.05695}}, {"city": "Meadowbrook", "country": "Australia", "geoPoint": {"lat": -27.66401, "lon": 153.14465}}, {"city": "Richmond", "country": "Australia", "geoPoint": {"lat": -37.81819, "lon": 145.00176}}, {"city": "Vancouver", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "St. John'S", "state": "Newfoundland and Labrador", "country": "Canada", "geoPoint": {"lat": 47.56494, "lon": -52.70931}}, {"city": "<PERSON><PERSON>", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 44.40011, "lon": -79.66634}}, {"city": "London", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.98339, "lon": -81.23304}}, {"city": "Markham", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.86682, "lon": -79.2663}}, {"city": "Oakville", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.45011, "lon": -79.68292}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Montreal", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.50884, "lon": -73.58781}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 10.96854, "lon": -74.78132}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "<PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 48.60603, "lon": 2.48757}}, {"city": "Paris", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 45.70254, "lon": 4.87147}}, {"city": "Thessalonikis", "country": "Greece"}, {"city": "Thessaloniki", "country": "Greece", "geoPoint": {"lat": 40.64361, "lon": 22.93086}}, {"city": "<PERSON><PERSON>", "country": "Hong Kong", "geoPoint": {"lat": 22.38333, "lon": 114.18333}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Auckland", "country": "New Zealand", "geoPoint": {"lat": -36.84853, "lon": 174.76349}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Katowice", "country": "Poland", "geoPoint": {"lat": 50.25841, "lon": 19.02754}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "Sibiu", "country": "Romania", "geoPoint": {"lat": 45.8, "lon": 24.15}}, {"city": "Pretoria", "country": "South Africa", "geoPoint": {"lat": -25.74486, "lon": 28.18783}}, {"city": "Granada", "country": "Spain", "geoPoint": {"lat": 37.18817, "lon": -3.60667}}, {"city": "Madrid", "country": "Spain", "geoPoint": {"lat": 40.4165, "lon": -3.70256}}, {"city": "Pozuelo De Alarcon", "country": "Spain", "geoPoint": {"lat": 40.43293, "lon": -3.81338}}, {"city": "Sevilla", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Göteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Uppsala", "country": "Sweden", "geoPoint": {"lat": 59.85882, "lon": 17.63889}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Switzerland", "geoPoint": {"lat": 47.5296, "lon": 7.59902}}, {"city": "St Gallen", "country": "Switzerland", "geoPoint": {"lat": 47.42391, "lon": 9.37477}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Birmingham", "country": "United Kingdom", "geoPoint": {"lat": 52.48142, "lon": -1.89983}}, {"city": "Cardiff", "country": "United Kingdom", "geoPoint": {"lat": 51.48, "lon": -3.18}}, {"city": "Glasgow", "country": "United Kingdom", "geoPoint": {"lat": 55.86515, "lon": -4.25763}}, {"city": "Liverpool", "country": "United Kingdom", "geoPoint": {"lat": 53.41058, "lon": -2.97794}}, {"city": "Manchester", "country": "United Kingdom", "geoPoint": {"lat": 53.48095, "lon": -2.23743}}, {"city": "Reading", "country": "United Kingdom", "geoPoint": {"lat": 51.45625, "lon": -0.97113}}, {"city": "Salford", "country": "United Kingdom", "geoPoint": {"lat": 53.48771, "lon": -2.29042}}]}, "referencesModule": {"references": [{"pmid": "28619659", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Effects of Canagliflozin on Cardiovascular Biomarkers in Older Adults With Type 2 Diabetes. J Am Coll Cardiol. 2017 Aug 8;70(6):704-712. doi: 10.1016/j.jacc.2017.06.016. Epub 2017 Jun 12."}, {"pmid": "27977934", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>-term safety and tolerability of canagliflozin in patients with type 2 diabetes: a pooled analysis. Curr Med Res Opin. 2017 Mar;33(3):553-562. doi: 10.1080/03007995.2016.1271780. Epub 2017 Jan 4."}, {"pmid": "27002421", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Effects of canagliflozin on body weight and body composition in patients with type 2 diabetes over 104 weeks. Postgrad Med. 2016 May;128(4):371-80. doi: 10.1080/00325481.2016.1169894. Epub 2016 Apr 7."}, {"pmid": "26580237", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of Canagliflozin on Fracture Risk in Patients With Type 2 Diabetes Mellitus. J Clin Endocrinol Metab. 2016 Jan;101(1):157-66. doi: 10.1210/jc.2015-3167. Epub 2015 Nov 18."}, {"pmid": "26580234", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Rosenthal N. Evaluation of Bone Mineral Density and Bone Biomarkers in Patients With Type 2 Diabetes Treated With Canagliflozin. J Clin Endocrinol Metab. 2016 Jan;101(1):44-51. doi: 10.1210/jc.2015-1860. Epub 2015 Nov 18."}, {"pmid": "24786834", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effect of canagliflozin on serum electrolytes in patients with type 2 diabetes in relation to estimated glomerular filtration rate (eGFR). Curr Med Res Opin. 2014 Sep;30(9):1759-68. doi: 10.1185/03007995.2014.919907. Epub 2014 May 22."}, {"pmid": "24517339", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Genital mycotic infections with canagliflozin, a sodium glucose co-transporter 2 inhibitor, in patients with type 2 diabetes mellitus: a pooled analysis of clinical studies. Curr Med Res Opin. 2014 Jun;30(6):1109-19. doi: 10.1185/03007995.2014.890925. Epub 2014 Feb 21."}, {"pmid": "23680739", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>ica<PERSON> and safety of canagliflozin treatment in older subjects with type 2 diabetes mellitus: a randomized trial. Hosp Pract (1995). 2013 Apr;41(2):72-84. doi: 10.3810/hp.2013.04.1020."}]}}}