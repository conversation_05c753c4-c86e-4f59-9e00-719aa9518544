#!/usr/bin/env python3
"""
Extract adverse events using the correct ClinicalTrials.gov API
"""

import requests
import json
import pandas as pd
from urllib.parse import quote

def extract_from_api_v2():
    """
    Try the new ClinicalTrials.gov API v2
    """
    
    nct_id = "NCT04505774"
    
    # Try the new API format
    api_urls = [
        f"https://clinicaltrials.gov/api/v2/studies/{nct_id}",
        f"https://clinicaltrials.gov/api/v2/studies?query.term={nct_id}&format=json",
        f"https://clinicaltrials.gov/api/v2/studies?filter.ids={nct_id}&format=json",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
    }
    
    for i, url in enumerate(api_urls):
        print(f"\nTrying API URL {i+1}: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("Successfully retrieved JSON data")
                    
                    # Save the response
                    filename = f"results_posted.json"
                    with open(filename, 'w') as f:
                        json.dump(data, f, indent=2)
                    print(f"Saved response to {filename}")
                    
                    # Look for adverse events data
                    if isinstance(data, dict):
                        # Navigate through the data structure to find adverse events
                        studies = data.get('studies', [])
                        if not studies and 'protocolSection' in data:
                            studies = [data]
                        
                        for study in studies:
                            print(f"Processing study: {study.get('protocolSection', {}).get('identificationModule', {}).get('nctId', 'Unknown')}")
                            
                            # Look for results section
                            results_section = study.get('resultsSection', {})
                            if results_section:
                                print("Found resultsSection")
                                
                                # Look for adverse events
                                adverse_events = results_section.get('adverseEventsModule', {})
                                if adverse_events:
                                    print("Found adverseEventsModule!")
                                    
                                    # Save adverse events data
                                    with open('adverse_events.json', 'w') as f:
                                        json.dump(adverse_events, f, indent=2)
                                    print("Saved adverse events data to adverse_events.json")
                                    
                                    return adverse_events
                                else:
                                    print("No adverseEventsModule found")
                                    print("Available modules in resultsSection:", list(results_section.keys()))
                            else:
                                print("No resultsSection found")
                                if 'protocolSection' in study:
                                    print("Available sections:", list(study.keys()))
                    
                except json.JSONDecodeError as e:
                    print(f"Failed to parse JSON: {e}")
                    print("Response content:", response.text[:500])
            else:
                print(f"HTTP error: {response.status_code}")
                print("Response:", response.text[:500])
                
        except requests.RequestException as e:
            print(f"Request failed: {e}")

def extract_from_classic_api():
    """
    Try the classic ClinicalTrials.gov API
    """
    
    nct_id = "NCT04505774"
    
    # Try different classic API endpoints
    api_urls = [
        f"https://classic.clinicaltrials.gov/api/query/full_studies?expr={nct_id}&fmt=json",
        f"https://classic.clinicaltrials.gov/api/query/study_fields?expr={nct_id}&fields=NCTId,BriefTitle,OverallStatus,Phase,StudyType,Condition,InterventionName&fmt=json",
        f"https://clinicaltrials.gov/ct2/results?term={nct_id}&Search=Search",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/html',
    }
    
    for i, url in enumerate(api_urls):
        print(f"\nTrying classic API URL {i+1}: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                # Save the response
                filename = f"classic_api_response_{i+1}.txt"
                with open(filename, 'w') as f:
                    f.write(response.text)
                print(f"Saved response to {filename}")
                
                # Try to parse as JSON
                try:
                    data = response.json()
                    print("Successfully parsed as JSON")
                    
                    filename_json = f"classic_api_response_{i+1}.json"
                    with open(filename_json, 'w') as f:
                        json.dump(data, f, indent=2)
                    print(f"Saved JSON to {filename_json}")
                    
                    return data
                    
                except json.JSONDecodeError:
                    print("Response is not JSON, might be HTML")
                    if 'html' in response.text.lower():
                        print("Response appears to be HTML content")
            else:
                print(f"HTTP error: {response.status_code}")
                
        except requests.RequestException as e:
            print(f"Request failed: {e}")

def try_direct_study_access():
    """
    Try to access the study page directly and extract any JSON data
    """
    
    nct_id = "NCT04505774"
    url = f"https://clinicaltrials.gov/study/{nct_id}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    }
    
    print(f"\nTrying direct study access: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            # Look for any JSON data embedded in the HTML
            import re
            
            # Look for JSON data in script tags
            json_patterns = [
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__STUDY_DATA__\s*=\s*({.*?});',
                r'var\s+studyData\s*=\s*({.*?});',
                r'"study":\s*({.*?})',
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, response.text, re.DOTALL)
                if matches:
                    print(f"Found potential JSON data with pattern: {pattern}")
                    for i, match in enumerate(matches):
                        try:
                            data = json.loads(match)
                            filename = f"embedded_json_{i+1}.json"
                            with open(filename, 'w') as f:
                                json.dump(data, f, indent=2)
                            print(f"Saved embedded JSON to {filename}")
                            
                            # Look for adverse events in this data
                            if isinstance(data, dict):
                                # Recursively search for adverse events
                                def find_adverse_events(obj, path=""):
                                    if isinstance(obj, dict):
                                        for key, value in obj.items():
                                            new_path = f"{path}.{key}" if path else key
                                            if 'adverse' in key.lower():
                                                print(f"Found adverse events key at: {new_path}")
                                                return value
                                            result = find_adverse_events(value, new_path)
                                            if result:
                                                return result
                                    elif isinstance(obj, list):
                                        for i, item in enumerate(obj):
                                            result = find_adverse_events(item, f"{path}[{i}]")
                                            if result:
                                                return result
                                    return None
                                
                                adverse_data = find_adverse_events(data)
                                if adverse_data:
                                    print("Found adverse events data in embedded JSON!")
                                    with open('adverse_events_embedded.json', 'w') as f:
                                        json.dump(adverse_data, f, indent=2)
                                    return adverse_data
                        except json.JSONDecodeError:
                            print(f"Could not parse match as JSON: {match[:100]}...")
            
            print("No embedded JSON data found")
            
        else:
            print(f"HTTP error: {response.status_code}")
            
    except requests.RequestException as e:
        print(f"Request failed: {e}")

def main():
    print("Trying to extract adverse events data using various API approaches...")
    
    # Try API v2
    print("\n=== Approach 1: ClinicalTrials.gov API v2 ===")
    result = extract_from_api_v2()
    if result:
        print("Successfully found adverse events data!")
        return result
    
    # Try classic API
    print("\n=== Approach 2: Classic ClinicalTrials.gov API ===")
    result = extract_from_classic_api()
    if result:
        print("Successfully found data from classic API!")
        return result
    
    # Try direct access
    print("\n=== Approach 3: Direct study page access ===")
    result = try_direct_study_access()
    if result:
        print("Successfully found adverse events data from direct access!")
        return result
    
    print("\nCould not extract adverse events data through any API approach")
    return None

if __name__ == "__main__":
    main()
