install.packages("rjson")
library(rjson)
r <- fromJSON(file='adverse_events_api_v2.json')
print(r)
r1 <- as.data.frame(r)
View(r1)
install.packages("jsonlite")
library(jsonlite)
r <- fromJSON(file='adverse_events_api_v2.json')
r <- fromJSON('adverse_events_api_v2.json')
r1 <- as.data.frame(r)
r <- fromJSON('adverse_events_api_v2.json')
r1 <- as.data.frame(r)
r
r$otherEvents
r1<-as.data.frame(r$otherEvents)
View(r1)
View(r1[[4]][[1]])
r1$stats
