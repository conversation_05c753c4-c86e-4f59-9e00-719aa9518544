#!/usr/bin/env python3
"""
Script to extract adverse events table from ClinicalTrials.gov using crawl4ai
"""

import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
import pandas as pd
import json
import re
from bs4 import BeautifulSoup

async def extract_adverse_events_crawl4ai(url):
    """
    Extract adverse events table using crawl4ai
    """
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        print(f"Crawling URL: {url}")
        
        # First, let's crawl the page to get the content
        result = await crawler.arun(
            url=url,
            word_count_threshold=10,
            extraction_strategy=None,  # First get raw content
            bypass_cache=True,
            js_code="""
            // Wait for page to load completely
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Try to scroll down to load more content
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Look for and click on Results tab or section
            const resultElements = document.querySelectorAll('*');
            for (let element of resultElements) {
                const text = element.textContent;
                if (text && (text.toLowerCase().includes('results') || text.toLowerCase().includes('outcomes'))) {
                    if (element.tagName === 'A' || element.tagName === 'BUTTON' || element.getAttribute('role') === 'tab') {
                        console.log('Clicking on results element:', text.substring(0, 50));
                        element.click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        break;
                    }
                }
            }

            // Try to find and expand any collapsible sections
            const expandableElements = document.querySelectorAll('[aria-expanded="false"], .collapsed, [data-toggle="collapse"]');
            for (let element of expandableElements) {
                element.click();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Look for adverse events specific content
            const adverseElements = document.querySelectorAll('*');
            for (let element of adverseElements) {
                const text = element.textContent;
                if (text && (text.toLowerCase().includes('adverse') || text.toLowerCase().includes('safety'))) {
                    if (element.tagName === 'A' || element.tagName === 'BUTTON') {
                        console.log('Clicking on adverse events element:', text.substring(0, 50));
                        element.click();
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        break;
                    }
                }
            }

            // Final wait to ensure all content is loaded
            await new Promise(resolve => setTimeout(resolve, 3000));
            """,
            wait_for="networkidle"
        )
        
        if not result.success:
            print(f"Failed to crawl the page: {result.error_message}")
            return None
        
        print(f"Successfully crawled page. Content length: {len(result.html)}")
        
        # Save the HTML for debugging
        with open('crawl4ai_page.html', 'w', encoding='utf-8') as f:
            f.write(result.html)
        print("Saved HTML content to crawl4ai_page.html")
        
        # Parse the HTML to look for tables
        soup = BeautifulSoup(result.html, 'html.parser')
        
        # Look for adverse events content
        adverse_events_data = []
        
        # Find all tables
        tables = soup.find_all('table')
        print(f"Found {len(tables)} tables on the page")
        
        for i, table in enumerate(tables):
            table_text = table.get_text().lower()
            table_html = str(table)
            
            # Check if this table might contain adverse events data
            if any(keyword in table_text for keyword in ['adverse', 'safety', 'side effect', 'toxicity', 'event']):
                print(f"Table {i+1} might contain adverse events data")
                
                # Extract table data
                try:
                    rows = table.find_all('tr')
                    if len(rows) < 2:  # Need at least header and one data row
                        continue
                    
                    # Extract headers
                    header_row = rows[0]
                    headers = []
                    for cell in header_row.find_all(['th', 'td']):
                        headers.append(cell.get_text().strip())
                    
                    if not headers:
                        continue
                    
                    print(f"Table {i+1} headers: {headers}")
                    
                    # Extract data rows
                    data_rows = []
                    for row in rows[1:]:
                        cells = row.find_all(['td', 'th'])
                        row_data = []
                        for cell in cells:
                            row_data.append(cell.get_text().strip())
                        
                        if any(cell for cell in row_data):  # Only add non-empty rows
                            data_rows.append(row_data)
                    
                    if data_rows:
                        print(f"Table {i+1} has {len(data_rows)} data rows")
                        
                        # Create DataFrame
                        max_cols = max(len(headers), max(len(row) for row in data_rows) if data_rows else 0)
                        headers_padded = headers + [f'Column_{j}' for j in range(len(headers), max_cols)]
                        rows_padded = [row + [''] * (max_cols - len(row)) for row in data_rows]
                        
                        df = pd.DataFrame(rows_padded, columns=headers_padded)
                        
                        adverse_events_data.append({
                            'table_id': f'table_{i+1}',
                            'dataframe': df,
                            'html': table_html
                        })
                        
                except Exception as e:
                    print(f"Error processing table {i+1}: {e}")
                    continue
        
        # If no tables found, let's try to find any structured content
        if not adverse_events_data:
            print("No tables with adverse events keywords found. Looking for other structured content...")

            # Look for any divs or sections that might contain adverse events data
            content_sections = soup.find_all(['div', 'section', 'article'],
                                           string=lambda text: text and any(keyword in text.lower() for keyword in ['adverse', 'safety', 'side effect']))

            for i, section in enumerate(content_sections):
                print(f"Found content section {i+1} with potential adverse events data")
                print(f"Content preview: {section.get_text()[:200]}...")

            # Look for any lists that might contain adverse events
            lists = soup.find_all(['ul', 'ol'])
            for i, list_elem in enumerate(lists):
                list_text = list_elem.get_text().lower()
                if any(keyword in list_text for keyword in ['adverse', 'safety', 'side effect', 'event']):
                    print(f"Found list {i+1} with potential adverse events data")

                    # Extract list items
                    items = list_elem.find_all('li')
                    if items:
                        list_data = []
                        for item in items:
                            list_data.append(item.get_text().strip())

                        if list_data:
                            df = pd.DataFrame({'adverse_event': list_data})
                            adverse_events_data.append({
                                'table_id': f'list_{i+1}',
                                'dataframe': df,
                                'html': str(list_elem)
                            })

            # Also check if there's any text content that mentions adverse events
            page_text = soup.get_text()
            if any(keyword in page_text.lower() for keyword in ['adverse event', 'side effect', 'safety']):
                print("Found text mentioning adverse events in the page")
                # Extract sentences containing adverse events keywords
                sentences = re.split(r'[.!?]+', page_text)
                adverse_sentences = []
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in ['adverse event', 'side effect', 'safety outcome']):
                        adverse_sentences.append(sentence.strip())

                if adverse_sentences:
                    df = pd.DataFrame({'adverse_event_mention': adverse_sentences})
                    adverse_events_data.append({
                        'table_id': 'text_mentions',
                        'dataframe': df,
                        'html': 'Text mentions of adverse events'
                    })
        
        return adverse_events_data

async def main():
    url = "https://clinicaltrials.gov/study/NCT02673138?intr=Canagliflozin&aggFilters=results:with,status:com&viewType=Card&rank=1&tab=results"
    
    print("Extracting adverse events table using crawl4ai...")
    
    adverse_events_data = await extract_adverse_events_crawl4ai(url)
    
    if adverse_events_data:
        print(f"\nFound {len(adverse_events_data)} potential adverse events tables:")
        
        for data in adverse_events_data:
            table_id = data['table_id']
            df = data['dataframe']
            
            print(f"\n=== {table_id} ===")
            print(f"Shape: {df.shape}")
            print("\nColumns:", df.columns.tolist())
            print("\nFirst few rows:")
            print(df.head())
            
            # Save to CSV
            filename = f"adverse_events_{table_id}.csv"
            df.to_csv(filename, index=False)
            print(f"Saved to: {filename}")
            
            # Save HTML for reference if available
            if data['html'] != 'LLM extracted data':
                html_filename = f"{table_id}.html"
                with open(html_filename, 'w', encoding='utf-8') as f:
                    f.write(data['html'])
                print(f"Saved HTML to: {html_filename}")
    else:
        print("No adverse events tables found")

if __name__ == "__main__":
    asyncio.run(main())
