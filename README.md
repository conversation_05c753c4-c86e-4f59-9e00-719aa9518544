# Clinical Trials AI Analysis System

A comprehensive system for extracting and analyzing clinical trial data from ClinicalTrials.gov with AI-powered search capabilities.

## 🎯 Overview

This system provides a complete workflow for clinical trials research:
- **Step 1**: Find, download, and process clinical trial data
- **Step 2**: AI-powered analysis using local LLMs
- **Web Interface**: Real-time progress tracking and results visualization
- **Drug-Specific Organization**: All outputs organized by drug name for easy management

## 🚀 Quick Start

### Prerequisites

```bash
# Install Python dependencies
pip install crawl4ai requests flask

# Install and start Ollama
# Download from: https://ollama.ai
ollama serve

# Pull recommended AI models
ollama pull qwen2.5:latest
ollama pull llama3.1:latest
```

### Launch the System

```bash
# Start the web interface
python server.py

# Open browser to: http://localhost:8080
```

## 📁 System Architecture

### Core Files

```
├── server.py                           # Web server and API
├── clinical_trials_web_ui.html         # Web interface
├── step1_find_studies.py               # Find and filter studies
├── step1_batch_download.py             # Download study data
├── step1_extract_study_details.py      # Extract study details tab
├── step1_extract_researcher_view.py    # Extract researcher view tab
├── step1_extract_results_posted.py     # Extract results tab
├── step1_json2csv.py                   # Convert JSON to CSV
├── step2_ai_analysis.py                # AI analysis engine
└── output/                             # Organized output directory
```

### Workflow Pipeline

```
🔍 STEP 1: DATA EXTRACTION
step1_find_studies.py → Find studies matching criteria
         ↓
step1_batch_download.py → Download all study data
         ↓
step1_extract_*.py → Extract specific data tabs
         ↓
step1_json2csv.py → Convert to CSV format

🤖 STEP 2: AI ANALYSIS  
step2_ai_analysis.py → Analyze studies with LLM
```

## 🔧 Usage

### Web Interface (Recommended)

1. **Start Server**: `python server.py`
2. **Open Browser**: http://localhost:8080
3. **Select Drug**: Choose from extracted datasets or extract new data
4. **Configure Search**: Enter search criteria (e.g., "diabetes, arrhythmia")
5. **Choose AI Model**: Select from available Ollama models
6. **Run Analysis**: Click "Analyze Studies" for real-time processing

### Command Line Usage

#### Step 1: Extract Data

```bash
# Find studies for a drug
python step1_find_studies.py canagliflozin

# Download all matching studies
python step1_batch_download.py --output-dir output/canagliflozin

# Extract single study (manual)
python step1_extract_study_details.py    # (configure NCT ID in script)
python step1_extract_researcher_view.py
python step1_extract_results_posted.py
python step1_json2csv.py
```

#### Step 2: AI Analysis

```bash
# Analyze studies with AI
python step2_ai_analysis.py "diabetes, arrhythmia" qwen2.5:latest --studies-dir output/canagliflozin
```

## 📊 Output Organization

All outputs are organized by drug name for easy management:

```
output/
├── canagliflozin_matching_studies.json      # Found studies
├── canagliflozin_batch_extraction_summary.json  # Extraction summary
├── canagliflozin_ai_analysis_results.json   # AI analysis results
└── canagliflozin/                           # Study data
    ├── NCT01106625/
    │   ├── study_details.json               # Study Details tab
    │   ├── researcher_view.json             # Researcher View tab
    │   ├── results_posted.json              # Results Posted tab
    │   └── adverse_events.csv               # Processed adverse events
    ├── NCT01106677/
    │   └── [same structure...]
    └── [more studies...]
```

## 🎯 Search Capabilities

The AI analyzes studies in four key areas:

### 1. Outcome Measures
- Primary and secondary endpoints
- Efficacy measurements
- Safety assessments

### 2. Trial Description  
- Study titles and summaries
- Medical conditions
- Interventions and treatments
- Study design and phases

### 3. Recruitment Information
- Study status and enrollment
- Eligibility criteria
- Participant demographics

### 4. Adverse Events
- Serious adverse events
- Other adverse events
- Safety data

## 🤖 AI Models

### Recommended Models

- **qwen2.5:latest** - Fast and accurate (recommended)
- **llama3.1:latest** - Good balance of speed and quality
- **deepseek-r1** - Detailed reasoning (slower)

### Model Performance

| Model | Speed | Accuracy | Best For |
|-------|-------|----------|----------|
| qwen2.5:latest | ⚡⚡⚡ | ⭐⭐⭐ | Production use |
| llama3.1:latest | ⚡⚡ | ⭐⭐⭐ | Balanced analysis |
| deepseek-r1 | ⚡ | ⭐⭐⭐⭐ | Detailed research |

## 🔍 Search Examples

### Basic Searches
```
diabetes          # Find diabetes-related studies
arrhythmia        # Find heart rhythm studies  
fibrillation      # Find atrial/ventricular fibrillation
```

### Multi-term Searches
```
diabetes, arrhythmia              # Studies with ANY of these terms
myocardial infarction, stroke     # Cardiovascular events
nausea, vomiting, diarrhea       # Gastrointestinal side effects
```

### Advanced Searches
```
type 2 diabetes, cardiovascular   # Specific diabetes type + CV
atrial fibrillation, stroke       # AF and stroke relationship
serious adverse events           # Focus on safety signals
```

## ⚙️ Configuration

### Search Criteria
- **Intervention**: Specified drug name
- **Status**: Completed studies only
- **Results**: Studies with posted results only
- **Data Quality**: Studies with sufficient data for analysis

### AI Analysis Settings
- **Temperature**: 0.1 (low for consistent analysis)
- **Timeout**: 120s (300s for reasoning models)
- **Retry Logic**: 2 attempts per study
- **Response Limit**: 500 tokens max

## 🛠️ Troubleshooting

### Common Issues

**Server won't start:**
```bash
# Check if port 8080 is in use
lsof -ti:8080 | xargs kill -9
python server.py
```

**Ollama connection error:**
```bash
# Ensure Ollama is running
ollama serve

# Check available models
ollama list
```

**No studies found:**
- Verify drug name spelling
- Check if studies exist with posted results
- Try broader search terms

**AI analysis timeout:**
- Use faster models (qwen2.5:latest)
- Reduce search criteria complexity
- Check Ollama server status

## 📈 Performance Tips

### For Large Datasets
- Use qwen2.5:latest for speed
- Process in smaller batches
- Monitor system resources

### For Detailed Analysis
- Use deepseek-r1 for complex queries
- Allow longer processing time
- Review results carefully

### For Production Use
- Use web interface for monitoring
- Save results with drug name headers
- Regular backup of output folder

## 🔒 Data Privacy

- **Local Processing**: All data processed locally
- **No External APIs**: Except ClinicalTrials.gov (public data)
- **Offline Analysis**: AI models run locally via Ollama
- **Data Retention**: Full control over stored data

## 📝 License

This system is designed for research and educational purposes. Clinical trial data is sourced from ClinicalTrials.gov (public domain).

## 🤝 Support

For issues or questions:
1. Check troubleshooting section
2. Verify all dependencies are installed
3. Ensure Ollama is running with required models
4. Review terminal output for specific error messages

## 📚 Technical Details

### Data Extraction Process

1. **Search Phase**: Queries ClinicalTrials.gov API for studies matching drug name
2. **Filtering**: Selects only completed studies with posted results
3. **Download**: Uses crawl4ai to extract data from three key tabs:
   - Study Details (basic information)
   - Researcher View (detailed study design)
   - Results Posted (outcomes and adverse events)
4. **Processing**: Converts adverse events to structured CSV format

### AI Analysis Process

1. **Data Loading**: Reads JSON files from extracted studies
2. **Content Extraction**: Focuses on four key areas for analysis
3. **LLM Query**: Sends structured prompts to local Ollama models
4. **Result Processing**: Parses JSON responses and validates matches
5. **Output Generation**: Creates comprehensive analysis reports

### File Dependencies

```
server.py
├── clinical_trials_web_ui.html (web interface)
├── step1_find_studies.py (search studies)
├── step1_batch_download.py (orchestrate extraction)
│   ├── step1_extract_study_details.py
│   ├── step1_extract_researcher_view.py
│   ├── step1_extract_results_posted.py
│   └── step1_json2csv.py
└── step2_ai_analysis.py (AI analysis)
```

## 🔄 System Requirements

### Minimum Requirements
- **Python**: 3.8+
- **RAM**: 4GB (8GB recommended for large datasets)
- **Storage**: 1GB per 100 studies
- **Network**: Internet connection for data extraction

### Recommended Setup
- **Python**: 3.10+
- **RAM**: 16GB (for multiple concurrent analyses)
- **Storage**: SSD for faster processing
- **CPU**: Multi-core for parallel processing

## 🚀 Advanced Usage

### Batch Processing Multiple Drugs

```bash
# Process multiple drugs sequentially
for drug in "canagliflozin" "empagliflozin" "dapagliflozin"; do
    python step1_find_studies.py $drug
    python step1_batch_download.py --output-dir output/$drug
    python step2_ai_analysis.py "diabetes" qwen2.5:latest --studies-dir output/$drug
done
```

### Custom Analysis Scripts

```python
# Example: Custom analysis script
import json
from pathlib import Path

def analyze_drug_studies(drug_name, search_terms):
    results_file = f"output/{drug_name}_ai_analysis_results.json"
    with open(results_file, 'r') as f:
        results = json.load(f)

    # Custom analysis logic here
    matching_studies = [r for r in results if r['analysis']['matches']]
    return matching_studies
```

---

**Ready to analyze clinical trials with AI? Start with `python server.py` and open http://localhost:8080** 🚀
