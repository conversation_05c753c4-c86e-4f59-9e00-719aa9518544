# Clinical Trials Data Extraction System

## Overview
This system extracts data from ClinicalTrials.gov studies with specific criteria:
- **Intervention**: Canagliflozin
- **Status**: Completed
- **Results**: With results

## Files

### Core Extraction Scripts
- `extract_study_details.py` → Creates `study_details.json`
- `extract_researcher_view.py` → Creates `researcher_view.json`
- `extract_results_posted.py` → Creates `results_posted.json` + `adverse_events.json`
- `extract2csv.py` → Converts `adverse_events.json` → `adverse_events.csv`

### Batch Processing
- `test_search_only.py` → Searches and filters studies, creates `matching_studies.json`
- `batch_extract_all_canagliflozin.py` → Batch extracts all matching studies
- `extract_all_tabs.py` → Extracts all 3 tabs for a single study

### Data Files
- `matching_studies.json` → List of 44 studies matching criteria

## Usage

### Single Study Extraction
1. Update NCT ID in the extraction scripts
2. Run: `python extract_all_tabs.py`

### Batch Extraction (All 44 Studies)
1. Run: `python test_search_only.py` (creates matching_studies.json)
2. Run: `python batch_extract_all_canagliflozin.py`

## Output Structure
```
canagliflozin_studies/
├── NCT01106625/
│   ├── study_details.json      (Study Details tab)
│   ├── researcher_view.json    (Researcher View tab)
│   ├── results_posted.json     (Results Posted tab)
│   ├── adverse_events.json     (Raw adverse events)
│   └── adverse_events.csv      (Serious + Non-serious adverse events)
├── NCT02220920/
│   └── [same 5 files]
└── [42 more study folders...]
```

## Data Sources
- **API**: ClinicalTrials.gov API v2
- **Method**: Direct API calls (no web scraping needed)
- **Reliability**: High (uses official API endpoints)
