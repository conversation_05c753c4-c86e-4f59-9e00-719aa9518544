# Clinical Trials Data Extraction & AI Analysis System

## Overview
This system extracts data from ClinicalTrials.gov studies and uses AI to analyze them:
- **Step 1**: Extract studies for any drug (Completed + With results)
- **Step 2**: AI analysis using Ollama LLMs to match search criteria

## Files

### Core Extraction Scripts
- `extract_study_details.py` → Creates `study_details.json`
- `extract_researcher_view.py` → Creates `researcher_view.json`
- `extract_results_posted.py` → Creates `results_posted.json` + `adverse_events.json`
- `extract2csv.py` → Converts `adverse_events.json` → `adverse_events.csv`

### Batch Processing & AI Analysis
- `test_search_only.py` → Searches and filters studies for any drug, creates `matching_studies.json`
- `batch_extract_all_studies.py` → Batch extracts all matching studies
- `ai_analyze_studies.py` → AI analysis using Ollama LLMs
- `extract_all_tabs.py` → Extracts all 3 tabs for a single study

### Web Interface
- `clinical_trials_web_ui.html` → Web interface with drug search and AI analysis
- `serve_web_ui.py` → Flask backend that integrates with Ollama

### Data Files
- `matching_studies.json` → List of studies matching criteria for current drug

## Usage

### Web Interface (Recommended)
1. Start Ollama: `ollama serve`
2. Run web server: `python serve_web_ui.py`
3. Open browser: http://localhost:8080
4. Enter drug name, search criteria, select AI model
5. Click "Start Extraction & Analysis"

### Command Line Usage

#### Single Study Extraction
1. Update NCT ID in the extraction scripts
2. Run: `python extract_all_tabs.py`

#### Batch Extraction + AI Analysis
1. Search: `python test_search_only.py "DrugName"`
2. Extract: `python batch_extract_all_studies.py`
3. Analyze: `python ai_analyze_studies.py "search criteria" "llm_model"`

## Output Structure
```
studies/
├── NCT01106625/
│   ├── study_details.json      (Study Details tab)
│   ├── researcher_view.json    (Researcher View tab)
│   ├── results_posted.json     (Results Posted tab)
│   ├── adverse_events.json     (Raw adverse events)
│   └── adverse_events.csv      (Serious + Non-serious adverse events)
├── NCT02220920/
│   └── [same 5 files]
└── [more study folders...]
ai_analysis_results.json         (AI analysis results with matching studies)
batch_extraction_summary.json   (Extraction summary)
```

## Data Sources
- **API**: ClinicalTrials.gov API v2
- **Method**: Direct API calls (no web scraping needed)
- **Reliability**: High (uses official API endpoints)
