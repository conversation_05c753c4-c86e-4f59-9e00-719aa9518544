# Clinical Trials AI Analysis System

A clean, minimal system for extracting and analyzing clinical trial data from ClinicalTrials.gov with AI-powered search capabilities.

## 🎯 Features

- **Data Extraction**: Batch extract clinical trial data for any drug
- **AI Analysis**: Search studies using natural language criteria
- **Web Interface**: Real-time analysis with progress tracking
- **Multiple LLMs**: Support for various Ollama models
- **Focused Search**: Searches only in 4 key areas (Outcome Measures, Trial Description, Recruitment Information, Adverse Events)

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install crawl4ai requests flask
```

### 2. Setup Ollama
```bash
# Start Ollama
ollama serve

# Pull recommended models
ollama pull qwen2.5:latest
ollama pull llama3.1:latest
```

### 3. Extract Data
```bash
# Extract studies for a drug (e.g., canagliflozin)
python batch_extract_all_studies.py
```

### 4. Start Web Interface
```bash
python server.py
```

Open http://localhost:8080 in your browser.

## 📁 File Structure

```
├── server.py                    # Web server and API
├── ai_analyze_studies.py        # AI analysis engine  
├── batch_extract_all_studies.py # Data extraction script
├── extract2csv.py               # CSV conversion utility
├── output/                      # Extracted data storage
│   └── [drug_name]/            # Drug-specific folders
│       └── [NCT_ID]/           # Individual study data
└── README.md                   # This file
```

## 🔍 How It Works

1. **Extract**: `batch_extract_all_studies.py` downloads study data from ClinicalTrials.gov
2. **Analyze**: `ai_analyze_studies.py` uses LLMs to search studies based on your criteria
3. **Interface**: `server.py` provides a web UI for easy interaction

## 🎯 Search Capabilities

The AI searches for your specified terms in:
- **Outcome Measures**: Primary and secondary endpoints
- **Trial Description**: Titles, summaries, conditions, interventions
- **Recruitment Information**: Status, eligibility criteria
- **Adverse Events**: Serious and other adverse events

## 📊 Supported Models

- **qwen2.5:latest** (Recommended - Fast & Accurate)
- **llama3.1:latest** (Good balance)
- **deepseek-r1** models (Slower but detailed reasoning)

## 🛠️ Usage Examples

**Search for diabetes studies:**
- Criteria: `diabetes`
- Finds: diabetes, diabetic, T1DM, T2DM, etc.

**Search for cardiovascular events:**
- Criteria: `myocardial infarction, stroke`
- Finds studies mentioning these specific terms

**Multi-term search:**
- Criteria: `diabetes, arrhythmia, fibrillation`
- Finds studies with ANY of these terms

## 📋 Output Structure

```
output/
└── canagliflozin/              # Drug-specific folder
    ├── NCT01106625/
    │   ├── researcher_view.json    # Study details & design
    │   ├── results_posted.json     # Results & adverse events
    │   └── adverse_events.csv      # Processed adverse events
    ├── NCT01106677/
    │   └── [same files...]
    └── [more studies...]
```

## 🔧 Dependencies

- **crawl4ai**: Web scraping and data extraction
- **requests**: HTTP requests for API calls
- **flask**: Web server framework
- **ollama**: Local LLM inference

## 📝 Notes

- System filters for studies with Status=Completed and Results=With results
- AI analysis is strictly limited to your specified search criteria
- Web interface provides real-time progress tracking
- All data is stored locally for privacy and reusability
