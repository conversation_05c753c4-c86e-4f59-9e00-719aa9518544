{"nctId": "NCT02025907", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT02025907", "orgStudyIdInfo": {"id": "CR103477"}, "secondaryIdInfos": [{"id": "2013-004819-40", "type": "EUDRACT_NUMBER"}, {"id": "28431754DIA4004", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "A Study to Evaluate the Efficacy and Safety of the Addition of Canagliflozin in Participants With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin and Sitagliptin", "officialTitle": "A Randomized, Double-blind, Placebo Controlled, 2-arm, Parallel-group, 26-week, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin in the Treatment of Subjects With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin and Sitagliptin Therapy"}, "statusModule": {"statusVerifiedDate": "2016-10", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2014-02"}, "primaryCompletionDateStruct": {"date": "2015-09", "type": "ACTUAL"}, "completionDateStruct": {"date": "2015-09", "type": "ACTUAL"}, "studyFirstSubmitDate": "2013-12-30", "studyFirstSubmitQcDate": "2013-12-30", "studyFirstPostDateStruct": {"date": "2014-01-01", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2016-07-22", "resultsFirstSubmitQcDate": "2016-07-22", "resultsFirstPostDateStruct": {"date": "2016-09-02", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2016-10-28", "lastUpdatePostDateStruct": {"date": "2016-10-31", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": false}, "descriptionModule": {"briefSummary": "The purpose of this study is to assess the effect of canagliflozin (JNJ-28431754) compared to placebo in the treatment of participants with Type 2 Diabetes Mellitus (T2DM), who have inadequate glycemic control on maximally or near-maximally effective doses of metformin and sitagliptin.", "detailedDescription": "This is a randomized (the study medication is assigned by chance), double-blind (neither physician nor participant knows the identity of the assigned treatment), placebo-controlled (an inactive substance that is compared with a study drug, to test whether the study drug has a real effect), multicenter study of efficacy, safety, and tolerability of canagliflozin in participants with T2DM, who have inadequate glycemic (blood sugar) control on maximally or near-maximally effective doses of metformin \\>=1500 mg/day and sitagliptin 100 mg/day. Approximately 200 participants will be randomly assigned to 1 of 2 treatment groups in 1:1 ratio for 26 weeks. During the study the participants will be also provided with diet and exercise counseling (standardized non-pharmacological therapy)."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Diabetes Mellitus, Type 2", "Canagliflozin", "Hemoglobin A1c", "Metformin", "<PERSON><PERSON><PERSON><PERSON>", "T2DM", "JNJ-28431754"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE4"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 218, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canaglif<PERSON>zin (JNJ-28431754)", "type": "EXPERIMENTAL", "description": "Each participant will receive canagliflozin (JNJ-28431754) 100 mg once daily during the first 6 weeks, then the dose may be increased to 300 mg once daily.", "interventionNames": ["Drug: Canagliflozin, 100 mg", "Drug: Canagliflozin, 300 mg"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each participant will receive placebo (inactive medication) once daily for 28 weeks.", "interventionNames": ["Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Canagliflozin, 100 mg", "description": "One 100 mg capsule taken orally (by mouth) once daily.", "armGroupLabels": ["Canaglif<PERSON>zin (JNJ-28431754)"]}, {"type": "DRUG", "name": "Canagliflozin, 300 mg", "description": "One 300 mg capsule taken orally (by mouth) once daily.", "armGroupLabels": ["Canaglif<PERSON>zin (JNJ-28431754)"]}, {"type": "DRUG", "name": "Placebo", "description": "One placebo capsule taken orally (by mouth) once daily.", "armGroupLabels": ["Placebo"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change From Baseline in Glycosylated Hemoglobin (HbA1c) at Week 26", "timeFrame": "Baseline and Week 26"}], "secondaryOutcomes": [{"measure": "Change From Baseline in Fasting Plasma Glucose (FPG) at Week 26", "timeFrame": "Baseline and Week 26"}, {"measure": "Percent Change From Baseline in Body Weight at Week 26", "timeFrame": "Baseline and Week 26"}, {"measure": "Percentage of Participants With HbA1c Less Than (<) 7.0 Percent at Week 26", "timeFrame": "Week 26"}, {"measure": "Change From Baseline in Systolic Blood Pressure (SBP) at Week 26", "timeFrame": "Baseline and Week 26"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Must have a diagnosis of type 2 diabetes mellitus\n* Must have a screening HbA1c of \\>=7.5% to \\<=10.5%\n* Must be on metformin \\>=1500 mg/day and sitagliptin 100 mg/day (or equivalent fixed dose combination) at a stable dose for at least 12 weeks before screening\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis or T1DM, hereditary glucose-galactose malabsorption or primary renal glycosuria\n* A myocardial infarction, unstable angina, revascularization procedure or cerebrovascular accident within 12 weeks before screening\n* eGFR \\<60 ml/min/1.73m2, or serum creatinine \\>=1.4 mg/dL for men and \\>=1.3 mg/dL for women\n* Known significant liver disease (eg, acute hepatitis, chronic active hepatitis, cirrhosis)\n* Major surgery (ie, requiring general anesthesia) within 12 weeks before screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "75 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "<PERSON>", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 32.36681, "lon": -86.29997}}, {"city": "Phoenix", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.44838, "lon": -112.07404}}, {"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Norwalk", "state": "California", "country": "United States", "geoPoint": {"lat": 33.90224, "lon": -118.08173}}, {"city": "Rancho Cucamonga", "state": "California", "country": "United States", "geoPoint": {"lat": 34.1064, "lon": -117.59311}}, {"city": "San Ramon", "state": "California", "country": "United States", "geoPoint": {"lat": 37.77993, "lon": -121.97802}}, {"city": "Aurora", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.72943, "lon": -104.83192}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Littleton", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.61332, "lon": -105.01665}}, {"city": "Cooper City", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.05731, "lon": -80.27172}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "North Miami Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.93315, "lon": -80.16255}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "<PERSON>", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.45821, "lon": -83.73157}}, {"city": "Shawnee Mission", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04167, "lon": -94.72024}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.89937, "lon": -90.10035}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Metarie", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Rockville", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.084, "lon": -77.15276}}, {"city": "<PERSON>", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 32.29876, "lon": -90.18481}}, {"city": "Picayune", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 30.52556, "lon": -89.67788}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Nashua", "state": "New Hampshire", "country": "United States", "geoPoint": {"lat": 42.76537, "lon": -71.46757}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "Albany", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.65258, "lon": -73.75623}}, {"city": "Arlington", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.73569, "lon": -97.10807}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Sugarland", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.61968, "lon": -95.63495}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Coffs Harbour", "country": "Australia", "geoPoint": {"lat": -30.29626, "lon": 153.11351}}, {"city": "Freemantle", "country": "Australia"}, {"city": "Geelong", "country": "Australia", "geoPoint": {"lat": -38.14711, "lon": 144.36069}}, {"city": "Heidelberg", "country": "Australia", "geoPoint": {"lat": -37.75, "lon": 145.06667}}, {"city": "<PERSON><PERSON>", "country": "Australia", "geoPoint": {"lat": -27.44453, "lon": 153.01852}}, {"city": "Melbourne", "country": "Australia", "geoPoint": {"lat": -37.814, "lon": 144.96332}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Australia", "geoPoint": {"lat": -32.94801, "lon": 151.74325}}, {"city": "<PERSON>", "country": "Australia", "geoPoint": {"lat": -31.06485, "lon": 152.72879}}, {"city": "Sydney", "country": "Australia", "geoPoint": {"lat": -33.86785, "lon": 151.20732}}, {"city": "Wollongong", "country": "Australia", "geoPoint": {"lat": -34.424, "lon": 150.89345}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "Hawkesbury", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 45.60009, "lon": -74.61595}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "La Rochelle Cedex 1 Poitou-Cha", "country": "France"}, {"city": "La Tronche", "country": "France", "geoPoint": {"lat": 45.20429, "lon": 5.73645}}, {"city": "Nancy", "country": "France", "geoPoint": {"lat": 48.68439, "lon": 6.18496}}, {"city": "Narbonne Cedex", "country": "France", "geoPoint": {"lat": 43.18396, "lon": 3.00141}}, {"city": "Nice Cedex 3", "country": "France", "geoPoint": {"lat": 43.70313, "lon": 7.26608}}, {"city": "Paris Cedex 15", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 45.70254, "lon": 4.87147}}, {"city": "Freiburg", "country": "Germany", "geoPoint": {"lat": 47.9959, "lon": 7.85222}}, {"city": "<PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 50.55162, "lon": 9.67518}}, {"city": "Hamburg", "country": "Germany", "geoPoint": {"lat": 53.57532, "lon": 10.01534}}, {"city": "Münster", "country": "Germany", "geoPoint": {"lat": 51.96236, "lon": 7.62571}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 50.4336, "lon": 7.47057}}, {"city": "Pirna", "country": "Germany", "geoPoint": {"lat": 50.95843, "lon": 13.93702}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Germany", "geoPoint": {"lat": 49.32083, "lon": 8.43111}}]}, "referencesModule": {"references": [{"pmid": "29313267", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Traina S. Impact of Canagliflozin Treatment on Health-Related Quality of Life among People with Type 2 Diabetes Mellitus: A Pooled Analysis of Patient-Reported Outcomes from Randomized Controlled Trials. Patient. 2018 Jun;11(3):341-352. doi: 10.1007/s40271-017-0290-4."}]}}}