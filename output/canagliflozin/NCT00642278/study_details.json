{"nctId": "NCT00642278", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT00642278", "orgStudyIdInfo": {"id": "CR014587"}, "secondaryIdInfos": [{"id": "28431754DIA2001", "type": "OTHER", "domain": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C."}], "organization": {"fullName": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "class": "INDUSTRY"}, "briefTitle": "An Efficacy, Safety, and Tolerability Study of Canagliflozin (JNJ-28431754) in Patients With Type 2 Diabetes", "officialTitle": "A Randomized, Double-Blind, Placebo-Controlled, Double-Dummy, Parallel Group, Multicenter, Dose-Ranging Study in Subjects With Type 2 Diabetes Mellitus to Evaluate the Efficacy, Safety, and Tolerability of Orally Administered SGLT2 Inhibitor JNJ-28431754 With <PERSON>ag<PERSON><PERSON> as a Reference Arm"}, "statusModule": {"statusVerifiedDate": "2013-07", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2008-04"}, "primaryCompletionDateStruct": {"date": "2009-01", "type": "ACTUAL"}, "completionDateStruct": {"date": "2009-01", "type": "ACTUAL"}, "studyFirstSubmitDate": "2008-03-21", "studyFirstSubmitQcDate": "2008-03-24", "studyFirstPostDateStruct": {"date": "2008-03-25", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2013-04-01", "resultsFirstSubmitQcDate": "2013-04-01", "resultsFirstPostDateStruct": {"date": "2013-05-17", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2009-07-23", "dispFirstSubmitQcDate": "2009-07-23", "dispFirstPostDateStruct": {"date": "2009-08-10", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2013-07-15", "lastUpdatePostDateStruct": {"date": "2013-07-19", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": false}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the effectiveness, safety, and tolerability of JNJ-28431754 compared with placebo in patients with type 2 diabetes.", "detailedDescription": "Type 2 diabetes mellitus is a metabolic disorder that is characterized by decreased secretion of insulin by the pancreas and resistance to the action of insulin in various tissues (muscle, liver, and adipose), which results in impaired glucose uptake. Chronic hyperglycemia leads to progressive impairment of insulin secretion and to insulin resistance of peripheral tissues in diabetes (so-called glucose toxicity), which further worsens control of blood glucose. In addition, chronic hyperglycemia is a major risk factor for complications, including heart disease, retinopathy, nephropathy, and neuropathy. Although numerous treatments have been developed for the treatment of diabetes and individual agents may be highly effective for some patients, it is still difficult to maintain optimal glycemic control in most patients with diabetes. This is a randomized, double-blind, placebo-controlled, parallel group, multicenter, dose-ranging study to determine the efficacy, safety and tolerability of JNJ-28431754 taken orally over 12 weeks, compared with placebo, in the treatment of Type 2 diabetes mellitus. The primary clinical hypothesis is that JNJ-28431754 is superior to placebo as measured by the change in hemoglobin A1c from baseline through Week 12 in the treatment of type 2 diabetes mellitus. Subject safety will be monitored throughout the study using spontaneous adverse event reporting, clinical laboratory tests (hematology, serum chemistry, urinalysis); severe and serious hypoglycemic episodes, assessment of urinary albumin excretion and markers of proximal renal tubular function; pregnancy tests; electrocardiograms (ECGs); vital sign measurements; physical examinations, assessment of calcium and phosphate homeostasis, bone formation and resorption markers, and hormones regulating calcium and phosphorus homeostasis; and vaginal and urine sample collection for fungal and bacterial culture in subjects with symptoms consistent with vulvovaginal candidiasis (VVC) or urinary tract infection (UTI)."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type II", "Diabe<PERSON>, Non Insulin Dependent"], "keywords": ["Type 2 diabetes mellitus", "Metformin", "Hemoglobin A1c"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE2"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 451, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 50 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 100 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 200 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 300 mg daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo capsule once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)", "Drug: Placebo"]}, {"label": "Canagliflozin 300 mg twice daily", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks.", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JNJ-28431754)"]}, {"label": "Sitagliptin 100 mg daily", "type": "ACTIVE_COMPARATOR", "description": "Each patient will receive 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening).", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON>", "Drug: Placebo"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each patient will receive matching placebo twice daily for 12 weeks.", "interventionNames": ["Drug: Placebo"]}], "interventions": [{"type": "DRUG", "name": "Canaglif<PERSON>zin (JNJ-28431754)", "description": "One 50 mg, 100 mg, 200 mg, or 300 mg over-encapsulated tablet orally (by mouth) once daily for 12 weeks or one 300 mg over-encapsulated tablet orally twice daily for 12 weeks.", "armGroupLabels": ["Canagliflozin 100 mg daily", "Canagliflozin 200 mg daily", "Canagliflozin 300 mg daily", "Canagliflozin 300 mg twice daily", "Canagliflozin 50 mg daily"], "otherNames": ["JNJ-28431754"]}, {"type": "DRUG", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "One 100 mg over-encapsulated tablet orally (by mouth) once daily for 12 weeks.", "armGroupLabels": ["Sitagliptin 100 mg daily"]}, {"type": "DRUG", "name": "Placebo", "description": "One matching placebo capsule orally (by mouth) once or twice daily for 12 weeks.", "armGroupLabels": ["Canagliflozin 100 mg daily", "Canagliflozin 200 mg daily", "Canagliflozin 300 mg daily", "Canagliflozin 50 mg daily", "Placebo", "Sitagliptin 100 mg daily"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 12", "description": "The table below shows the mean change in HbA1c from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}], "secondaryOutcomes": [{"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 12", "description": "The table below shows the mean change in FPG from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Percentage of Patients With Symptoms of Hypoglycemia", "description": "The table below shows the percentage of patients who experienced symptomatic hypoglycemic events between Baseline and Week 12.", "timeFrame": "Up to Week 12"}, {"measure": "Change in Overnight Urine Glucose/Creatinine Ratio From Baseline to Week 12", "description": "The table below shows the mean change in overnight urine glucose/creatinine ratio from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Absolute Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean absolute change in body weight from Baseline to Week 12 for each treatment group.", "timeFrame": "Day 1 (Baseline) and Week 12"}, {"measure": "Percent Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean percent change in body weight from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "timeFrame": "Day 1 (Baseline) and Week 12"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Patients must have a diagnosis of type 2 diabetes mellitus\n* Hemoglobin A1c levels \\>=7% and \\<=10.5%\n* taking a stable daily dose of metformin\n* Body mass index (BMI) 25 to 45 kg/m2 except those of Asian descent who must have a BMI of 24 to 45 kg/m2\n* Stable body weight\n* Serum creatinine \\<=1.5 mg/dL (132.6 umol/L) for men and \\<=1.4 mg/dL (123.76 umol/L) for women\n\nExclusion Criteria:\n\n* Patients must not have prior exposure or known contraindication or suspected hypersensitivity to canagliflozin (JNJ-28431754)\n* Known contraindication or suspected hypersensitivity to sitagliptin or metformin\n* A history of diabetic ketoacidosis or type 1 diabetes mellitus\n* History of pancreas or beta-cell transplantation\n* History of active proliferative diabetic retinopathy\n* History of hereditary glucose-galactose malabsorption or primary renal glucosuria", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "65 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Johnson & Johnson Pharmaceutical Research & Development, L.L. C. Clinical Trial", "affiliation": "Johnson & Johnson Pharmaceutical Research & Development, L.L.C.", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Birmingham", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.52066, "lon": -86.80249}}, {"city": "Mesa", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.42227, "lon": -111.82264}}, {"city": "Tucson", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 32.22174, "lon": -110.92648}}, {"city": "Encinitas", "state": "California", "country": "United States", "geoPoint": {"lat": 33.03699, "lon": -117.29198}}, {"city": "Lincoln", "state": "California", "country": "United States", "geoPoint": {"lat": 38.89156, "lon": -121.29301}}, {"city": "Los Angeles", "state": "California", "country": "United States", "geoPoint": {"lat": 34.05223, "lon": -118.24368}}, {"city": "Merced", "state": "California", "country": "United States", "geoPoint": {"lat": 37.30216, "lon": -120.48297}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Golden", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.75554, "lon": -105.2211}}, {"city": "Hollywood", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.0112, "lon": -80.14949}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Boise", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.6135, "lon": -116.20345}}, {"city": "<PERSON><PERSON>", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.54072, "lon": -116.56346}}, {"city": "Topeka", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 39.04833, "lon": -95.67804}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.7762, "lon": -71.07728}}, {"city": "Cherry Hill", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 39.93484, "lon": -75.03073}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "New Hyde Park", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.7351, "lon": -73.68791}}, {"city": "Salisbury", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.67097, "lon": -80.47423}}, {"city": "Oklahoma City", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.46756, "lon": -97.51643}}, {"city": "Tulsa", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 36.15398, "lon": -95.99277}}, {"city": "Portland", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.52345, "lon": -122.67621}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "New Braunfels", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.703, "lon": -98.12445}}, {"city": "Odessa", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.84568, "lon": -102.36764}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Bueos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Pleven", "country": "Bulgaria", "geoPoint": {"lat": 43.41667, "lon": 24.61667}}, {"city": "Sofia N/A", "country": "Bulgaria"}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "Chilliwack", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.16638, "lon": -121.95257}}, {"city": "Coquitlam", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.28297, "lon": -122.75262}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "Sarnia", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.97866, "lon": -82.40407}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Pointe<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.44868, "lon": -73.81669}}, {"city": "St Romuald", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.75818, "lon": -71.23921}}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "Olomouc 9", "country": "Czech Republic", "geoPoint": {"lat": 49.59552, "lon": 17.25175}}, {"city": "Pisek 1", "country": "Czech Republic", "geoPoint": {"lat": 49.3088, "lon": 14.1475}}, {"city": "Praha 28", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 5", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Kota Bharu", "country": "Malaysia", "geoPoint": {"lat": 6.13328, "lon": 102.2386}}, {"city": "Kuala Lumpur N/A", "country": "Malaysia"}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "Ciudad De Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Zapopan", "country": "Mexico", "geoPoint": {"lat": 20.72356, "lon": -103.38479}}, {"city": "Bydgoszcz", "country": "Poland", "geoPoint": {"lat": 53.1235, "lon": 18.00762}}, {"city": "Gdansk", "country": "Poland", "geoPoint": {"lat": 54.35205, "lon": 18.64637}}, {"city": "Kutno 001", "country": "Poland", "geoPoint": {"lat": 52.23064, "lon": 19.36409}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin N/A", "country": "Poland"}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Ponce Pr", "country": "Puerto Rico"}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "Baia Mare", "country": "Romania", "geoPoint": {"lat": 47.65529, "lon": 23.57381}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.76667, "lon": 23.6}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.45, "lon": 28.05}}, {"city": "P<PERSON>iesti", "country": "Romania", "geoPoint": {"lat": 44.95, "lon": 26.01667}}, {"city": "Moscow N/A", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "St Petersburg N/A", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Belfast", "country": "United Kingdom", "geoPoint": {"lat": 54.59682, "lon": -5.92541}}, {"city": "Bolton", "country": "United Kingdom", "geoPoint": {"lat": 53.58333, "lon": -2.43333}}, {"city": "Exeter", "country": "United Kingdom", "geoPoint": {"lat": 50.7236, "lon": -3.52751}}, {"city": "Lincoln", "country": "United Kingdom", "geoPoint": {"lat": 53.22683, "lon": -0.53792}}]}, "referencesModule": {"references": [{"pmid": "22632452", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Evaluation of vulvovaginal symptoms and Candida colonization in women with type 2 diabetes mellitus treated with canagliflozin, a sodium glucose co-transporter 2 inhibitor. Curr Med Res Opin. 2012 Jul;28(7):1173-8. doi: 10.1185/03007995.2012.697053. Epub 2012 Jun 14."}, {"pmid": "22548646", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> of canagliflozin, a sodium glucose co-transporter 2 (SGLT2) inhibitor, on bacteriuria and urinary tract infection in subjects with type 2 diabetes enrolled in a 12-week, phase 2 study. Curr Med Res Opin. 2012 Jul;28(7):1167-71. doi: 10.1185/03007995.2012.689956. Epub 2012 May 15."}, {"pmid": "22492586", "type": "DERIVED", "citation": "<PERSON><PERSON> J, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; Canagliflozin DIA 2001 Study Group. Dose-ranging effects of canagliflozin, a sodium-glucose cotransporter 2 inhibitor, as add-on to metformin in subjects with type 2 diabetes. Diabetes Care. 2012 Jun;35(6):1232-8. doi: 10.2337/dc11-1926. Epub 2012 Apr 9."}]}}}