{"nctId": "NCT01106677", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT01106677", "orgStudyIdInfo": {"id": "CR017023"}, "secondaryIdInfos": [{"id": "28431754DIA3006", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "The CANTATA-D Trial (CANagliflozin Treatment and Trial Analysis - DPP-4 Inhibitor Comparator Trial)", "officialTitle": "A Randomized, Double-Blind, Placebo and Active-Controlled, 4-Arm, Parallel Group, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin in the Treatment of Subjects With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin Monotherapy"}, "statusModule": {"statusVerifiedDate": "2013-07", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2010-05"}, "primaryCompletionDateStruct": {"date": "2011-10", "type": "ACTUAL"}, "completionDateStruct": {"date": "2012-05", "type": "ACTUAL"}, "studyFirstSubmitDate": "2010-04-01", "studyFirstSubmitQcDate": "2010-04-19", "studyFirstPostDateStruct": {"date": "2010-04-20", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2013-04-04", "resultsFirstSubmitQcDate": "2013-07-25", "resultsFirstPostDateStruct": {"date": "2013-07-30", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2012-03-30", "dispFirstSubmitQcDate": "2012-04-24", "dispFirstPostDateStruct": {"date": "2012-04-25", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2013-07-25", "lastUpdatePostDateStruct": {"date": "2013-07-30", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": true}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the efficacy and safety of canagliflozin compared with sitagliptin and placebo in patients with type 2 diabetes mellitus who are receiving treatment with metformin monotherapy (i.e., treatment with a single drug) and have inadequate glycemic (blood sugar) control.", "detailedDescription": "Canagliflozin is a drug that is being tested to see if it may be useful in treating patients diagnosed with type 2 diabetes mellitus (T2DM). This is a randomized (study drug assigned by chance), double-blind (neither the patient nor the study doctor will know the identity of assigned study drug), placebo- and active-controlled, parallel-group, 4-arm (4 treatment groups) multicenter study to determine the efficacy, safety, and tolerability of canagliflozin (100 mg and 300 mg) compared to placebo (a capsule that looks like all the other treatments but has no real medicine) and an active-control (sitagliptin 100 mg, an antihyperglycemic agent) in patients with T2DM who are not achieving an adequate response from current antihyperglycemic therapy with metformin Immediate Release (IR) to control their diabetes. Approximately 1,260 patients with T2DM who are receiving treatment with metformin IR and have inadequate glycemic (blood sugar) will receive once-daily treatment with canagliflozin (100 mg or 300 mg), sitagliptin 100 mg, or placebo capsules for 26 weeks (Period I) followed by another 26-weeks where patients treated with canagliflozin (100 mg or 300 mg) or sitagliptin 100 mg will continue treatment for an additional 26 weeks and patients treated with placebo will be switched to active double-blind treatment with sitagliptin 100 mg capsules administered once-daily for 26 weeks (Period II). In addition, all patients will take protocol specified stable doses of metformin IR along with assigned study drug for the duration of the study. Patients will participate in the study for approximately 59 to 71 weeks. During the study, if a patient's fasting blood sugar remains high despite treatment with study drug, metformin IR, and reinforcement with diet and exercise, the patient will receive treatment with glimepiride (rescue therapy) consistent with local prescribing information. During treatment, patients will be monitored for safety by review of adverse events, results from laboratory tests, 12-lead electrocardiograms (ECGs), vital sign measurements, body weight, physical examinations, and self-monitored blood glucose (SMGB) measurements. The primary outcome measure in the study is to assess the effect of canagliflozin relative to placebo on hemoglobin A1c (HbA1c) after 26 weeks of treatment. Study drug will be taken orally (by mouth) once daily before the first meal each day unless otherwise specified. Patients will take single-blind placebo for 2 weeks before randomization. After randomization, patients in the study will take double-blind canagliflozin (100 mg or 300 mg) or sitagliptin 100 mg for 52 weeks OR placebo for 26 weeks switched to double-blind treatment with sitaliptin 100 mg for 26 weeks."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Type 2 diabetes mellitus", "Canagliflozin", "Placebo", "<PERSON><PERSON><PERSON><PERSON>", "Jan<PERSON><PERSON>", "Metformin Immediate Release (IR)", "Hemoglobin A1c"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE3"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "TRIPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR"]}}, "enrollmentInfo": {"count": 1284, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 100 mg", "type": "EXPERIMENTAL", "description": "Each patient will receive 100 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release.", "interventionNames": ["Drug: Canagliflozin", "Drug: Metformin immediate release"]}, {"label": "Canagliflozin 300 mg", "type": "EXPERIMENTAL", "description": "Each patient will receive 300 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release.", "interventionNames": ["Drug: Canagliflozin", "Drug: Metformin immediate release"]}, {"label": "Sitagliptin 100 mg", "type": "ACTIVE_COMPARATOR", "description": "Each patient will receive 100 mg of sitagliptin once daily for 52 weeks with protocol-specified doses of metformin immediate release.", "interventionNames": ["Drug: <PERSON><PERSON><PERSON><PERSON>", "Drug: Metformin immediate release"]}, {"label": "Placebo/Sitagliptin", "type": "OTHER", "description": "Each patient will receive matching placebo once daily for 26 weeks and will then switch from placebo to 100 mg of sitagliptin once daily until Week 52. Placebo and sitagliptin will be given with protocol-specified doses of metformin immediate release.", "interventionNames": ["Drug: Placebo", "Drug: <PERSON><PERSON><PERSON><PERSON>", "Drug: Metformin immediate release"]}], "interventions": [{"type": "DRUG", "name": "Placebo", "description": "One matching placebo capsule orally (by mouth) once daily for 26 weeks with protocol-specified doses of metformin immediate release.", "armGroupLabels": ["Placebo/Sitagliptin"]}, {"type": "DRUG", "name": "Canagliflozin", "description": "One 100 mg or 300 mg over-encapsulated tablet orally once daily for 52 weeks with protocol-specified doses of metformin immediate release.", "armGroupLabels": ["Canagliflozin 100 mg", "Canagliflozin 300 mg"]}, {"type": "DRUG", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "One 100 mg over-encapsulated tablet orally once daily for 52 weeks (sitagliptin 100 mg arm) or once daily beginning at Week 26 until Week 52 (placebo/sitagliptin arm). Sitagliptin will be given with protocol-specified doses of metformin immediate release.", "armGroupLabels": ["Placebo/Sitagliptin", "Sitagliptin 100 mg"]}, {"type": "DRUG", "name": "Metformin immediate release", "description": "The patient's stable dose of metformin immediate release background therapy should be continued throughout the study.", "armGroupLabels": ["Canagliflozin 100 mg", "Canagliflozin 300 mg", "Placebo/Sitagliptin", "Sitagliptin 100 mg"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}], "secondaryOutcomes": [{"measure": "Percentage of Patients With HbA1c <7% at Week 26", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 26 in each treatment group. The statistical analyses show the treatment differences between each canagliflozin or sitagliptin group and placebo.", "timeFrame": "Week 26"}, {"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in 2-hour Post-prandial Glucose From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in 2-hour post-prandial glucose from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Body Weight From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in Systolic Blood Pressure (SBP) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean change in SBP from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Triglycerides From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in triglycerides from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in High-density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 26", "description": "The table below shows the least-squares (LS) mean percent change in HDL-C from Baseline to Week 26 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Change in HbA1c From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in Body Weight From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean percent change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Change in Systolic Blood Pressure (SBP) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean change in SBP from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in Triglycerides From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean percent change in triglycerides from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean percent change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}, {"measure": "Percent Change in High-density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 52", "description": "The table below shows the least-squares (LS) mean percent change in HDL-C from Baseline to Week 52 for each active treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus sitagliptin) in the LS mean percent change.", "timeFrame": "Day 1 (<PERSON>ine) and Week 52"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* All patients must have a diagnosis of T2DM and be currently treated with metformin IR\n* Patients in the study must have a HbA1c between \\>=7 and \\<=10.5%\n* Patients must have a fasting plasma glucose (FPG) \\<270 mg/dL (15 mmol/L)\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus (T1DM), pancreas or beta cell transplantation, diabetes secondary to pancreatitis or pancreatectomy, or a severe hypoglycemic episode within 6 months before screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "80 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "Fountain Valley", "state": "California", "country": "United States", "geoPoint": {"lat": 33.70918, "lon": -117.95367}}, {"city": "National City", "state": "California", "country": "United States", "geoPoint": {"lat": 32.67811, "lon": -117.0992}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Colorado Springs", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 38.83388, "lon": -104.82136}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.49893, "lon": -82.57482}}, {"city": "Brooksville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.55554, "lon": -82.38991}}, {"city": "Defuniak Springs", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.72102, "lon": -86.11522}}, {"city": "Hialeah", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.8576, "lon": -80.27811}}, {"city": "Niceville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.51686, "lon": -86.48217}}, {"city": "Tampa", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.94752, "lon": -82.45843}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Savannah", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.08354, "lon": -81.09983}}, {"city": "Champaign", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 40.11642, "lon": -88.24338}}, {"city": "Avon", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.76282, "lon": -86.39972}}, {"city": "<PERSON>s", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.95559, "lon": -86.01387}}, {"city": "<PERSON>", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.48061, "lon": -86.05499}}, {"city": "West Des Moines", "state": "Iowa", "country": "United States", "geoPoint": {"lat": 41.57721, "lon": -93.71133}}, {"city": "Wichita", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 37.69224, "lon": -97.33754}}, {"city": "Munfordville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.27228, "lon": -85.89108}}, {"city": "Portland", "state": "Maine", "country": "United States", "geoPoint": {"lat": 43.66147, "lon": -70.25533}}, {"city": "Benzonia", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 44.62139, "lon": -86.09926}}, {"city": "Interlochen", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 44.64472, "lon": -85.7673}}, {"city": "Troy", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.60559, "lon": -83.14993}}, {"city": "Picayune", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 30.52556, "lon": -89.67788}}, {"city": "Florissant", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.78922, "lon": -90.32261}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "Mansfield", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 40.09122, "lon": -74.71377}}, {"city": "New Hyde Park", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.7351, "lon": -73.68791}}, {"city": "Asheboro", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.70791, "lon": -79.81364}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Kettering", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.6895, "lon": -84.16883}}, {"city": "Oregon City", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.35734, "lon": -122.60676}}, {"city": "Altoona", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.51868, "lon": -78.39474}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.6359, "lon": -78.29585}}, {"city": "East Providence", "state": "Rhode Island", "country": "United States", "geoPoint": {"lat": 41.81371, "lon": -71.37005}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Mount Pleasant", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 32.79407, "lon": -79.86259}}, {"city": "Nashville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.16589, "lon": -86.78444}}, {"city": "New Braunfels", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.703, "lon": -98.12445}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Mendoza", "country": "Argentina", "geoPoint": {"lat": -32.89084, "lon": -68.82717}}, {"city": "San Juan", "country": "Argentina", "geoPoint": {"lat": -31.5375, "lon": -68.53639}}, {"city": "Pleven", "country": "Bulgaria", "geoPoint": {"lat": 43.41667, "lon": 24.61667}}, {"city": "Plovdiv", "country": "Bulgaria", "geoPoint": {"lat": 42.15, "lon": 24.75}}, {"city": "Sevlievo", "country": "Bulgaria", "geoPoint": {"lat": 43.02583, "lon": 25.11361}}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 10.96854, "lon": -74.78132}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "Medellin", "country": "Colombia", "geoPoint": {"lat": 6.25184, "lon": -75.56359}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czech Republic", "geoPoint": {"lat": 49.96382, "lon": 14.072}}, {"city": "Pardubice", "country": "Czech Republic", "geoPoint": {"lat": 50.04075, "lon": 15.77659}}, {"city": "Plzen", "country": "Czech Republic", "geoPoint": {"lat": 49.74747, "lon": 13.37759}}, {"city": "Rychnov Nad Kneznou", "country": "Czech Republic", "geoPoint": {"lat": 50.16284, "lon": 16.27488}}, {"city": "Tabor", "country": "Czech Republic", "geoPoint": {"lat": 49.41441, "lon": 14.6578}}, {"city": "Tallinn", "country": "Estonia", "geoPoint": {"lat": 59.43696, "lon": 24.75353}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Greece", "geoPoint": {"lat": 37.94745, "lon": 23.63708}}, {"city": "Thessalonikis", "country": "Greece"}, {"city": "Ahmedabad, Gujarat", "country": "India"}, {"city": "Aurangabad", "country": "India", "geoPoint": {"lat": 19.87757, "lon": 75.34226}}, {"city": "Bangalore, Karnataka", "country": "India"}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Belgaum", "country": "India", "geoPoint": {"lat": 15.85212, "lon": 74.50447}}, {"city": "Coimbatore", "country": "India", "geoPoint": {"lat": 11.00555, "lon": 76.96612}}, {"city": "Mumbai", "country": "India", "geoPoint": {"lat": 19.07283, "lon": 72.88261}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Trivandrum", "country": "India", "geoPoint": {"lat": 8.4855, "lon": 76.94924}}, {"city": "Daugavpils", "country": "Latvia", "geoPoint": {"lat": 55.88333, "lon": 26.53333}}, {"city": "Limbazi", "country": "Latvia", "geoPoint": {"lat": 57.51287, "lon": 24.71941}}, {"city": "Riga", "country": "Latvia", "geoPoint": {"lat": 56.946, "lon": 24.10589}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Latvia", "geoPoint": {"lat": 57.24562, "lon": 22.58137}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia"}, {"city": "Kuala Lumpur N/A", "country": "Malaysia"}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 3.55, "lon": 102.56667}}, {"city": "Selangor", "country": "Malaysia"}, {"city": "Culiacan", "country": "Mexico", "geoPoint": {"lat": 24.79032, "lon": -107.38782}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Que<PERSON>ro", "country": "Mexico"}, {"city": "Tampico", "country": "Mexico", "geoPoint": {"lat": 22.26695, "lon": -97.86815}}, {"city": "Lima 1 Lima Lima", "country": "Peru"}, {"city": "Leczyca", "country": "Poland", "geoPoint": {"lat": 52.05959, "lon": 19.19972}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Zgierz", "country": "Poland", "geoPoint": {"lat": 51.85561, "lon": 19.40623}}, {"city": "Aveiro", "country": "Portugal", "geoPoint": {"lat": 40.64427, "lon": -8.64554}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Portugal", "geoPoint": {"lat": 39.74362, "lon": -8.80705}}, {"city": "Lisboa", "country": "Portugal", "geoPoint": {"lat": 38.71667, "lon": -9.13333}}, {"city": "Portalegre", "country": "Portugal", "geoPoint": {"lat": 39.29379, "lon": -7.43122}}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "Chelyabinsk", "country": "Russian Federation", "geoPoint": {"lat": 55.15402, "lon": 61.42915}}, {"city": "Dzerzhinsky Moscow Region", "country": "Russian Federation"}, {"city": "Ekaterinburg", "country": "Russian Federation", "geoPoint": {"lat": 56.8519, "lon": 60.6122}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 53.20066, "lon": 45.00464}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Singapore", "country": "Singapore", "geoPoint": {"lat": 1.28967, "lon": 103.85007}}, {"city": "Banska Bystrica", "country": "Slovakia", "geoPoint": {"lat": 48.73946, "lon": 19.15349}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.71395, "lon": 21.25808}}, {"city": "<PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.07408, "lon": 18.94946}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.62858, "lon": 21.71954}}, {"city": "Göteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Stockholm", "country": "Sweden", "geoPoint": {"lat": 59.33258, "lon": 18.0649}}, {"city": "Bangkok", "country": "Thailand", "geoPoint": {"lat": 13.75398, "lon": 100.50144}}, {"city": "<PERSON>", "country": "Thailand", "geoPoint": {"lat": 18.79038, "lon": 98.98468}}, {"city": "<PERSON><PERSON>", "country": "Thailand", "geoPoint": {"lat": 16.44671, "lon": 102.833}}, {"city": "Ankara", "country": "Turkey", "geoPoint": {"lat": 39.91987, "lon": 32.85427}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Turkey", "geoPoint": {"lat": 36.90812, "lon": 30.69556}}, {"city": "Istanbul", "country": "Turkey", "geoPoint": {"lat": 41.01384, "lon": 28.94966}}, {"city": "Izmir", "country": "Turkey", "geoPoint": {"lat": 38.41273, "lon": 27.13838}}, {"city": "Konya", "country": "Turkey", "geoPoint": {"lat": 37.87135, "lon": 32.48464}}, {"city": "Donetsk", "country": "Ukraine", "geoPoint": {"lat": 48.023, "lon": 37.80224}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 48.9215, "lon": 24.70972}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 50.9216, "lon": 34.80029}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}, {"city": "Zaporozhye", "country": "Ukraine", "geoPoint": {"lat": 47.82289, "lon": 35.19031}}]}, "referencesModule": {"references": [{"pmid": "29313267", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Traina S. Impact of Canagliflozin Treatment on Health-Related Quality of Life among People with Type 2 Diabetes Mellitus: A Pooled Analysis of Patient-Reported Outcomes from Randomized Controlled Trials. Patient. 2018 Jun;11(3):341-352. doi: 10.1007/s40271-017-0290-4."}, {"pmid": "28327140", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>icacy and safety of canagliflozin in patients with type 2 diabetes based on history of cardiovascular disease or cardiovascular risk factors: a post hoc analysis of pooled data. Cardiovasc Diabetol. 2017 Mar 21;16(1):40. doi: 10.1186/s12933-017-0517-7."}, {"pmid": "28241822", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> of canagliflozin, a sodium glucose co-transporter 2 inhibitor, on blood pressure and markers of arterial stiffness in patients with type 2 diabetes mellitus: a post hoc analysis. Cardiovasc Diabetol. 2017 Feb 27;16(1):29. doi: 10.1186/s12933-017-0511-0."}, {"pmid": "28197834", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Effects of Canagliflozin on Serum Magnesium in Patients With Type 2 Diabetes Mellitus: A Post Hoc Analysis of Randomized Controlled Trials. Diabetes Ther. 2017 Apr;8(2):451-458. doi: 10.1007/s13300-017-0232-0. Epub 2017 Feb 14."}, {"pmid": "27977934", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>-term safety and tolerability of canagliflozin in patients with type 2 diabetes: a pooled analysis. Curr Med Res Opin. 2017 Mar;33(3):553-562. doi: 10.1080/03007995.2016.1271780. Epub 2017 Jan 4."}, {"pmid": "27600862", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Efficacy and safety of canagliflozin in patients with type 2 diabetes mellitus living in hot climates. Int J Clin Pract. 2016 Sep;70(9):775-85. doi: 10.1111/ijcp.12868."}, {"pmid": "27391951", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>zin provides greater attainment of both HbA1c and body weight reduction versus sitagliptin in patients with type 2 diabetes. Postgrad Med. 2016 Nov;128(8):725-730. doi: 10.1080/00325481.2016.1210988. Epub 2016 Jul 26."}, {"pmid": "26580237", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effects of Canagliflozin on Fracture Risk in Patients With Type 2 Diabetes Mellitus. J Clin Endocrinol Metab. 2016 Jan;101(1):157-66. doi: 10.1210/jc.2015-3167. Epub 2015 Nov 18."}, {"pmid": "26579834", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Alba M. <PERSON>fficacy and safety of canagliflozin in patients with type 2 diabetes mellitus from Latin America. Curr Med Res Opin. 2016;32(3):427-39. doi: 10.1185/03007995.2015.1121865. Epub 2016 Jan 14."}, {"pmid": "26373629", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Achievement of treatment goals with canagliflozin in patients with type 2 diabetes mellitus: a pooled analysis of randomized controlled trials. Curr Med Res Opin. 2015 Nov;31(11):1993-2000. doi: 10.1185/03007995.2015.1082991. Epub 2015 Sep 28."}, {"pmid": "26121561", "type": "DERIVED", "citation": "<PERSON> 3rd, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. The efficacy and safety of canagliflozin across racial groups in patients with type 2 diabetes mellitus. Curr Med Res Opin. 2015;31(9):1693-702. doi: 10.1185/03007995.2015.1067192. Epub 2015 Sep 4."}, {"pmid": "25813214", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Effects of canagliflozin on body weight and relationship to HbA1c and blood pressure changes in patients with type 2 diabetes. Diabetologia. 2015 Jun;58(6):1183-7. doi: 10.1007/s00125-015-3547-2. Epub 2015 Mar 27."}, {"pmid": "25795432", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Blon<PERSON> L<PERSON>-Related Composite Quality End Point Attainment: Canagliflozin Versus Sitagliptin Based on a Pooled Analysis of 2 Clinical Trials. Clin Ther. 2015 May 1;37(5):1045-54. doi: 10.1016/j.clinthera.2015.02.020. Epub 2015 Mar 18."}, {"pmid": "25329038", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Effect of canagliflozin on blood pressure and adverse events related to osmotic diuresis and reduced intravascular volume in patients with type 2 diabetes mellitus. <PERSON> <PERSON><PERSON> (Greenwich). 2014 Dec;16(12):875-82. doi: 10.1111/jch.12425. Epub 2014 Oct 20."}, {"pmid": "24918789", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ninger G. Safety and tolerability of canagliflozin in patients with type 2 diabetes mellitus: pooled analysis of phase 3 study results. Postgrad Med. 2014 May;126(3):16-34. doi: 10.3810/pgm.2014.05.2753."}, {"pmid": "24786834", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Effect of canagliflozin on serum electrolytes in patients with type 2 diabetes in relation to estimated glomerular filtration rate (eGFR). Curr Med Res Opin. 2014 Sep;30(9):1759-68. doi: 10.1185/03007995.2014.919907. Epub 2014 May 22."}, {"pmid": "24742013", "type": "DERIVED", "citation": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Mei<PERSON><PERSON> G. Efficacy and safety of canagliflozin compared with placebo in older patients with type 2 diabetes mellitus: a pooled analysis of clinical studies. BMC Endocr Disord. 2014 Apr 18;14:37. doi: 10.1186/1472-6823-14-37."}, {"pmid": "24517339", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Genital mycotic infections with canagliflozin, a sodium glucose co-transporter 2 inhibitor, in patients with type 2 diabetes mellitus: a pooled analysis of clinical studies. Curr Med Res Opin. 2014 Jun;30(6):1109-19. doi: 10.1185/03007995.2014.890925. Epub 2014 Feb 21."}, {"pmid": "24026211", "type": "DERIVED", "citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> G. Effica<PERSON> and safety of canagliflozin compared with placebo and sitagliptin in patients with type 2 diabetes on background metformin monotherapy: a randomised trial. Diabetologia. 2013 Dec;56(12):2582-92. doi: 10.1007/s00125-013-3039-1. Epub 2013 Sep 13."}]}}}