{"nctId": "NCT01340664", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Researcher View", "fullStudyData": {"protocolSection": {"identificationModule": {"nctId": "NCT01340664", "orgStudyIdInfo": {"id": "CR017914"}, "secondaryIdInfos": [{"id": "28431754DIA2003", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}, {"id": "2010-024256-28", "type": "EUDRACT_NUMBER"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "An Efficacy, Safety, and Tolerability Study of Canagliflozin in the Treatment of Patients With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin Monotherapy", "officialTitle": "A Randomized, Double-Blind, Placebo-Controlled, 3-Arm, Parallel-Group, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin in the Treatment of Subjects With Type 2 Diabetes Mellitus With Inadequate Glycemic Control on Metformin"}, "statusModule": {"statusVerifiedDate": "2014-09", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2011-07"}, "primaryCompletionDateStruct": {"date": "2012-04", "type": "ACTUAL"}, "completionDateStruct": {"date": "2012-04", "type": "ACTUAL"}, "studyFirstSubmitDate": "2011-04-21", "studyFirstSubmitQcDate": "2011-04-21", "studyFirstPostDateStruct": {"date": "2011-04-22", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2014-08-20", "resultsFirstSubmitQcDate": "2014-08-20", "resultsFirstPostDateStruct": {"date": "2014-09-01", "type": "ESTIMATED"}, "dispFirstSubmitDate": "2012-03-30", "dispFirstSubmitQcDate": "2012-04-24", "dispFirstPostDateStruct": {"date": "2012-04-25", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2014-09-05", "lastUpdatePostDateStruct": {"date": "2014-09-16", "type": "ESTIMATED"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": true}, "descriptionModule": {"briefSummary": "The purpose of this study is to evaluate the efficacy and safety of 2 different doses of canagliflozin compared with placebo in patients with type 2 diabetes mellitus who are receiving treatment with metformin monotherapy (ie, treatment with a single drug) and have inadequate glycemic (blood sugar) control.", "detailedDescription": "This is a randomized (study drug assigned by chance), double-blind (neither the patient nor the study doctor will know the identity of assigned study drug), placebo-controlled, parallel-group,3-arm (3 treatment groups) multicenter study to determine the efficacy, safety, and tolerability of canagliflozin compared to placebo (a capsule that is identical in appearance to canagliflozin but does not contain active drug) in patients with type 2 diabetes mellitus (T2DM) who are not achieving an adequate response from current antihyperglycemic therapy with metformin to control their diabetes. Canagliflozin (a Sodium-Glucose Cotransporter 2 inhibitor) is currently under development to lower blood sugar levels in patients with T2DM. Patients will take single-blind placebo capsules orally (by mouth) for 2 weeks before randomization. Patients will be randomly assigned canagliflozin or matching placebo, orally, twice daily, with 1 capsule taken with the morning meal and 1 capsule taken with the evening meal (2 capsules per day) and concurrent with metformin, if applicable, for up to 18 weeks. The dosing time of metformin should remain the same throughout the study."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Canagliflozin", "Placebo", "Metformin", "Hemoglobin A1c", "Type 2 diabetes mellitus"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE2"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 279, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 50 mg bid", "type": "EXPERIMENTAL", "description": "Each patient will receive 50 mg canagliflozin twice daily for 18 weeks.", "interventionNames": ["Drug: Canagliflozin 50 mg", "Drug: Metformin"]}, {"label": "Canagliflozin 150 mg bid", "type": "EXPERIMENTAL", "description": "Each patient will receive 150 mg canagliflozin twice daily for 18 weeks", "interventionNames": ["Drug: Canagliflozin 150 mg", "Drug: Metformin"]}, {"label": "Placebo", "type": "PLACEBO_COMPARATOR", "description": "Each patient will receive matching placebo twice daily for 18 weeks", "interventionNames": ["Drug: Placebo", "Drug: Metformin"]}], "interventions": [{"type": "DRUG", "name": "Canagliflozin 50 mg", "description": "One canagliflozin 50-mg capsule taken orally twice daily with a meal for 18 weeks", "armGroupLabels": ["Canagliflozin 50 mg bid"]}, {"type": "DRUG", "name": "Placebo", "description": "1 placebo capsule taken orally twice daily with a meal for 18 weeks", "armGroupLabels": ["Placebo"]}, {"type": "DRUG", "name": "Canagliflozin 150 mg", "description": "1 canagliflozin 150-mg capsule taken orally twice daily with a meal for 18 weeks", "armGroupLabels": ["Canagliflozin 150 mg bid"]}, {"type": "DRUG", "name": "Metformin", "description": "The patient's stable daily dose of Metformin background therapy should be continued throughout the study.", "armGroupLabels": ["Canagliflozin 150 mg bid", "Canagliflozin 50 mg bid", "Placebo"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in HbA1c From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 18"}], "secondaryOutcomes": [{"measure": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "timeFrame": "Day 1 (Baseline) and Week 18"}, {"measure": "Percent Change in Body Weight From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean percent change.", "timeFrame": "Day 1 (Baseline) and Week 18"}, {"measure": "Percentage of Patients With HbA1c <7% at Week 18", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 18 in each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the percentage.", "timeFrame": "Week 18"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* All patients must have a diagnosis of T2DM and be currently treated with metformin\n* Patients in the study must have a HbA1c between \\>=7 and \\<=10.5%\n* Patients must have a fasting plasma glucose (FPG) \\<270 mg/dL (15 mmol/L)\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus (T1DM), pancreas or beta cell transplantation, diabetes secondary to pancreatitis or pancreatectomy, or a severe hypoglycemic episode within 6 months before screening", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "80 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "National City", "state": "California", "country": "United States", "geoPoint": {"lat": 32.67811, "lon": -117.0992}}, {"city": "Spring Valley", "state": "California", "country": "United States", "geoPoint": {"lat": 32.74477, "lon": -116.99892}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.49893, "lon": -82.57482}}, {"city": "<PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.77436, "lon": -85.22687}}, {"city": "Savannah", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.08354, "lon": -81.09983}}, {"city": "Avon", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.76282, "lon": -86.39972}}, {"city": "Valparaiso", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.47309, "lon": -87.06114}}, {"city": "Madisonville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.3281, "lon": -87.49889}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Columbus", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.96118, "lon": -82.99879}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.36006, "lon": -84.30994}}, {"city": "Perrysburg", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.557, "lon": -83.62716}}, {"city": "Tulsa", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 36.15398, "lon": -95.99277}}, {"city": "Yukon", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.50672, "lon": -97.76254}}, {"city": "Oregon City", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.35734, "lon": -122.60676}}, {"city": "Pittsburg", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.44062, "lon": -79.99589}}, {"city": "East Providence", "state": "Rhode Island", "country": "United States", "geoPoint": {"lat": 41.81371, "lon": -71.37005}}, {"city": "Nashville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.16589, "lon": -86.78444}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "Pearland", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.56357, "lon": -95.28605}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Virginia Beach", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.85293, "lon": -75.97799}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "St. John'S", "state": "Newfoundland and Labrador", "country": "Canada", "geoPoint": {"lat": 47.56494, "lon": -52.70931}}, {"city": "Halifax", "state": "Nova Scotia", "country": "Canada", "geoPoint": {"lat": 44.64533, "lon": -63.57239}}, {"city": "Brampton", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.68341, "lon": -79.76633}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czech Republic", "geoPoint": {"lat": 49.96382, "lon": 14.072}}, {"city": "Olomouc 9", "country": "Czech Republic", "geoPoint": {"lat": 49.59552, "lon": 17.25175}}, {"city": "Ostrava", "country": "Czech Republic", "geoPoint": {"lat": 49.83465, "lon": 18.28204}}, {"city": "Pardubice", "country": "Czech Republic", "geoPoint": {"lat": 50.04075, "lon": 15.77659}}, {"city": "Pisek", "country": "Czech Republic", "geoPoint": {"lat": 49.3088, "lon": 14.1475}}, {"city": "Prague 10", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Praha 11", "country": "Czech Republic", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Znojmo", "country": "Czech Republic", "geoPoint": {"lat": 48.8555, "lon": 16.0488}}, {"city": "Aguascalientes", "country": "Mexico", "geoPoint": {"lat": 21.88234, "lon": -102.28259}}, {"city": "Guadalajara, Jalisco.", "country": "Mexico"}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Tampico", "country": "Mexico", "geoPoint": {"lat": 22.26695, "lon": -97.86815}}, {"city": "Bacau", "country": "Romania", "geoPoint": {"lat": 46.56718, "lon": 26.91384}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "Cluj-Napoca", "country": "Romania", "geoPoint": {"lat": 46.76667, "lon": 23.6}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.45, "lon": 28.05}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.54245, "lon": 24.55747}}, {"city": "Kursk", "country": "Russian Federation", "geoPoint": {"lat": 51.73733, "lon": 36.18735}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Saint-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Tyumen", "country": "Russian Federation", "geoPoint": {"lat": 57.15222, "lon": 65.52722}}, {"city": "Voronezh", "country": "Russian Federation", "geoPoint": {"lat": 51.67204, "lon": 39.1843}}, {"city": "Banska Bystrica", "country": "Slovakia", "geoPoint": {"lat": 48.73946, "lon": 19.15349}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.71395, "lon": 21.25808}}, {"city": "Presov", "country": "Slovakia", "geoPoint": {"lat": 48.99839, "lon": 21.23393}}, {"city": "<PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.07408, "lon": 18.94946}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.62858, "lon": 21.71954}}]}, "referencesModule": {"references": [{"pmid": "29159083", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ica<PERSON> and safety of twice-daily treatment with canagliflozin, a sodium glucose co-transporter 2 inhibitor, added on to metformin monotherapy in patients with type 2 diabetes mellitus. J Clin Transl Endocrinol. 2014 May 5;1(2):54-60. doi: 10.1016/j.jcte.2014.04.001. eCollection 2014 Jun."}]}}, "resultsSection": {"participantFlowModule": {"preAssignmentDetails": "A total of 279 patients were randomly allocated to the 3 treatment arms in the study. All 279 patients received at least 1 dose of study drug and were included in the modified intent-to-treat analysis set which was used for the efficacy and safety analyses.", "recruitmentDetails": "This study evaluated the efficacy and safety of canagliflozin in patients with type 2 diabetes mellitus with inadequate control despite treatment with metformin. The study was conducted between 27 June 2011 and 20 April 2012 and recruited patients from 60 study centers located in 7 countries worldwide.", "groups": [{"id": "FG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "FG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks."}, {"id": "FG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "93"}, {"groupId": "FG001", "numSubjects": "93"}, {"groupId": "FG002", "numSubjects": "93"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "86"}, {"groupId": "FG001", "numSubjects": "85"}, {"groupId": "FG002", "numSubjects": "80"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "7"}, {"groupId": "FG001", "numSubjects": "8"}, {"groupId": "FG002", "numSubjects": "13"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "7"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "2"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "4"}, {"groupId": "FG002", "numSubjects": "0"}]}, {"type": "Glycemic withdrawal criteria", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}]}, {"type": "Creatinine or eGFR withdrawal criteria", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "2"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "2"}, {"groupId": "FG002", "numSubjects": "2"}]}]}]}, "baselineCharacteristicsModule": {"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "BG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks."}, {"id": "BG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "93"}, {"groupId": "BG001", "value": "93"}, {"groupId": "BG002", "value": "93"}, {"groupId": "BG003", "value": "279"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "79"}, {"groupId": "BG001", "value": "69"}, {"groupId": "BG002", "value": "75"}, {"groupId": "BG003", "value": "223"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "24"}, {"groupId": "BG002", "value": "18"}, {"groupId": "BG003", "value": "56"}]}]}]}, {"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "57", "spread": "9.32"}, {"groupId": "BG001", "value": "58.6", "spread": "8.88"}, {"groupId": "BG002", "value": "56.7", "spread": "10.33"}, {"groupId": "BG003", "value": "57.4", "spread": "9.53"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "47"}, {"groupId": "BG001", "value": "53"}, {"groupId": "BG002", "value": "49"}, {"groupId": "BG003", "value": "149"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "46"}, {"groupId": "BG001", "value": "40"}, {"groupId": "BG002", "value": "44"}, {"groupId": "BG003", "value": "130"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "17"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "12"}, {"groupId": "BG003", "value": "40"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "12"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "10"}, {"groupId": "BG003", "value": "32"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "13"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "37"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "18"}, {"groupId": "BG001", "value": "17"}, {"groupId": "BG002", "value": "20"}, {"groupId": "BG003", "value": "55"}]}]}, {"title": "SLOVAKIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "9"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "30"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "23"}, {"groupId": "BG001", "value": "25"}, {"groupId": "BG002", "value": "25"}, {"groupId": "BG003", "value": "73"}]}]}]}]}, "outcomeMeasuresModule": {"outcomeMeasures": [{"type": "PRIMARY", "title": "Change in HbA1c From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean change in HbA1c from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when Week 18 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent", "timeFrame": "Day 1 (Baseline) and Week 18", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks"}, {"id": "OG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "92"}, {"groupId": "OG001", "value": "90"}, {"groupId": "OG002", "value": "91"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.01", "spread": "0.069"}, {"groupId": "OG001", "value": "-0.45", "spread": "0.070"}, {"groupId": "OG002", "value": "-0.61", "spread": "0.069"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.44", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.637", "ciUpperLimit": "-0.251", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.098"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.60", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.792", "ciUpperLimit": "-0.407", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.098"}]}, {"type": "SECONDARY", "title": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean change in FPG from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when Week 18 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "mg/dL", "timeFrame": "Day 1 (Baseline) and Week 18", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks"}, {"id": "OG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "92"}, {"groupId": "OG001", "value": "90"}, {"groupId": "OG002", "value": "91"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "8.1", "spread": "3.291"}, {"groupId": "OG001", "value": "-15.5", "spread": "3.327"}, {"groupId": "OG002", "value": "-15.9", "spread": "3.313"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-23.6", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-32.78", "ciUpperLimit": "-14.38", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "4.673"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-24.0", "ciPctValue": "95", "ciLowerLimit": "-33.18", "ciUpperLimit": "-14.83", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "4.661"}]}, {"type": "SECONDARY", "title": "Percent Change in Body Weight From Baseline to Week 18", "description": "The table below shows the least-squares (LS) mean percent change in body weight from Baseline to Week 18 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the LS mean percent change.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when Week 18 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "LEAST_SQUARES_MEAN", "dispersionType": "Standard Error", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (Baseline) and Week 18", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks"}, {"id": "OG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "92"}, {"groupId": "OG001", "value": "90"}, {"groupId": "OG002", "value": "91"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.6", "spread": "0.3"}, {"groupId": "OG001", "value": "-2.8", "spread": "0.3"}, {"groupId": "OG002", "value": "-3.2", "spread": "0.3"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-2.2", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.1", "ciUpperLimit": "-1.3", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "paramType": "Least-Squares Mean Difference", "paramValue": "-2.6", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.5", "ciUpperLimit": "-1.7", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}]}, {"type": "SECONDARY", "title": "Percentage of Patients With HbA1c <7% at Week 18", "description": "The table below shows the percentage of patients with HbA1c \\<7% at Week 18 in each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin group minus placebo) in the percentage.", "populationDescription": "This analysis used the modified intent-to-treat analysis set (all patients who were randomly assigned to a treatment group and received at least 1 dose of study drug). The last-observation-carried-forward method was applied when Week 18 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "NUMBER", "unitOfMeasure": "Percentage of Participants", "timeFrame": "Week 18", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks"}, {"id": "OG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "92"}, {"groupId": "OG001", "value": "90"}, {"groupId": "OG002", "value": "91"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "31.5"}, {"groupId": "OG001", "value": "47.8"}, {"groupId": "OG002", "value": "57.1"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.013", "statisticalMethod": "Regression, Logistic", "paramType": "<PERSON><PERSON> (OR)", "paramValue": "2.43", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "1.21", "ciUpperLimit": "4.90"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "Regression, Logistic", "paramType": "<PERSON><PERSON> (OR)", "paramValue": "3.38", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "1.68", "ciUpperLimit": "6.81"}]}]}, "adverseEventsModule": {"frequencyThreshold": "5", "timeFrame": "Adverse events were reported for the duration of the study; each patient participated in the study for approximately 18 weeks.", "description": "Only patients who had at least one of the treatment-emergent adverse events listed in the \"Other (non-Serious) Adverse Event\" table are included in the total number of patients with non-serious adverse Events.", "eventGroups": [{"id": "EG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 18 weeks", "seriousNumAffected": 1, "seriousNumAtRisk": 93, "otherNumAffected": 0, "otherNumAtRisk": 93}, {"id": "EG001", "title": "Canagliflozin 50 mg Bid", "description": "Each patient received 50 mg canagliflozin twice daily for 18 weeks.", "seriousNumAffected": 0, "seriousNumAtRisk": 93, "otherNumAffected": 0, "otherNumAtRisk": 93}, {"id": "EG002", "title": "Canagliflozin 150 mg Bid", "description": "Each patient received 150 mg canagliflozin twice daily for 18 weeks", "seriousNumAffected": 3, "seriousNumAtRisk": 93, "otherNumAffected": 0, "otherNumAtRisk": 93}], "seriousEvents": [{"term": "Oroantral fistula", "organSystem": "Gastrointestinal disorders", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 93}]}, {"term": "P<PERSON>lone<PERSON><PERSON><PERSON>", "organSystem": "Infections and infestations", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 93}]}, {"term": "Postoperative wound complication", "organSystem": "Injury, poisoning and procedural complications", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 93}]}, {"term": "Colon cancer", "organSystem": "Neoplasms benign, malignant and unspecified (incl cysts and polyps)", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 93}]}, {"term": "Nephrolithiasis", "organSystem": "Renal and urinary disorders", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 1, "numAtRisk": 93}]}, {"term": "Dysfunctional uterine bleeding", "organSystem": "Reproductive system and breast disorders", "sourceVocabulary": "MEDDRA 15.0", "assessmentType": "NON_SYSTEMATIC_ASSESSMENT", "stats": [{"groupId": "EG000", "numAffected": 1, "numAtRisk": 93}, {"groupId": "EG001", "numAffected": 0, "numAtRisk": 93}, {"groupId": "EG002", "numAffected": 0, "numAtRisk": 93}]}]}, "moreInfoModule": {"certainAgreement": {"piSponsorEmployee": false, "restrictionType": "OTHER", "restrictiveAgreement": true, "otherDetails": "If an investigator wishes to publish information from the study, a copy of the manuscript must be provided to the sponsor for review at least 60 days before submission for publication or presentation. Expedited reviews will be arranged for abstracts, poster presentations, or other materials. If requested by the sponsor in writing, the investigator will withhold such publication for up to an additional 60 days to allow for filing of a patent application."}, "pointOfContact": {"title": "Vice President, Franchise Medical Leader, Cardiovascular & Metabolism Franchise", "organization": "Janssen Research & Development, LLC", "phone": "**************"}}}, "derivedSection": {"miscInfoModule": {"versionHolder": "2025-07-08", "removedCountries": ["Poland"]}, "conditionBrowseModule": {"meshes": [{"id": "D003920", "term": "Diabe<PERSON>"}, {"id": "D003924", "term": "Diabetes Mellitus, Type 2"}], "ancestors": [{"id": "D044882", "term": "Glucose Metabolism Disorders"}, {"id": "D008659", "term": "Metabolic Diseases"}, {"id": "D004700", "term": "Endocrine System Diseases"}], "browseLeaves": [{"id": "M7115", "name": "Diabe<PERSON>", "asFound": "Diabe<PERSON>", "relevance": "HIGH"}, {"id": "M7119", "name": "Diabetes Mellitus, Type 2", "asFound": "Type 2 Diabetes Mellitus", "relevance": "HIGH"}, {"id": "M11639", "name": "Metabolic Diseases", "relevance": "LOW"}, {"id": "M25403", "name": "Glucose Metabolism Disorders", "relevance": "LOW"}, {"id": "M7862", "name": "Endocrine System Diseases", "relevance": "LOW"}], "browseBranches": [{"abbrev": "BC18", "name": "Nutritional and Metabolic Diseases"}, {"abbrev": "BC19", "name": "Gland and Hormone Related Diseases"}, {"abbrev": "All", "name": "All Conditions"}]}, "interventionBrowseModule": {"meshes": [{"id": "D008687", "term": "Metformin"}, {"id": "D000068896", "term": "Canagliflozin"}], "ancestors": [{"id": "D007004", "term": "Hypoglycemic Agents"}, {"id": "D045505", "term": "Physiological Effects of Drugs"}, {"id": "D000077203", "term": "Sodium-Glucose Transporter 2 Inhibitors"}, {"id": "D045504", "term": "Molecular Mechanisms of Pharmacological Action"}], "browseLeaves": [{"id": "M11667", "name": "Metformin", "asFound": "Chemotherapy", "relevance": "HIGH"}, {"id": "M331", "name": "Canagliflozin", "asFound": "Lip", "relevance": "HIGH"}, {"id": "M207501", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relevance": "LOW"}, {"id": "M10054", "name": "Hypoglycemic Agents", "relevance": "LOW"}, {"id": "M1691", "name": "Sodium-Glucose Transporter 2 Inhibitors", "relevance": "LOW"}], "browseBranches": [{"abbrev": "Hypo", "name": "Hypoglycemic Agents"}, {"abbrev": "All", "name": "All Drugs and Chemicals"}, {"abbrev": "Infl", "name": "Anti-Inflammatory Agents"}, {"abbrev": "<PERSON><PERSON>", "name": "Antirheumatic Agents"}, {"abbrev": "Analg", "name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "hasResults": true}}