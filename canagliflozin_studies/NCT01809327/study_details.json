{"nctId": "NCT01809327", "extractedAt": "python-requests/2.32.3", "dataSource": "ClinicalTrials.gov API v2", "tabType": "Study Details", "protocolSection": {"identificationModule": {"nctId": "NCT01809327", "orgStudyIdInfo": {"id": "CR100034"}, "secondaryIdInfos": [{"id": "28431754DIA3011", "type": "OTHER", "domain": "Janssen Research & Development, LLC"}, {"id": "2011-000400-17", "type": "EUDRACT_NUMBER"}], "organization": {"fullName": "Janssen Research & Development, LLC", "class": "INDUSTRY"}, "briefTitle": "A Study to Evaluate the Effectiveness, Safety, and Tolerability of Canagliflozin in Combination With Metformin in the Treatment of Patients With Type 2 Diabetes Mellitus With Inadequate Glycemic Control With Diet and Exercise", "officialTitle": "A Randomized, Double-Blind, 5-Arm, Parallel-Group, 26-Week, Multicenter Study to Evaluate the Efficacy, Safety, and Tolerability of Canagliflozin in Combination With Metformin as Initial Combination Therapy in the Treatment of Subjects With Type 2 Diabetes Mellitus With Inadequate Glycemic Control With Diet and Exercise"}, "statusModule": {"statusVerifiedDate": "2017-06", "overallStatus": "COMPLETED", "expandedAccessInfo": {"hasExpandedAccess": false}, "startDateStruct": {"date": "2013-06-04", "type": "ACTUAL"}, "primaryCompletionDateStruct": {"date": "2014-12-02", "type": "ACTUAL"}, "completionDateStruct": {"date": "2014-12-02", "type": "ACTUAL"}, "studyFirstSubmitDate": "2013-03-08", "studyFirstSubmitQcDate": "2013-03-08", "studyFirstPostDateStruct": {"date": "2013-03-12", "type": "ESTIMATED"}, "resultsFirstSubmitDate": "2015-12-01", "resultsFirstSubmitQcDate": "2015-12-01", "resultsFirstPostDateStruct": {"date": "2016-01-06", "type": "ESTIMATED"}, "lastUpdateSubmitDate": "2017-06-12", "lastUpdatePostDateStruct": {"date": "2017-07-11", "type": "ACTUAL"}}, "sponsorCollaboratorsModule": {"responsibleParty": {"type": "SPONSOR"}, "leadSponsor": {"name": "Janssen Research & Development, LLC", "class": "INDUSTRY"}}, "oversightModule": {"oversightHasDmc": true, "isFdaRegulatedDevice": false}, "descriptionModule": {"briefSummary": "The purpose of this study is to assess the effectiveness of the co-administration of canagliflozin and metformin extended release (XR) compared with canagliflozin alone, and metformin XR alone in patients with type 2 diabetes mellitus with inadequate control despite treatment with diet and exercise. The safety and tolerability of canagliflozin will also be assessed.", "detailedDescription": "This study will be a randomized (the study medication is assigned by chance), double-blind (neither physician nor participant knows the identity of the assigned treatment), active-controlled (one of the treatments is an established effective treatment for type 2 diabetes mellitus), parallel-group (each group of participants will be treated at the same time), 5-arm (groups), multicenter study. Approximately 1,200 participants will be randomly assigned to the 5 treatment arms in a 1:1:1:1:1 ratio for 26 weeks."}, "conditionsModule": {"conditions": ["Diabetes Mellitus, Type 2"], "keywords": ["Diabetes Mellitus, Type 2", "Diabe<PERSON>", "Canaglif<PERSON>zin (JNJ-28431754)", "Metformin"]}, "designModule": {"studyType": "INTERVENTIONAL", "phases": ["PHASE3"], "designInfo": {"allocation": "RANDOMIZED", "interventionModel": "PARALLEL", "primaryPurpose": "TREATMENT", "maskingInfo": {"masking": "QUADRUPLE", "whoMasked": ["PARTICIPANT", "CARE_PROVIDER", "INVESTIGATOR", "OUTCOMES_ASSESSOR"]}}, "enrollmentInfo": {"count": 1186, "type": "ACTUAL"}}, "armsInterventionsModule": {"armGroups": [{"label": "Canagliflozin 100 mg", "type": "EXPERIMENTAL", "description": "Participants will receive one 100 mg canagliflozin capsule before the morning meal and one matching placebo capsule with the evening meal plus placebo tablets with the evening meal (to match the metformin XR tablets administered in other treatment arms) for 26 weeks.", "interventionNames": ["Drug: Canagliflozin 100 mg"]}, {"label": "Canagliflozin 300 mg", "type": "EXPERIMENTAL", "description": "Participants will receive one 300 mg canagliflozin capsule before the morning meal and one matching placebo capsule with the evening meal plus placebo tablets with the evening meal (to match the metformin XR tablets administered in other treatment arms) for 26 weeks.", "interventionNames": ["Drug: Canagliflozin 300 mg"]}, {"label": "Metformin XR", "type": "EXPERIMENTAL", "description": "Participants will receive metformin XR tablets (in doses titrated over 9 weeks) once daily with the evening meal, plus one placebo capsule before the morning meal and one placebo capsule with the evening meal (to match the canagliflozin capsules administered in other treatment arms) for 26 weeks.", "interventionNames": ["Drug: Metformin XR"]}, {"label": "Canagliflozin 100 mg + Metformin XR", "type": "EXPERIMENTAL", "description": "Participants will receive one 100 mg canagliflozin capsule with the evening meal and one matching placebo capsule before the morning meal plus metformin XR tablets (in doses titrated over 9 weeks) once daily with the evening meal for 26 weeks.", "interventionNames": ["Drug: Canagliflozin 100 mg", "Drug: Metformin XR"]}, {"label": "Canagliflozin 300 mg + Metformin XR", "type": "EXPERIMENTAL", "description": "Participants will receive one 300 mg canagliflozin capsule with the evening meal and one matching placebo capsule before the morning meal plus metformin XR tablets (in doses titrated over 9 weeks) once daily with the evening meal for 26 weeks.", "interventionNames": ["Drug: Canagliflozin 300 mg", "Drug: Metformin XR"]}], "interventions": [{"type": "DRUG", "name": "Canagliflozin 100 mg", "description": "One 100 mg capsule taken orally (by mouth) once daily either before the morning meal (for the Canagliflozin 100 mg arm) or with the evening meal (for the Canagliflozin 100 mg + Metformin XR arm).", "armGroupLabels": ["Canagliflozin 100 mg", "Canagliflozin 100 mg + Metformin XR"]}, {"type": "DRUG", "name": "Canagliflozin 300 mg", "description": "One 300 mg capsule taken orally (by mouth) once daily either before the morning meal (for the Canagliflozin 300 mg arm) or with the evening meal (for the Canagliflozin 300 mg + Metformin XR arm).", "armGroupLabels": ["Canagliflozin 300 mg", "Canagliflozin 300 mg + Metformin XR"]}, {"type": "DRUG", "name": "Metformin XR", "description": "One 500 mg tablet (Day 1 up to week 1); two 500 mg tablets (Week 1 up to Week 3); three 500 mg tablets (Week 3 to Week 6); four 500 mg tablets (Week 6 to Week 9). Tablets will be administered with the evening meal.", "armGroupLabels": ["Canagliflozin 100 mg + Metformin XR", "Canagliflozin 300 mg + Metformin XR", "Metformin XR"]}]}, "outcomesModule": {"primaryOutcomes": [{"measure": "Change in Glycated Hemoglobin (HbA1c) From Baseline at Week 26", "description": "The change in the value of glycated hemoglobin (the concentration of glucose bound to hemoglobin as a percent of the absolute maximum that can be bound) from baseline at Week 26 was compared between the different treatment groups.", "timeFrame": "Day 1 (Baseline) and Week 26"}], "secondaryOutcomes": [{"measure": "Percent Change in Body Weight From Baseline to Week 26", "description": "The percentage change in body weight from baseline to Week 26 was compared between the different treatment groups.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percentage of Participants With Glycated Hemoglobin (HbAIc) Less Than 7 Percent at Week 26", "description": "The percentage of participants achieved HbAIc less than 7 percent at Week 26 was compared between the different treatment groups.", "timeFrame": "Week 26"}, {"measure": "Change in Systolic Blood Pressure From Baseline at Week 26", "description": "The change in systolic blood pressure from baseline at Week 26 was compared between the different treatment groups.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Fasting High-Density Lipoprotein Cholesterol (HDL-C) From Baseline to Week 26", "description": "The percentage change in Fasting High-Density Lipoprotein Cholesterol (HDL-C) from baseline to Week 26 was compared between the different treatment groups.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Percent Change in Triglycerides From Baseline to Week 26", "description": "The percentage change in triglycerides from baseline to Week 26 was compared between the different treatment groups.", "timeFrame": "Day 1 (Baseline) and Week 26"}, {"measure": "Number of Participants With Treatment Emergent Adverse Events (AEs)", "description": "An adverse event (AE) was any untoward medical occurrence in a participant who received study drug without regard to possibility of causal relationship. A serious adverse event (SAE) was an AE resulting in any of the following outcomes or deemed significant for any other reason: death; initial or prolonged inpatient hospitalization; life-threatening experience (immediate risk of dying); persistent or significant disability/incapacity; congenital anomaly. Treatment-emergent were events between administration of study drug and up to 30 days after last dose of study drug that were absent before treatment or that worsened relative to pre-treatment state.", "timeFrame": "Up to 30 weeks of last study drug administration"}]}, "eligibilityModule": {"eligibilityCriteria": "Inclusion Criteria:\n\n* Must have type 2 diabetes mellitus with inadequate glycemic control on diet and exercise\n* Not on antihyperglycemic agent therapy (at least 12 weeks before screening) and have a screening visit fingerstick glycated hemoglobin (HbA1c) of more than or equal to 7 percent and less than or equal to 12.5 percent\n* Have a screening visit HbA1c of more than or equal to 7.5 percent and less than or equal to 12 percent as determined by the central laboratory\n* Must have a fasting plasma glucose of less than or equal to 300 mg/dL (16.7 mmol/L) prior to randomization\n* Must have a fasting fingerstick glucose of greater than 120 mg/dL (6.7 mmol/L) performed at home or at the study center prior to randomization\n\nExclusion Criteria:\n\n* History of diabetic ketoacidosis, type 1 diabetes mellitus (T1DM), pancreas or beta-cell transplantation, or diabetes secondary to pancreatitis or pancreatectomy\n* Fasting C-peptide less than 0.70 ng/mL (0.23 nmol/L) in participants for whom the investigator cannot reasonably exclude T1DM based upon clinical evaluation\n* Repeated (2 or more over a 1 week period) fasting self-monitored blood glucose measurements more than 300 mg/dL (16.7 mmol/L) prior to randomization, despite reinforcement of diet and exercise counseling\n* History of hereditary glucose-galactose malabsorption or primary renal glucosuria\n* Has history of, or currently active, illness considered to be clinically significant by the Investigator or any other illness that the Investigator considers should exclude the patient from the study or that could interfere with the interpretation of the study results", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "18 Years", "maximumAge": "75 Years", "stdAges": ["ADULT", "OLDER_ADULT"]}, "contactsLocationsModule": {"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Birmingham", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.52066, "lon": -86.80249}}, {"city": "Phoenix", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.44838, "lon": -112.07404}}, {"city": "Encinitas", "state": "California", "country": "United States", "geoPoint": {"lat": 33.03699, "lon": -117.29198}}, {"city": "Newport Beach", "state": "California", "country": "United States", "geoPoint": {"lat": 33.61891, "lon": -117.92895}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Norwalk", "state": "California", "country": "United States", "geoPoint": {"lat": 33.90224, "lon": -118.08173}}, {"city": "Rancho Cucamonga", "state": "California", "country": "United States", "geoPoint": {"lat": 34.1064, "lon": -117.59311}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Northglenn", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.88554, "lon": -104.9872}}, {"city": "Hialeah", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.8576, "lon": -80.27811}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "New Port Richey", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.24418, "lon": -82.71927}}, {"city": "Opa-locka", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.90232, "lon": -80.25033}}, {"city": "<PERSON>", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.45821, "lon": -83.73157}}, {"city": "Evansville", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 37.97476, "lon": -87.55585}}, {"city": "Mandeville", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.35825, "lon": -90.06563}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.98409, "lon": -90.15285}}, {"city": "Sunset", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.41131, "lon": -92.06845}}, {"city": "Flint", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 43.01253, "lon": -83.68746}}, {"city": "Kalamazoo", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.29171, "lon": -85.58723}}, {"city": "Royal Oak", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.48948, "lon": -83.14465}}, {"city": "Troy", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.60559, "lon": -83.14993}}, {"city": "Olive Branch", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 34.96176, "lon": -89.82953}}, {"city": "Picayune", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 30.52556, "lon": -89.67788}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "West Seneca", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.85006, "lon": -78.79975}}, {"city": "<PERSON><PERSON><PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.73319, "lon": -81.3412}}, {"city": "Mooresville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.58486, "lon": -80.81007}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.55895, "lon": -84.30411}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.36006, "lon": -84.30994}}, {"city": "Perrysburg", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.557, "lon": -83.62716}}, {"city": "Zanesville", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.94035, "lon": -82.01319}}, {"city": "Oklahoma City", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.46756, "lon": -97.51643}}, {"city": "Yukon", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.50672, "lon": -97.76254}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.38401, "lon": -122.76399}}, {"city": "Fleetwood", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.45398, "lon": -75.81798}}, {"city": "Norristown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.1215, "lon": -75.3399}}, {"city": "Pittsburgh", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.44062, "lon": -79.99589}}, {"city": "Rapid City", "state": "South Dakota", "country": "United States", "geoPoint": {"lat": 44.08054, "lon": -103.23101}}, {"city": "Nashville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.16589, "lon": -86.78444}}, {"city": "Austin", "state": "Texas", "country": "United States", "geoPoint": {"lat": 30.26715, "lon": -97.74306}}, {"city": "Bellaire", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.70579, "lon": -95.45883}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "Pearland", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.56357, "lon": -95.28605}}, {"city": "Plano", "state": "Texas", "country": "United States", "geoPoint": {"lat": 33.01984, "lon": -96.69889}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "Danville", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.58597, "lon": -79.39502}}, {"city": "Norfolk", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.84681, "lon": -76.28522}}, {"city": "Richmond", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 37.55376, "lon": -77.46026}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma De Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Cordoba", "country": "Argentina", "geoPoint": {"lat": -31.4135, "lon": -64.18105}}, {"city": "Mar Del Plata", "country": "Argentina", "geoPoint": {"lat": -38.00228, "lon": -57.55754}}, {"city": "<PERSON><PERSON>", "country": "Argentina", "geoPoint": {"lat": -34.6509, "lon": -58.61956}}, {"city": "Rosario", "country": "Argentina", "geoPoint": {"lat": -32.94682, "lon": -60.63932}}, {"city": "Zarate", "country": "Argentina", "geoPoint": {"lat": -34.09814, "lon": -59.02858}}, {"city": "Passo Fundo", "country": "Brazil", "geoPoint": {"lat": -28.26278, "lon": -52.40667}}, {"city": "Sao Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "São Paulo", "country": "Brazil", "geoPoint": {"lat": -23.5475, "lon": -46.63611}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 49.29785, "lon": 17.39312}}, {"city": "Pardubice", "country": "Czechia", "geoPoint": {"lat": 50.04075, "lon": 15.77659}}, {"city": "Praha 8", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czechia", "geoPoint": {"lat": 50.08804, "lon": 14.42076}}, {"city": "Unicov", "country": "Czechia", "geoPoint": {"lat": 49.77092, "lon": 17.12144}}, {"city": "Balatonfured", "country": "Hungary", "geoPoint": {"lat": 46.96188, "lon": 17.87187}}, {"city": "Budapest", "country": "Hungary", "geoPoint": {"lat": 47.49801, "lon": 19.03991}}, {"city": "<PERSON><PERSON>", "country": "Hungary", "geoPoint": {"lat": 47.90265, "lon": 20.37329}}, {"city": "Szikszó", "country": "Hungary", "geoPoint": {"lat": 48.2, "lon": 20.93333}}, {"city": "Zalaegerszeg", "country": "Hungary", "geoPoint": {"lat": 46.84, "lon": 16.84389}}, {"city": "Goyang-Si", "country": "Korea, Republic of", "geoPoint": {"lat": 37.65639, "lon": 126.835}}, {"city": "Seoul", "country": "Korea, Republic of", "geoPoint": {"lat": 37.566, "lon": 126.9784}}, {"city": "<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.29111, "lon": 127.00889}}, {"city": "Celaya", "country": "Mexico", "geoPoint": {"lat": 20.52353, "lon": -100.8157}}, {"city": "Durango", "country": "Mexico", "geoPoint": {"lat": 24.02032, "lon": -104.65756}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Mexico", "geoPoint": {"lat": 20.11697, "lon": -98.73329}}, {"city": "Tampico", "country": "Mexico", "geoPoint": {"lat": 22.26695, "lon": -97.86815}}, {"city": "Carolina", "country": "Puerto Rico", "geoPoint": {"lat": 18.38078, "lon": -65.95739}}, {"city": "Ponce", "country": "Puerto Rico", "geoPoint": {"lat": 18.01108, "lon": -66.61406}}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "Trujillo Alto", "country": "Puerto Rico", "geoPoint": {"lat": 18.35467, "lon": -66.00739}}, {"city": "Bacau", "country": "Romania", "geoPoint": {"lat": 46.56718, "lon": 26.91384}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 44.42802, "lon": 26.09665}}, {"city": "Oradea", "country": "Romania", "geoPoint": {"lat": 47.0458, "lon": 21.91833}}, {"city": "Tg Mu<PERSON>", "country": "Romania"}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Barnaul", "country": "Russian Federation", "geoPoint": {"lat": 53.36056, "lon": 83.76361}}, {"city": "Chelyabinsk", "country": "Russian Federation", "geoPoint": {"lat": 55.15402, "lon": 61.42915}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 53.20066, "lon": 45.00464}}, {"city": "Rostov-On-Don", "country": "Russian Federation", "geoPoint": {"lat": 47.23135, "lon": 39.72328}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Saint-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "Smolensk", "country": "Russian Federation", "geoPoint": {"lat": 54.7818, "lon": 32.0401}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 61.67642, "lon": 50.80994}}, {"city": "Tomsk", "country": "Russian Federation", "geoPoint": {"lat": 56.49771, "lon": 84.97437}}, {"city": "Tyumen", "country": "Russian Federation", "geoPoint": {"lat": 57.15222, "lon": 65.52722}}, {"city": "Voronezh", "country": "Russian Federation", "geoPoint": {"lat": 51.67204, "lon": 39.1843}}, {"city": "Banska Bystrica", "country": "Slovakia", "geoPoint": {"lat": 48.73946, "lon": 19.15349}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "Malacky", "country": "Slovakia", "geoPoint": {"lat": 48.43604, "lon": 17.02188}}, {"city": "Presov", "country": "Slovakia", "geoPoint": {"lat": 48.99839, "lon": 21.23393}}, {"city": "Rimavska Sobota", "country": "Slovakia", "geoPoint": {"lat": 48.38284, "lon": 20.02239}}, {"city": "<PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.07408, "lon": 18.94946}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.62858, "lon": 21.71954}}, {"city": "Cape Town", "country": "South Africa", "geoPoint": {"lat": -33.92584, "lon": 18.42322}}, {"city": "Durban", "country": "South Africa", "geoPoint": {"lat": -29.8579, "lon": 31.0292}}, {"city": "Halfway", "country": "South Africa"}, {"city": "Johannesburg N/A", "country": "South Africa"}, {"city": "Johannesburg", "country": "South Africa", "geoPoint": {"lat": -26.20227, "lon": 28.04363}}, {"city": "Soweto, Johannesburg", "country": "South Africa"}, {"city": "Worcester", "country": "South Africa", "geoPoint": {"lat": -33.64651, "lon": 19.44852}}, {"city": "Cherkasy", "country": "Ukraine", "geoPoint": {"lat": 49.42854, "lon": 32.06207}}, {"city": "Dnepropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "Donetsk", "country": "Ukraine", "geoPoint": {"lat": 48.023, "lon": 37.80224}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 48.9215, "lon": 24.70972}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Kyiv", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Lviv", "country": "Ukraine", "geoPoint": {"lat": 49.83826, "lon": 24.02324}}, {"city": "Odesa", "country": "Ukraine", "geoPoint": {"lat": 46.47747, "lon": 30.73262}}, {"city": "Poltava", "country": "Ukraine", "geoPoint": {"lat": 49.59373, "lon": 34.54073}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 50.9216, "lon": 34.80029}}, {"city": "Zaporozhye", "country": "Ukraine", "geoPoint": {"lat": 47.82289, "lon": 35.19031}}]}, "referencesModule": {"references": [{"pmid": "26786577", "type": "DERIVED", "citation": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Initial Combination Therapy With Canagliflozin Plus Metformin Versus Each Component as Monotherapy for Drug-Naive Type 2 Diabetes. Diabetes Care. 2016 Mar;39(3):353-62. doi: 10.2337/dc15-1736. Epub 2016 Jan 19."}]}}}